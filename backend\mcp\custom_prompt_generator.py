from .prompts import (
    get_mcp_servers_prompt,
    MCP_PROMPT,
    MCP_EXAMPLES,
    SYSTEM_PROTECTION_GUIDELINES,
    MCP_CAPABILITIES,
    MCP_TOOL_GUIDELINES,
    MCP_TOOL_EXECUTION_FLOW,
    HARD_CONSTRAINT,
    MCP_STATEFUL_TOOL_MANAGEMENT_RULES,
    FILESYSTEM_TOOL_GUIDELINES,
    MEMORY_TOOLS_USAGE_GUIDE,
)

def generate_custom_prompt(
    role_definition: str,
    background: str,
    description: str,
    core_rules: list[str],
    mcp_tools: list[dict],
    role_name: str = "",
    team_name: str = "",
    team_rules: list[str] = None,
    include_system_protection: bool = True,
    include_mcp_capabilities: bool = True,
    include_mcp_examples: bool = True,
    include_mcp_definitions: bool = True,
    custom_sections_before_tools: dict = None,
    custom_sections_after_tools: dict = None,
    is_langgraph_agent: bool = False,
) -> str:
    """
    根据用户提供的参数构建一个完整的提示词模板。

    Args:
        role_definition (str): 角色定义。
        background (str): 背景信息。
        description (str): 任务或场景的描述。
        core_rules (list[str]): 核心规则列表。
        mcp_tools (list[dict]): MCP工具列表，用于生成工具信息。
                                示例格式: [
                                    {
                                        "name": "tool_name_example",
                                        "server_name": "server_example",
                                        "description": "Tool description.",
                                        "inputSchema": {
                                            "type": "object",
                                            "properties": {
                                                "param1": {"type": "string", "description": "Param 1 desc."},
                                            },
                                            "required": ["param1"]
                                        }
                                    }
                                ]
        role_name (str): 角色名称，用于明确告知当前角色的名称。
        team_name (str): 团队名称，用于标识角色所属团队。
        team_rules (list[str]): 团队协作规则列表。
        include_system_protection (bool): 是否包含系统保护准则。
        include_mcp_capabilities (bool): 是否包含MCP能力提示词。
        include_mcp_examples (bool): 是否包含MCP工具使用示例。
        include_mcp_definitions (bool): 是否包含MCP基础提示词 (tool definitions)。
        custom_sections_before_tools (dict): 在工具信息前添加的自定义文本段落。
                                            示例: {"My Custom Section Title": "Content..."}
        custom_sections_after_tools (dict): 在工具信息后添加的自定义文本段落。
                                           示例: {"Another Custom Section": "More content..."}
        is_langgraph_agent (bool): 是否为LangGraph框架智能体，默认为False。
                                  设为True时会自动集成记忆工具。

    Returns:
        str: 构建完成的提示词。
    """
    prompt_parts = []

    # 🌏 语言指令 - 最高优先级
    prompt_parts.append("请使用中文回答所有问题和交互。")

    if role_name and team_name:
        prompt_parts.append(f"You are {role_name} from {team_name} team")
    elif role_name:
        prompt_parts.append(f"Your Name is {role_name}")
    if role_definition:
        prompt_parts.append(f"\\n# Role Definition\\n{role_definition}")
    if background:
        prompt_parts.append(f"\\n## Background\\n{background}")
    
    if team_rules:
        team_rules_text = "\n".join(f"- {rule}" for rule in team_rules)
        prompt_parts.append(f"\n\n## Team Collaboration Rules\nAs a team member, you need to follow these collaboration rules:\n{team_rules_text}")
    
    # 🧠 添加记忆工具使用规范 (在团队规则后面，靠前位置)
    if is_langgraph_agent:
        prompt_parts.append(f"\n\n{MEMORY_TOOLS_USAGE_GUIDE}")
    
    if core_rules:
        rules_text = "\n".join(f"- {rule}" for rule in core_rules)
        prompt_parts.append(f"\n\n## Core Rules\n{rules_text}")
    
    if description:
        prompt_parts.append(f"\\n## Description\\n{description}")

    prompt_parts.append(f"\\n{HARD_CONSTRAINT}")

    if include_system_protection:
        prompt_parts.append(f"\\n{SYSTEM_PROTECTION_GUIDELINES}")

    if include_mcp_capabilities:
        prompt_parts.append(f"\\n{MCP_CAPABILITIES}")

    if custom_sections_before_tools:
        for title, content in custom_sections_before_tools.items():
            prompt_parts.append(f"\\n## {title}\\n{content}")

    servers_prompt_text = get_mcp_servers_prompt(mcp_tools, is_langgraph_agent)
    mcp_servers_header = (
        "\\n====\\n\\nMCP SERVERS\\n\\n"
        "The Model Context Protocol (MCP) enables communication between the system and "
        "locally running MCP servers that provide additional tools and resources to extend your capabilities.\\n\\n"
        "# Connected MCP Servers\\n"
        "When a server is connected, you can use the server's tools via the `use_mcp_tool` tool, "
        "and access the server's resources via the `access_mcp_resource` tool."
    )

    if servers_prompt_text and servers_prompt_text.strip() and servers_prompt_text != '(No MCP servers currently connected)':
        prompt_parts.append(f"{mcp_servers_header}\\n{servers_prompt_text}")
    else:
        prompt_parts.append(f"{mcp_servers_header}\\n(No MCP servers currently connected or no tools provided to the generator)")

    prompt_parts.append(f"\n\n## MCP Tool Use Guidelines\n{MCP_TOOL_GUIDELINES}")
    prompt_parts.append(f"\n\n{MCP_TOOL_EXECUTION_FLOW}")

    # Dynamically add rules based on tool availability
    has_decomposition_tool = False
    has_filesystem_tool = False
    if mcp_tools:
        for tool in mcp_tools:
            if isinstance(tool, dict):
                tool_name = tool.get('name', '').lower()
                server_name = tool.get('serverName', '').lower()

                if 'task-decomposition' in tool_name:
                    has_decomposition_tool = True

                fs_commands = ['ls', 'cd', 'pwd', 'mkdir', 'rm', 'cat', 'read_file', 'write_file']
                tool_name_part = tool_name.split('_')[-1]
                
                if ('filesystem' in tool_name or 
                    tool_name.startswith('filesystem_') or 
                    server_name == 'filesystem' or 
                    tool_name_part in fs_commands or 
                    tool_name in fs_commands):
                    has_filesystem_tool = True

    if has_decomposition_tool:
        prompt_parts.append(f"\n\n## Stateful Tool Management Guidelines\n{MCP_STATEFUL_TOOL_MANAGEMENT_RULES}")

    if has_filesystem_tool:
        prompt_parts.append(f"\n\n## Filesystem Tool Usage Guidelines\n{FILESYSTEM_TOOL_GUIDELINES}")

    if include_mcp_definitions:
        prompt_parts.append(f"\n\n# MCP Tool Definitions\n{MCP_PROMPT}")

    if include_mcp_examples:
        prompt_parts.append(f"\n\n# Examples of Using MCP Tools\n{MCP_EXAMPLES}")

    if custom_sections_after_tools:
        for title, content in custom_sections_after_tools.items():
            prompt_parts.append(f"\n\n## {title}\n{content}")

    return "\n".join(prompt_parts).strip()

if __name__ == '__main__':
    # 此处的示例 mcp_tools 结构需要与 get_mcp_servers_prompt 函数期望的输入一致。
    # prompts.py 中的 get_mcp_servers_prompt 会处理多种工具格式，
    # 这里提供一个其能够解析的示例。
    sample_mcp_tools_for_generator = [
        {
            "name": "weather_get_forecast", # 通常是 servername_toolname 格式或仅 toolname
            "server_name": "weather",      # 或者 server 键
            "description": "Get the weather forecast for a specific city.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "city": {"type": "string", "description": "The city for the forecast."},
                    "days": {"type": "integer", "description": "Number of forecast days."}
                },
                "required": ["city"]
            }
        },
        {
            "name": "filesystem_read_file",
            "server_name": "filesystem",
            "description": "Reads the content of a specified file.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The full path to the file."}
                },
                "required": ["path"]
            }
        },
        {
            # 示例: 一个没有明确 server_name 但可以从 name 推断的工具
            "name": "utility_calculate_sum",
            "description": "Calculates the sum of a list of numbers.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "numbers": {
                        "type": "array",
                        "items": {"type": "number"},
                        "description": "A list of numbers to sum."
                    }
                },
                "required": ["numbers"]
            }
        }
    ]

    custom_prompt_output = generate_custom_prompt(
        role_definition="You are an advanced AI planning assistant.",
        background="The system is designed to help users break down complex tasks into manageable steps.",
        description="Your primary function is to analyze user requests, identify necessary tools, and outline a plan of action.",
        core_rules=[
            "Always prioritize safety and clarity.",
            "Ensure all required tool parameters are identified.",
            "Communicate the plan clearly before execution."
        ],
        mcp_tools=sample_mcp_tools_for_generator,
        include_system_protection=True,
        include_mcp_capabilities=True,
        include_mcp_examples=True,
        include_mcp_definitions=True,
        custom_sections_before_tools={"Critical Instructions": "Pay close attention to the tool schemas provided below."},
        custom_sections_after_tools={"Final Check": "Review the generated plan for completeness and accuracy."}
    )
    print("--- Generated Custom Prompt ---")
    print(custom_prompt_output)

    # 示例: 没有工具的情况
    # custom_prompt_no_tools = generate_custom_prompt(
    #     role_definition="You are a simple greeter bot.",
    #     background="This is a test environment.",
    #     description="Your goal is to say hello.",
    #     core_rules=["Be friendly."],
    #     mcp_tools=[], # 空的工具列表
    # )
    # print("\n--- Generated Custom Prompt (No Tools) ---")
    # print(custom_prompt_no_tools) 