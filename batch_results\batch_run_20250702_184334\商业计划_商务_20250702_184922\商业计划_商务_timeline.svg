<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 定义线性渐变，用于背景装饰和强调元素 -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="gradientText" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- 定义滤镜，用于卡片阴影效果 -->
    <filter id="shadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)" />
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)" />
    </filter>
  </defs>

  <style>
    /* 全局颜色定义 */
    .bg-color { fill: #F8FAFC; }
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .text-light { fill: #94A3B8; }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .card-bg { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; }
    .container-bg { fill: #E0F2FE; } /* 根据设计规范中的container_background */

    /* 字体样式定义 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
    .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

    /* 卡片样式 */
    .card {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      rx: 12px; /* 圆角半径 */
      ry: 12px;
      filter: url(#shadowFilter); /* 应用阴影滤镜 */
    }

    /* 时间轴特定样式 */
    .timeline-line-main {
      fill: #E0F2FE; /* 主时间轴线条的填充色，使用容器背景色 */
      rx: 4px; /* 增加圆角使线条更柔和 */
      ry: 4px;
    }
    .timeline-arrow-head {
      fill: #E0F2FE; /* 时间轴箭头颜色 */
    }

    .timeline-node-circle {
      fill: #1E40AF; /* 时间节点圆点的主色 */
      stroke: #BAE6FD;
      stroke-width: 2px;
    }
    .timeline-node-circle-accent {
      fill: #3B82F6; /* 里程碑节点圆点的强调色 */
      stroke: #1E40AF;
      stroke-width: 3px;
    }
    .timeline-node-line {
      stroke: #BAE6FD; /* 连接时间节点与卡片的线条 */
      stroke-width: 2px;
      stroke-linecap: round;
    }
    .milestone-node-line {
      stroke: #3B82F6; /* 里程碑节点连接线的强调色 */
      stroke-width: 3px;
      stroke-linecap: round;
    }

    .timeline-date {
      fill: #1E40AF; /* 日期文本的主色 */
      font-size: 20px;
      font-weight: 600;
    }
    .timeline-title {
      fill: #1E293B; /* 标题文本的主色 */
      font-size: 24px;
      font-weight: 600;
    }
    .timeline-description {
      fill: #64748B; /* 描述文本的辅助色 */
      font-size: 18px;
      font-weight: 400;
    }
    .milestone-label {
      fill: #3B82F6; /* 里程碑标签的强调色 */
      font-size: 18px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    /* 装饰元素样式 */
    .geometric-shape-bg {
      fill: #E0F2FE; /* 几何背景形状的填充色 */
      opacity: 0.3; /* 半透明效果 */
    }
    .geometric-shape-accent {
      fill: #3B82F6; /* 几何强调形状的填充色 */
      opacity: 0.1;
    }
    .divider-decorative {
      stroke: url(#gradientAccent); /* 装饰性分割线的渐变描边 */
      stroke-width: 2px;
      fill: none;
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- 装饰性几何图形，增加页面的现代感和层次感 -->
  <g class="decorative-elements">
    <rect x="0" y="0" width="200" height="200" class="geometric-shape-bg" transform="translate(-50, -50) rotate(45)" />
    <circle cx="1920" cy="1080" r="180" class="geometric-shape-bg" transform="translate(50, 50)" />
    <polygon points="1700,0 1920,0 1920,150" class="geometric-shape-accent" opacity="0.15" />
    <polygon points="0,930 0,1080 220,1080" class="geometric-shape-accent" opacity="0.15" />
  </g>

  <!-- 页面标题区域 -->
  <g class="page-title-section">
    <text x="960" y="120" text-anchor="middle" class="main-title text-primary font-primary">{title}</text>
    <text x="960" y="180" text-anchor="middle" class="body-text text-secondary font-secondary">{subtitle}</text>
    <!-- 装饰性分割线 -->
    <path d="M760 220 H1160" class="divider-decorative" />
  </g>

  <!-- 时间轴布局容器 -->
  <g class="timeline-container" transform="translate(180, 360)">
    <!-- 主时间轴线条 (使用矩形模拟粗线条) -->
    <rect x="0" y="-8" width="1560" height="16" class="timeline-line-main" />
    <!-- 时间流向箭头 -->
    <polygon points="1560,-16 1580,0 1560,16" class="timeline-arrow-head" />

    <!-- 时间轴节点示例 (共5个节点，其中一个为里程碑) -->

    <!-- 节点 1 -->
    <g class="timeline-node node-1" transform="translate(0, 0)">
      <circle cx="0" cy="0" r="12" class="timeline-node-circle" />
      <line x1="0" y1="12" x2="0" y2="60" class="timeline-node-line" />
      <rect x="-120" y="60" width="240" height="150" class="card" />
      <text x="0" y="95" text-anchor="middle" class="timeline-date font-primary">{date_1}</text>
      <text x="0" y="125" text-anchor="middle" class="timeline-title font-primary">{event_title_1}</text>
      <text x="0" y="155" text-anchor="middle" class="timeline-description font-secondary">{event_desc_1}</text>
    </g>

    <!-- 节点 2 -->
    <g class="timeline-node node-2" transform="translate(390, 0)">
      <circle cx="0" cy="0" r="12" class="timeline-node-circle" />
      <line x1="0" y1="12" x2="0" y2="60" class="timeline-node-line" />
      <rect x="-120" y="60" width="240" height="150" class="card" />
      <text x="0" y="95" text-anchor="middle" class="timeline-date font-primary">{date_2}</text>
      <text x="0" y="125" text-anchor="middle" class="timeline-title font-primary">{event_title_2}</text>
      <text x="0" y="155" text-anchor="middle" class="timeline-description font-secondary">{event_desc_2}</text>
    </g>

    <!-- 里程碑节点 3 (视觉上更突出) -->
    <g class="timeline-node node-3 milestone" transform="translate(780, 0)">
      <circle cx="0" cy="0" r="20" class="timeline-node-circle-accent" />
      <line x1="0" y1="20" x2="0" y2="70" class="milestone-node-line" />
      <rect x="-140" y="70" width="280" height="180" class="card" />
      <text x="0" y="105" text-anchor="middle" class="milestone-label font-primary">里程碑</text>
      <text x="0" y="135" text-anchor="middle" class="timeline-date font-primary">{date_3}</text>
      <text x="0" y="165" text-anchor="middle" class="timeline-title font-primary">{event_title_3}</text>
      <text x="0" y="195" text-anchor="middle" class="timeline-description font-secondary">{event_desc_3}</text>
      <!-- 里程碑图标 -->
      <path d="M -8 0 L 0 -12 L 8 0 L 0 12 Z" fill="#3B82F6" transform="translate(0, -30)" />
    </g>

    <!-- 节点 4 -->
    <g class="timeline-node node-4" transform="translate(1170, 0)">
      <circle cx="0" cy="0" r="12" class="timeline-node-circle" />
      <line x1="0" y1="12" x2="0" y2="60" class="timeline-node-line" />
      <rect x="-120" y="60" width="240" height="150" class="card" />
      <text x="0" y="95" text-anchor="middle" class="timeline-date font-primary">{date_4}</text>
      <text x="0" y="125" text-anchor="middle" class="timeline-title font-primary">{event_title_4}</text>
      <text x="0" y="155" text-anchor="middle" class="timeline-description font-secondary">{event_desc_4}</text>
    </g>

    <!-- 节点 5 -->
    <g class="timeline-node node-5" transform="translate(1560, 0)">
      <circle cx="0" cy="0" r="12" class="timeline-node-circle" />
      <line x1="0" y1="12" x2="0" y2="60" class="timeline-node-line" />
      <rect x="-120" y="60" width="240" height="150" class="card" />
      <text x="0" y="95" text-anchor="middle" class="timeline-date font-primary">{date_5}</text>
      <text x="0" y="125" text-anchor="middle" class="timeline-title font-primary">{event_title_5}</text>
      <text x="0" y="155" text-anchor="middle" class="timeline-description font-secondary">{event_desc_5}</text>
    </g>

  </g>

  <!-- 页面底部信息 -->
  <g class="footer-section">
    <text x="960" y="1030" text-anchor="middle" class="small-text text-light font-secondary">页面 8/10 | {author}</text>
  </g>

  <!-- Logo 占位符 -->
  <g class="logo-placeholder">
    <rect x="80" y="60" width="150" height="50" fill="#E0F2FE" rx="8" ry="8" />
    <text x="155" y="90" text-anchor="middle" class="small-text text-primary font-primary">LOGO</text>
    <!-- 如果有实际Logo图片，请取消注释下方image标签，并替换xlink:href="{logo_url}" -->
    <!-- <image xlink:href="{logo_url}" x="80" y="60" width="150" height="50" preserveAspectRatio="xMidYMid meet" /> -->
  </g>

</svg>