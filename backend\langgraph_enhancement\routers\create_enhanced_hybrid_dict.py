# -*- coding: utf-8 -*-
"""创建增强版混合词典 - 包含更多领域"""

import json
import jieba
from collections import defaultdict

def create_enhanced_dictionary():
    # 加载jieba词典
    jieba_dict = {}
    dict_path = jieba.get_dict_file().name

    print("📚 加载jieba词典...")
    with open(dict_path, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split(' ')
            if len(parts) >= 2:
                word = parts[0]
                freq = int(parts[1])
                pos = parts[2] if len(parts) > 2 else 'x'
                jieba_dict[word] = {"freq": freq, "pos": pos}
    print(f"✅ 加载完成: {len(jieba_dict)} 词条")

    semantic_dict = defaultdict(list)
    
    # 定义要提取的语义类别和规则
    semantic_rules = {
        "task_verbs": {
            "keywords": ["分析", "生成", "创建", "计算", "设计", "开发", "研究", "评估", "规划", "编写", "处理", "优化", "执行", "实现", "构建"],
            "pos_filter": ["v", "vn"],
            "min_freq": 1000
        },
        "travel_words": {
            "keywords": ["旅游", "旅行", "行程", "路线", "景点", "攻略", "游览", "观光", "出行", "目的地", "交通", "住宿", "酒店", "机票", "门票"],
            "pos_filter": ["n", "v", "j"],
            "min_freq": 500
        },
        "location_words": {
            "keywords": [], # 地名直接按词性提取
            "pos_filter": ["ns"],
            "min_freq": 1000
        },
        "general_nouns": {
            "keywords": ["信息", "内容", "方案", "建议", "问题", "方法", "服务", "需求", "情况", "时间", "地方", "东西"],
            "pos_filter": ["n", "nz"],
            "min_freq": 5000
        },
        "greeting_words": {
            "keywords": ["你好", "您好", "早上好", "晚上好", "再见", "欢迎"],
            "pos_filter": ["l", "i", "v"],
            "min_freq": 100
        },
        "question_words": {
            "keywords": ["什么", "怎么", "为什么", "哪里", "谁", "何时", "如何", "多少", "哪个", "哪些"],
            "pos_filter": ["r", "d"],
            "min_freq": 1000
        }
    }

    # 提取词汇
    for category, rules in semantic_rules.items():
        print(f"\n🔍 筛选 {category}...")
        for word, info in jieba_dict.items():
            if info["pos"] in rules["pos_filter"] and info["freq"] >= rules["min_freq"]:
                # 如果有关键词，则必须匹配；否则直接添加
                if not rules["keywords"] or any(kw in word for kw in rules["keywords"]):
                    semantic_dict[category].append({
                        "word": word,
                        "freq": info["freq"],
                        "pos": info["pos"]
                    })
    
    # 排序并限制数量
    for category in semantic_dict:
        semantic_dict[category].sort(key=lambda x: x["freq"], reverse=True)
        # 对地名和旅游词汇保留更多
        limit = 100 if category in ["location_words", "travel_words"] else 50
        semantic_dict[category] = semantic_dict[category][:limit]

    # 创建并保存词典
    hybrid_dict = {
        "metadata": {
            "source": "jieba + enhanced semantic mapping",
            "version": "2.0",
            "total_words": sum(len(words) for words in semantic_dict.values())
        },
        "semantic_categories": {
            cat: {
                "words": [w["word"] for w in words],
                "word_info": {w["word"]: {"freq": w["freq"], "pos": w["pos"]} for w in words},
                "total_count": len(words)
            } for cat, words in semantic_dict.items()
        }
    }
    
    output_filename = "hybrid_dictionary.json"
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(hybrid_dict, f, ensure_ascii=False, indent=2)

    # 打印统计
    print("\n📊 增强版混合词典统计:")
    print(f"  总词汇数: {hybrid_dict['metadata']['total_words']}")
    for cat, info in hybrid_dict['semantic_categories'].items():
        print(f"  {cat}: {info['total_count']} 词")
    print(f"\n✅ 增强版混合词典已保存到 {output_filename}")

if __name__ == "__main__":
    create_enhanced_dictionary() 