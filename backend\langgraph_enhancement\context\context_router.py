"""
🔥 智能上下文路由器 - LangGraph 增强系统

功能：
- 基于上下文状态智能路由
- 自动触发压缩和优化
- 智能任务复杂度评估
- 动态处理策略选择
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum

# 导入压缩引擎
from .compression_engine import ContextCompressionEngine, CompressionLevel

logger = logging.getLogger(__name__)


class ProcessingStrategy(Enum):
    """处理策略枚举"""
    DIRECT = "direct"                    # 直接处理
    COMPRESS_THEN_PROCESS = "compress"   # 先压缩再处理
    MULTI_AGENT = "multi_agent"          # 多智能体协作
    OPTIMIZATION_REQUIRED = "optimize"   # 需要优化


class TaskComplexity(Enum):
    """任务复杂度枚举"""
    SIMPLE = "simple"        # 简单任务
    MEDIUM = "medium"        # 中等复杂任务  
    COMPLEX = "complex"      # 复杂任务
    VERY_COMPLEX = "very_complex"  # 非常复杂任务


class SmartContextRouter:
    """
    🔥 智能上下文路由器
    
    核心功能：
    - 分析上下文状态和任务复杂度
    - 智能选择最优处理策略
    - 自动触发上下文优化
    - 动态路由决策
    """
    
    def __init__(self, compression_engine: ContextCompressionEngine = None):
        """初始化智能路由器"""
        self.compression_engine = compression_engine or ContextCompressionEngine()
        
        # 路由阈值配置
        self.routing_config = {
            'context_size_thresholds': {
                'small': 2000,
                'medium': 8000,
                'large': 16000,
                'very_large': 32000
            },
            'complexity_keywords': {
                'simple': ['简单', '基础', '快速', '直接'],
                'medium': ['分析', '比较', '总结', '整理'],
                'complex': ['深入', '详细', '复杂', '全面', '系统'],
                'very_complex': ['综合', '多维', '深度', '战略', '架构']
            },
            'compression_triggers': {
                'size_based': 5000,      # 🔥 优化：合理的压缩触发阈值
                'repetition_based': 0.3,  # 降低重复率阈值，更严格的重复检测
                'quality_based': 0.7      # 提高质量阈值，保证内容质量
            }
        }
        
        logger.info("🔥 智能上下文路由器初始化完成")
    
    async def smart_route(self, state: Dict) -> Tuple[ProcessingStrategy, Dict]:
        """智能路由主入口"""
        return await self.route_context_strategy(state)
    
    async def route_context_strategy(self, state: Dict) -> Tuple[ProcessingStrategy, Dict]:
        """
        智能路由主方法
        
        Args:
            state: LangGraph 状态字典
            
        Returns:
            Tuple[ProcessingStrategy, Dict]: (处理策略, 路由元数据)
        """
        try:
            logger.info("🧠 开始智能路由分析")
            
            # 1. 提取关键信息
            messages = state.get('messages', [])
            current_task = self._extract_current_task(messages)
            
            # 2. 分析上下文状态
            context_analysis = await self._analyze_context_state(messages)
            
            # 3. 评估任务复杂度
            task_complexity = self._evaluate_task_complexity(current_task)
            
            # 4. 智能决策路由策略
            strategy, actions = self._decide_processing_strategy(
                context_analysis, task_complexity, state
            )
            
            # 5. 生成路由元数据
            routing_metadata = {
                'context_analysis': context_analysis,
                'task_complexity': task_complexity.value,
                'processing_strategy': strategy.value,
                'actions': actions,
                'current_task': current_task,
                'routing_reason': self._explain_routing_decision(
                    context_analysis, task_complexity, strategy
                )
            }
            
            logger.info(f"🎯 路由决策: {strategy.value} (任务复杂度: {task_complexity.value})")
            return strategy, routing_metadata
            
        except Exception as e:
            logger.error(f"❌ 智能路由失败: {str(e)}")
            return ProcessingStrategy.DIRECT, {'error': str(e)}
    
    def _extract_current_task(self, messages: List[Dict]) -> str:
        """提取当前任务描述"""
        if not messages:
            return ""
        
        # 获取最后一条用户消息作为当前任务
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                return msg.get('content', '')
        
        return ""
    
    async def _analyze_context_state(self, messages: List[Dict]) -> Dict:
        """分析上下文状态"""
        if not messages:
            return {
                'size': 0,
                'message_count': 0,
                'repetition_score': 0.0,
                'quality_score': 1.0,
                'needs_compression': False
            }
        
        # 基础统计
        context_size = self.compression_engine._estimate_context_size(messages)
        message_count = len(messages)
        
        # 计算重复率
        repetition_score = self._calculate_repetition_rate(messages)
        
        # 评估质量分数
        quality_score = self._estimate_quality_score(messages)
        
        # 判断是否需要压缩
        needs_compression = self._should_compress(
            context_size, repetition_score, quality_score
        )
        
        return {
            'size': context_size,
            'message_count': message_count,
            'repetition_score': repetition_score,
            'quality_score': quality_score,
            'needs_compression': needs_compression,
            'size_category': self._categorize_context_size(context_size)
        }
    
    def _evaluate_task_complexity(self, task_description: str) -> TaskComplexity:
        """评估任务复杂度"""
        if not task_description:
            return TaskComplexity.SIMPLE
        
        task_lower = task_description.lower()
        
        # 计算各复杂度的关键词匹配分数
        complexity_scores = {}
        
        for complexity, keywords in self.routing_config['complexity_keywords'].items():
            score = 0
            for keyword in keywords:
                if keyword in task_lower:
                    score += 1
            
            # 额外的复杂度指标
            if complexity == 'complex':
                # 长文本通常更复杂
                if len(task_description) > 200:
                    score += 1
                # 包含多个问号的任务更复杂
                if task_description.count('？') + task_description.count('?') > 2:
                    score += 1
            
            elif complexity == 'very_complex':
                # 包含多个步骤或要求
                if len(re.findall(r'[1-9]\.', task_description)) > 3:
                    score += 2
                # 包含多个"和"连接的任务
                if task_description.count('和') + task_description.count('以及') > 3:
                    score += 1
            
            complexity_scores[complexity] = score
        
        # 选择得分最高的复杂度
        max_complexity = max(complexity_scores, key=complexity_scores.get)
        max_score = complexity_scores[max_complexity]
        
        # 如果得分太低，默认为简单
        if max_score == 0:
            return TaskComplexity.SIMPLE
        
        return TaskComplexity(max_complexity)
    
    def _decide_processing_strategy(
        self, 
        context_analysis: Dict, 
        task_complexity: TaskComplexity, 
        state: Dict
    ) -> Tuple[ProcessingStrategy, List[str]]:
        """决策处理策略，返回策略和动作列表"""
        
        # 1. 首先检查是否需要压缩
        if context_analysis.get('needs_compression', False):
            return ProcessingStrategy.COMPRESS_THEN_PROCESS, ["compress", "process"]
        
        # 2. 根据任务复杂度决策
        if task_complexity in [TaskComplexity.SIMPLE, TaskComplexity.MEDIUM]:
            # 简单和中等任务直接处理
            return ProcessingStrategy.DIRECT, ["process"]
        
        elif task_complexity == TaskComplexity.COMPLEX:
            # 复杂任务考虑多智能体协作
            if context_analysis.get('size', 0) > 8000:
                return ProcessingStrategy.MULTI_AGENT, ["multi_agent", "process"]
            else:
                return ProcessingStrategy.DIRECT, ["process"]
        
        else:  # VERY_COMPLEX
            # 非常复杂的任务需要多智能体协作
            return ProcessingStrategy.MULTI_AGENT, ["multi_agent", "process"]
    
    def _calculate_repetition_rate(self, messages: List[Dict]) -> float:
        """计算消息重复率"""
        if len(messages) < 2:
            return 0.0
        
        contents = [msg.get('content', '') for msg in messages]
        content_hashes = [hash(content[:50]) for content in contents]  # 使用前50字符
        unique_hashes = set(content_hashes)
        
        return 1.0 - (len(unique_hashes) / len(content_hashes))
    
    def _estimate_quality_score(self, messages: List[Dict]) -> float:
        """估算上下文质量分数"""
        if not messages:
            return 1.0
        
        quality_factors = []
        
        # 因子1：消息长度分布合理性
        lengths = [len(msg.get('content', '')) for msg in messages]
        avg_length = sum(lengths) / len(lengths)
        if 50 <= avg_length <= 500:  # 合理长度范围
            quality_factors.append(0.8)
        else:
            quality_factors.append(0.4)
        
        # 因子2：角色交替合理性
        roles = [msg.get('role', '') for msg in messages]
        role_changes = sum(1 for i in range(1, len(roles)) if roles[i] != roles[i-1])
        role_change_ratio = role_changes / len(roles) if len(roles) > 1 else 0
        if 0.3 <= role_change_ratio <= 0.7:  # 合理的角色交替
            quality_factors.append(0.8)
        else:
            quality_factors.append(0.5)
        
        # 因子3：内容完整性
        empty_messages = sum(1 for msg in messages if not msg.get('content', '').strip())
        completeness = 1.0 - (empty_messages / len(messages))
        quality_factors.append(completeness)
        
        return sum(quality_factors) / len(quality_factors)
    
    def _should_compress(self, context_size: int, repetition_score: float, quality_score: float) -> bool:
        """判断是否应该压缩"""
        config = self.routing_config['compression_triggers']
        
        # 基于大小的判断
        if context_size > config['size_based']:
            return True
        
        # 基于重复率的判断
        if repetition_score > config['repetition_based']:
            return True
        
        # 基于质量的判断
        if quality_score < config['quality_based']:
            return True
        
        return False
    
    def _categorize_context_size(self, context_size: int) -> str:
        """分类上下文大小"""
        thresholds = self.routing_config['context_size_thresholds']
        
        if context_size <= thresholds['small']:
            return 'small'
        elif context_size <= thresholds['medium']:
            return 'medium'
        elif context_size <= thresholds['large']:
            return 'large'
        else:
            return 'very_large'
    
    def _explain_routing_decision(
        self, 
        context_analysis: Dict, 
        task_complexity: TaskComplexity, 
        strategy: ProcessingStrategy
    ) -> str:
        """解释路由决策原因"""
        reasons = []
        
        # 上下文状态原因
        if context_analysis.get('needs_compression', False):
            reasons.append(f"上下文需要压缩 (大小: {context_analysis.get('size', 0)} 字符)")
        
        # 任务复杂度原因
        reasons.append(f"任务复杂度: {task_complexity.value}")
        
        # 策略选择原因
        if strategy == ProcessingStrategy.DIRECT:
            reasons.append("适合直接处理")
        elif strategy == ProcessingStrategy.COMPRESS_THEN_PROCESS:
            reasons.append("需要先压缩再处理")
        elif strategy == ProcessingStrategy.MULTI_AGENT:
            reasons.append("需要多智能体协作")
        
        return "; ".join(reasons)
    
    async def apply_compression_if_needed(self, messages: List[Dict], strategy: str = 'adaptive') -> Tuple[List[Dict], bool]:
        """如果需要，应用压缩"""
        context_analysis = await self._analyze_context_state(messages)
        
        if context_analysis.get('needs_compression', False):
            logger.info("🔧 路由器触发上下文压缩")
            compressed_messages, metadata = await self.compression_engine.adaptive_compression(
                messages, strategy
            )
            return compressed_messages, True
        else:
            return messages, False
    
    def get_routing_config(self) -> Dict:
        """获取路由配置"""
        return self.routing_config.copy()
    
    def update_routing_config(self, new_config: Dict) -> None:
        """更新路由配置"""
        self.routing_config.update(new_config)
        logger.info("🔧 路由配置已更新")


class ContextStateAnalyzer:
    """
    🔥 上下文状态分析器
    
    专门分析上下文的各种状态指标
    """
    
    def __init__(self):
        self.analysis_cache = {}
    
    def analyze_message_flow(self, messages: List[Dict]) -> Dict:
        """分析消息流特征"""
        if not messages:
            return {'flow_type': 'empty', 'characteristics': []}
        
        analysis = {
            'message_count': len(messages),
            'role_distribution': self._analyze_role_distribution(messages),
            'temporal_pattern': self._analyze_temporal_pattern(messages),
            'content_evolution': self._analyze_content_evolution(messages),
            'interaction_quality': self._analyze_interaction_quality(messages)
        }
        
        return analysis
    
    def _analyze_role_distribution(self, messages: List[Dict]) -> Dict:
        """分析角色分布"""
        roles = [msg.get('role', 'unknown') for msg in messages]
        role_counts = {}
        
        for role in roles:
            role_counts[role] = role_counts.get(role, 0) + 1
        
        total = len(roles)
        role_ratios = {role: count/total for role, count in role_counts.items()}
        
        return {
            'counts': role_counts,
            'ratios': role_ratios,
            'dominant_role': max(role_counts, key=role_counts.get) if role_counts else None
        }
    
    def _analyze_temporal_pattern(self, messages: List[Dict]) -> Dict:
        """分析时间模式"""
        # 简化版本：分析消息间隔和节奏
        timestamps = []
        for msg in messages:
            if 'timestamp' in msg:
                timestamps.append(msg['timestamp'])
        
        if len(timestamps) < 2:
            return {'pattern': 'insufficient_data'}
        
        # 这里可以添加更复杂的时间分析逻辑
        return {
            'pattern': 'regular',
            'message_frequency': len(messages),
            'has_timestamps': len(timestamps) > 0
        }
    
    def _analyze_content_evolution(self, messages: List[Dict]) -> Dict:
        """分析内容演化"""
        if not messages:
            return {'evolution': 'none'}
        
        content_lengths = [len(msg.get('content', '')) for msg in messages]
        
        return {
            'avg_length': sum(content_lengths) / len(content_lengths),
            'length_trend': self._calculate_trend(content_lengths),
            'content_diversity': len(set([msg.get('content', '')[:50] for msg in messages])) / len(messages)
        }
    
    def _analyze_interaction_quality(self, messages: List[Dict]) -> Dict:
        """分析交互质量"""
        if len(messages) < 2:
            return {'quality': 'insufficient_data'}
        
        # 简化的交互质量评估
        user_messages = [msg for msg in messages if msg.get('role') == 'user']
        assistant_messages = [msg for msg in messages if msg.get('role') == 'assistant']
        
        return {
            'user_engagement': len(user_messages) / len(messages),
            'assistant_responsiveness': len(assistant_messages) / len(messages),
            'interaction_balance': abs(len(user_messages) - len(assistant_messages)) / len(messages)
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算数值趋势"""
        if len(values) < 2:
            return 'stable'
        
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        if second_avg > first_avg * 1.2:
            return 'increasing'
        elif second_avg < first_avg * 0.8:
            return 'decreasing'
        else:
            return 'stable'


# 导出主要类
__all__ = [
    'SmartContextRouter',
    'ContextStateAnalyzer',
    'ProcessingStrategy',
    'TaskComplexity'
]

# 全局上下文路由器实例
def get_context_router() -> SmartContextRouter:
    """获取全局上下文路由器实例"""
    if not hasattr(get_context_router, '_instance'):
        get_context_router._instance = SmartContextRouter()
    return get_context_router._instance

async def get_routing_stats() -> Dict[str, Any]:
    """获取路由器统计信息"""
    router = get_context_router()
    return {
        "router_initialized": True,
        "strategies": [strategy.value for strategy in ProcessingStrategy],
        "complexity_levels": [level.value for level in TaskComplexity],
        "routing_config": router.get_routing_config()
    }

def get_routing_stats_sync() -> Dict[str, Any]:
    """获取路由器统计信息（同步版本）"""
    router = get_context_router()
    return {
        "router_initialized": True,
        "strategies": [strategy.value for strategy in ProcessingStrategy],
        "complexity_levels": [level.value for level in TaskComplexity],
        "routing_config": router.get_routing_config()
    } 