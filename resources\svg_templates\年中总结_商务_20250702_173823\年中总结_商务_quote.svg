<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette Variables -->
    <style>
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6;
      }

      /* Font System */
      .primary-font { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .secondary-font { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .accent-font { font-family: "Times New Roman", serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.1; }
      .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.4; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      /* Text Colors */
      .text-primary-color { fill: var(--text-primary); }
      .text-secondary-color { fill: var(--text-secondary); }
      .text-accent-color { fill: var(--accent-color); }
      .text-light-color { fill: var(--text-light); }

      /* Fill Colors */
      .fill-primary { fill: var(--primary-color); }
      .fill-secondary { fill: var(--secondary-color); }
      .fill-accent { fill: var(--accent-color); }
      .fill-background { fill: var(--background-color); }
      .fill-card-background { fill: var(--card-background); }
      .fill-container-background { fill: var(--container-background); }

      /* Stroke Colors */
      .stroke-primary { stroke: var(--primary-color); }
      .stroke-accent { stroke: var(--accent-color); }
      .stroke-card-border { stroke: var(--card-border); }
      .stroke-width-2 { stroke-width: 2px; }

      /* Shadows */
      .card-shadow {
        filter: url(#cardShadow);
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2" />
    </linearGradient>

    <!-- Filter for subtle shadow -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="fill-background" />

  <!-- Decorative Geometric Shapes (Bento-like structure, tech feel) -->
  <g id="decorative-elements">
    <!-- Top-left subtle rectangle -->
    <rect x="0" y="0" width="400" height="120" fill="url(#accentTransparentGradient)" opacity="0.1" />
    <!-- Bottom-right subtle rectangle -->
    <rect x="1520" y="960" width="400" height="120" fill="url(#accentTransparentGradient)" opacity="0.1" />
    <!-- Vertical line on left -->
    <rect x="80" y="60" width="2" height="960" class="fill-accent" opacity="0.2" />
    <!-- Horizontal line on top -->
    <rect x="80" y="60" width="1760" height="2" class="fill-accent" opacity="0.2" />
    <!-- Large, subtle "L" shape from accent color -->
    <path d="M1600 0 L1920 0 L1920 320 L1800 320 L1800 120 L1600 120 Z" fill="url(#accentTransparentGradient)" opacity="0.05"/>
    <path d="M0 760 L0 1080 L320 1080 L320 960 L120 960 L120 760 Z" fill="url(#accentTransparentGradient)" opacity="0.05"/>
  </g>

  <!-- Main Content Area - Quote Section -->
  <g id="quote-section" transform="translate(960, 540)">
    <!-- Quote Card Background (optional, for emphasis) -->
    <rect x="-700" y="-300" width="1400" height="600" rx="24" ry="24" class="fill-card-background card-shadow" />

    <!-- Decorative Opening Quote Mark -->
    <text x="-620" y="-120" class="primary-font font-bold text-accent-color" font-size="200" fill-opacity="0.8">“</text>

    <!-- Quote Content -->
    <text id="quote-content" text-anchor="middle" class="primary-font font-bold text-primary-color" font-size="56">
      <tspan x="0" dy="-0.5em">卓越并非偶然，而是长期承诺于</tspan>
      <tspan x="0" dy="1.4em">高质量工作和持续改进的必然结果。</tspan>
      <tspan x="0" dy="1.4em" fill="url(#accentGradient)">{content}</tspan>
    </text>

    <!-- Decorative Closing Quote Mark -->
    <text x="620" y="240" class="primary-font font-bold text-accent-color" font-size="200" text-anchor="end" fill-opacity="0.8">”</text>

    <!-- Source Information -->
    <text x="0" y="300" class="secondary-font font-semibold text-secondary-color body-text" text-anchor="middle">
      —— {author}
    </text>
  </g>

  <!-- Page Number -->
  <text x="1840" y="1020" text-anchor="end" class="secondary-font small-text text-light-color">
    9/10
  </text>

  <!-- Date -->
  <text x="80" y="1020" text-anchor="start" class="secondary-font small-text text-light-color">
    {date}
  </text>

  <!-- Logo (Top Left) -->
  <g id="logo" transform="translate(80, 60)">
    <!-- Placeholder for logo. Replace with actual image path if available. -->
    <!-- For example: <image href="{logo_url}" x="0" y="0" width="120" height="auto" /> -->
    <rect x="0" y="0" width="120" height="40" rx="6" ry="6" class="fill-primary" />
    <text x="60" y="28" text-anchor="middle" class="primary-font small-text" fill="#FFFFFF">
      {logo_url}
    </text>
  </g>

  <!-- Main Title (Placeholder, perhaps for context on first page or section) -->
  <!-- This template is for a quote page, so a main title might not be primary, but included for completeness -->
  <text x="960" y="140" text-anchor="middle" class="primary-font main-title text-primary-color">
    {title}
  </text>
  <text x="960" y="190" text-anchor="middle" class="secondary-font content-title text-secondary-color">
    {subtitle}
  </text>

</svg>