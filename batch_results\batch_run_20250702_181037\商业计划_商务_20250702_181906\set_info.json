{"set_name": "商业计划_商务_20250702_181906", "scenario": "商业计划", "style": "商务", "created_at": "2025-07-02T18:19:06.744873", "template_count": 10, "templates": [{"template_id": "商业计划_商务_cover", "type": "封面页", "filename": "商业计划_商务_cover.svg", "page_number": 1}, {"template_id": "商业计划_商务_agenda", "type": "目录页", "filename": "商业计划_商务_agenda.svg", "page_number": 2}, {"template_id": "商业计划_商务_section_divider", "type": "章节分隔页", "filename": "商业计划_商务_section_divider.svg", "page_number": 3}, {"template_id": "商业计划_商务_title_content", "type": "标题内容页", "filename": "商业计划_商务_title_content.svg", "page_number": 4}, {"template_id": "商业计划_商务_image_text", "type": "图文混排页", "filename": "商业计划_商务_image_text.svg", "page_number": 5}, {"template_id": "商业计划_商务_data_display", "type": "数据展示页", "filename": "商业计划_商务_data_display.svg", "page_number": 6}, {"template_id": "商业计划_商务_comparison", "type": "对比分析页", "filename": "商业计划_商务_comparison.svg", "page_number": 7}, {"template_id": "商业计划_商务_timeline", "type": "时间线页", "filename": "商业计划_商务_timeline.svg", "page_number": 8}, {"template_id": "商业计划_商务_quote", "type": "引用页", "filename": "商业计划_商务_quote.svg", "page_number": 9}, {"template_id": "商业计划_商务_conclusion", "type": "总结页", "filename": "商业计划_商务_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "商业计划", "display_name": "商业计划", "description": "商业计划书、投资提案、战略规划", "visual_characteristics": {"emphasis_on": "逻辑严密、数据支撑", "layout_style": "商务正式", "decorative_elements": "商业图表、财务数据、流程图"}, "content_focus": ["市场分析", "财务预测", "风险评估"], "target_audience": "投资人、合作伙伴", "tone": "professional"}, "style": {"style_type": "商务", "display_name": "商务", "description": "专业正式，体现权威和可信度", "design_principles": {"layout": "规范布局、对称平衡", "elements": "正式图形、商务图标", "emphasis": "专业性、权威性"}, "visual_elements": {"shapes": "矩形、稳重几何形", "lines": "直线、规整边框", "decorations": "商务图标、数据图表"}, "typography": {"font_style": "正式字体", "weight": "中粗", "spacing": "标准间距"}}, "colors": {"primary": "#1E40AF", "secondary": "#475569", "accent": "#3B82F6", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "scenario", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": []}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "商务风格", "scenario_adaptation": "商业计划场景优化", "visual_theme": "黑底特斯拉红高亮与蓝色系辅助的现代商务主题", "design_philosophy": "以专业性、权威性和前瞻性为核心，旨在为投资人、合作伙伴提供清晰、有冲击力且高可信度的视觉体验。融合Apple官网Bento Grid设计理念，强调内容组织与视觉冲击。", "fusion_strategy": "场景（商业计划）优先，风格（商务）深度融合，并吸纳现代科技感元素。"}, "color_palette": {"primary_color": "#1E40AF", "secondary_color": "#475569", "accent_color": "#3B82F6", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#3B82F6", "gradient_primary": "linear-gradient(135deg, #1E40AF, #475569)", "gradient_accent": "linear-gradient(45deg, #3B82F6, #1E40AF)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#1E40AF", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64, "4xl": 96}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Microsoft YaHei, Segoe UI, sans-serif", "secondary_font": "Source <PERSON> CN, Noto Sans CJK SC, sans-serif", "accent_font": "Times New Roman, serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 12, "border": "1px solid #BAE6FD", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["几何图形装饰", "渐变背景", "图标点缀", "分割线", "阴影效果"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "所有模板围绕商业计划的核心叙事展开，确保内容流和视觉元素的连续性，从市场分析到财务预测，保持统一的专业语境和信息传递效率。", "style_unity": "所有页面严格遵循现代商务风格，保持简洁、高对比度、清晰的排版和结构化Bento Grid卡片系统，确保品牌形象的一致性。", "color_harmony": "严格应用黑底、特斯拉红色高亮、蓝色系主色和辅助色的配色方案，确保色彩在所有元素（文本、图表、背景、装饰）中的和谐统一，避免色调冲突。", "visual_rhythm": "通过Bento Grid布局的节奏感、超大字体的视觉冲击和简洁线条的引导，创建引人入胜的视觉节奏和信息层次，提升用户阅读体验。", "brand_consistency": "通过统一的Logo处理、核心配色方案和设计语言，在所有模板中强化品牌识别度，传递专业和值得信赖的品牌形象，确保投资人、合作伙伴对演示文稿的专业认可。"}, "created_at": "2025-07-02T18:11:14.281460", "scenario": "商业计划", "style": "商务", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"商务风格\",\n        \"scenario_adaptation\": \"商业计划场景优化\",\n        \"visual_theme\": \"黑底特斯拉红高亮与蓝色系辅助的现代商务主题\",\n        \"design_philosophy\": \"以专业性、权威性和前瞻性为核心，旨在为投资人、合作伙伴提供清晰、有冲击力且高可信度的视觉体验。融合Apple官网Bento Grid设计理念，强调内容组织与视觉冲击。\",\n        \"fusion_strategy\": \"场景（商业计划）优先，风格（商务）深度融合，并吸纳现代科技感元素。\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#1E40AF\",\n        \"secondary_color\": \"#475569\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#1D1D1F\",\n        \"hover_color\": \"#E31937\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": \"在关键视觉分割和元素比例中考虑黄金比例，增强美感和平衡性。\",\n        \"grid_system\": {\n            \"columns\": 12,\n            \"gutter\": 24,\n            \"margin\": 80,\n            \"type\": \"Bento Grid\",\n            \"description\": \"采用非对称、灵活的Bento Grid布局，允许不同大小的区块组合，高效组织内容并创造视觉冲击力。区块尺寸将根据内容重要性和页面空间动态调整，但严格遵循整体网格结构。\"\n        },\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\n            \"module_gap\": 32,\n            \"section_gap\": 48,\n            \"element_gap\": 16,\n            \"text_line_height_multiplier\": 1.8,\n            \"text_padding_min\": 30,\n            \"vertical_spacing_min\": {\n                \"title_to_content\": 30,\n                \"paragraph_to_paragraph\": 24,\n                \"list_item_to_item\": 20\n            }\n        },\n        \"visual_hierarchy\": \"通过Bento Grid布局、超大字体、色彩对比和模块化设计，创建清晰、引导性强的视觉层次，突出商业计划核心信息和关键数据点。\",\n        \"alignment_system\": \"基于商务专业和Bento Grid的精确对齐，确保视觉平衡、工整性及信息流的顺畅。\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"优先选用现代、高可读性的无衬线字体，如 'Noto Sans SC', 'Inter', 'Roboto', 'system-ui'，确保中英文兼容性和多字重支持。\",\n        \"font_sizes\": {\n            \"hero_title\": 120,\n            \"main_title\": 72,\n            \"section_title\": 48,\n            \"content_title\": 32,\n            \"body_text\": 24,\n            \"small_text\": 16,\n            \"accent_number\": 180\n        },\n        \"font_weights\": {\n            \"title\": \"bold\",\n            \"content\": \"normal\",\n            \"emphasis\": \"600\",\n            \"chinese\": \"bold\",\n            \"english\": \"300\"\n        },\n        \"line_heights\": {\n            \"title\": 1.1,\n            \"content\": 1.6,\n            \"dense\": 1.3,\n            \"chinese_mixed\": 1.8\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"大号粗体，作为主要信息和视觉焦点，占据主导地位。\",\n            \"english_style\": \"小号细体，作为辅助信息、点缀或专业术语，提供细节。\",\n            \"number_style\": \"超大号，使用高亮色，突出关键数据和财务指标，形成强烈视觉锚点。\"\n        },\n        \"readability_optimization\": \"高对比度（WCAG AA+），充足行间距和内边距，确保在各种显示环境下投资人、合作伙伴能够轻松阅读和理解复杂信息。长标题自动换行，行间距至少为字号的2.0倍。\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"趋势线、增长图表、财务报表布局、市场分析图示、风险矩阵、团队介绍模块。所有图表和数据可视化元素均采用简洁勾线图形风格。\",\n        \"style_characteristics\": \"简洁、现代、专业、科技感、高对比度、注重数据可视化和信息密度。通过黑底红高亮和蓝色系辅助色营造极致视觉体验。\",\n        \"bento_grid_layout\": \"采用类似Apple官网的非对称、灵活的Bento Grid布局，通过不同大小的矩形区块组合，实现内容的高效组织与视觉冲击力，确保黑底红高亮主题下的平衡美感。\",\n        \"black_red_theme\": \"纯黑色背景（#000000）与特斯拉红色（#E31937）作为核心高亮色，辅以蓝色系（#1E40AF）作为主色，营造极致现代与专业的对比视觉效果。\",\n        \"oversized_typography\": \"在关键数据（如市场份额、增长率、营收数字）和页面主题上，运用超大字体（hero_title, accent_number），结合特斯拉红色高亮，形成强烈的视觉锚点和信息冲击力。\",\n        \"decorative_elements\": [\n            \"简洁线条图形元素：用于数据可视化（如极简折线图、条形图）、流程图、连接线和背景纹理，保持纤细、精准，颜色为高亮色或辅助色，透明度低（0.05-0.15）。\",\n            \"特斯拉红色透明度渐变元素：仅使用特斯拉红色的自身透明度渐变，从实色到透明，营造科技感和深度，应用于背景叠加、卡片边框或局部强调区域，绝不使用多色渐变。\",\n            \"中英文混排排版元素：强调中文标题和核心信息的大号粗体，英文作为辅助性说明或专业术语，以小号细体呈现，形成独特的国际化视觉风格。\",\n            \"符合商务风格的装饰元素：如精致的分割线、统一的图标系统（扁平或线性）、微妙的几何形状（如矩形、圆形）作为背景图案或点缀，辅助填充空间。\",\n            \"适合商业计划场景的装饰元素：如象征增长的向上箭头、数据点连接、轻微的网格背景，暗示数据和连接性，且透明度极低，不干扰内容。\"\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"统一使用16-24px的圆角，体现现代感和柔和性，避免锐利边缘。\",\n            \"shadow\": \"微妙的内阴影或极浅的外阴影，增强卡片深度感而不显突兀，颜色为rgba(0,0,0,0.3)或rgba(227, 25, 55, 0.1)的极低透明度高亮色阴影。\",\n            \"border\": \"1.5-2px的特斯拉红色细边框，或特斯拉红色透明度渐变边框，强化视觉焦点和科技感。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": \"0px，或与Bento Grid模块统一的圆角（16-24px），具体取决于其在网格中的位置和角色。\",\n            \"shadow_style\": \"无阴影或极简阴影，保持简洁，避免干扰图片内容。\",\n            \"overlay_style\": \"可选的特斯拉红色半透明遮罩（opacity: 0.1-0.3），用于图片背景或强调特定区域，避免覆盖关键信息。\",\n            \"bento_grid_placement\": \"图片作为Bento Grid中的一个模块，尺寸和位置严格遵循网格系统，与周围文本、数据模块和谐共存，形成视觉焦点或辅助说明。支持外部URL和本地路径的SVG <image>标签。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"通常位于页面左上角或右下角，确保在任何布局下都有清晰的展示空间，且不干扰核心内容。\",\n            \"size_guidelines\": \"尺寸适中，确保可识别性但不过于抢眼，与整体设计比例协调。\",\n            \"integration_style\": \"若Logo本身非黑白，则考虑将其转换为白色或浅灰色，或添加微妙的特斯拉红色描边/背景光，以和谐融入黑底红高亮主题。\",\n            \"animation_hint\": \"在滚动或切换页面时，Logo可有微妙的淡入淡出或缩放动效，模仿Apple官网的流畅体验。\"\n        },\n        \"chart_components\": \"优先引用在线图表组件，确保其样式（颜色、字体、线条、简洁性）与整体设计规范完全一致，数据清晰呈现，避免过度装饰。\"\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"所有模板围绕商业计划的核心叙事展开，确保内容流和视觉元素的连续性，从市场分析到财务预测，保持统一的专业语境和信息传递效率。\",\n        \"style_unity\": \"所有页面严格遵循现代商务风格，保持简洁、高对比度、清晰的排版和结构化Bento Grid卡片系统，确保品牌形象的一致性。\",\n        \"color_harmony\": \"严格应用黑底、特斯拉红色高亮、蓝色系主色和辅助色的配色方案，确保色彩在所有元素（文本、图表、背景、装饰）中的和谐统一，避免色调冲突。\",\n        \"visual_rhythm\": \"通过Bento Grid布局的节奏感、超大字体的视觉冲击和简洁线条的引导，创建引人入胜的视觉节奏和信息层次，提升用户阅读体验。\",\n        \"brand_consistency\": \"通过统一的Logo处理、核心配色方案和设计语言，在所有模板中强化品牌识别度，传递专业和值得信赖的品牌形象，确保投资人、合作伙伴对演示文稿的专业认可。\"\n    }\n}\n```"}}