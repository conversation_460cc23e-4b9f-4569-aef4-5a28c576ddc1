#!/bin/bash
# 安装可视化依赖的脚本

echo "🎨 安装LangGraph工作流可视化依赖..."

# 安装Python包
echo "📦 安装matplotlib和networkx..."
pip install matplotlib networkx

# 如果需要更高级的图布局，安装graphviz
echo "📦 安装graphviz（可选，用于更好的图布局）..."
pip install pygraphviz

# 在Ubuntu/Debian系统上安装graphviz系统包
if command -v apt-get &> /dev/null; then
    echo "🔧 在Ubuntu/Debian上安装graphviz系统包..."
    sudo apt-get update
    sudo apt-get install -y graphviz graphviz-dev
fi

# 在macOS上安装graphviz
if command -v brew &> /dev/null; then
    echo "🔧 在macOS上安装graphviz..."
    brew install graphviz
fi

echo "✅ 依赖安装完成！"
echo "💡 现在可以运行以下命令生成可视化图："
echo "   python3 simple_graph_demo.py"
echo "   python3 generate_workflow_visualization.py"