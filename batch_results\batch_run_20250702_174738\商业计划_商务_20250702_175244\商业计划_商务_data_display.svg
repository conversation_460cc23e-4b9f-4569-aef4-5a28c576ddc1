<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <style>
    /* Color Scheme - Defined using CSS variables for reusability and clarity */
    :root {
      --primary-color: #1E40AF;
      --secondary-color: #475569;
      --accent-color: #3B82F6;
      --background-color: #F8FAFC;
      --text-primary: #1E293B;
      --text-secondary: #64748B;
      --text-light: #94A3B8;
      --card-background: #FFFFFF;
      --card-border: #BAE6FD;
      --container-background: #E0F2FE; /* Used for subtle icon backgrounds */
      --success-color: #10B981;
      --warning-color: #F59E0B;
      --error-color: #EF4444;
      --info-color: #3B82F6;
      --hover-color: #7DD3FC;
      --active-color: #1E40AF;
      --disabled-color: #64748B;
    }

    /* Font System */
    .primary-font { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .secondary-font { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .accent-font { font-family: 'Times New Roman', serif; }

    /* Font Sizes based on design norms */
    .hero-title { font-size: 72px; }
    .main-title { font-size: 56px; }
    .section-title { font-size: 36px; }
    .content-title { font-size: 28px; }
    .body-text { font-size: 22px; }
    .small-text { font-size: 16px; }
    .caption { font-size: 14px; }

    /* Font Weights */
    .font-normal { font-weight: 400; }
    .font-medium { font-weight: 500; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }
    .font-black { font-weight: 900; }

    /* Text Colors */
    .text-primary { fill: var(--text-primary); }
    .text-secondary { fill: var(--text-secondary); }
    .text-light { fill: var(--text-light); }
    .text-accent { fill: var(--accent-color); }
    .text-white { fill: #FFFFFF; }

    /* Card Style - unified with design norms */
    .card-background {
      fill: var(--card-background);
      stroke: var(--card-border);
      stroke-width: 1px;
      rx: 12px; /* border-radius */
    }
    .shadow {
      filter: url(#card-shadow);
    }
    .hover-shadow {
      filter: url(#card-hover-shadow);
    }

    /* Icon System - outline style */
    .icon-style {
      stroke: var(--accent-color);
      stroke-width: 2px;
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
  </style>

  <defs>
    <!-- Gradients for background and decorative elements -->
    <linearGradient id="primary-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accent-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="background-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="var(--background-color)"/>
      <stop offset="100%" stop-color="var(--container-background)"/>
    </linearGradient>
    <!-- Accent color with transparency gradient for tech feel -->
    <linearGradient id="accent-transparent-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="var(--accent-color)" stop-opacity="0.1"/>
        <stop offset="50%" stop-color="var(--accent-color)" stop-opacity="0.5"/>
        <stop offset="100%" stop-color="var(--accent-color)" stop-opacity="0.1"/>
    </linearGradient>

    <!-- Drop Shadows for cards -->
    <filter id="card-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4"/>
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2"/>
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2"/>
      <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
      <feMerge>
        <feMergeNode in="matrixOut"/>
        <feMergeNode in="matrixOut2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="card-hover-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="10"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="7.5"/>
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="3"/>
      <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
      <feMerge>
        <feMergeNode in="matrixOut"/>
        <feMergeNode in="matrixOut2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Page Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#background-gradient)"/>

  <!-- Decorative Elements: Subtle background shapes -->
  <circle cx="1800" cy="100" r="250" fill="var(--primary-color)" opacity="0.05"/>
  <circle cx="1750" cy="150" r="200" fill="var(--accent-color)" opacity="0.05"/>
  <rect x="0" y="800" width="300" height="300" fill="var(--primary-color)" opacity="0.03" rx="50"/>

  <!-- Header Section -->
  <g id="header">
    <image xlink:href="{logo_url}" x="80" y="60" width="150" height="40" preserveAspectRatio="xMidYMid meet"/>
    <text x="1760" y="85" text-anchor="end" class="small-text text-secondary primary-font">
      6/10
    </text>
    <text x="1760" y="110" text-anchor="end" class="small-text text-light primary-font">
      {date}
    </text>
  </g>

  <!-- Main Title and Subtitle Section -->
  <g id="page-title-section">
    <text x="80" y="180" class="main-title text-primary primary-font font-bold">
      市场洞察和增长预测
    </text>
    <text x="80" y="225" class="content-title text-secondary primary-font font-normal">
      Market Insights and Growth Projections
    </text>
  </g>

  <!-- Main Content Area - Bento Grid Style Layout -->
  <g id="content-area" transform="translate(80, 270)">
    <!-- Max content width: 1920 - 2*80 = 1760px -->
    <!-- This layout is based on a 12-column grid, with gutters.
         Main Chart Area: ~8 columns (1160px)
         Data Cards Area: ~4 columns (576px)
         Gutter between them: 24px
         Total width: 1160 + 24 + 576 = 1760px -->

    <!-- Main Chart Area (Left Section) -->
    <rect x="0" y="0" width="1160" height="680" class="card-background shadow"/>
    <text x="30" y="45" class="content-title text-primary primary-font font-semibold">
      市场份额趋势分析
    </text>
    <text x="30" y="75" class="small-text text-secondary primary-font">
      Market Share Trend Analysis
    </text>

    <!-- Placeholder for Line Chart (Simplified for SVG) -->
    <g id="line-chart-placeholder" transform="translate(60, 120)">
      <!-- X-Axis -->
      <line x1="0" y1="480" x2="1050" y2="480" stroke="var(--text-light)" stroke-width="1"/>
      <text x="0" y="500" class="small-text text-light primary-font">Q1</text>
      <text x="250" y="500" class="small-text text-light primary-font">Q2</text>
      <text x="500" y="500" class="small-text text-light primary-font">Q3</text>
      <text x="750" y="500" class="small-text text-light primary-font">Q4</text>
      <text x="1050" y="500" text-anchor="end" class="small-text text-light primary-font">Year End</text>

      <!-- Y-Axis -->
      <line x1="0" y1="0" x2="0" y2="480" stroke="var(--text-light)" stroke-width="1"/>
      <text x="-15" y="480" text-anchor="end" class="small-text text-light primary-font">0%</text>
      <text x="-15" y="360" text-anchor="end" class="small-text text-light primary-font">25%</text>
      <text x="-15" y="240" text-anchor="end" class="small-text text-light primary-font">50%</text>
      <text x="-15" y="120" text-anchor="end" class="small-text text-light primary-font">75%</text>
      <text x="-15" y="0" text-anchor="end" class="small-text text-light primary-font">100%</text>

      <!-- Grid Lines -->
      <line x1="0" y1="360" x2="1050" y2="360" stroke="var(--text-light)" stroke-width="0.5" stroke-dasharray="2 2"/>
      <line x1="0" y1="240" x2="1050" y2="240" stroke="var(--text-light)" stroke-width="0.5" stroke-dasharray="2 2"/>
      <line x1="0" y1="120" x2="1050" y2="120" stroke="var(--text-light)" stroke-width="0.5" stroke-dasharray="2 2"/>

      <!-- Sample Data Line 1 (Primary focus) -->
      <polyline points="0,400 260,300 520,250 780,200 1040,150"
                stroke="var(--accent-color)" stroke-width="4" fill="none"/>
      <circle cx="0" cy="400" r="6" fill="var(--accent-color)"/>
      <circle cx="260" cy="300" r="6" fill="var(--accent-color)"/>
      <circle cx="520" cy="250" r="6" fill="var(--accent-color)"/>
      <circle cx="780" cy="200" r="6" fill="var(--accent-color)"/>
      <circle cx="1040" cy="150" r="6" fill="var(--accent-color)"/>
      <text x="1040" y="140" text-anchor="middle" class="small-text text-accent primary-font font-semibold">25%</text>

      <!-- Sample Data Line 2 (Secondary data) -->
      <polyline points="0,450 260,380 520,350 780,320 1040,300"
                stroke="var(--primary-color)" stroke-width="2" fill="none" opacity="0.6"/>
      <circle cx="0" cy="450" r="4" fill="var(--primary-color)" opacity="0.6"/>
      <circle cx="260" cy="380" r="4" fill="var(--primary-color)" opacity="0.6"/>
      <circle cx="520" cy="350" r="4" fill="var(--primary-color)" opacity="0.6"/>
      <circle cx="780" cy="320" r="4" fill="var(--primary-color)" opacity="0.6"/>
      <circle cx="1040" cy="300" r="4" fill="var(--primary-color)" opacity="0.6"/>

      <!-- Chart Legend -->
      <rect x="850" y="30" width="20" height="10" fill="var(--accent-color)"/>
      <text x="880" y="40" class="small-text text-secondary primary-font">主营业务 (Core Business)</text>
      <rect x="850" y="55" width="20" height="10" fill="var(--primary-color)" opacity="0.6"/>
      <text x="880" y="65" class="small-text text-secondary primary-font">新业务线 (New Ventures)</text>
    </g>

    <!-- Data Cards (Right Section) -->
    <g id="data-cards" transform="translate(1184, 0)"> <!-- X-position: 1160 (chart width) + 24 (gutter) -->
      <!-- Card 1: Key Metric 1 - Large number emphasis -->
      <rect x="0" y="0" width="576" height="210" class="card-background shadow"/>
      <text x="30" y="60" class="section-title text-primary primary-font font-bold">
        <tspan class="hero-title text-accent font-black">75%</tspan>
      </text>
      <text x="30" y="100" class="content-title text-primary primary-font font-semibold">
        市场份额增长
      </text>
      <text x="30" y="130" class="small-text text-secondary primary-font">
        Market Share Growth
      </text>
      <!-- Outline Icon for growth -->
      <g transform="translate(500, 45)">
        <circle cx="0" cy="0" r="20" fill="var(--container-background)"/>
        <polyline points="-10,5 0,-5 10,5" class="icon-style" stroke="var(--accent-color)" stroke-width="3"/>
        <line x1="0" y1="5" x2="0" y2="15" class="icon-style" stroke="var(--accent-color)" stroke-width="3"/>
      </g>

      <!-- Card 2: Key Metric 2 - Large number emphasis -->
      <rect x="0" y="235" width="576" height="210" class="card-background shadow"/>
      <text x="30" y="60" class="section-title text-primary primary-font font-bold">
        <tspan class="hero-title text-accent font-black">$2.5B</tspan>
      </text>
      <text x="30" y="100" class="content-title text-primary primary-font font-semibold">
        年度营收预测
      </text>
      <text x="30" y="130" class="small-text text-secondary primary-font">
        Annual Revenue Forecast
      </text>
      <!-- Outline Icon for revenue -->
      <g transform="translate(500, 280)">
        <circle cx="0" cy="0" r="20" fill="var(--container-background)"/>
        <path d="M-8,0 L0,-10 L8,0 M-5,0 L-5,10 M5,0 L5,10" class="icon-style" stroke="var(--accent-color)" stroke-width="3"/>
      </g>

      <!-- Card 3: Key Metric 3 (Smaller, stacked below) -->
      <rect x="0" y="470" width="280" height="210" class="card-background shadow"/>
      <text x="20" y="60" class="section-title text-primary primary-font font-bold">
        <tspan class="main-title text-accent font-black">15%</tspan>
      </text>
      <text x="20" y="100" class="content-title text-primary primary-font font-semibold">
        成本优化
      </text>
      <text x="20" y="125" class="small-text text-secondary primary-font">
        Cost Optimization
      </text>
      <!-- Outline Icon for optimization -->
      <g transform="translate(240, 500)">
        <circle cx="0" cy="0" r="15" fill="var(--container-background)"/>
        <path d="M-8,-5 L8,-5 L8,5 L-8,5 Z M0,-10 L0,10" class="icon-style" stroke="var(--accent-color)" stroke-width="2"/>
      </g>

      <!-- Card 4: Key Metric 4 (Smaller, stacked below) -->
      <rect x="296" y="470" width="280" height="210" class="card-background shadow"/> <!-- X-position: 280 (card 3 width) + 16 (spacing) -->
      <text x="20" y="60" class="section-title text-primary primary-font font-bold">
        <tspan class="main-title text-accent font-black">90%</tspan>
      </text>
      <text x="20" y="100" class="content-title text-primary primary-font font-semibold">
        客户满意度
      </text>
      <text x="20" y="125" class="small-text text-secondary primary-font">
        Customer Satisfaction
      </text>
      <!-- Outline Icon for satisfaction -->
      <g transform="translate(536, 500)">
        <circle cx="0" cy="0" r="15" fill="var(--container-background)"/>
        <path d="M-8,0 A8,8 0 0,1 8,0 M-5,5 L5,5" class="icon-style" stroke="var(--accent-color)" stroke-width="2"/>
      </g>
    </g>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <rect x="0" y="1020" width="1920" height="60" fill="url(#primary-gradient)"/>
    <text x="960" y="1055" text-anchor="middle" class="small-text text-white primary-font">
      {author} 和#38; Business Plan - Confidential
    </text>
  </g>

</svg>