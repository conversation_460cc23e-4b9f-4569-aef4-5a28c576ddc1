.messages-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
}

.message-item {
  display: flex;
  padding: 16px 20px;
  animation: fadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin-bottom: 4px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-message {
  flex-direction: row-reverse;
}

.assistant-message {
  flex-direction: row;
}

.message-avatar {
  margin: 0 16px;
  flex-shrink: 0;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.message-content {
  flex: 1;
  max-width: 80%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
  /* 建立视觉层次 */
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.message-time {
  margin-left: 0;
  font-size: 11px;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  color: #6b7280;
  font-weight: 500;
  /* 精致时间显示 */
  font-family: 'JetBrains Mono', 'SF Mono', monospace;
  letter-spacing: 0.5px;
  font-variant-numeric: tabular-nums;
}

.message-actions {
  margin-left: auto;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateX(8px);
}

.message-item:hover .message-actions {
  opacity: 1;
  transform: translateX(0);
}

.message-body {
  width: 100%;
}

.user-content {
  background: #1890ff;
  color: white;
  padding: 16px 20px;
  border-radius: 20px 8px 20px 20px;
  word-break: break-word;
  white-space: pre-wrap;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
  position: relative;
  backdrop-filter: blur(10px);
  /* 优化字体可读性 */
  font-size: 15px;
  line-height: 1.6;
  letter-spacing: 0.01em;
  font-weight: 400;
  text-size-adjust: 100%;
}

.assistant-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 18px 22px;
  border-radius: 8px 20px 20px 20px;
  word-break: break-word;
  white-space: pre-wrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
  /* 优化字体可读性 */
  font-size: 15px;
  line-height: 1.65;
  letter-spacing: 0.01em;
  font-weight: 400;
  color: #374151;
  text-size-adjust: 100%;
}

.user-avatar {
  background: #1890ff;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.assistant-avatar {
  background: #f0f0f0;
  color: #666;
  border: 2px solid rgba(0, 0, 0, 0.15);
}

.message-status {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  padding: 12px 20px;
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 10;
  animation: statusPulse 2s ease-in-out infinite;
}

.loading-spinner {
  margin-right: 0;
  color: #1890ff;
}

.empty-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 16px;
  font-weight: 500;
}

/* Markdown 样式优化 */
.assistant-content pre {
  margin: 1.5em 0;
  padding: 1.5em;
  border-radius: 14px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  overflow-x: auto;
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 2px 10px rgba(139, 92, 246, 0.15);
  position: relative;
}

.assistant-content pre code {
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', monospace;
  font-size: 0.9em;
  color: #e2e8f0;
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  font-feature-settings: 'liga' 1, 'calt' 1;
  line-height: 1.5;
}

.assistant-content p {
  margin-bottom: 1em;
}

.assistant-content p:last-child {
  margin-bottom: 0;
}

/* Markdown 样式 */
.assistant-content p {
  margin-bottom: 1em;
}

.assistant-content p:last-child {
  margin-bottom: 0;
}

.assistant-content h1,
.assistant-content h2,
.assistant-content h3,
.assistant-content h4,
.assistant-content h5,
.assistant-content h6 {
  margin-bottom: 0.6em;
  font-weight: 700;
  letter-spacing: -0.025em;
  color: #111827;
  line-height: 1.3;
}

.assistant-content h1 {
  font-size: 1.5em;
  color: #1f2937;
}

.assistant-content h2 {
  font-size: 1.3em;
  color: #374151;
}

.assistant-content h3 {
  font-size: 1.1em;
  color: #4b5563;
}

.assistant-content code {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0.3em 0.7em;
  border-radius: 8px;
  font-size: 0.88em;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Cascadia Code', Consolas, monospace;
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: #e11d48;
  font-weight: 500;
  font-feature-settings: 'liga' 1, 'calt' 1;
  letter-spacing: 0.02em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.assistant-content pre {
  margin: 1.5em 0;
  overflow: auto;
  border-radius: 12px;
}

.assistant-content ul,
.assistant-content ol {
  margin-bottom: 1em;
  padding-left: 2em;
}

.assistant-content blockquote {
  margin: 1em 0;
  padding-left: 1em;
  border-left: 4px solid #ddd;
  color: #666;
}

.assistant-content a {
  color: #4f46e5;
  text-decoration: none;
}

.assistant-content a:hover {
  text-decoration: underline;
}

/* 优化分隔线样式 */
.assistant-content hr {
  margin: 1em 0;
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
  opacity: 0.6;
}

.assistant-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.assistant-content th,
.assistant-content td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.assistant-content th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.markdown-pre {
  margin: 0 !important;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-item {
  animation: fadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 新增动画 */
@keyframes statusPulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(1.02);
    opacity: 0.9;
  }
}

/* 消息气泡悬停效果 */
.user-content:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.35);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.assistant-content:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 头像悬停效果 */
.message-avatar:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* 用户消息气泡边框光晕效果 */
.user-content::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #40a9ff, #1890ff, #096dd9);
  border-radius: 22px 10px 22px 22px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-content:hover::before {
  opacity: 0.5;
}

/* 助手消息气泡内阴影效果 */
.assistant-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.02) 100%);
  border-radius: 8px 20px 20px 20px;
  pointer-events: none;
}

/* 消息容器滚动条美化 */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式字体优化 */
@media screen and (max-width: 768px) {
  .message-item {
    padding: 12px 16px;
    margin-bottom: 2px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .message-avatar {
    margin: 0 12px;
  }
  
  .user-content, .assistant-content {
    padding: 14px 16px;
    font-size: 14px;
    line-height: 1.55;
  }
  
  .message-header {
    font-size: 13px;
  }
  
  .message-time {
    font-size: 10px;
  }
  
  .assistant-content code {
    font-size: 0.85em;
    padding: 0.25em 0.5em;
  }
  
  .message-status {
    bottom: 16px;
    padding: 10px 16px;
    border-radius: 20px;
  }
}

@media screen and (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }
  
  .message-avatar {
    margin: 0 8px;
  }
  
  .user-content, .assistant-content {
    padding: 12px 14px;
    font-size: 13px;
    line-height: 1.5;
  }
  
  .message-header {
    font-size: 12px;
  }
  
  .assistant-content h1 {
    font-size: 1.3em;
  }
  
  .assistant-content h2 {
    font-size: 1.2em;
  }
  
  .assistant-content h3 {
    font-size: 1.05em;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .assistant-content {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: #e5e7eb;
    border-color: rgba(255, 255, 255, 0.1);
    font-weight: 350;
  }
  
  .message-header {
    color: #f3f4f6;
  }
  
  .assistant-content h1,
  .assistant-content h2,
  .assistant-content h3 {
    color: #f9fafb;
  }
  
  .assistant-content code {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    color: #fbbf24;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .assistant-content pre {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    border-color: rgba(139, 92, 246, 0.4);
  }
  
  .assistant-content hr {
    background: linear-gradient(90deg, transparent 0%, #4b5563 50%, transparent 100%);
    opacity: 0.8;
  }
}

/* 打字机效果 - 可选 */
.typing-indicator {
  display: inline-block;
  animation: typingBlink 1.4s infinite;
}

@keyframes typingBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 错误消息样式 */
.message-body.error {
  color: #dc2626;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-left: 4px solid #dc2626;
}

/* 加载状态优化 */
.message-body.loading {
  opacity: 0.7;
  position: relative;
}

.message-body.loading::after {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid #1890ff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}

/* 消息渐入优化 */
.message-item.new-message {
  animation: messageSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-30px) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateX(5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 用户消息从右侧滑入 */
.user-message.new-message {
  animation: userMessageSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes userMessageSlideIn {
  0% {
    opacity: 0;
    transform: translateX(30px) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}