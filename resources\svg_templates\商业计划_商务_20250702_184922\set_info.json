{"set_name": "商业计划_商务_20250702_184922", "scenario": "商业计划", "style": "商务", "created_at": "2025-07-02T18:49:22.931912", "template_count": 10, "templates": [{"template_id": "商业计划_商务_cover", "type": "封面页", "filename": "商业计划_商务_cover.svg", "page_number": 1}, {"template_id": "商业计划_商务_agenda", "type": "目录页", "filename": "商业计划_商务_agenda.svg", "page_number": 2}, {"template_id": "商业计划_商务_section_divider", "type": "章节分隔页", "filename": "商业计划_商务_section_divider.svg", "page_number": 3}, {"template_id": "商业计划_商务_title_content", "type": "标题内容页", "filename": "商业计划_商务_title_content.svg", "page_number": 4}, {"template_id": "商业计划_商务_image_text", "type": "图文混排页", "filename": "商业计划_商务_image_text.svg", "page_number": 5}, {"template_id": "商业计划_商务_data_display", "type": "数据展示页", "filename": "商业计划_商务_data_display.svg", "page_number": 6}, {"template_id": "商业计划_商务_comparison", "type": "对比分析页", "filename": "商业计划_商务_comparison.svg", "page_number": 7}, {"template_id": "商业计划_商务_timeline", "type": "时间线页", "filename": "商业计划_商务_timeline.svg", "page_number": 8}, {"template_id": "商业计划_商务_quote", "type": "引用页", "filename": "商业计划_商务_quote.svg", "page_number": 9}, {"template_id": "商业计划_商务_conclusion", "type": "总结页", "filename": "商业计划_商务_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "商业计划", "display_name": "商业计划", "description": "商业计划书、投资提案、战略规划", "visual_characteristics": {"emphasis_on": "逻辑严密、数据支撑", "layout_style": "商务正式", "decorative_elements": "商业图表、财务数据、流程图"}, "content_focus": ["市场分析", "财务预测", "风险评估"], "target_audience": "投资人、合作伙伴", "tone": "professional"}, "style": {"style_type": "商务", "display_name": "商务", "description": "专业正式，体现权威和可信度", "design_principles": {"layout": "规范布局、对称平衡", "elements": "正式图形、商务图标", "emphasis": "专业性、权威性"}, "visual_elements": {"shapes": "矩形、稳重几何形", "lines": "直线、规整边框", "decorations": "商务图标、数据图表"}, "typography": {"font_style": "正式字体", "weight": "中粗", "spacing": "标准间距"}}, "colors": {"primary": "#1E40AF", "secondary": "#475569", "accent": "#3B82F6", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "scenario", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": []}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "商务风格", "scenario_adaptation": "商业计划场景优化", "visual_theme": "黑底特斯拉红高亮现代商务风格", "design_philosophy": "结合投资人、合作伙伴需求的专业正式，体现权威和可信度设计理念，通过高对比度与现代感增强视觉冲击力与记忆点。", "fusion_strategy": "scenario优先的场景风格融合"}, "color_palette": {"primary_color": "#1E40AF", "secondary_color": "#475569", "accent_color": "#3B82F6", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#3B82F6", "gradient_primary": "linear-gradient(135deg, #1E40AF, #475569)", "gradient_accent": "linear-gradient(45deg, #3B82F6, #1E40AF)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#1E40AF", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64, "4xl": 96}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Microsoft YaHei, Segoe UI, sans-serif", "secondary_font": "Source <PERSON> CN, Noto Sans CJK SC, sans-serif", "accent_font": "Times New Roman, serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 12, "border": "1px solid #BAE6FD", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["几何图形装饰", "渐变背景", "图标点缀", "分割线", "阴影效果"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "确保所有模板符合商业计划场景特点，内容结构清晰，信息传达高效。", "style_unity": "保持商务风格的一致性，通过统一的色彩、字体和布局原则实现。", "color_harmony": "黑底红高亮的现代对比配色体系贯穿始终，确保视觉冲击力与专业度的平衡。", "visual_rhythm": "通过Bento Grid布局、超大字体与常规字体、图文组合等方式，形成富有节奏感的视觉流，引导用户阅读。", "brand_consistency": "一致的品牌形象和视觉识别，确保每一页都体现出高端、专业的商业气息。"}, "created_at": "2025-07-02T18:44:14.314801", "scenario": "商业计划", "style": "商务", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"商务风格\",\n        \"scenario_adaptation\": \"商业计划场景优化\",\n        \"visual_theme\": \"黑底特斯拉红高亮现代商务风格\",\n        \"design_philosophy\": \"结合投资人、合作伙伴需求的专业正式，体现权威和可信度设计理念，通过高对比度与现代感增强视觉冲击力与记忆点。\",\n        \"fusion_strategy\": \"scenario优先的场景风格融合\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#1E40AF\",\n        \"secondary_color\": \"#475569\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#1D1D1F\",\n        \"hover_color\": \"#E31937\",\n        \"subtle_blue_gradient_usage\": \"蓝色系（如Primary Color）可用于背景中的微妙渐变或装饰性元素，以增加层次感，但主背景仍为纯黑色。\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": true,\n        \"grid_system\": {\"columns\": 12, \"gutter\": 24, \"margin\": 80},\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\"module_gap\": 32, \"section_gap\": 48, \"element_gap\": 16},\n        \"visual_hierarchy\": \"清晰的层次结构，符合商业计划场景需求，强调关键数据和论点。\",\n        \"alignment_system\": \"基于商务风格的左对齐和居中对齐原则，确保专业与平衡。\",\n        \"card_module_height_rule\": \"卡片/模块高度将严格遵循第一阶段设定的强制高度规则（单行800-900px，两行400-445px），并在此高度内优化内容布局和填充。\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"推荐使用高可读性、多字重且现代感的无衬线字体，如 'Noto Sans SC', 'Inter', 'Roboto', 'system-ui'\",\n        \"font_sizes\": {\n            \"hero_title\": 120,\n            \"main_title\": 72,\n            \"section_title\": 48,\n            \"content_title\": 32,\n            \"body_text\": 24,\n            \"small_text\": 16,\n            \"accent_number\": 180\n        },\n        \"font_weights\": {\n            \"title\": \"bold\",\n            \"content\": \"normal\",\n            \"emphasis\": \"600\",\n            \"chinese\": \"bold\",\n            \"english\": \"300\"\n        },\n        \"line_heights\": {\n            \"title\": 1.1,\n            \"content\": 1.5,\n            \"dense\": 1.3,\n            \"chinese_content\": 1.8\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"大号粗体\",\n            \"english_style\": \"小号细体\",\n            \"number_style\": \"超大号突出\"\n        },\n        \"readability_optimization\": \"针对投资人、合作伙伴优化的可读性，确保黑底白字高对比度，并利用超大字体突出核心信息。\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"适合商业计划的财务图表、增长曲线、市场分析图、风险矩阵等数据可视化元素。\",\n        \"style_characteristics\": \"体现商务风格的专业、简洁、权威性，通过几何线条和高对比度色彩增强现代感。\",\n        \"bento_grid_layout\": \"Apple风格的Bento Grid网格布局，灵活组合大小不同的信息区块，实现信息聚焦和视觉动感。\",\n        \"black_red_theme\": \"纯黑色背景(#000000)与特斯拉红色(#E31937)高亮色的现代高对比度配色方案，营造科技感和高端商务氛围。\",\n        \"oversized_typography\": \"超大字号(120px+，甚至180px+)的数字或关键短语作为视觉焦点，配合简洁线条图形，强化视觉冲击力。\",\n        \"decorative_elements\": [\n            {\n                \"name\": \"简洁线条图形元素\",\n                \"description\": \"用于数据可视化、流程图和背景纹理，线条流畅且色彩内敛（次要色或低透明度高亮色）。\"\n            },\n            {\n                \"name\": \"特斯拉红色透明度渐变元素\",\n                \"description\": \"仅使用特斯拉红色自身透明度渐变，避免多色渐变，用于背景叠加、卡片边框或强调区域，增加科技感和层次。\"\n            },\n            {\n                \"name\": \"中英文混排排版元素\",\n                \"description\": \"中文标题和核心内容使用大号粗体，英文作为辅助性、解释性或点缀性文本使用小号细体，提升国际化视觉效果。\"\n            },\n            {\n                \"name\": \"符合商务风格的装饰元素\",\n                \"description\": \"如极简图标、箭头、分割线，色彩与整体配色和谐。\"\n            },\n            {\n                \"name\": \"适合商业计划场景的装饰元素\",\n                \"description\": \"如数据增长趋势线、目标靶心、连接点等抽象图形，以低透明度形式存在。\"\n            }\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"适度的圆角（16-24px），增加现代感而非传统商务的锐利。\",\n            \"shadow\": \"柔和、扩散性的内部阴影或外部阴影，增加卡片深度，避免生硬感。\",\n            \"border\": \"特斯拉红色细边框（1-2px），或使用特斯拉红色透明度渐变边框，强调卡片结构。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": \"8-16px，与卡片圆角保持一致或略小。\",\n            \"shadow_style\": \"无阴影或极简、柔和的阴影，避免喧宾夺主。\",\n            \"overlay_style\": \"特斯拉红色半透明遮罩（opacity 0.2-0.4），用于图片背景或强调特定图片区域，与主题色保持一致。\",\n            \"bento_grid_placement\": \"图片作为Bento Grid中的一个模块，尺寸和比例根据网格系统和图片内容智能适配，确保视觉平衡。\",\n            \"support_high_res\": \"支持外部URL和本地路径的高清图片引用，使用SVG的<image>标签。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"页面顶部或底部，确保在关键信息区域留出充足空间，且不干扰内容阅读。\",\n            \"size_guidelines\": \"适中尺寸，确保清晰可见但不过大，与整体设计比例协调。\",\n            \"integration_style\": \"与黑底红高亮主题和谐的Logo处理，可考虑白色或高亮色版本，无背景或半透明背景。\",\n            \"animation_hint\": \"考虑滚动时Logo的微动效或透明度变化，增加交互感。\"\n        },\n        \"chart_components\": \"可引用在线图表组件，确保样式（颜色、字体、线条）与整体黑底红高亮商务风格高度一致，数据清晰易读。\",\n        \"icon_system\": \"统一的图标风格，优先使用扁平化或线条图标，色彩与配色方案协调，符合场景和风格特点。\"\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"确保所有模板符合商业计划场景特点，内容结构清晰，信息传达高效。\",\n        \"style_unity\": \"保持商务风格的一致性，通过统一的色彩、字体和布局原则实现。\",\n        \"color_harmony\": \"黑底红高亮的现代对比配色体系贯穿始终，确保视觉冲击力与专业度的平衡。\",\n        \"visual_rhythm\": \"通过Bento Grid布局、超大字体与常规字体、图文组合等方式，形成富有节奏感的视觉流，引导用户阅读。\",\n        \"brand_consistency\": \"一致的品牌形象和视觉识别，确保每一页都体现出高端、专业的商业气息。\"\n    }\n}\n```"}}