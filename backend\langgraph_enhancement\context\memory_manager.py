# -*- coding: utf-8 -*-
"""
LangGraph 增强系统 - 内存管理器

核心功能：
- LangGraph MemorySaver 集成
- 分层内存管理（core_context, working_memory, agent_memory）
- 自动清理和优化机制
- 支持检查点保存和恢复
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

try:
    from langgraph.checkpoint.memory import MemorySaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    # 🔥 优化：改进LangGraph集成处理
    class MemorySaver:
        def __init__(self):
            self._storage = {}
        
        def put(self, key, value):
            self._storage[key] = value
        
        def get(self, key, default=None):
            return self._storage.get(key, default)
        
        def delete(self, key):
            return self._storage.pop(key, None)
    
    LANGGRAPH_AVAILABLE = False

logger = logging.getLogger(__name__)


class EnhancedMemoryManager:
    """增强内存管理器 - LangGraph 最强上下文管理"""
    
    def __init__(self, max_working_memory_size: int = 15000, 
                 max_agent_memory_size: int = 8000):
        """🔥 优化：初始化增强内存管理器"""
        self.memory_saver = MemorySaver()
        self.langgraph_available = LANGGRAPH_AVAILABLE
        
        # 分层内存存储
        self.core_context: Dict[str, Any] = {}
        self.working_memory: Dict[str, Any] = {}
        self.agent_memory: Dict[str, Dict] = {}
        # 🔥 新增：团队记忆空间 - 真正的共享记忆机制
        self.team_memory: Dict[str, Dict] = {}  # {team_id: {key: memory_item}}
        
        # 🔥 优化：调整内存大小参数，更适合实际使用
        self.max_working_memory_size = max_working_memory_size
        self.max_agent_memory_size = max_agent_memory_size
        
        # 🔥 优化：增强统计信息
        self.memory_metrics = {
            "total_allocations": 0,
            "total_cleanups": 0,
            "last_cleanup": datetime.now(),
            "peak_memory_usage": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "langgraph_integration": self.langgraph_available
        }
        
        logger.info(f"🔥 增强内存管理器初始化完成 (LangGraph: {'已集成' if self.langgraph_available else '降级模式'})")
    
    async def store_core_context(self, key: str, value: Any) -> None:
        """存储永久核心上下文"""
        self.core_context[key] = value
        self.memory_metrics["total_allocations"] += 1
        logger.debug(f"存储核心上下文: {key}")
    
    async def store_working_memory(self, key: str, value: Any, ttl_minutes: int = 60) -> None:
        """存储工作记忆（带过期时间）"""
        expire_time = datetime.now() + timedelta(minutes=ttl_minutes)
        self.working_memory[key] = {
            "value": value,
            "created_at": datetime.now(),
            "expires_at": expire_time
        }
        self.memory_metrics["total_allocations"] += 1
        
        if len(self.working_memory) > self.max_working_memory_size:
            await self._cleanup_working_memory()
        
        logger.debug(f"存储工作记忆: {key}")
    
    async def store_agent_memory(self, agent_id: str, key: str, value: Any) -> None:
        """存储智能体专用记忆"""
        if agent_id not in self.agent_memory:
            self.agent_memory[agent_id] = {}
        
        self.agent_memory[agent_id][key] = {
            "value": value,
            "created_at": datetime.now(),
            "accessed_count": 0
        }
        
        if len(self.agent_memory[agent_id]) > self.max_agent_memory_size:
            await self._cleanup_agent_memory(agent_id)
        
        self.memory_metrics["total_allocations"] += 1
        logger.debug(f"存储智能体记忆: {agent_id}.{key}")
    
    async def get_core_context(self, key: str, default: Any = None) -> Any:
        """获取核心上下文"""
        return self.core_context.get(key, default)
    
    async def get_working_memory(self, key: str, default: Any = None) -> Any:
        """获取工作记忆（检查过期）"""
        if key not in self.working_memory:
            return default
        
        item = self.working_memory[key]
        if datetime.now() > item["expires_at"]:
            del self.working_memory[key]
            return default
        
        return item["value"]
    
    async def get_agent_memory(self, agent_id: str, key: str, default: Any = None) -> Any:
        """获取智能体记忆"""
        if agent_id not in self.agent_memory or key not in self.agent_memory[agent_id]:
            return default
        
        item = self.agent_memory[agent_id][key]
        item["accessed_count"] += 1
        return item["value"]
    
    # 🔥 ============ 团队记忆管理方法 ============
    
    async def store_team_memory(self, team_id: str, key: str, value: Any) -> bool:
        """
        存储团队记忆
        
        Args:
            team_id: 团队ID
            key: 记忆键名
            value: 要存储的值
            
        Returns:
            bool: 存储是否成功
        """
        try:
            if team_id not in self.team_memory:
                self.team_memory[team_id] = {}
            
            self.team_memory[team_id][key] = {
                "value": value,
                "created_at": datetime.now(),
                "accessed_count": 0,
                "is_team_memory": True,
                "team_id": team_id
            }
            
            self.memory_metrics["total_allocations"] += 1
            logger.debug(f"🏢 存储团队记忆: {team_id}.{key}")
            return True
        except Exception as e:
            logger.error(f"❌ 存储团队记忆失败: {e}")
            return False
    
    async def get_team_memory(self, team_id: str, key: str, default: Any = None) -> Any:
        """
        获取团队记忆
        
        Args:
            team_id: 团队ID
            key: 记忆键名
            default: 默认值
            
        Returns:
            记忆值或默认值
        """
        if team_id not in self.team_memory or key not in self.team_memory[team_id]:
            return default
        
        item = self.team_memory[team_id][key]
        item["accessed_count"] += 1
        return item["value"]
    
    async def list_team_memories(self, team_id: str) -> List[str]:
        """
        列出团队的所有记忆键名
        
        Args:
            team_id: 团队ID
            
        Returns:
            List[str]: 记忆键名列表
        """
        return list(self.team_memory.get(team_id, {}).keys())
    
    async def search_team_memories(self, team_id: str, pattern: str) -> Dict[str, Any]:
        """
        搜索团队记忆
        
        Args:
            team_id: 团队ID
            pattern: 搜索模式
            
        Returns:
            Dict: 匹配的记忆项
        """
        team_memories = self.team_memory.get(team_id, {})
        matched = {}
        
        for key, memory_item in team_memories.items():
            # 在键名和值中搜索
            if (pattern.lower() in key.lower() or 
                pattern.lower() in str(memory_item.get("value", "")).lower()):
                matched[key] = memory_item["value"]
        
        return matched
    
    async def clear_team_memory(self, team_id: str, key: str = None) -> bool:
        """
        清除团队记忆
        
        Args:
            team_id: 团队ID
            key: 要清除的记忆键名，None表示清除该团队的所有记忆
            
        Returns:
            bool: 清除是否成功
        """
        try:
            if team_id not in self.team_memory:
                return True
            
            if key is None:
                # 清除该团队的所有记忆
                self.team_memory[team_id].clear()
                logger.info(f"🏢 清除团队 {team_id} 的所有记忆")
            else:
                # 清除特定记忆
                if key in self.team_memory[team_id]:
                    del self.team_memory[team_id][key]
                    logger.debug(f"🏢 清除团队记忆: {team_id}.{key}")
            return True
        except Exception as e:
            logger.error(f"❌ 清除团队记忆失败: {e}")
            return False
    
    async def get_team_memory_stats(self, team_id: str) -> Dict[str, Any]:
        """
        获取团队记忆统计信息
        
        Args:
            team_id: 团队ID
            
        Returns:
            Dict: 团队记忆统计信息
        """
        team_memories = self.team_memory.get(team_id, {})
        stats = {
            "team_id": team_id,
            "total_memories": len(team_memories),
            "memory_keys": list(team_memories.keys()),
            "total_access_count": sum(item.get("accessed_count", 0) for item in team_memories.values())
        }
        
        # 找出最常访问的记忆
        if team_memories:
            access_counts = [(k, v.get("accessed_count", 0)) for k, v in team_memories.items()]
            access_counts.sort(key=lambda x: x[1], reverse=True)
            stats["most_accessed"] = access_counts[0] if access_counts else None
        
        return stats
    
    async def append_team_memory(self, team_id: str, key: str, value: Any, separator: str = "\n") -> bool:
        """
        追加内容到团队记忆
        
        Args:
            team_id: 团队ID
            key: 记忆键名
            value: 要追加的内容
            separator: 分隔符
            
        Returns:
            bool: 追加是否成功
        """
        try:
            # 获取现有记忆
            existing_value = await self.get_team_memory(team_id, key)
            
            # 追加内容
            if existing_value is None:
                new_value = value
            else:
                new_value = str(existing_value) + separator + str(value)
            
            # 存储更新后的记忆
            await self.store_team_memory(team_id, key, new_value)
            return True
        except Exception as e:
            logger.error(f"❌ 追加团队记忆失败: {e}")
            return False
    
    async def get_full_context_for_agent(self, agent_id: str) -> Dict[str, Any]:
        """获取智能体的完整上下文"""
        context = {
            "core_context": self.core_context.copy(),
            "working_memory": {},
            "agent_memory": self.agent_memory.get(agent_id, {})
        }
        
        now = datetime.now()
        for key, item in list(self.working_memory.items()):
            if now <= item["expires_at"]:
                context["working_memory"][key] = item["value"]
            else:
                del self.working_memory[key]
        
        return context
    
    async def _cleanup_working_memory(self) -> None:
        """清理过期的工作记忆"""
        now = datetime.now()
        expired_keys = []
        
        for key, item in self.working_memory.items():
            if now > item["expires_at"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.working_memory[key]
        
        self.memory_metrics["total_cleanups"] += 1
        self.memory_metrics["last_cleanup"] = now
        
        if expired_keys:
            logger.info(f"清理过期工作记忆: {len(expired_keys)} 项")
    
    async def _cleanup_agent_memory(self, agent_id: str) -> None:
        """清理智能体记忆（基于访问频率）"""
        if agent_id not in self.agent_memory:
            return
        
        agent_mem = self.agent_memory[agent_id]
        sorted_items = sorted(
            agent_mem.items(),
            key=lambda x: x[1]["accessed_count"]
        )
        
        keep_count = self.max_agent_memory_size // 2
        for key, _ in sorted_items[:-keep_count]:
            del agent_mem[key]
        
        logger.info(f"清理智能体记忆 {agent_id}: 保留 {keep_count} 项")
    
    async def get_memory_usage_stats(self) -> Dict[str, Any]:
        """获取内存使用统计"""
        total_items = (
            len(self.core_context) +
            len(self.working_memory) +
            sum(len(agent_mem) for agent_mem in self.agent_memory.values()) +
            sum(len(team_mem) for team_mem in self.team_memory.values())
        )
        
        stats = {
            "total_items": total_items,
            "core_context_items": len(self.core_context),
            "working_memory_items": len(self.working_memory),
            "agent_memory_items": {
                agent_id: len(agent_mem) 
                for agent_id, agent_mem in self.agent_memory.items()
            },
            "team_memory_items": {
                team_id: len(team_mem)
                for team_id, team_mem in self.team_memory.items()
            },
            "metrics": self.memory_metrics,
            "last_cleanup": self.memory_metrics["last_cleanup"].isoformat()
        }
        
        if total_items > self.memory_metrics["peak_memory_usage"]:
            self.memory_metrics["peak_memory_usage"] = total_items
        
        return stats
    
    async def auto_cleanup(self) -> None:
        """自动清理过期内容"""
        await self._cleanup_working_memory()
        
        for agent_id in list(self.agent_memory.keys()):
            if len(self.agent_memory[agent_id]) > self.max_agent_memory_size:
                await self._cleanup_agent_memory(agent_id)
    
    def get_langgraph_memory_saver(self) -> MemorySaver:
        """获取 LangGraph 原生内存保存器"""
        return self.memory_saver
    
    # 🔥 ============ 跨智能体记忆共享 ============
    
    async def share_memory_between_agents(self, 
                                        source_agent_id: str, 
                                        target_agent_id: str, 
                                        memory_key: str,
                                        new_key: str = None) -> bool:
        """
        在智能体之间共享记忆
        
        Args:
            source_agent_id: 源智能体ID
            target_agent_id: 目标智能体ID  
            memory_key: 要共享的记忆键名
            new_key: 在目标智能体中的新键名，默认使用原键名
            
        Returns:
            bool: 共享是否成功
        """
        try:
            # 获取源记忆
            source_memory = await self.get_agent_memory(source_agent_id, memory_key)
            if source_memory is None:
                logger.warning(f"⚠️ 源智能体 {source_agent_id} 没有记忆: {memory_key}")
                return False
            
            # 设置目标键名
            target_key = new_key if new_key else memory_key
            
            # 复制到目标智能体
            await self.store_agent_memory(target_agent_id, target_key, source_memory)
            
            logger.info(f"🔄 记忆共享: {source_agent_id}.{memory_key} -> {target_agent_id}.{target_key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 记忆共享失败: {e}")
            return False
    
    async def create_shared_memory(self, memory_key: str, value: Any, agent_ids: List[str]) -> bool:
        """
        创建多个智能体的共享记忆
        
        Args:
            memory_key: 记忆键名
            value: 记忆值
            agent_ids: 要共享的智能体ID列表
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 为每个智能体创建共享记忆
            for agent_id in agent_ids:
                shared_key = f"shared_{memory_key}"
                await self.store_agent_memory(agent_id, shared_key, {
                    "value": value,
                    "shared_with": agent_ids,
                    "created_at": datetime.now().isoformat(),
                    "is_shared": True
                })
            
            logger.info(f"🔗 创建共享记忆 '{memory_key}' 给 {len(agent_ids)} 个智能体")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建共享记忆失败: {e}")
            return False
    
    async def get_shared_memories_for_agent(self, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体的所有共享记忆
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            Dict: 共享记忆字典
        """
        try:
            agent_memories = self.agent_memory.get(agent_id, {})
            shared_memories = {}
            
            for key, memory_item in agent_memories.items():
                if key.startswith("shared_") or memory_item.get("value", {}).get("is_shared"):
                    shared_memories[key] = memory_item["value"]
            
            return shared_memories
            
        except Exception as e:
            logger.error(f"❌ 获取共享记忆失败: {e}")
            return {}
    
    async def broadcast_memory_to_team(self, 
                                     sender_agent_id: str,
                                     memory_key: str, 
                                     value: Any,
                                     team_agent_ids: List[str]) -> int:
        """
        向团队广播记忆
        
        Args:
            sender_agent_id: 发送者智能体ID
            memory_key: 记忆键名
            value: 记忆值
            team_agent_ids: 团队成员智能体ID列表
            
        Returns:
            int: 成功接收的智能体数量
        """
        try:
            success_count = 0
            broadcast_key = f"team_broadcast_{memory_key}"
            
            for agent_id in team_agent_ids:
                if agent_id != sender_agent_id:  # 不发送给自己
                    success = await self.store_agent_memory(agent_id, broadcast_key, {
                        "value": value,
                        "sender": sender_agent_id,
                        "broadcast_time": datetime.now().isoformat(),
                        "is_broadcast": True
                    })
                    if success:
                        success_count += 1
            
            logger.info(f"📢 {sender_agent_id} 向团队广播记忆 '{memory_key}': {success_count}/{len(team_agent_ids)-1} 成功")
            return success_count
            
        except Exception as e:
            logger.error(f"❌ 团队记忆广播失败: {e}")
            return 0


# 全局内存管理器实例
def get_memory_manager() -> EnhancedMemoryManager:
    """获取全局内存管理器实例"""
    if not hasattr(get_memory_manager, '_instance'):
        get_memory_manager._instance = EnhancedMemoryManager()
    return get_memory_manager._instance 