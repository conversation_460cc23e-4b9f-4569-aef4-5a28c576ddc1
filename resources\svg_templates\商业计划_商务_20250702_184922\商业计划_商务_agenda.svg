<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 颜色和字体定义 -->
    <style type="text/css">
      <![CDATA[
      /* 配色方案 */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary-color { fill: #1E293B; } /* text_primary */
      .text-secondary-color { fill: #64748B; } /* text_secondary */
      .text-light-color { fill: #94A3B8; } /* text_light */
      .card-background-color { fill: #FFFFFF; } /* card_background */
      .card-border-color { stroke: #BAE6FD; } /* card_border */
      .container-background-color { fill: #E0F2FE; } /* container_background */

      /* 字体系统 */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* 字体大小和粗细 */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* 目录章节特定样式 */
      .chapter-number { font-size: 96px; font-weight: 700; font-family: "Microsoft YaHei", sans-serif; }
      .chapter-title-cn { font-size: 36px; font-weight: 700; font-family: "Microsoft YaHei", sans-serif; }
      .chapter-desc-en { font-size: 22px; font-weight: 400; font-family: "Segoe UI", sans-serif; }

      /* 图标样式 */
      .icon-stroke { stroke: #3B82F6; stroke-width: 3; stroke-linecap: round; stroke-linejoin: round; fill: none; }

      /* 进度条填充渐变 */
      .progress-bar-fill { fill: url(#progressGradient); }
      ]]>
    </style>

    <!-- 渐变定义 -->
    <linearGradient id="accentGradientTransparentRight" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </linearGradient>

    <linearGradient id="accentGradientTransparentLeft" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.1"/>
    </linearGradient>

    <linearGradient id="progressGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 顶部装饰性渐变 -->
  <rect x="0" y="0" width="300" height="1080" class="accentGradientTransparentLeft"/>
  <rect x="1620" y="0" width="300" height="1080" class="accentGradientTransparentRight"/>

  <!-- 头部区域 -->
  <!-- Logo 占位符 -->
  <rect x="80" y="60" width="160" height="40" class="primary-color" rx="8" />
  <text x="160" y="88" text-anchor="middle" class="body-text" fill="#FFFFFF" font-weight="700" font-family="Microsoft YaHei, sans-serif">LOGO</text>
  
  <!-- 页码信息 -->
  <text x="1840" y="88" text-anchor="end" class="section-title text-secondary-color" font-size="36px" font-weight="700">2<tspan class="small-text text-light-color" dx="5" dy="-5">/10</tspan></text>

  <!-- 主标题区域 -->
  <text x="960" y="240" text-anchor="middle" class="main-title text-primary-color">{title}</text>
  <text x="960" y="300" text-anchor="middle" class="content-title text-secondary-color" font-weight="400">{subtitle}</text>

  <!-- 目录章节列表 -->
  <!-- 布局中心线 X=960px，内容宽度约为 1000px，所以从 960 - 500 = 460px 开始 -->
  <g transform="translate(460, 420)">
    <!-- 章节 01: 市场分析 -->
    <g class="toc-item">
      <text x="0" y="0" class="chapter-number accent-color">01</text>
      <text x="140" y="-10" class="chapter-title-cn text-primary-color">市场分析</text>
      <text x="140" y="30" class="chapter-desc-en text-secondary-color">Market Overview and Competitive Landscape</text>
      <!-- 图标: 市场分析 (概念图表) -->
      <path d="M70 10 L70 50 M80 20 L80 50 M90 0 L90 50 M100 30 L100 50" class="icon-stroke" transform="translate(0, -10)" />
      <line x1="0" y1="60" x2="900" y2="60" class="card-border-color" stroke-width="1"/>
    </g>

    <!-- 章节 02: 商业模式和产品 -->
    <g class="toc-item" transform="translate(0, 100)">
      <text x="0" y="0" class="chapter-number accent-color">02</text>
      <text x="140" y="-10" class="chapter-title-cn text-primary-color">商业模式和产品</text>
      <text x="140" y="30" class="chapter-desc-en text-secondary-color">Business Model and Product Innovation</text>
      <!-- 图标: 商业模式 (概念齿轮/流程) -->
      <circle cx="85" cy="20" r="15" class="icon-stroke" transform="translate(0, -10)"/>
      <path d="M85 5 L85 -5 M70 15 L60 10 M70 25 L60 30 M100 15 L110 10 M100 25 L110 30" class="icon-stroke" transform="translate(0, -10)"/>
      <line x1="0" y1="60" x2="900" y2="60" class="card-border-color" stroke-width="1"/>
    </g>

    <!-- 章节 03: 营销和销售策略 -->
    <g class="toc-item" transform="translate(0, 200)">
      <text x="0" y="0" class="chapter-number accent-color">03</text>
      <text x="140" y="-10" class="chapter-title-cn text-primary-color">营销和销售策略</text>
      <text x="140" y="30" class="chapter-desc-en text-secondary-color">Marketing and Sales Strategies</text>
      <!-- 图标: 营销 (概念扩音器) -->
      <path d="M70 20 L100 0 L100 40 Z" class="icon-stroke" transform="translate(0, -10)"/>
      <circle cx="65" cy="20" r="8" class="icon-stroke" transform="translate(0, -10)"/>
      <path d="M55 20 L40 20" class="icon-stroke" transform="translate(0, -10)"/>
      <line x1="0" y1="60" x2="900" y2="60" class="card-border-color" stroke-width="1"/>
    </g>

    <!-- 章节 04: 财务预测和资金需求 -->
    <g class="toc-item" transform="translate(0, 300)">
      <text x="0" y="0" class="chapter-number accent-color">04</text>
      <text x="140" y="-10" class="chapter-title-cn text-primary-color">财务预测和资金需求</text>
      <text x="140" y="30" class="chapter-desc-en text-secondary-color">Financial Projections and Funding Requirements</text>
      <!-- 图标: 财务 (概念钱袋/货币符号) -->
      <path d="M70 5 L100 5 L100 35 L70 35 C60 35 60 5 70 5 Z" class="icon-stroke" transform="translate(0, -10)"/>
      <path d="M85 5 L85 35" class="icon-stroke" transform="translate(0, -10)"/>
      <path d="M75 15 H95 M75 25 H95" class="icon-stroke" transform="translate(0, -10)"/>
      <line x1="0" y1="60" x2="900" y2="60" class="card-border-color" stroke-width="1"/>
    </g>
  </g>

  <!-- 底部进度指示器 -->
  <rect x="80" y="1000" width="1760" height="8" class="container-background-color" rx="4"/>
  <!-- 假设总共10页，当前为第2页，进度为 2/10 = 20% -->
  <rect x="80" y="1000" width="352" height="8" class="progress-bar-fill" rx="4"/> <!-- 1760 * (2/10) = 352 -->

  <!-- 底部公司信息 -->
  <text x="960" y="1050" text-anchor="middle" class="small-text text-light-color" font-family="Segoe UI, sans-serif">© {date} {author} - All Rights Reserved.</text>

</svg>