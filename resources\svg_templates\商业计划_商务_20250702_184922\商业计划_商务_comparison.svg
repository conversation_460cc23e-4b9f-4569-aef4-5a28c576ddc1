<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css"><![CDATA[
      /* Color Definitions */
      .primary-color-fill { fill: #1E40AF; }
      .secondary-color-fill { fill: #475569; }
      .accent-color-fill { fill: #3B82F6; }
      .background-color-fill { fill: #F8FAFC; }
      .text-primary-fill { fill: #1E293B; }
      .text-secondary-fill { fill: #64748B; }
      .card-background-fill { fill: #FFFFFF; }
      .card-border-stroke { stroke: #BAE6FD; }
      .container-background-fill { fill: #E0F2FE; }
      .info-color-fill { fill: #3B82F6; } /* Same as accent_color */
      .light-text-fill { fill: #BAE6FD; }
      .white-text-fill { fill: #FFFFFF; }

      /* Font System */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* Font Sizes */
      .hero-title { font-size: 72px; }
      .main-title { font-size: 56px; }
      .section-title { font-size: 36px; }
      .content-title { font-size: 28px; }
      .body-text { font-size: 22px; }
      .small-text { font-size: 16px; }
      .caption-text { font-size: 14px; }

      /* Font Weights */
      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      /* Line Heights (for tspan dy) */
      /* Note: For SVG, line-height is typically managed by explicit dy attributes on tspans. */
      /* These classes are illustrative for conceptual mapping. */
      .line-height-normal { line-height: 1.4; } 
      .line-height-relaxed { line-height: 1.6; }

      /* Card Style */
      .card-shadow { filter: url(#drop-shadow); }

      /* Text Alignment (using text-anchor property) */
      .text-align-left { text-anchor: start; }
      .text-align-center { text-anchor: middle; }
      .text-align-right { text-anchor: end; }

      /* Stroke Styles for Icons/Graphics */
      .stroke-accent { stroke: #3B82F6; stroke-width: 2px; fill: none; }
      .stroke-primary { stroke: #1E40AF; stroke-width: 2px; fill: none; }
      .stroke-secondary { stroke: #475569; stroke-width: 2px; fill: none; }
    ]]></style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4" result="blur" />
      <feOffset in="blur" dx="0" dy="4" result="offsetBlur" />
      <feFlood flood-color="rgba(0, 0, 0, 0.1)" result="flood" />
      <feComposite in="flood" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icon: Checkmark -->
    <symbol id="icon-checkmark" viewBox="0 0 24 24">
      <path d="M20 6L9 17l-5-5" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- Icon: Cross -->
    <symbol id="icon-cross" viewBox="0 0 24 24">
      <path d="M18 6L6 18M6 6l12 12" stroke="#EF4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- Icon: Lightbulb (Idea) -->
    <symbol id="icon-lightbulb" viewBox="0 0 24 24">
        <path d="M9 18h6" class="stroke-accent" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12 2C9.243 2 7 4.243 7 7c0 2.215 1.54 3.708 2.5 5.5S10 16 10 16h4s.5-2.285 2.5-5.5C17.46 10.708 19 9.215 19 7c0-2.757-2.243-5-5-5z" class="stroke-accent" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- Icon: Chart (Analytics) -->
    <symbol id="icon-chart" viewBox="0 0 24 24">
        <path d="M3 3v18h18" class="stroke-accent" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M18.7 8.3c-2-2-5.2-2-7.2 0L9 10.8 6.3 8.1 3 11.4" class="stroke-accent" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M18.7 12.3c-2-2-5.2-2-7.2 0L9 14.8 6.3 12.1 3 15.4" class="stroke-accent" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative geometric shapes -->
  <circle cx="1800" cy="100" r="80" fill="#3B82F6" opacity="0.1" />
  <rect x="1700" y="900" width="200" height="200" fill="#1E40AF" opacity="0.05" transform="rotate(45 1800 1000)"/>
  <rect x="0" y="800" width="300" height="300" fill="#475569" opacity="0.03" transform="rotate(-30 150 950)"/>


  <!-- Header -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="160" height="40" class="primary-color-fill" rx="8" ry="8" />
    <text x="160" y="88" class="small-text font-semibold text-align-center white-text-fill">
      <tspan class="font-primary">您的</tspan>
      <tspan class="font-accent">Logo</tspan>
    </text>

    <!-- Page Title -->
    <text x="960" y="100" class="main-title font-primary text-primary-fill text-align-center">
      商业计划：{title}
    </text>

    <!-- Page Number -->
    <text x="1840" y="88" class="small-text font-primary text-secondary-fill text-align-right">7/10</text>
  </g>

  <!-- Main Content Area: Comparison Section -->
  <g id="comparison-section">
    <!-- Section Subtitle -->
    <text x="960" y="200" class="section-title font-primary text-primary-fill text-align-center">
      {subtitle}
    </text>

    <!-- Left Card: Option A -->
    <g id="option-a-card">
      <rect x="150" y="280" width="780" height="580" rx="12" ry="12" class="card-background-fill card-border-stroke card-shadow" />
      <text x="540" y="340" class="content-title font-primary text-primary-fill text-align-center">
        选项 A：{content_title_A}
      </text>
      <use xlink:href="#icon-chart" x="528" y="370" width="24" height="24" class="info-color-fill" />
      <text x="540" y="400" class="body-text font-secondary text-secondary-fill text-align-center line-height-relaxed">
        <tspan x="540" dy="1.6em">市场份额：{market_share_A}</tspan>
        <tspan x="540" dy="1.6em">增长潜力：{growth_potential_A}</tspan>
        <tspan x="540" dy="1.6em">主要优势：{advantage_A_1}</tspan>
        <tspan x="540" dy="1.6em">{advantage_A_2}</tspan>
        <tspan x="540" dy="1.6em">{advantage_A_3}</tspan>
        <tspan x="540" dy="1.6em">风险评估：{risk_A}</tspan>
      </text>
      <!-- Example Data Visualization Placeholder -->
      <rect x="200" y="650" width="680" height="150" class="container-background-fill" rx="8" ry="8" />
      <text x="540" y="730" class="small-text font-primary text-secondary-fill text-align-center">
        数据图表 A 占位符
      </text>
    </g>

    <!-- Right Card: Option B -->
    <g id="option-b-card">
      <rect x="990" y="280" width="780" height="580" rx="12" ry="12" class="card-background-fill card-border-stroke card-shadow" />
      <text x="1380" y="340" class="content-title font-primary text-primary-fill text-align-center">
        选项 B：{content_title_B}
      </text>
      <use xlink:href="#icon-chart" x="1368" y="370" width="24" height="24" class="info-color-fill" />
      <text x="1380" y="400" class="body-text font-secondary text-secondary-fill text-align-center line-height-relaxed">
        <tspan x="1380" dy="1.6em">市场份额：{market_share_B}</tspan>
        <tspan x="1380" dy="1.6em">增长潜力：{growth_potential_B}</tspan>
        <tspan x="1380" dy="1.6em">主要优势：{advantage_B_1}</tspan>
        <tspan x="1380" dy="1.6em">{advantage_B_2}</tspan>
        <tspan x="1380" dy="1.6em">{advantage_B_3}</tspan>
        <tspan x="1380" dy="1.6em">风险评估：{risk_B}</tspan>
      </text>
      <!-- Example Data Visualization Placeholder -->
      <rect x="1040" y="650" width="680" height="150" class="container-background-fill" rx="8" ry="8" />
      <text x="1380" y="730" class="small-text font-primary text-secondary-fill text-align-center">
        数据图表 B 占位符
      </text>
    </g>

    <!-- Central Divider / Difference Highlight -->
    <g id="difference-highlight">
      <circle cx="960" cy="570" r="60" fill="url(#accentGradient)" opacity="0.8" />
      <text x="960" y="575" class="hero-title font-bold font-primary text-align-center white-text-fill">
        VS.
      </text>
      <text x="960" y="660" class="content-title font-primary text-primary-fill text-align-center">
        主要差异点
      </text>
      <text x="960" y="700" class="body-text font-secondary text-secondary-fill text-align-center line-height-relaxed">
        <tspan x="960" dy="1.6em">差异点一：{diff_point_1}</tspan>
        <tspan x="960" dy="1.6em">差异点二：{diff_point_2}</tspan>
        <tspan x="960" dy="1.6em">差异点三：{diff_point_3}</tspan>
      </text>
    </g>
  </g>

  <!-- Conclusion Section -->
  <g id="conclusion-section">
    <rect x="80" y="900" width="1760" height="120" rx="12" ry="12" class="primary-color-fill card-shadow" opacity="0.9" />
    <use xlink:href="#icon-lightbulb" x="120" y="930" width="48" height="48" class="white-text-fill" />
    <text x="190" y="960" class="content-title font-primary text-align-left white-text-fill">
      结论：{conclusion_summary}
    </text>
    <text x="190" y="990" class="body-text font-secondary text-align-left light-text-fill">
      {conclusion_detail}
    </text>
  </g>

  <!-- Footer -->
  <g id="footer">
    <text x="80" y="1050" class="small-text font-primary text-secondary-fill text-align-left">
      日期：{date}
    </text>
    <text x="1840" y="1050" class="small-text font-primary text-secondary-fill text-align-right">
      作者：{author}
    </text>
  </g>

</svg>