import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Layout, ConfigProvider, theme, Menu, Modal } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import Sidebar from './components/Sidebar';
import { createNewConversation, createSystemMessage, formatMessages } from './utils';
import { getTools } from './mcp';
import AppRoutes from './routes';
import './App.css';
import { MessageService } from './utils/MessageService';
import { updateConversationById } from './service/ConversationService';
import { 
  processNextMessage, 
  processWebSocketMessage, 
  handleChunkMessage, 
  handleFinalResultMessage, 
  handleCompleteMessage,
  handleStatusUpdateMessage,
  handleToolResultMessage
} from './service/WebSocketService';
import conversationCache from './utils/ConversationCache';
import { parseConcatenatedJson } from './utils/JsonParser';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { updateMCPDirectory } from './mcp';
import webSocketPool from './service/WebSocketPool';

const { Header, Content } = Layout;

const App = () => {
  // 路由钩子
  const navigate = useNavigate();
  const location = useLocation();
  
  // 使用Modal.useModal创建modal实例
  const [modal, contextHolder] = Modal.useModal();
  
  // 会话状态
  const [conversations, setConversations] = useState([]);
  const [currentConversation, setCurrentConversation] = useState(null);
  
  // 提升抽屉状态
  const [conversationDrawerOpen, setConversationDrawerOpen] = useState(false);
  
  // AI服务状态
  const [selectedService, setSelectedService] = useState('openai');
  
  // 功能状态
  const [functionTools, setFunctionTools] = useState([]);
  const [selectedTool, setSelectedTool] = useState(null);
  const [showConversationList, setShowConversationList] = useState(true);
  // MCP服务器状态
  const [mcpServers, setMcpServers] = useState({});
  const [mcpServersStatus, setMcpServersStatus] = useState({});
  const [selectedServers, setSelectedServers] = useState([]);
  
  // UI状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 自定义右键菜单状态
  const [contextMenu, setContextMenu] = useState({ visible: false, x: 0, y: 0 });
  const contextMenuRef = useRef(null);
  
  // 使用refs存储最新状态，解决闭包问题
  const conversationsRef = useRef([]);
  const currentConversationRef = useRef(null);
  const selectedServiceRef = useRef('openai');
  const selectedModelRef = useRef('');
  const selectedToolRef = useRef(null);
  const selectedServersRef = useRef([]);
  const settingsRef = useRef({});
  
  // 添加WebSocket消息队列
  const messageQueueRef = useRef([]);
  const processingMessageRef = useRef(false);
  
  // 跟踪活跃会话及其最后活动时间
  const activeSessionsRef = useRef({});
  
  // 应用设置
  const [settings, setSettings] = useState({
    openaiKey: '',
    defaultService: 'openai',
    defaultModels: {
      openai: ''
    }
  });
  
  // WebSocket连接
  const [socket, setSocket] = useState(null);
  
  // 模型列表
  const [modelsCache, setModelsCache] = useState({
    openai: []
  });
  
  // 添加选定模型状态
  const [selectedModel, setSelectedModel] = useState('');
  
  // 添加工具结果状态
  const [toolResults, setToolResults] = useState([]);
  const toolResultIdCounter = useRef(1);
  
  // 新增：切换历史记录抽屉可见性的函数
  const handleToggleConversationDrawer = () => {
    setConversationDrawerOpen(prev => !prev);
  };
  
  // 处理视图变更 (由 Sidebar 的 handleMenuClick 触发)
  const handleViewChange = async (newViewKey) => {
    // 在切换页面前，先保存所有缓存的会话
    const stats = conversationCache.getStats();
    if (stats.totalCached > 0 || stats.newConversationsCount > 0) {
      console.log(`[页面切换] 正在保存 ${stats.totalCached} 个缓存会话和 ${stats.newConversationsCount} 个新会话`);
      
      try {
        const savedCount = await conversationCache.saveAll();
        console.log(`[页面切换] 成功保存 ${savedCount} 个会话`);
      } catch (error) {
        console.error('[页面切换] 保存缓存会话时出错:', error);
      }
    }
    
    if (newViewKey === 'home') {
      navigate('/');
    } else if (newViewKey === 'image') {
      navigate('/image');
    } else if (newViewKey === 'chat') {
      if (conversations && conversations.length > 0) {
        const firstConversationSummary = conversations[0];
        // 新增检查：确保第一个会话及其ID有效
        if (firstConversationSummary && firstConversationSummary.id) {
        handleSelectConversation(firstConversationSummary);
      } else {
          // 如果第一个会话无效或ID为空，则创建新会话
          console.warn(
            'Sidebar "chat" click: 第一个会话无效或ID为空，正在创建新会话。'
          );
          handleNewConversation(); 
        }
      } else {
        // 如果没有会话，则创建新会话 (而不是仅仅导航)
        console.log('Sidebar "chat" click: 没有会话，正在创建新会话。 ');
        handleNewConversation(); 
      }
    } else if (newViewKey === 'settings') {
      navigate('/settings');
    } else if (newViewKey === 'workflow') {
      navigate('/workflow');
    }
  };
  
  // 保存会话和设置到本地存储
  useEffect(() => {
    if (conversations.length > 0) {
      localStorage.setItem('conversations', JSON.stringify(conversations));
      
      // 同步当前会话到后端 -- 我们将移除这里的服务器保存逻辑
      // if (currentConversation && conversations.some(c => c.id === currentConversation.id)) {
      //   // 只有当会话已添加到历史记录中时，才保存到服务器
      //   saveConversationToServer(currentConversation);
      // }
    }
  }, [conversations]);
  
  useEffect(() => {
    if (settings) {
      localStorage.setItem('settings', JSON.stringify(settings));
    }
  }, [settings]);
  
  // 保存会话到服务器
  const saveConversationToServer = async (conversation) => {
    try {
      // 检查是否为新会话
      const isNewConversation = conversationCache.isNewConversation(conversation.id);
      
      // 如果是已存在的会话且没有消息，不保存到服务器
      if (!isNewConversation && (!conversation.messages || conversation.messages.length === 0)) {
        console.log('已存在会话没有消息，跳过保存到服务器:', conversation.id);
        return false;
      }
      
      console.log(`[保存到服务器] 会话: ${conversation.id}, 新会话: ${isNewConversation}, 消息数: ${conversation.messages?.length || 0}`);
      
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(conversation),
      });
      
      const result = await response.json();
      const success = result.success === true;
      
      console.log(`[保存到服务器] 会话 ${conversation.id} 保存${success ? '成功' : '失败'}`);
      return success;
    } catch (e) {
      console.error('保存会话到服务器失败:', e);
      return false;
    }
  };
  
  // 从服务器加载会话
  const loadConversationsFromServer = async () => {
    try {
      const response = await fetch('/api/conversations');
      const data = await response.json();
      
      if (data.conversations && data.conversations.length > 0) {
        return data.conversations;
      }
      
      return [];
    } catch (e) {
      console.error('从服务器加载会话失败:', e);
      return [];
    }
  };
  
  // 从服务器加载指定会话
  const loadConversationFromServer = async (conversationId) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}`);
      if (response.status === 404) {
        return null;
      }
      
      const conversation = await response.json();
      
      // 确保会话有有效的messages数组
      if (!conversation.messages) {
        console.warn(`会话 ${conversationId} 缺少messages字段，初始化为空数组`);
        conversation.messages = [];
      } else if (!Array.isArray(conversation.messages)) {
        console.warn(`会话 ${conversationId} 的messages不是数组，初始化为空数组`);
        conversation.messages = [];
      }
      
      if (conversation?.messages?.length > 0) {
        // 检查并记录是否有工具调用历史
        const hasToolCallsHistory = conversation.messages.some(msg => msg.tool_calls_history && msg.tool_calls_history.length > 0);
        console.log(`加载会话 ${conversationId}: ${hasToolCallsHistory ? '包含工具调用历史' : '不包含工具调用历史'}`);
        
        // 处理已保存的消息，确保工具调用历史正确显示在内容中
        conversation.messages = conversation.messages.map(msg => {
          // 只处理助手消息，且有工具调用历史但内容中可能没有工具调用格式
          if (msg.role === 'assistant' && 
              msg.tool_calls_history && 
              msg.tool_calls_history.length > 0 && 
              (!msg.content.includes('**Tool Call:**') && !msg.content.includes('**Tool Result:**'))) {
            
            console.log('转换工具调用历史到消息内容');
            
            // 创建一个包含工具调用和结果的内容字符串
            let toolContent = '';
            
            // 遍历工具调用历史，按顺序构建内容
            msg.tool_calls_history.forEach((toolCall, index) => {
              if (index > 0 && (index % 2 === 0)) {
                toolContent += '\n\n'; // 每对工具调用/结果之间添加空行
              }
              
              if (toolCall.type === 'function_call' || toolCall.type === 'xml_tool_call') {
                const serverInfo = toolCall.server ? ` (Server: ${toolCall.server})` : '';
                toolContent += `\n\n**Tool Call:**${serverInfo} ${toolCall.name}\n\`\`\`json\n${JSON.stringify(toolCall.arguments, null, 2)}\n\`\`\`\n\n`;
              } 
              else if (toolCall.type === 'function_result' || toolCall.type === 'xml_tool_result') {
                toolContent += `**Tool Result:**\n\`\`\`json\n${JSON.stringify(toolCall.result, null, 2)}\n\`\`\`\n\n`;
              }
              else if (toolCall.type === 'function_error' || toolCall.type === 'xml_tool_error') {
                toolContent += `**Tool Call Error:**\n\`\`\`json\n{"error": "${toolCall.error}"}\n\`\`\`\n\n`;
              }
            });
            
            // 将工具内容附加到原始内容后面
            return {
              ...msg,
              content: msg.content + toolContent
            };
          }
          return msg;
        });
        
        if (hasToolCallsHistory) {
          const msgWithHistory = conversation.messages.find(msg => msg.tool_calls_history && msg.tool_calls_history.length > 0);
          console.log(`工具调用历史示例:`, msgWithHistory.tool_calls_history);
        }
      }
      
      // 如果从服务器获取的会话状态中包含loading，则更新全局loading状态
      if (typeof conversation.loading === 'boolean') {
        setLoading(conversation.loading);
      }
      
      return conversation;
    } catch (e) {
      console.error(`从服务器加载会话 ${conversationId} 失败:`, e);
      return null;
    }
  };
  
  // 删除服务器上的会话
  const deleteConversationFromServer = async (conversationId) => {
    try {
      await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
      });
    } catch (e) {
      console.error(`删除服务器上的会话 ${conversationId} 失败:`, e);
    }
  };
  
  // 清空当前会话历史
  const handleClearConversationHistory = async () => {
    if (!currentConversation) return;
    
    // 创建一个新的会话对象，保留id和元数据，但清空消息
    const clearedConversation = {
      ...currentConversation,
      messages: [],
      updatedAt: Date.now()
    };
    
    // 更新当前会话
    setCurrentConversation(clearedConversation);
    
    // 更新会话列表
    const updatedConversations = conversations.map(c =>
      c.id === clearedConversation.id ? clearedConversation : c
    );
    setConversations(updatedConversations);
    
    // 同步到服务器
    await saveConversationToServer(clearedConversation);
    
    // 确保URL保持更新
    navigate(`/chat/${clearedConversation.id}`);
    
    // 显示成功消息
    MessageService.success('会话历史已清空');
  };
  
  // 删除所有会话
  const handleDeleteAllConversations = async () => {
    try {
      // 向服务器发送请求
      const response = await fetch('/api/conversations', {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }
      
      // 清空本地会话列表
      setConversations([]);
      
      // 创建新的会话作为当前会话
      const newConversation = createNewConversation('新会话', selectedService);
      setCurrentConversation(newConversation);
      setConversations([newConversation]);
      
      // 更新URL为新创建的会话ID
      navigate(`/chat/${newConversation.id}`);
      
      // 显示成功消息
      MessageService.success('所有会话已删除');
    } catch (error) {
      console.error('删除所有会话失败:', error);
      MessageService.error('删除所有会话失败: ' + error.message);
    }
  };
  
  // 重命名会话
  const handleRenameConversation = (id, newTitle) => {
    const updatedConversations = conversations.map(c =>
      c.id === id ? { ...c, title: newTitle } : c
    );
    
    setConversations(updatedConversations);
    
    if (currentConversation && currentConversation.id === id) {
      setCurrentConversation({ ...currentConversation, title: newTitle });
    }
  };
  
  // 初始化会话列表
  const initConversations = async () => {
    // 从URL中获取会话ID，支持两种格式：
    // 1. 查询参数格式: /chat?conversation_id=xxx
    // 2. 路径参数格式: /chat/xxx
    let conversationIdFromUrl;
    
    // 检查是否使用路径参数格式
    const pathMatch = location.pathname.match(/\/chat\/([^/]+)/);
    if (pathMatch && pathMatch[1]) {
      conversationIdFromUrl = pathMatch[1];
      console.log(`从URL路径中获取到会话ID: ${conversationIdFromUrl}`);
    } else {
      // 回退到查询参数格式
      const urlParams = new URLSearchParams(window.location.search);
      conversationIdFromUrl = urlParams.get('conversation_id');
      if (conversationIdFromUrl) {
        console.log(`从URL查询参数中获取到会话ID: ${conversationIdFromUrl}`);
      }
    }
    
    if (conversationIdFromUrl) {
      // 从服务器加载指定对话
      const conversation = await loadConversationFromServer(conversationIdFromUrl);
      if (conversation) {
        // 确保会话对象有有效的messages数组
        if (!conversation.messages || !Array.isArray(conversation.messages)) {
          console.warn('从服务器加载的会话缺少有效的messages数组，初始化为空数组');
          conversation.messages = [];
        }
        
        console.log(`成功加载URL指定的对话: ${conversationIdFromUrl}`);
        setCurrentConversation(conversation);
        
        // 加载所有会话
        const serverConversations = await loadConversationsFromServer();
        if (serverConversations.length > 0) {
          setConversations(serverConversations);
        } else {
          // 如果服务器没有会话列表，则创建一个包含当前会话的列表
          setConversations([{
            id: conversation.id,
            title: conversation.title,
            updatedAt: conversation.updatedAt
          }]);
        }
        
        // 使用路径参数格式导航到聊天页面
        navigate(`/chat/${conversation.id}`);
        return;
      } else {
        console.error(`无法加载URL指定的对话: ${conversationIdFromUrl}`);
        
        // URL中有conversation_id但找不到对应会话，创建一个临时会话
        const newConversation = createNewConversation(
          '新会话', 
          selectedService,
          selectedModel || settings?.defaultModels?.[selectedService] || ''
        );
        
        // 设置为当前会话，但不添加到历史记录
        setCurrentConversation(newConversation);
        currentConversationRef.current = newConversation;
        
        // 使用路径参数格式导航到新会话
        navigate(`/chat/${newConversation.id}`);
        
        // 正常加载会话列表
        const serverConversations = await loadConversationsFromServer();
        if (serverConversations.length > 0) {
          setConversations(serverConversations);
          return;
        }
      }
    }
    
    // 如果URL中没有conversation_id或加载失败，则正常初始化会话
    // 优先从服务器加载会话
    const serverConversations = await loadConversationsFromServer();
    
    if (serverConversations.length > 0) {
      setConversations(serverConversations);
      
      // 加载第一个会话
      const firstConversation = await loadConversationFromServer(serverConversations[0].id);
      if (firstConversation) {
        // 确保会话对象有有效的messages数组
        if (!firstConversation.messages || !Array.isArray(firstConversation.messages)) {
          console.warn(`会话 ${firstConversation.id} 缺少有效的messages数组，初始化为空数组`);
          firstConversation.messages = [];
        }
        setCurrentConversation(firstConversation);
        // 如果从服务器获取的会话状态中包含loading，则更新全局loading状态
        if (typeof firstConversation.loading === 'boolean') {
          setLoading(firstConversation.loading);
        }
      }
      
      return;
    }
    
    // 如果服务器没有会话，从本地存储加载
    const savedConversations = localStorage.getItem('conversations');
    if (savedConversations) {
      try {
        const parsedConversations = JSON.parse(savedConversations);
        
        // 确保每个会话都有有效的messages数组
        const validConversations = parsedConversations.map(conv => {
          if (!conv.messages || !Array.isArray(conv.messages)) {
            console.warn(`会话 ${conv.id} 缺少有效的messages数组，初始化为空数组`);
            return {
              ...conv,
              messages: []
            };
          }
          return conv;
        });
        
        setConversations(validConversations);
        
        // 如果有会话，加载最后一个
        if (validConversations.length > 0) {
          const firstConversation = validConversations[0];
          // 确保会话对象有有效的messages数组
          if (!firstConversation.messages || !Array.isArray(firstConversation.messages)) {
            firstConversation.messages = [];
          }
          setCurrentConversation(firstConversation);
          
          // 同步到服务器
          for (const conv of validConversations) {
            await saveConversationToServer(conv);
          }
        }
      } catch (e) {
        console.error('加载会话失败:', e);
      }
    }
  };
  
  // 同步状态到refs
  useEffect(() => {
    conversationsRef.current = conversations;
  }, [conversations]);
  
  useEffect(() => {
    currentConversationRef.current = currentConversation;
  }, [currentConversation]);
  
  // 新增：当 currentConversation 的 id 为 null 时，初始化新会话
  useEffect(() => {
    if (currentConversation && currentConversation.id === null) {
      console.warn(
        '当前会话的 ID 为 null。正在初始化一个新的会话以纠正此状态。'
      );
      // 调用 handleNewConversation() 会创建一个新的、空的会话，
      // 并将其设为当前会话，同时更新路由。
      // handleNewConversation 内部会确保新会话有一个有效的ID。
      handleNewConversation();
    }
    // 注意：此处的依赖项是 [currentConversation]。
    // 如果 handleNewConversation 未被 useCallback 完整优化，
    // 理论上如果它同步修改 currentConversation 可能导致循环，
    // 但由于 handleNewConversation 应该总是生成带有效ID的新会话，
    // 下一次此 effect 运行时 currentConversation.id 不会再是 null，从而避免循环。
    // 为更严格的 linting 或未来重构，handleNewConversation 可能需要 useCallback。
  }, [currentConversation]);
  
  useEffect(() => {
    selectedServiceRef.current = selectedService;
  }, [selectedService]);
  
  useEffect(() => {
    selectedModelRef.current = selectedModel;
  }, [selectedModel]);
  
  useEffect(() => {
    selectedToolRef.current = selectedTool;
  }, [selectedTool]);
  
  useEffect(() => {
    selectedServersRef.current = selectedServers;
  }, [selectedServers]);
  
  useEffect(() => {
    settingsRef.current = settings;
  }, [settings]);
  
  // 新增：当会话交互结束（loading变为false）时保存当前会话到服务器
  useEffect(() => {
    const conversation = currentConversationRef.current;
    // 确保会话存在，有ID，并且当前不是加载状态
    if (conversation && conversation.id && typeof conversation.loading === 'boolean' && !conversation.loading) {
      // 确保会话中有消息
      if (conversation.messages && conversation.messages.length > 0) {
        console.log(`会话 ${conversation.id} 交互结束，保存到服务器。`);
        saveConversationToServer(conversation);
      }
    }
    // 依赖项包括会话的loading状态和ID，以及消息内容的变化，以确保在这些关键属性更新后触发保存
  }, [currentConversationRef.current?.loading, currentConversationRef.current?.id, currentConversationRef.current?.messages]);
  
  // 加载设置
  const loadSettings = async () => {
    try {
      // 首先尝试从后端加载设置
      const response = await fetch('/api/settings');
      
      if (response.ok) {
        const serverSettings = await response.json();
        setSettings(serverSettings);
        setSelectedService(serverSettings.defaultService || 'openai');
        console.log('从服务器加载设置成功');
        return;
      }
      
      // 如果从后端加载失败，从本地存储加载
      console.log('从后端加载设置失败，尝试从本地存储加载');
    } catch (error) {
      console.error('从后端加载设置失败:', error);
    }
    
    // 从本地存储加载设置
    const savedSettings = localStorage.getItem('settings');
    
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
        setSelectedService(parsedSettings.defaultService || 'openai');
        console.log('从本地存储加载设置成功');
      } catch (e) {
        console.error('解析本地设置失败:', e);
      }
    }
  };
  
  // 获取所有服务的模型列表
  const fetchAllModels = async () => {
    // 先从本地存储中尝试加载模型列表
    const savedModels = localStorage.getItem('modelsCache');
    console.log('savedModels',savedModels);
    if (savedModels) {
      try {
        const parsedModels = JSON.parse(savedModels);
        setModelsCache(parsedModels);
        console.log('从本地存储加载模型列表成功');
        // 如果本地有缓存的模型列表，不再请求API
        return;
      } catch (e) {
        console.error('解析本地存储的模型列表失败:', e);
        // 解析失败，继续请求API
      }
    }
    
    try {
      const response = await fetch('/api/models');
      
      if (!response.ok) {
        // 如果API返回错误，使用默认模型列表
        const defaultModels = {
          openai: []
        };
        setModelsCache(defaultModels);
        // 将默认模型保存到本地存储
        localStorage.setItem('modelsCache', JSON.stringify(defaultModels));
        return;
      }
      
      const data = await response.json();
      console.log('data',data);
      if (data.models) {
        setModelsCache(data.models);
        // 将从API获取的模型列表保存到本地存储
        localStorage.setItem('modelsCache', JSON.stringify(data.models));
      }
    } catch (e) {
      console.error('获取模型列表失败:', e);
      // 出错时使用默认值
      const defaultModels = {
        openai: [] 
      };
      setModelsCache(defaultModels);
      // 将默认模型保存到本地存储
      localStorage.setItem('modelsCache', JSON.stringify(defaultModels));
    }
  };
  
  // 强制刷新模型列表
  const refreshAllModels = async () => {
    try {
      // 调用API强制刷新模型列表
      const response = await fetch('/api/models?isupdate=update');
      
      if (!response.ok) {
        console.error('强制刷新模型列表失败');
        MessageService.error('强制刷新模型列表失败');
        return false;
      }
      
      const data = await response.json();
      
      if (data.models) {
        setModelsCache(data.models);
        // 将从API获取的模型列表保存到本地存储
        localStorage.setItem('modelsCache', JSON.stringify(data.models));
        console.log('强制刷新模型列表成功');
        MessageService.success('强制刷新模型列表成功');
        return true;
      }
    } catch (e) {
      console.error('强制刷新模型列表失败:', e);
      MessageService.error('强制刷新模型列表失败: ' + e.message);
      return false;
    }
    return false;
  };
  
  // 处理WebSocket消息
  const handleParsedWebSocketMessage = (messageObject, sessionId) => {
    // 添加会话ID验证
    if (sessionId && messageObject.conversation_id && messageObject.conversation_id !== sessionId) {
      console.warn(`[会话隔离] 消息会话ID不匹配: 期望 ${sessionId}, 收到 ${messageObject.conversation_id}`);
      return;
    }

    // Add the parsed message object to the queue
    messageQueueRef.current.push({
      ...messageObject,
      receivedAt: Date.now(),
      sessionId: sessionId // 添加会话ID到消息对象
    });

    console.log(`[消息队列] 添加已解析消息到队列，类型: ${messageObject.type}，会话ID: ${messageObject.conversation_id || sessionId || '未指定'}，队列长度: ${messageQueueRef.current.length}`);

    // Trigger processing of the queue
    const deps = {
      messageQueueRef,
      processingMessageRef,
      processWebSocketMessage,
      conversationDeps: {
        setCurrentConversation,
        setConversations,
        conversationsRef,
        currentConversationRef,
        activeSessionsRef,
        saveConversationToServer,
        loadConversationFromServer,
        updateBackendConversationLoadingStatus
      },
      currentConversationRef,
      selectedServiceRef,
      selectedModelRef,
      setLoading,
      MessageService,
      onToolResultReceived: handleToolResult
    };
    processNextMessage(deps);
  };
  
  // 获取可用工具
  const fetchTools = async () => {
    try {
      const tools = await getTools();
      setFunctionTools(tools);
    } catch (e) {
      console.error('获取工具列表失败:', e);
      setError('获取工具列表失败');
    }
  };
  
  // 刷新工具列表
  const refreshTools = async () => {
    setLoading(true);
    await fetchTools();
    setLoading(false);
    MessageService.success('工具列表已刷新');
  };
  
  // 新增会话处理函数
  const handleNewConversation = async (initialMessageContent = null, initialModel = null, options = {}) => {
    console.log('[handleNewConversation] 接收到的options:', options);
    
    let conversationTitle = '新会话';
    let userMessageContent = initialMessageContent; // This will be the actual message content for the conversation

    if (initialMessageContent) {
      if (Array.isArray(initialMessageContent)) {
        // Find the first text part for the title
        const firstTextPart = initialMessageContent.find(item => item.type === 'text');
        if (firstTextPart && firstTextPart.text) {
          conversationTitle = firstTextPart.text.substring(0, 30);
        } else if (initialMessageContent.length > 0) {
          // If no text, but has other parts, title might be less descriptive
          conversationTitle = '新会话 (含附件)';
        }
      } else if (typeof initialMessageContent === 'string') {
        conversationTitle = initialMessageContent.substring(0, 30);
      }
    }

    // 如果options中没有提供team_id或role_id，尝试从localStorage加载
    if (!options.team_id && !options.role_id) {
      try {
        const savedTeamId = localStorage.getItem('jimu_selected_team_id');
        const savedRoleId = localStorage.getItem('jimu_selected_role_id');
        
        if (savedTeamId) {
          options.team_id = savedTeamId;
          console.log('[handleNewConversation] 从localStorage恢复team_id:', savedTeamId);
        } else if (savedRoleId) {
          options.role_id = savedRoleId;
          console.log('[handleNewConversation] 从localStorage恢复role_id:', savedRoleId);
        }
      } catch (error) {
        console.error('从localStorage恢复ID失败:', error);
      }
    }

    const convOptions = {
      ...options,
      service: selectedServiceRef.current,
      model: initialModel || selectedModelRef.current || settingsRef.current?.defaultModels?.[selectedServiceRef.current] || '',
      loading: !!initialMessageContent, // 如果有初始消息，则将会话初始状态设置为加载中
    };
    
    console.log('[handleNewConversation] 构建的convOptions:', convOptions);

    const baseConversation = createNewConversation(
        conversationTitle, // Pass the derived title
        convOptions.service,
        convOptions.model,
        convOptions // Pass relevant options
    );
    
    // 处理用户消息内容，确保兼容后端格式
    let processedUserContent = userMessageContent;
    if (userMessageContent && Array.isArray(userMessageContent)) {
      // 从数组中提取文本内容
      const textParts = userMessageContent
        .filter(item => item.type === 'text')
        .map(item => item.text || '');
      
      // 检测是否包含多模态内容（如图片、文件）
      const hasMultimodalContent = userMessageContent.some(item => 
        item.type === 'image_url' || item.type === 'file'
      );
      
      if (textParts.length > 0) {
        // 如果有文本部分，使用它们
        console.log("[handleNewConversation] 消息包含文本部分，合并为字符串");
        processedUserContent = textParts.join(' ');
      } else if (hasMultimodalContent) {
        // 如果只有非文本内容（如图片、文件），添加一个默认文本提示
        console.log("[handleNewConversation] 消息只包含非文本内容，添加默认提示");
        processedUserContent = "[用户发送了多媒体内容]";
      }
      
      // 如果有多模态内容且选项支持，保留原始结构
      if (hasMultimodalContent && options.supportMultimodal) {
        console.log("[handleNewConversation] 保留多模态内容原始结构");
        processedUserContent = userMessageContent;
      }
      
      console.log("[handleNewConversation] 处理后的用户内容:", {
        original: userMessageContent.length + " items",
        processed: typeof processedUserContent === 'string' ? processedUserContent.substring(0, 50) + '...' : 'multimodal array',
        hasMultimodal: hasMultimodalContent
      });
    }

    const newConversation = {
      ...baseConversation,
      messages: userMessageContent ? [
        createSystemMessage(),
        {
          id: `msg-user-${Date.now()}`,
          role: 'user',
          content: processedUserContent, // 使用处理后的内容
          timestamp: new Date().toISOString(),
        },
        {
          id: `msg-assistant-${Date.now()}`,
          role: 'assistant',
          content: '...',
          timestamp: new Date().toISOString(),
          service: convOptions.service,
          model: convOptions.model,
          isLoading: true, // 添加加载中的AI消息
        }
      ] : [createSystemMessage()],
      model: initialModel || selectedModelRef.current || settingsRef.current?.defaultModels?.[selectedServiceRef.current],
      is_web_search_enabled: options.is_web_search_enabled || false,
      role_id: options.role_id || null, // 保存角色ID
      team_id: options.team_id || null, // 保存团队ID
      systemPrompt: options.systemPrompt || null, // 保存系统提示
      maxTokens: options.maxTokens || null // 保存maxTokens
    };
    
    console.log('[handleNewConversation] 创建的新会话:', {
      id: newConversation.id,
      role_id: newConversation.role_id,
      team_id: newConversation.team_id,
      title: newConversation.title
    });
    
    // 标记为新会话
    conversationCache.markAsNewConversation(newConversation.id);
    
    setCurrentConversation(newConversation);
    currentConversationRef.current = newConversation; // 更新ref
    
    // 立即保存新会话到服务器
    try {
      console.log(`[新会话] 首次保存包含初始消息的会话: ${newConversation.id}`);
      const saveSuccess = await conversationCache.saveNewConversation(newConversation.id, newConversation);
      if (saveSuccess) {
        // 保存成功后添加到左侧列表
        setConversations(prev => [newConversation, ...prev]);
        conversationsRef.current = [newConversation, ...conversationsRef.current];
      } else {
        console.error('保存新会话失败，但继续执行');
      }
    } catch (error) {
      console.error('保存新会话时出错:', error);
    }
    
    // 不立即将会话添加到左侧列表，等待保存成功后再添加
    // 导航到新会话的URL
    navigate(`/chat/${newConversation.id}`);
    //console.log('initialMessageContent',initialMessageContent);
    // 只有在提供了初始消息内容时才触发API调用
    if (initialMessageContent) {
      const apiCallOptions = { isNewConversation: true, ...convOptions };
      console.log('[handleNewConversation] 调用triggerApiCall的options:', apiCallOptions);
      triggerApiCall(newConversation, newConversation.model, apiCallOptions);
    }
  };
  
  // 选择会话
  const handleSelectConversation = async (conversationSummary) => {
    // 新增检查：如果传入的 conversationSummary 或其 id 无效，则直接创建新会话
    if (!conversationSummary || !conversationSummary.id) {
      console.warn(
        'handleSelectConversation: 传入的会话摘要无效或ID为空，正在创建新会话。'
      );
      handleNewConversation();
      return; // 提前返回，不继续执行后续逻辑
    }

    // 记录切换会话的操作
    console.log(`用户切换到会话: ${conversationSummary.id}`);
    
    // 优先检查缓存中是否有更新的版本
    let conversation = null;
    const cachedConversation = conversationCache.getConversation(conversationSummary.id);
    
    if (cachedConversation) {
      console.log(`从缓存加载会话: ${conversationSummary.id}`);
      conversation = cachedConversation;
    } else {
      // 如果缓存中没有，从服务器加载
      console.log(`从服务器加载会话: ${conversationSummary.id}`);
      conversation = await loadConversationFromServer(conversationSummary.id);
    }
    
    if (conversation) {
      setCurrentConversation(conversation);
      // 如果从服务器获取的会话状态中包含loading，则更新全局loading状态
      if (typeof conversation.loading === 'boolean') {
        setLoading(conversation.loading);
      }
      // Set view to 'history' when a conversation is selected
      handleViewChange('history'); 
      // 更新URL，使用路径参数格式
      navigate(`/chat/${conversation.id}`);
      console.log(`成功加载会话: ${conversationSummary.id}`);
    } else {
      // 如果无法从服务器加载，则尝试从本地会话列表中找到
      const localConversation = conversations.find(c => c.id === conversationSummary.id);
      if (localConversation) {
        setCurrentConversation(localConversation);
        // Set view to 'history' here as well
        handleViewChange('history');
        // 更新URL，使用路径参数格式
        navigate(`/chat/${localConversation.id}`);
        console.log(`从本地加载会话: ${conversationSummary.id}`);
      } else {
        console.error(`无法加载会话: ${conversationSummary.id}，该会话可能已被删除`);
      }
    }
  };
  
  // 删除会话
  const handleDeleteConversation = async (id) => {
    try {
      // 查找要删除的会话，验证其数据完整性
      const conversationToDelete = conversations.find(conv => conv.id === id);
      
      if (!conversationToDelete) {
        console.error(`要删除的会话 ${id} 不存在`);
        MessageService.error('删除失败：找不到指定会话');
        return;
      }
      
      // 检查会话标题，如果为空或不完整，使用默认值替代
      if (!conversationToDelete.title || conversationToDelete.title.trim() === '') {
        conversationToDelete.title = '未命名会话';
      }
      
      // 检查会话消息，移除可能包含无效JSON的消息
      if (conversationToDelete.messages && Array.isArray(conversationToDelete.messages)) {
        conversationToDelete.messages = conversationToDelete.messages.filter(msg => {
          // 检查工具调用结果，确保是有效的JSON
          if (msg.tool_calls_history) {
            try {
              // 验证工具调用历史是否为有效的JSON
              JSON.stringify(msg.tool_calls_history);
              return true;
            } catch (e) {
              console.error('发现无效的工具调用历史，已过滤:', e);
              return false;
            }
          }
          return true;
        });
      }
      
      // 更新本地会话列表
      const updatedConversations = conversations.filter(
        conversation => conversation.id !== id
      );
      
      setConversations(updatedConversations);
      
      // 如果删除的是当前会话，选择第一个会话或清空当前会话
      if (currentConversation && currentConversation.id === id) {
        const newCurrentConversation = updatedConversations[0] || null;
        setCurrentConversation(newCurrentConversation);
        
        // 更新URL
        if (newCurrentConversation) {
          // 如果有新的当前会话，更新URL为新会话的ID
          navigate(`/chat/${newCurrentConversation.id}`);
        } else {
          // 如果没有会话了，移除URL中的conversation_id参数
          navigate('/chat');
        }
      }
      
      // 从服务器删除会话
      await deleteConversationFromServer(id);
    } catch (error) {
      console.error('删除会话时出错:', error);
      MessageService.error('删除会话失败，请重试');
    }
  };
  
  
  
  // 更新设置
  const handleUpdateSettings = async (newSettings) => {
    // 更新本地状态
    setSettings(newSettings);
    setSelectedService(newSettings.defaultService);
    
    // 保存到本地存储（已通过useEffect自动完成）
    
    // 保存到后端服务器
    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        MessageService.success('设置已保存到服务器');
      } else {
        MessageService.warning('设置已保存到本地，但服务器保存失败');
      }
    } catch (error) {
      console.error('保存设置到服务器失败:', error);
      MessageService.warning('设置已保存到本地，但服务器保存失败');
    }
  };
  
  // 更改AI服务
  const handleServiceChange = (service) => {
    setSelectedService(service);
  };
  
  // 选择函数工具
  const handleSelectFunctionTool = (toolName) => {
    if (!toolName) {
      setSelectedTool(null);
      return;
    }
    
    const tool = functionTools.find(t => t.name === toolName);
    setSelectedTool(tool);
  };
  
  // 选择MCP服务器
  const handleSelectServers = (serverNames) => {
    setSelectedServers(serverNames);
  };
  
  // 更新会话模型
  const handleUpdateConversationModel = (model, additionalProps = {}) => {
    // 更新currentConversation
    if (currentConversation) {
      // 创建更新后的会话对象
      const updatedConversation = {
        ...currentConversation,
        model: model || currentConversation.model,
        updatedAt: Date.now(),
        ...additionalProps // 添加额外属性，如systemPrompt和maxTokens
      };
      
      setCurrentConversation(updatedConversation);
      
      // 更新会话列表
      const updatedConversations = conversations.map(c => 
        c.id === updatedConversation.id ? updatedConversation : c
      );
      setConversations(updatedConversations);
      
      // 重要：手动同步conversationsRef，确保立即更新，不等待useEffect
      conversationsRef.current = updatedConversations;
      // 同时也更新currentConversationRef
      currentConversationRef.current = updatedConversation;
      
      // 更新UI显示
      if (model && model !== selectedModel) {
        setSelectedModel(model);
      }
      
      // 处理网络搜索选项
      if (additionalProps.is_web_search_enabled !== undefined) {
        console.log(`更新会话 ${updatedConversation.id} 的网络搜索状态为: ${additionalProps.is_web_search_enabled}`);
      }
      
      // 处理系统提示词更新
      if (additionalProps.systemPrompt !== undefined) {
        console.log(`更新会话 ${updatedConversation.id} 的系统提示词`);
      }
      
      // 处理Token长度更新
      if (additionalProps.maxTokens !== undefined) {
        console.log(`更新会话 ${updatedConversation.id} 的Token长度为: ${additionalProps.maxTokens}`);
      }
      
      // 保存到服务器
      saveConversationToServer(updatedConversation).catch(e => 
        console.error('保存更新后的会话模型失败:', e)
      );
    }
  };
  
  // 处理发送消息 (简化：只处理已存在会话的后续消息)
  const handleSendMessage = async (text, model, options = {}) => {
    // 1. 检查当前会话是否存在
    if (!currentConversationRef.current) {
      console.error("尝试在没有当前会话的情况下发送消息");
      setError("无法发送消息：没有活动的会话。");
      return; 
    }

    // 直接使用 currentConversationRef.current 来获取和更新会话
    let conversation = currentConversationRef.current;
    
    // 确保会话对象具有有效的messages数组
    if (!conversation.messages || !Array.isArray(conversation.messages)) {
      console.warn('会话对象缺少有效的messages数组，初始化为空数组');
      conversation = {
        ...conversation,
        messages: []
      };
    }
    
    // 检查是否为首次发送消息（空会话的第一条消息）
    const isFirstMessage = conversation.messages.length === 0;
    const isNewConversation = conversationCache.isNewConversation(conversation.id);
    
    console.log(`[发送消息] 会话ID: ${conversation.id}, 首次消息: ${isFirstMessage}, 新会话: ${isNewConversation}`);
    
    // 处理消息内容，确保兼容后端格式
    // 如果消息内容是数组（包含图片等多模态内容），确保后端能正确处理
    let processedContent = text;
    let hasMultimodalContent = false;
    
    // 检查是否为数组格式的内容
    if (Array.isArray(text)) {
      // 从数组中提取文本内容
      const textParts = text
        .filter(item => item.type === 'text')
        .map(item => item.text || '');
      
      // 检测是否包含多模态内容（如图片）
      hasMultimodalContent = text.some(item => item.type === 'image_url' || item.type === 'file');
      
      if (textParts.length > 0) {
        // 如果有文本部分，使用它们
        console.log("消息包含文本部分，合并为字符串");
        processedContent = textParts.join(' ');
      } else if (hasMultimodalContent) {
        // 如果只有非文本内容（如图片），添加一个默认文本提示
        console.log("消息只包含非文本内容，添加默认提示");
        processedContent = "[用户发送了多媒体内容]";
      }
      
      // 如果有多模态内容且后端支持，保留原始结构
      if (hasMultimodalContent && options.supportMultimodal) {
        console.log("保留多模态内容原始结构");
        processedContent = text;
      }
    }
    
    // 添加用户消息到会话
    const userMessage = {
      role: 'user',
      content: processedContent,
      timestamp: Date.now()
    };
    
    const loadingAssistantMessage = {
      role: 'assistant',
      content: '...',
      timestamp: Date.now(),
      service: selectedServiceRef.current, // 使用 ref
      model: model || selectedModelRef.current || settingsRef.current?.defaultModels?.[selectedServiceRef.current] || '',  // 使用 ref
      isLoading: true
    };
    
    const is_web_search_enabled = options.is_web_search_enabled || false;
    
    // 4. 更新当前会话状态
    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, userMessage, loadingAssistantMessage],
      model: model || selectedModelRef.current || conversation.model || settingsRef.current?.defaultModels?.[selectedServiceRef.current] || '',
      updatedAt: Date.now(),
      is_web_search_enabled: is_web_search_enabled,
      loading: true // <--- 设置会话特定 loading 为 true
    };
    
    setCurrentConversation(updatedConversation);
    currentConversationRef.current = updatedConversation; // 更新 ref

    // 智能保存策略：确保会话在发送API请求前已保存
    try {
      if (isNewConversation || isFirstMessage) {
        // 新会话或首次消息：立即保存到服务器
        console.log(`[保存策略] 新会话或首次消息，立即保存: ${updatedConversation.id}`);
        const saveSuccess = await conversationCache.smartSave(
          updatedConversation.id, 
          updatedConversation, 
          { forceImmediate: true, isFirstMessage: isFirstMessage }
        );
        
        if (!saveSuccess) {
          console.error('保存会话失败，但继续执行API调用');
        }
      } else {
        // 后续消息：使用缓存策略
        console.log(`[保存策略] 后续消息，使用缓存策略: ${updatedConversation.id}`);
        conversationCache.cacheConversation(updatedConversation.id, updatedConversation);
      }
    } catch (error) {
      console.error('保存会话时出错:', error);
      // 继续执行，不因保存失败而阻止消息发送
    }

    // 更新会话列表
    const isExistingConversation = conversationsRef.current.some(c => c.id === updatedConversation.id);
    let updatedConversationsList;
    if (isExistingConversation) {
      updatedConversationsList = conversationsRef.current.map(c => 
        c.id === updatedConversation.id ? updatedConversation : c
      );
    } else {
      updatedConversationsList = [updatedConversation, ...conversationsRef.current];
      console.log('将新会话添加到历史记录:', updatedConversation.id);
    }
    setConversations(updatedConversationsList);
    conversationsRef.current = updatedConversationsList; // 更新 ref
    
    // 触发 API 调用
    await triggerApiCall(updatedConversation, model || selectedModelRef.current, { 
      is_web_search_enabled,
      is_team_test: options.is_team_test || false,
    });
  };
  
  // 新增：触发 API 调用的辅助函数 (提取公共逻辑)
  const triggerApiCall = async (conversationToUpdate, modelToUse, options = {}) => {
    // 在 triggerApiCall 开始时，确保全局 setLoading(true)
    setLoading(true); 
    setError(null);

    try {
      // 检查会话是否已经完成了任务分解
      if (conversationToUpdate.taskDecompositionCompleted) {
        console.log('检测到会话已完成任务分解，不再发送新请求');
        setLoading(false);
        
        // 移除加载中的消息
        setCurrentConversation(prev => {
          if (prev && prev.id === conversationToUpdate.id) {
            return { ...prev, messages: prev.messages.filter(m => !m.isLoading) };
          }
          return prev;
        });
        
        // 显示提示消息
        MessageService.info('任务分解已完成，请创建新会话或使用其他功能');
        return;
      }
      
      // 准备发送到API的消息 (过滤掉加载消息)
      // 使用会话中的自定义systemPrompt（如果有）
      const systemMessage = createSystemMessage(
        selectedServiceRef.current, 
        conversationToUpdate.systemPrompt
      );
      
      const messagesToSend = [
        systemMessage,
        ...conversationToUpdate.messages.filter(m => !m.isLoading) 
      ];
      
      // 准备函数定义
      let functions = null;
      if (selectedToolRef.current) {
        const tool = functionTools.find(t => t.name === selectedToolRef.current);
        if (tool) {
          functions = [tool];
        }
      }
      
      // 使用指定的模型或默认模型
      const useModel = modelToUse;
      
      // 为Gemini 2.5 Flash Preview模型设置思考预算
      let thinking_budget = null;
      if (useModel && useModel.includes('gemini-2.5-flash-preview')) {
        // 根据是否为Agent模式设置不同的思考预算
        if (currentConversationRef.current?.isAgentMode) {
          thinking_budget = 10240; // Agent模式使用更大的思考预算
        } else {
          thinking_budget = 1024; // 非Agent模式使用默认思考预算
        }
      }
      
      // 设置Agent检查器 - 使用当前会话的Agent模式状态
      if (currentConversationRef.current?.isAgentMode) {
        setLoading(true);
        setError(null);
        
        // 确保有足够的token来处理Agent请求
        //const maxTokensForAgent = Math.max(40000, conversationToUpdate.maxTokens || 0);
        //conversationToUpdate.maxTokens = maxTokensForAgent;
      }
      
      // 获取网络搜索状态
      const is_web_search_enabled = options.is_web_search_enabled || conversationToUpdate.is_web_search_enabled || false;
      
      const conversation_id = conversationToUpdate.id;
      if (!conversation_id) {
        console.error('发送消息时缺少有效的会话ID');
        throw new Error('无效的会话ID');
      }
      
      // 记录活跃会话 - 添加会话ID到活跃会话列表，记录消息发送时间和会话信息
      activeSessionsRef.current[conversation_id] = {
        lastActive: Date.now(),
        title: conversationToUpdate.title,
        modelUsed: useModel,
        hasUserMessages: conversationToUpdate.messages.some(m => m.role === 'user'),
        messageCount: conversationToUpdate.messages.length
      };
      
      console.log(`[会话跟踪] 已将会话 ${conversation_id} 添加到活跃会话列表, 当前活跃会话数: ${Object.keys(activeSessionsRef.current).length}`);
      
      // 发送请求 - 使用WebSocket连接池
      console.log(`发送请求到会话: ${conversation_id}`);
      
      const messageData = {
        messages: messagesToSend,
        service: selectedServiceRef.current,
        model: useModel,
        functions,
        selected_servers: selectedServersRef.current,
        conversation_id, 
        is_agent: currentConversationRef.current?.isAgentMode || false,
        thinking_budget: thinking_budget,
        is_web_search_enabled: is_web_search_enabled,
        max_tokens: conversationToUpdate.maxTokens || '',
        role_id: options.is_team_test ? null : (conversationToUpdate.role_id || options.role_id || null), // 强制role_id为null
        team_id: options.team_id || conversationToUpdate.team_id || null // 添加team_id字段
      };

      console.log('[triggerApiCall] 发送到后端的完整数据:', {
        conversation_id,
        role_id: messageData.role_id,
        team_id: messageData.team_id,
        is_web_search_enabled: messageData.is_web_search_enabled
      });
      
      // 使用连接池发送消息
      const success = await webSocketPool.sendToSession(conversation_id, messageData);
      
      if (!success) {
        console.error("WebSocket连接池发送消息失败");
        setError("无法连接到服务器，请稍后重试。");
        setLoading(false);
        // 清理刚才添加的加载消息
        setCurrentConversation(prev => {
          if (prev && prev.id === conversationToUpdate.id) {
            return { ...prev, messages: prev.messages.filter(m => !m.isLoading) };
          }
          return prev;
        });
      }
    } catch (e) {
      console.error('触发 API 调用失败:', e);
      setError('发送消息失败: ' + e.message);
      setLoading(false); // API 调用失败，设置全局 loading false
       // 清理刚才添加的加载消息，并将此会话的 loading 设置为 false
       setCurrentConversation(prev => {
         if (prev && prev.id === conversationToUpdate.id) {
           const convWithoutLoadingMsg = { 
             ...prev, 
             messages: prev.messages.filter(m => !m.isLoading),
             loading: false // <--- 设置会话特定 loading 为 false
            };
           // 同步到后端
           // updateBackendConversationLoadingStatus(convWithoutLoadingMsg.id, false); // 已通过useEffect处理
           currentConversationRef.current = convWithoutLoadingMsg; // 更新ref
           return convWithoutLoadingMsg;
         }
         return prev;
       });
       // 更新会话列表中的状态
        setConversations(prevConvs => prevConvs.map(c => {
            if (c.id === conversationToUpdate.id) {
                return { ...c, loading: false, messages: c.messages.filter(m => !m.isLoading) };
            }
            return c;
        }));
    }
  };
  
  // 处理停止消息生成
  const handleStopGeneration = () => {
    const currentConversationId = currentConversationRef.current?.id;
    if (!currentConversationId) {
      console.warn('无法停止生成：当前没有活动会话');
      return;
    }

    // 立即更新会话级状态，设置会话 loading 为 false
    if (currentConversationRef.current) {
      const updatedConv = {
        ...currentConversationRef.current,
        loading: false,
        messages: currentConversationRef.current.messages.filter(m => !m.isLoading) // 移除加载中消息
      };
      setCurrentConversation(updatedConv);
      currentConversationRef.current = updatedConv;

      setConversations(prevConvs =>
        prevConvs.map(c => (c.id === currentConversationId ? updatedConv : c))
      );
      conversationsRef.current = conversationsRef.current.map(c => 
        (c.id === currentConversationId ? updatedConv : c)
      );
    }
    
    // 全局的loading状态也设置为false（如果当前停止的是活动会话）
    setLoading(false);

    // 通过WebSocket连接池发送停止命令
    webSocketPool.sendToSession(currentConversationId, {
      command: 'stop_generation',
      conversation_id: currentConversationId
    }).then(success => {
      if (success) {
        console.log('停止生成命令已发送');
      } else {
        console.warn('发送停止生成命令失败');
      }
    });
    
    // HTTP请求发送终止命令（作为备份或补充）
    fetch(`/api/terminate_generation/${currentConversationId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => console.log('HTTP终止命令发送成功:', data))
    .catch(error => console.error('HTTP终止命令发送失败:', error));
  };
  
  // 处理消息重新生成
  const handleRegenerate = async (messageIndex) => {
    // 1. 确认当前有会话，且消息索引有效
    if (!currentConversationRef.current || !currentConversationRef.current.messages || 
        messageIndex < 0 || messageIndex >= currentConversationRef.current.messages.length) {
      setError("无法重新生成消息：无效的消息或会话");
      return;
    }
    
    const targetConversation = currentConversationRef.current;
    const targetMessage = targetConversation.messages[messageIndex];
    
    if (targetMessage.role !== 'assistant') {
      setError("只能重新生成 AI 消息");
      return;
    }
    
    let lastUserMessageIndex = -1;
    for (let i = messageIndex - 1; i >= 0; i--) {
      if (targetConversation.messages[i].role === 'user') {
        lastUserMessageIndex = i;
        break;
      }
    }
    
    if (lastUserMessageIndex === -1) {
      setError("无法找到请求消息，无法重新生成");
      return;
    }
    
    // setLoading(true); // 由 triggerApiCall 内部处理全局 loading
    // setError(null);
    
    const messagesBeforeRegeneration = targetConversation.messages.slice(0, lastUserMessageIndex + 1);
    
    const loadingAssistantMessage = {
      role: 'assistant',
      content: '...',
      timestamp: Date.now(),
      service: selectedServiceRef.current,
      model: targetConversation.model || selectedModelRef.current || settingsRef.current?.defaultModels?.[selectedServiceRef.current] || '',
      isLoading: true
    };
    
    const updatedConversation = {
      ...targetConversation,
      messages: [...messagesBeforeRegeneration, loadingAssistantMessage],
      updatedAt: Date.now(),
      loading: true // <--- 设置会话特定 loading 为 true
    };
    
    setCurrentConversation(updatedConversation);
    currentConversationRef.current = updatedConversation;

    setConversations(prevConvs => 
      prevConvs.map(c => c.id === updatedConversation.id ? updatedConversation : c)
    );
    conversationsRef.current = conversationsRef.current.map(c => c.id === updatedConversation.id ? updatedConversation : c);
    
    try {
      await triggerApiCall(updatedConversation, updatedConversation.model);
      MessageService.success('正在重新生成消息'); // 移到成功调用后
    } catch (error) {
      // triggerApiCall 内部的catch已经处理了setCurrentConversation等 setLoading(false)和错误处理
      // 这里可以只记录错误或显示特定于重新生成失败的消息
      console.error("重新生成消息时API调用失败:", error);
      // setError 可以在 triggerApiCall 中设置，或者在这里覆盖/添加更具体的信息
      // MessageService.error('重新生成消息失败，请重试');
    }
  };
  
  // 加载MCP服务器列表
  const fetchMcpServers = async () => {
    try {
      const [serversData, statusData] = await Promise.all([
        fetch('/api/mcp-servers').then(res => res.json()),
        fetch('/api/mcp-servers/status').then(res => res.json())
      ]);
      
      setMcpServers(serversData.servers || {});
      setMcpServersStatus(statusData.status || {});
      //console.log('mcpServers',mcpServers);
      //console.log('mcpServersStatus',mcpServersStatus);
    } catch (e) {
      console.error('获取MCP服务器信息失败:', e);
      setError('获取MCP服务器信息失败');
    }
  };
  
  // 初始化时加载MCP服务器列表
  useEffect(() => {
    fetchMcpServers();
    
    // 每60秒刷新一次服务器状态
    const intervalId = setInterval(() => {
      fetch('/api/mcp-servers/status')
        .then(res => res.json())
        .then(data => {
          const newStatus = data.status || {};
          
          // 深度比较状态，只在真正变化时才更新
          setMcpServersStatus(prevStatus => {
            // 检查状态是否真的有变化
            const statusChanged = JSON.stringify(prevStatus) !== JSON.stringify(newStatus);
            
            if (!statusChanged) {
              console.log('MCP状态无变化，跳过更新');
              return prevStatus; // 返回相同引用，防止重渲染
            }
            
            console.log('MCP状态已更新:', newStatus);
            return newStatus;
          });
          
          //如果data.status为空，则清空setSelectedServers
          if (Object.keys(newStatus).length === 0) {
            setSelectedServers(prevSelected => {
              if (prevSelected.length === 0) return prevSelected; // 避免不必要的更新
              return [];
            });
          } else {
            //如果data.status不为空，则遍历data.status，找到running为false的服务器从selectedServers中移除
            console.log('mcpServersStatus3', newStatus);
            // 使用函数式更新确保使用最新的selectedServers状态
            setSelectedServers(prevSelectedServers => {
              const filteredServers = prevSelectedServers.filter(server => 
                !newStatus[server] || newStatus[server].running !== false
              );
              
              // 只在过滤结果真正不同时才更新
              if (JSON.stringify(filteredServers) === JSON.stringify(prevSelectedServers)) {
                return prevSelectedServers; // 返回相同引用，防止重渲染
              }
              
              return filteredServers;
            });
          }
        })
        .catch(e => console.error('刷新服务器状态失败:', e));
    }, 60000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  // 当选中的服务器变化时，刷新工具列表
  useEffect(() => {
    if (selectedServers.length > 0) {
      // 从选中的服务器获取工具
      fetch('/api/list-tools-from-servers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ servers: selectedServers }),
      })
        .then(res => res.json())
        .then(data => {
          setFunctionTools(data.tools || []);
          setSelectedTool(null); // 重置选中的工具
        })
        .catch(e => {
          console.error('获取服务器工具失败:', e);
          setError('获取服务器工具失败');
        });
    } else {
      // 如果没有选中服务器，清空工具列表
      setFunctionTools([]);
      setSelectedTool(null);
    }
  }, [selectedServers]);
  
  // 处理复制操作
  const handleCopy = () => {
    document.execCommand('copy');
    setContextMenu({ ...contextMenu, visible: false });
  };
  
  // 处理粘贴操作
  const handlePaste = () => {
    document.execCommand('paste');
    setContextMenu({ ...contextMenu, visible: false });
  };
  
  // 处理剪切操作
  const handleCut = () => {
    document.execCommand('cut');
    setContextMenu({ ...contextMenu, visible: false });
  };
  

  
  // 切换Agent模式
  const toggleAgentMode = async () => {
    // 检查是否有当前会话
    if (!currentConversationRef.current) {
      console.warn('无法切换Agent模式：当前没有活动会话');
      MessageService.warning('请先创建或选择一个会话');
      return;
    }

    const currentConversationId = currentConversationRef.current.id;
    const newAgentMode = !currentConversationRef.current.isAgentMode;

    // 如果是要开启Agent模式，先检查目录是否准备就绪
    if (newAgentMode) {
      try {
        console.log('开启Agent模式前检查目录是否准备就绪...');
        const response = await fetch('/api/check-agent-directory');
        const data = await response.json();
        
        if (!data.ready) {
          console.error('Agent目录检查失败:', data.error);
          MessageService.error(`无法开启Agent模式：${data.error}，请在设置中配置数据保存目录`);
          modal.confirm({
            title: '设置数据保存目录',
            icon: <ExclamationCircleOutlined />,
            content: '请在设置中配置数据保存目录',
            okText: '前往设置',
            cancelText: '取消',
            onOk: () => {
              navigate('/settings');
            }
          });
          return;
        }
        
        console.log('Agent目录检查通过:', data.directory);
      } catch (error) {
        console.error('检查Agent目录时出错:', error);
        MessageService.error('无法检查Agent目录状态，请稍后再试');
        return;
      }
    }

    try {
      console.log(`切换会话 ${currentConversationId} 的Agent模式：${currentConversationRef.current.isAgentMode} -> ${newAgentMode}`);

      // 更新当前会话的Agent模式状态
      const updatedConversation = {
        ...currentConversationRef.current,
        isAgentMode: newAgentMode,
        updatedAt: Date.now()
      };

      // 更新状态
      setCurrentConversation(updatedConversation);
      currentConversationRef.current = updatedConversation;

      // 更新会话列表中的对应会话
      setConversations(prevConvs =>
        prevConvs.map(c => (c.id === currentConversationId ? updatedConversation : c))
      );
      conversationsRef.current = conversationsRef.current.map(c => 
        (c.id === currentConversationId ? updatedConversation : c)
      );

      // 保存到服务器
      await saveConversationToServer(updatedConversation);

      // 显示切换消息
      MessageService.success(newAgentMode ? '已开启智能体模式' : '已关闭智能体模式');
      
      console.log(`Agent模式切换完成：会话 ${currentConversationId} 现在为 ${newAgentMode ? '开启' : '关闭'}`);
    } catch (error) {
      console.error('切换Agent模式出错:', error);
      // 出错时显示错误提示
      MessageService.error('切换Agent模式失败，请稍后再试');
    }
  };
  
  // 处理工具结果的函数
  const handleToolResult = (toolResult) => {
    if (!toolResult) return;
    
    console.log('App组件收到工具结果:', toolResult);
    
    // 添加ID和时间戳
    const toolResultWithId = {
      ...toolResult,
      id: `app-tool-${toolResultIdCounter.current++}`,
      timestamp: toolResult.timestamp || new Date().toISOString()
    };
    
    // 更新工具结果状态
    setToolResults(prevResults => [...prevResults, toolResultWithId]);
  };
  
  // 初始化WebSocket连接池
  const initWebSocketPool = () => {
    console.log('初始化WebSocket连接池');
    
    // 初始化连接池，传入全局消息处理器
    webSocketPool.initialize((rawData, sessionId) => {
      console.log(`[WebSocketPool] 收到消息，会话ID: ${sessionId || '未绑定'}，数据长度: ${rawData.length}`);
      
      // 使用现有的JSON解析器
      const parsedMessages = parseConcatenatedJson(rawData);
      
      if (parsedMessages.length > 0) {
        console.log(`[WebSocketPool] 解析出 ${parsedMessages.length} 条消息`);
        parsedMessages.forEach(messageObject => {
          if (messageObject && typeof messageObject === 'object' && messageObject.type) {
            handleParsedWebSocketMessage(messageObject, sessionId);
          } else {
            console.warn('[WebSocketPool] 跳过无效的解析对象:', messageObject);
          }
        });
      } else if (rawData.trim() !== '') {
        console.warn("[WebSocketPool] 收到非JSON消息或JSON解析失败:", rawData);
        try {
          const singleMessage = JSON.parse(rawData);
          if (singleMessage && singleMessage.type) {
            console.log('[WebSocketPool] 回退解析成功');
            handleParsedWebSocketMessage(singleMessage, sessionId);
          } else {
            setError("收到无法解析的消息格式");
          }
        } catch(e) {
          setError("收到无法解析的消息格式");
          console.error("[WebSocketPool] 回退JSON解析失败:", e);
        }
      }
    });
    
    // 启动定期清理任务
    webSocketPool.startCleanupTask();
    
    console.log('WebSocket连接池初始化完成');
  };
  
  // Initialize (remove the old handleWebSocketMessage)
  useEffect(() => {
    // 初始化会话缓存系统
    conversationCache.setSaveFunction(saveConversationToServer);
    conversationCache.setConfig({
      debounceDelay: 1000,
      debug: true // 开启调试以便追踪问题
    });
    console.log('会话缓存系统已初始化，保存函数已设置');
    
    // 运行缓存系统功能测试
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        const testResult = conversationCache.testCacheSystem();
        console.log('缓存系统测试结果:', testResult);
      }, 2000);
    }
    
    loadSettings();
    fetchAllModels();
    fetchTools();
    initWebSocketPool(); // This now sets up the new onmessage handler
    initConversations();

    // Context menu listeners (keep as is)
    const handleContextMenu = (e) => { /* ... */ };
    const handleClick = (e) => { /* ... */ };
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('click', handleClick);
    
    return () => {
      // 关闭WebSocket连接池
      webSocketPool.closeAll();
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('click', handleClick);
    };
  }, []); // Keep dependencies minimal for initialization
  
  // 生成对话链接的辅助函数
  const generateConversationLink = (conversationId) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/chat/${conversationId}`;
  };
  
  // 新增：更新后端会话loading状态的函数
  const updateBackendConversationLoadingStatus = async (conversationId, isLoading) => {
    if (!conversationId) return;
    try {
      await fetch(`/api/conversations/${conversationId}/loading`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ loading_status: isLoading }), //确保请求体格式正确
      });
      console.log(`已将后端会话 ${conversationId} 的loading状态更新为 ${isLoading}`);
    } catch (e) {
      console.error(`更新后端会话 ${conversationId} 的loading状态失败:`, e);
    }
  };

  // 当全局loading状态或当前会话的loading状态改变时，同步到后端
  useEffect(() => {
    // 只在 currentConversation 存在，并且其 loading 状态是布尔类型时才同步
    if (currentConversationRef.current && typeof currentConversationRef.current.loading === 'boolean') {
      updateBackendConversationLoadingStatus(currentConversationRef.current.id, currentConversationRef.current.loading);
    }
  }, [currentConversationRef.current?.loading, currentConversationRef.current?.id]);
  
  // 开发模式下的性能监控
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // 每30秒输出一次缓存性能报告
      const performanceInterval = setInterval(() => {
        const report = conversationCache.getPerformanceReport();
        if (report.totalCached > 0 || report.streamingCount > 0) {
          console.group('📊 会话缓存性能报告');
          console.log('总缓存数:', report.totalCached);
          console.log('流式响应中:', report.streamingCount);
          console.log('待保存数:', report.pendingSaves);
          console.log('新会话数:', report.newConversationsCount);
          console.log('缓存效率:', report.cacheEfficiency);
          console.log('平均缓存时间:', `${report.avgCacheTime}ms`);
          if (report.streamingDurations.length > 0) {
            console.log('活跃流式响应:', report.streamingDurations.map(s => 
              `${s.conversationId.slice(-8)}: ${Math.round(s.duration/1000)}s`
            ).join(', '));
          }
          console.groupEnd();
        }
      }, 30000);

      // 清理定时器
      return () => clearInterval(performanceInterval);
    }
  }, []);
  
  // 页面卸载时保存所有缓存
  useEffect(() => {
    const handleBeforeUnload = async (event) => {
      // 获取缓存统计
      const stats = conversationCache.getStats();
      
      if (stats.totalCached > 0 || stats.newConversationsCount > 0) {
        console.log(`[页面卸载] 正在保存 ${stats.totalCached} 个缓存会话和 ${stats.newConversationsCount} 个新会话`);
        
        try {
          // 保存所有缓存的会话
          const savedCount = await conversationCache.saveAll();
          console.log(`[页面卸载] 成功保存 ${savedCount} 个会话`);
        } catch (error) {
          console.error('[页面卸载] 保存缓存会话时出错:', error);
        }
        
        // 设置提示信息（可选）
        event.preventDefault();
        event.returnValue = '正在保存会话数据，请稍等...';
        return event.returnValue;
      }
    };
    
    const handleUnload = () => {
      // 最后一次尝试保存
      const stats = conversationCache.getStats();
      if (stats.totalCached > 0) {
        console.log('最后一次尝试保存',stats);
        // 使用 sendBeacon 进行最后的保存尝试
        /*navigator.sendBeacon('/api/save-cache', JSON.stringify({
          action: 'save_all_cache',
          timestamp: Date.now()
        }));*/
      }
    };
    
    // 添加事件监听器
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);
    
    // 清理事件监听器
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, []);
  
  return (
    <ConfigProvider
      // theme={{
      //   algorithm: theme.defaultAlgorithm,
      //   token: {
      //     colorPrimary: '#8b5cf6',
      //     borderRadius: 6,
      //   },
      // }}
    >
      {contextHolder}
      <Layout className="app-layout">
        {/* Conditionally render Sidebar based on the current route */}
        {location.pathname !== '/isInitializing' && (
          <Sidebar
            conversations={conversations}
            currentConversation={currentConversation}
            onNewConversation={handleNewConversation}
            onSelectConversation={handleSelectConversation}
            onDeleteConversation={handleDeleteConversation}
            onRenameConversation={handleRenameConversation}
            settings={settings}
            onUpdateSettings={handleUpdateSettings}
            onViewChange={handleViewChange}
            currentView={location.pathname === '/' ? 'home' : 
                        location.pathname === '/chat' ? 'history' : 
                        location.pathname === '/image' ? 'image' : 
                        location.pathname === '/settings' ? 'settings' : 
                        location.pathname.startsWith('/workflow') ? 'workflow' : ''}
            modelsCache={modelsCache}
            onRefreshTools={refreshTools}
            currentConversationId={currentConversation?.id}
            conversationDrawerOpen={conversationDrawerOpen}
            onToggleConversationDrawer={handleToggleConversationDrawer}
            onDeleteAllConversations={handleDeleteAllConversations}
          />
        )}
        <Layout className="site-layout">
          <AppRoutes 
            conversations={conversations}
            currentConversation={currentConversation}
            onSendMessage={handleSendMessage}
            onStopGeneration={handleStopGeneration}
            onSelectConversation={handleSelectConversation}
            onDeleteConversation={handleDeleteConversation}
            onRenameConversation={handleRenameConversation}
            onClearHistory={handleClearConversationHistory}
            onNewConversation={handleNewConversation}
            loading={loading}
            error={error}
            selectedService={selectedService}
            onServiceChange={handleServiceChange}
            functionTools={functionTools}
            onSelectFunctionTool={handleSelectFunctionTool}
            mcpServers={mcpServers}
            mcpServersStatus={mcpServersStatus}
            selectedServers={selectedServers}
            onSelectServers={handleSelectServers}
            modelsCache={modelsCache}
            settings={settings}
            onToggleConversationDrawer={handleToggleConversationDrawer}
            onUpdateSettings={handleUpdateSettings}
            onRefreshTools={refreshTools}
            onRefreshModels={refreshAllModels}
            onDeleteAllConversations={handleDeleteAllConversations}
            onUpdateMcpConfig={fetchMcpServers}
            onUpdateConversationModel={handleUpdateConversationModel}
            isAgentMode={currentConversation?.isAgentMode || false}
            onToggleAgentMode={toggleAgentMode}
            onRegenerate={handleRegenerate}
            toolResults={toolResults}
            onToolResult={handleToolResult}
          />
        </Layout>
        {contextMenu.visible && (
          <div 
            ref={contextMenuRef}
            className="custom-context-menu"
            style={{ 
              position: 'fixed',
              top: contextMenu.y,
              left: contextMenu.x,
              zIndex: 1000
            }}
          >
            <Menu>
              <Menu.Item key="copy" onClick={handleCopy}>复制</Menu.Item>
              <Menu.Item key="paste" onClick={handlePaste}>粘贴</Menu.Item>
              <Menu.Item key="cut" onClick={handleCut}>剪切</Menu.Item>
            </Menu>
          </div>
        )}
      </Layout>
    </ConfigProvider>
  );
};

export default App;