/**
 * SVG转PPT服务
 * 提供完整的SVG生成、转换、PPT打包和下载流程
 * 作者: AI Assistant  
 * 创建时间: 2025-06-22T15:40:00+08:00
 * 任务: P2-LD-005 实现前端SVG转PPT完整流程
 */

import SVGtoPNGConverter from './SVGtoPNGConverter.js';

class SVGPPTService {
    constructor() {
        // Get WEBHOST from environment variable, fallback to localhost
        const webHost = process.env.REACT_APP_WEBHOST || 'localhost';
        
        this.baseURL = process.env.NODE_ENV === 'development' 
            ? `http://${webHost}:8080/api/svg-ppt`
            : '/api/svg-ppt';
        
        this.wsURL = process.env.NODE_ENV === 'development'
            ? `ws://${webHost}:8080/api/svg-ppt`
            : `ws://${window.location.host}/api/svg-ppt`;
        
        this.progressWebSocket = null;
        this.currentSessionId = null;
        
        this.converter = new SVGtoPNGConverter({
            width: 1920,
            height: 1080,
            quality: 0.95,
            backgroundColor: '#ffffff'
        });
    }

    /**
     * 生成唯一的会话ID
     * @returns {string} 会话ID
     */
    generateSessionId() {
        return `svg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 建立WebSocket连接用于接收SVG生成进度
     * @param {string} sessionId - 会话ID
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<WebSocket>} WebSocket连接
     */
    async connectProgressWebSocket(sessionId, progressCallback) {
        return new Promise((resolve, reject) => {
            try {
                const wsUrl = `${this.wsURL}/svg-progress/${sessionId}`;
                console.log('连接SVG进度WebSocket:', wsUrl);
                
                const ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    console.log('SVG进度WebSocket连接已建立');
                    this.progressWebSocket = ws;
                    this.currentSessionId = sessionId;
                    
                    // 发送心跳
                    const heartbeat = setInterval(() => {
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'ping',
                                timestamp: Date.now()
                            }));
                        } else {
                            clearInterval(heartbeat);
                        }
                    }, 30000); // 30秒心跳
                    
                    resolve(ws);
                };
                
                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('收到SVG进度更新:', data);
                        
                        if (data.type === 'svg_progress' && progressCallback) {
                            progressCallback({
                                stage: data.stage,
                                message: data.message,
                                progress: data.progress
                            });
                        } else if (data.type === 'svg_page_completed' && progressCallback) {
                            // 处理增量SVG页面完成事件
                            console.log('收到增量SVG页面:', data.page_data);
                            
                            // 优化进度显示，基于页面数量计算百分比
                            const progressInfo = data.progress_info || {};
                            const completedPages = progressInfo.completed_pages || 0;
                            const totalPages = progressInfo.total_pages || 1;
                            const percentage = progressInfo.percentage || (data.progress * 100);
                            
                            progressCallback({
                                stage: data.stage,
                                message: data.message || `已完成 ${completedPages}/${totalPages} 页 (${percentage.toFixed(1)}%)`,
                                progress: data.progress,
                                type: 'svg_page_completed',
                                page_data: data.page_data,
                                progress_info: {
                                    completed_pages: completedPages,
                                    total_pages: totalPages,
                                    percentage: percentage
                                }
                            });
                        } else if (data.type === 'svg_error' && progressCallback) {
                            progressCallback({
                                stage: '生成失败',
                                message: data.error,
                                progress: 0,
                                error: true
                            });
                        } else if (data.type === 'svg_partial_complete' && progressCallback) {
                            // 处理部分完成事件
                            progressCallback({
                                stage: '部分生成完成',
                                message: data.message,
                                progress: data.progress || 80,
                                partial: true,
                                warning: data.warning,
                                error_details: data.error
                            });
                        } else if (data.type === 'pong') {
                            // 心跳响应，忽略
                            console.debug('WebSocket心跳响应');
                        }
                    } catch (error) {
                        console.error('解析WebSocket消息失败:', error);
                    }
                };
                
                ws.onerror = (error) => {
                    console.error('SVG进度WebSocket错误:', error);
                    reject(error);
                };
                
                ws.onclose = () => {
                    console.log('SVG进度WebSocket连接已关闭');
                    this.progressWebSocket = null;
                    this.currentSessionId = null;
                };
                
            } catch (error) {
                console.error('创建WebSocket连接失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 关闭WebSocket连接
     */
    closeProgressWebSocket() {
        if (this.progressWebSocket) {
            this.progressWebSocket.close();
            this.progressWebSocket = null;
            this.currentSessionId = null;
        }
    }

    /**
     * 检查后端任务状态
     * @param {string} sessionId - 会话ID
     * @returns {Promise<Object>} 任务状态
     */
    async checkTaskStatus(sessionId) {
        try {
            const response = await fetch(`${this.baseURL}/generation-status/${sessionId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.warn('检查任务状态失败:', error);
            return { status: 'unknown', error: error.message };
        }
    }

    /**
     * 使用WebSocket进度通信生成SVG内容（真正异步模式）
     * @param {string} sourceContent - 源内容
     * @param {Object} config - SVG生成配置
     * @param {Function} progressCallback - 进度回调
     * @param {string} sessionId - 会话ID（可选，如果不提供会自动生成）
     * @returns {Promise<Array>} SVG内容数组
     */
    async generateSVGContentWithProgress(sourceContent, config = {}, progressCallback = null, sessionId = null) {
        // 如果没有提供sessionId，则生成一个新的
        const finalSessionId = sessionId || this.generateSessionId();
        
        try {
            // {{CHENGQI:
            // Action: Modified
            // Timestamp: [2025-01-16T21:45:00+08:00]
            // Reason: 添加智能重连机制，超时后检查后端任务状态并重连，提升用户体验
            // Principle_Applied: 容错性 - 网络异常自动恢复; 用户体验 - 持续获取结果，不轻易放弃
            // Optimization: 超时重连逻辑，避免因网络问题丢失生成结果
            // Architectural_Note (AR): 增强的异步任务处理架构
            // Documentation_Note (DW): 根据用户反馈添加超时重连功能
            // }}
            
            // 建立WebSocket连接接收进度
            if (progressCallback) {
                await this.connectProgressWebSocket(finalSessionId, progressCallback);
            }
            
            if (progressCallback) {
                progressCallback({
                    stage: '启动任务',
                    message: '正在启动SVG生成任务...',
                    progress: 5
                });
            }

            // 发送HTTP请求启动后台任务（立即返回）
            const response = await fetch(`${this.baseURL}/generate-svg-with-progress`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: sourceContent,
                    style_config: {
                        svg_style: config.svg_style || 'modern',
                        svg_color_theme: config.svg_color_theme || 'blue',
                        svg_layout: config.svg_layout || 'standard',
                        svg_custom_requirements: config.svg_custom_requirements || ''
                    },
                    custom_requirements: config.svg_custom_requirements || '',
                    session_id: finalSessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const taskStartResult = await response.json();

            if (!taskStartResult.success) {
                throw new Error(taskStartResult.detail || '启动SVG生成任务失败');
            }

            console.log('✅ SVG生成任务已启动:', taskStartResult);
            
            if (progressCallback) {
                progressCallback({
                    stage: '任务已启动',
                    message: '后台正在生成SVG内容，请稍候...',
                    progress: 10
                });
            }

            // 等待WebSocket接收完整结果（带智能重连）
            return this._waitForSVGResult(finalSessionId, progressCallback);

        } catch (error) {
            console.error('SVG生成错误:', error);
            if (progressCallback) {
                progressCallback({
                    stage: '生成失败',
                    message: error.message,
                    progress: 0,
                    error: true
                });
            }
            throw error;
        } finally {
            // 注释掉自动关闭逻辑，WebSocket将在Promise resolve/reject时手动关闭
            // setTimeout(() => {
            //     this.closeProgressWebSocket();
            // }, 3000);
        }
    }

    /**
     * 等待SVG生成结果，支持智能重连
     * @param {string} sessionId - 会话ID
     * @param {Function} progressCallback - 进度回调
     * @param {number} reconnectCount - 重连次数
     * @returns {Promise<Array>} SVG内容数组
     */
    async _waitForSVGResult(sessionId, progressCallback, reconnectCount = 0) {
        const maxReconnects = 10; // 最大重连次数
        const timeoutDuration = 300000; // 5分钟超时
        
        return new Promise(async (resolve, reject) => {
            let finalSvgContent = [];
            let isCompleted = false;
            let hasError = false;
            const receivedPages = new Map(); // 用于存储增量接收的页面
            
            // 设置超时处理
            const timeoutId = setTimeout(async () => {
                if (!isCompleted && !hasError) {
                    console.warn(`⏰ SVG生成超时 (第${reconnectCount + 1}次尝试)，检查后端任务状态...`);
                    
                    // 检查后端任务状态
                    const taskStatus = await this.checkTaskStatus(sessionId);
                    console.log('📋 后端任务状态:', taskStatus);
                    
                    if (progressCallback) {
                        progressCallback({
                            stage: '检查任务状态',
                            message: `连接超时，正在检查后端任务状态... (第${reconnectCount + 1}次尝试)`,
                            progress: Math.max(10, (receivedPages.size > 0 ? 50 : 15))
                        });
                    }
                    
                    // 判断是否需要重连
                    const shouldReconnect = (
                        taskStatus.status === 'generating' || 
                        taskStatus.status === 'unknown' || 
                        (taskStatus.status === 'completed' && !taskStatus.generated_pages) ||
                        reconnectCount < 3 // 前3次无论如何都重连
                    ) && reconnectCount < maxReconnects;
                    
                    if (shouldReconnect) {
                        console.log(`🔄 尝试重连WebSocket (第${reconnectCount + 1}/${maxReconnects}次)`);
                        
                        if (progressCallback) {
                            progressCallback({
                                stage: '重新连接',
                                message: `正在重新连接获取结果... (第${reconnectCount + 1}/${maxReconnects}次)`,
                                progress: Math.max(15, (receivedPages.size > 0 ? 55 : 20))
                            });
                        }
                        
                        // 关闭当前WebSocket
                        this.closeProgressWebSocket();
                        
                                                 // 等待一下再重连
                         setTimeout(async () => {
                             try {
                                 // 先获取已生成的页面，恢复状态
                                 const existingPages = await this.getGeneratedPages(sessionId);
                                 console.log(`🔄 获取到 ${existingPages.length} 个已生成页面`);
                                 
                                 // 将已获取的页面添加到receivedPages中
                                 existingPages.forEach(page => {
                                     receivedPages.set(page.page_number, page);
                                 });
                                 
                                 if (progressCallback && existingPages.length > 0) {
                                     progressCallback({
                                         stage: '恢复已生成页面',
                                         message: `已恢复 ${existingPages.length} 个之前生成的页面`,
                                         progress: Math.max(25, (existingPages.length / 10) * 50) // 根据页面数量估算进度
                                     });
                                 }
                                 
                                 // 重新建立WebSocket连接
                                 await this.connectProgressWebSocket(sessionId, progressCallback);
                                 console.log('✅ WebSocket重连成功，继续等待结果...');
                                 
                                 // 递归调用，重新等待结果
                                 const result = await this._waitForSVGResult(sessionId, progressCallback, reconnectCount + 1);
                                 resolve(result);
                            } catch (reconnectError) {
                                console.error('❌ WebSocket重连失败:', reconnectError);
                                
                                // 如果有已生成的页面，返回部分结果
                                if (receivedPages.size > 0) {
                                    const completedPages = Array.from(receivedPages.values())
                                        .sort((a, b) => a.index - b.index);
                                    
                                    if (progressCallback) {
                                        progressCallback({
                                            stage: '重连失败，返回部分结果',
                                            message: `重连失败，但已获取 ${completedPages.length} 页结果`,
                                            progress: 90,
                                            partial: true
                                        });
                                    }
                                    
                                    setTimeout(() => this.closeProgressWebSocket(), 1000);
                                    resolve(completedPages);
                                } else {
                                    setTimeout(() => this.closeProgressWebSocket(), 1000);
                                    reject(new Error(`WebSocket重连失败，重连次数: ${reconnectCount + 1}`));
                                }
                            }
                        }, 2000); // 2秒后重连
                        
                    } else {
                        // 不再重连，返回已有结果或失败
                        if (receivedPages.size > 0) {
                            const completedPages = Array.from(receivedPages.values())
                                .sort((a, b) => a.index - b.index);
                            
                            console.log(`⚠️ 达到最大重连次数，返回已获取的 ${completedPages.length} 页结果`);
                            
                            if (progressCallback) {
                                progressCallback({
                                    stage: '达到最大重连次数',
                                    message: `已尝试 ${reconnectCount + 1} 次连接，返回已获取的 ${completedPages.length} 页结果`,
                                    progress: 95,
                                    partial: true
                                });
                            }
                            
                            setTimeout(() => this.closeProgressWebSocket(), 1000);
                            resolve(completedPages);
                        } else {
                            console.error(`❌ SVG生成最终失败，重连次数: ${reconnectCount + 1}, 后端状态: ${taskStatus.status}`);
                            setTimeout(() => this.closeProgressWebSocket(), 1000);
                            reject(new Error(`SVG生成超时且无任何结果，重连${reconnectCount + 1}次后放弃`));
                        }
                    }
                }
            }, timeoutDuration);
            
            // 重写WebSocket消息处理
            if (this.progressWebSocket) {
                const originalOnMessage = this.progressWebSocket.onmessage;
                
                this.progressWebSocket.onmessage = (event) => {
                    // 先调用原始的消息处理（用于进度回调）
                    if (originalOnMessage) {
                        originalOnMessage(event);
                    }
                    
                    try {
                        const data = JSON.parse(event.data);
                        
                        // 处理增量SVG页面完成
                        if (data.type === 'svg_page_completed' && data.page_data) {
                            const pageData = data.page_data;
                            const svgItem = {
                                content: pageData.svg_code,
                                title: pageData.title || `幻灯片 ${pageData.page_number}`,
                                index: pageData.page_number - 1,
                                page_number: pageData.page_number,
                                generated_at: pageData.generated_at,
                                layout_type: pageData.layout_type
                            };
                            
                            receivedPages.set(pageData.page_number, svgItem);
                            console.log(`📄 接收到第 ${pageData.page_number} 页SVG (重连第${reconnectCount}次)`);
                        }
                        
                        // 处理完成事件
                        else if (data.type === 'svg_progress') {
                            if (data.stage === '生成完成' && data.progress >= 100) {
                                isCompleted = true;
                                clearTimeout(timeoutId);
                                
                                finalSvgContent = Array.from(receivedPages.values())
                                    .sort((a, b) => a.index - b.index);
                                
                                console.log(`✅ SVG生成完全完成，共 ${finalSvgContent.length} 页 (重连第${reconnectCount}次)`);
                                
                                setTimeout(() => this.closeProgressWebSocket(), 1000);
                                resolve(finalSvgContent);
                            }
                            else if (data.stage === '部分生成完成') {
                                isCompleted = true;
                                clearTimeout(timeoutId);
                                
                                finalSvgContent = Array.from(receivedPages.values())
                                    .sort((a, b) => a.index - b.index);
                                
                                console.log(`⚠️ SVG部分生成完成，共 ${finalSvgContent.length} 页 (重连第${reconnectCount}次)`);
                                
                                setTimeout(() => this.closeProgressWebSocket(), 1000);
                                resolve(finalSvgContent);
                            }
                        }
                        
                        // 处理部分完成事件
                        else if (data.type === 'svg_partial_complete') {
                            isCompleted = true;
                            clearTimeout(timeoutId);
                            
                            finalSvgContent = Array.from(receivedPages.values())
                                .sort((a, b) => a.index - b.index);
                            
                            console.log(`⚠️ SVG部分完成，共 ${finalSvgContent.length} 页，原因: ${data.warning} (重连第${reconnectCount}次)`);
                            
                            setTimeout(() => this.closeProgressWebSocket(), 1000);
                            resolve(finalSvgContent);
                        }
                        
                        // 处理页面状态摘要（WebSocket重连时的快速状态恢复）
                        else if (data.type === 'svg_pages_summary') {
                            console.log(`📄 接收到页面状态摘要: ${data.pages_count} 页`);
                            
                            if (progressCallback) {
                                progressCallback({
                                    stage: data.stage,
                                    message: data.message,
                                    progress: data.progress,
                                    type: 'pages_summary',
                                    pages_count: data.pages_count,
                                    pages_info: data.pages_info
                                });
                            }
                        }
                        
                        // 处理会话清理通知
                        else if (data.type === 'session_cleanup') {
                            console.warn('⚠️ 后端任务会话已清理:', data.message);
                            hasError = true;
                            clearTimeout(timeoutId);
                            
                            if (progressCallback) {
                                progressCallback({
                                    stage: '会话已清理',
                                    message: data.message,
                                    progress: 0,
                                    error: true,
                                    cleanup: true
                                });
                            }
                            
                            setTimeout(() => this.closeProgressWebSocket(), 1000);
                            reject(new Error(data.message || '后端任务会话已清理'));
                        }
                        
                        // 处理错误事件
                        else if (data.type === 'svg_error') {
                            hasError = true;
                            clearTimeout(timeoutId);
                            
                            // 如果有已生成的页面，返回部分结果
                            if (receivedPages.size > 0) {
                                finalSvgContent = Array.from(receivedPages.values())
                                    .sort((a, b) => a.index - b.index);
                                
                                console.log(`❌ SVG生成出错但有部分结果，共 ${finalSvgContent.length} 页 (重连第${reconnectCount}次)`);
                                
                                setTimeout(() => this.closeProgressWebSocket(), 1000);
                                resolve(finalSvgContent);
                            } else {
                                setTimeout(() => this.closeProgressWebSocket(), 1000);
                                reject(new Error(data.error || 'SVG生成失败'));
                            }
                        }
                        
                    } catch (parseError) {
                        console.warn('解析WebSocket消息失败:', parseError);
                    }
                };
            } else {
                // 如果WebSocket未连接，等待一段时间后重试或失败
                setTimeout(() => {
                    if (!isCompleted && !hasError) {
                        reject(new Error('WebSocket连接失败，无法接收SVG生成结果'));
                    }
                }, 10000);
            }
        });
    }

    /**
     * 连接指定的WebSocket用于状态恢复（不发送请求，仅监听进度）
     * @param {string} sessionId - 会话ID
     * @param {Function} progressCallback - 进度回调
     */
    async connectWebSocket(sessionId, progressCallback) {
        try {
            await this.connectProgressWebSocket(sessionId, progressCallback);
            console.log('🔄 已重新连接SVG生成进度WebSocket:', sessionId);
        } catch (error) {
            console.warn('重新连接WebSocket失败:', error);
            throw error;
        }
    }

    /**
     * 调用后端API生成SVG内容（原始方法，保持向后兼容）
     * @param {string} sourceContent - 源内容
     * @param {Object} config - SVG生成配置
     * @param {Function} progressCallback - 进度回调
     * @param {string} sessionId - 会话ID（可选）
     * @returns {Promise<Array>} SVG内容数组
     */
    async generateSVGContent(sourceContent, config = {}, progressCallback = null, sessionId = null) {
        // 使用新的WebSocket版本
        return this.generateSVGContentWithProgress(sourceContent, config, progressCallback, sessionId);
    }

    /**
     * 将SVG内容转换为PPT数据包
     * @param {Array} svgContent - SVG内容数组
     * @param {Object} options - 转换选项
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<Object>} PPT数据包
     */
    async convertSVGToPPT(svgContent, options = {}, progressCallback = null) {
        try {
            if (!svgContent || svgContent.length === 0) {
                throw new Error('没有可转换的SVG内容');
            }

            if (progressCallback) {
                progressCallback({
                    stage: '准备转换',
                    message: '正在准备SVG转换...',
                    progress: 5
                });
            }

            // 验证SVG内容
            const validSVGs = [];
            for (let i = 0; i < svgContent.length; i++) {
                const svg = svgContent[i];
                if (this.converter.validateSVG(svg.content)) {
                    validSVGs.push(svg.content);
                } else {
                    console.warn(`第 ${i + 1} 页SVG内容无效，跳过转换`);
                }
            }

            if (validSVGs.length === 0) {
                throw new Error('没有有效的SVG内容可转换');
            }

            if (progressCallback) {
                progressCallback({
                    stage: '转换中',
                    message: `正在转换 ${validSVGs.length} 页SVG为PNG...`,
                    progress: 20
                });
            }

            // 转换SVG为PPT数据包
            const pptData = await this.converter.createPPTDataPackage(validSVGs, {
                title: options.title || 'SVG演示文稿',
                description: options.description || '由SVG生成的专业演示文稿',
                progressCallback: (current, total, success, error) => {
                    if (progressCallback) {
                        const progress = 20 + (current / total) * 60; // 20-80%
                        progressCallback({
                            stage: '转换中',
                            message: success 
                                ? `正在转换第 ${current}/${total} 页...`
                                : `第 ${current} 页转换失败: ${error}`,
                            progress: Math.round(progress)
                        });
                    }
                }
            });

            if (progressCallback) {
                progressCallback({
                    stage: '打包完成',
                    message: `成功转换 ${pptData.totalSlides} 页PPT`,
                    progress: 100
                });
            }

            return pptData;

        } catch (error) {
            console.error('SVG转PPT错误:', error);
            if (progressCallback) {
                progressCallback({
                    stage: '转换失败',
                    message: error.message,
                    progress: 0,
                    error: true
                });
            }
            throw error;
        }
    }

    /**
     * 下载PPT文件
     * @param {Object} pptData - PPT数据包
     * @param {string} filename - 文件名
     */
    downloadPPT(pptData, filename = null) {
        try {
            const defaultFilename = `SVG_PPT_${new Date().toISOString().slice(0, 19).replace(/[:\-T]/g, '_')}.json`;
            const finalFilename = filename || defaultFilename;

            // 创建下载数据
            const downloadData = {
                type: 'svg_ppt',
                filename: finalFilename.replace('.json', '.pptx'),
                ...pptData,
                downloadInfo: {
                    generatedAt: new Date().toISOString(),
                    totalSize: this.calculateDataSize(pptData),
                    format: 'PowerPoint PPTX (JSON Data Package)'
                }
            };

            // 创建Blob并下载
            const dataStr = JSON.stringify(downloadData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = finalFilename;
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL
            setTimeout(() => URL.revokeObjectURL(url), 1000);

            return {
                success: true,
                filename: finalFilename,
                size: dataBlob.size,
                slides: pptData.totalSlides
            };

        } catch (error) {
            console.error('PPT下载错误:', error);
            throw new Error(`下载失败: ${error.message}`);
        }
    }

    /**
     * 计算数据包大小
     * @param {Object} data - 数据对象
     * @returns {string} 格式化的大小字符串
     */
    calculateDataSize(data) {
        try {
            const jsonStr = JSON.stringify(data);
            const bytes = new TextEncoder().encode(jsonStr).length;
            
            if (bytes < 1024) {
                return `${bytes} B`;
            } else if (bytes < 1024 * 1024) {
                return `${(bytes / 1024).toFixed(1)} KB`;
            } else {
                return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
            }
        } catch (error) {
            return '未知大小';
        }
    }

    /**
     * 完整的SVG转PPT流程
     * @param {string} sourceContent - 源内容
     * @param {Object} config - 配置选项
     * @param {Object} callbacks - 回调函数集合
     * @returns {Promise<Object>} 下载结果
     */
    async processComplete(sourceContent, config = {}, callbacks = {}) {
        const {
            onSVGProgress = null,
            onConvertProgress = null,
            onComplete = null,
            onError = null
        } = callbacks;

        try {
            // 第一步：生成SVG内容
            const svgContent = await this.generateSVGContent(
                sourceContent, 
                config, 
                onSVGProgress
            );

            // 第二步：转换为PPT
            const pptData = await this.convertSVGToPPT(
                svgContent,
                {
                    title: config.title || '演示文稿',
                    description: config.description || '基于研究内容生成的SVG演示文稿'
                },
                onConvertProgress
            );

            // 第三步：下载文件
            const downloadResult = this.downloadPPT(
                pptData,
                config.filename
            );

            if (onComplete) {
                onComplete({
                    svgContent,
                    pptData,
                    downloadResult
                });
            }

            return {
                success: true,
                svgContent,
                pptData,
                downloadResult
            };

        } catch (error) {
            if (onError) {
                onError(error);
            }
            throw error;
        }
    }

    /**
     * 获取已生成的SVG页面
     * @param {string} sessionId - 会话ID
     * @returns {Promise<Array>} 已生成的SVG页面数组
     */
    async getGeneratedPages(sessionId) {
        try {
            const response = await fetch(`${this.baseURL}/generated-pages/${sessionId}`);
            if (!response.ok) {
                if (response.status === 404) {
                    return []; // 没有找到已生成的页面
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            
            if (result.success && result.pages) {
                return result.pages.map((page, index) => ({
                    content: page.svg_code,
                    title: page.title || `幻灯片 ${page.page_number}`,
                    index: page.page_number - 1,
                    page_number: page.page_number,
                    generated_at: page.generated_at,
                    layout_type: page.layout_type
                }));
            }
            
            return [];
        } catch (error) {
            console.warn('获取已生成页面失败:', error);
            return [];
        }
    }

    /**
     * 检查WebSocket连接状态
     * @returns {boolean} 是否连接正常
     */
    isWebSocketConnected() {
        return this.progressWebSocket && this.progressWebSocket.readyState === WebSocket.OPEN;
    }

    /**
     * 获取当前会话ID
     * @returns {string|null} 当前会话ID
     */
    getCurrentSessionId() {
        return this.currentSessionId;
    }

    /**
     * 强制重连指定会话的WebSocket（用户手动触发）
     * @param {string} sessionId - 会话ID
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<boolean>} 重连是否成功
     */
    async forceReconnect(sessionId, progressCallback = null) {
        try {
            if (progressCallback) {
                progressCallback({
                    stage: '手动重连',
                    message: '正在强制重新连接...',
                    progress: 10
                });
            }
            
            // 关闭现有连接
            this.closeProgressWebSocket();
            
            // 等待片刻
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 先恢复已生成的页面状态
            const existingPages = await this.getGeneratedPages(sessionId);
            console.log(`🔄 手动重连时获取到 ${existingPages.length} 个已生成页面`);
            
            if (progressCallback && existingPages.length > 0) {
                progressCallback({
                    stage: '恢复状态',
                    message: `已恢复 ${existingPages.length} 个之前生成的页面`,
                    progress: 30
                });
            }
            
            // 重新建立连接
            await this.connectProgressWebSocket(sessionId, progressCallback);
            
            if (progressCallback) {
                progressCallback({
                    stage: '重连成功',
                    message: 'WebSocket连接已重新建立',
                    progress: 50
                });
            }
            
            return true;
        } catch (error) {
            console.error('手动重连失败:', error);
            if (progressCallback) {
                progressCallback({
                    stage: '重连失败',
                    message: `重连失败: ${error.message}`,
                    progress: 0,
                    error: true
                });
            }
            return false;
        }
    }

    /**
     * 调用后端的重连状态同步（可选的额外同步方法）
     * @param {string} sessionId - 会话ID
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<Object>} 同步结果
     */
    async syncReconnectStatus(sessionId, progressCallback = null) {
        try {
            const response = await fetch(`${this.baseURL}/reconnect-session/${sessionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (progressCallback) {
                progressCallback({
                    stage: '状态同步',
                    message: result.message || '重连状态同步完成',
                    progress: result.progress || 50,
                    type: 'sync_status',
                    restored_pages: result.restored_pages || 0
                });
            }
            
            return result;
        } catch (error) {
            console.warn('重连状态同步失败:', error);
            if (progressCallback) {
                progressCallback({
                    stage: '同步失败',
                    message: `状态同步失败: ${error.message}`,
                    progress: 0,
                    error: true
                });
            }
            throw error;
        }
    }
}

export default SVGPPTService; 