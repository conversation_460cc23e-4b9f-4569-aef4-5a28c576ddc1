# -*- coding: utf-8 -*-
"""
LangGraph 增强系统 - 配置管理器

统一管理所有 YAML 配置文件
"""
import os
from typing import Dict, List, Any, Optional
import yaml
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器 - 统一管理所有 YAML 配置文件"""
    
    def __init__(self, base_path: str = None):
        """初始化配置管理器"""
        if base_path:
            self.base_path = base_path
        else:
            self.base_path = os.path.dirname(os.path.abspath(__file__))
        
        self.agents_config_path = os.path.join(self.base_path, "agents.yaml")
        self.teams_config_path = os.path.join(self.base_path, "teams.yaml")
        self.context_config_path = os.path.join(self.base_path, "context_config.yaml")

        self.agents_config = None
        self.teams_config = None
        self.context_config = None
        
        self.load_all_configs()
        logger.info(f"🔥 配置管理器初始化完成，配置目录: {self.base_path}")

    def load_all_configs(self):
        """加载所有配置文件"""
        self.load_agents_config(force_reload=True)
        self.load_teams_config(force_reload=True)
        self.load_context_config(force_reload=True)

    def _load_yaml_file(self, file_path: str) -> Optional[Dict]:
        """通用 YAML 文件加载器"""
        if not os.path.exists(file_path):
            logger.error(f"❌ 配置文件未找到: {file_path}")
            return None
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except yaml.YAMLError as e:
            logger.error(f"❌ 解析 YAML 文件失败 {file_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 加载配置文件时发生未知错误 {file_path}: {e}")
            return None

    def load_agents_config(self, force_reload: bool = False):
        """加载智能体配置"""
        if self.agents_config is None or force_reload:
            logger.info("🔄 重新加载智能体配置")
            config = self._load_yaml_file(self.agents_config_path)
            if config and self.validate_agents_config(config):
                self.agents_config = config
                logger.info(f"✅ 智能体配置验证通过: {len(config.get('agents', []))} 个智能体")

    def load_teams_config(self, force_reload: bool = False):
        """加载团队配置"""
        if self.teams_config is None or force_reload:
            logger.info("🔄 重新加载团队配置")
            config = self._load_yaml_file(self.teams_config_path)
            if config and self.validate_teams_config(config):
                self.teams_config = config
                logger.info(f"✅ 团队配置验证通过: {len(config.get('teams', []))} 个团队")

    def load_context_config(self, force_reload: bool = False):
        """加载上下文管理配置"""
        if self.context_config is None or force_reload:
            logger.info("🔄 重新加载上下文配置")
            config = self._load_yaml_file(self.context_config_path)
            if config:
                self.context_config = config.get('context_management', {})
                logger.info("✅ 上下文配置加载成功")

    def get_agent_config(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取指定智能体的配置，并合并基础提示词"""
        if self.agents_config is None:
            self.load_agents_config(force_reload=True)
        
        agent_config = self.agents_config.get('agents', {}).get(agent_id)
        if not agent_config:
            return None

        # 获取基础提示词
        base_prompt = self.agents_config.get('base_memory_prompt', [])
        
        if base_prompt:
            # 创建副本以避免修改原始配置
            agent_config = agent_config.copy()
            core_rules = agent_config.get('core_rules', [])
            
            # 🔥 修复：基础提示词在前，智能体规则在后，避免重复
            agent_config['core_rules'] = base_prompt + [rule for rule in core_rules if rule not in base_prompt]

        return agent_config

    def get_all_agent_ids(self) -> List[str]:
        """获取所有智能体的ID列表"""
        if self.agents_config is None:
            self.load_agents_config(force_reload=True)
        return list(self.agents_config.get('agents', {}).keys())

    def get_team_config(self, team_id: str) -> Optional[Dict[str, Any]]:
        """获取指定团队的配置，自动合并基础协作规则和记忆策略"""
        if self.teams_config is None:
            self.load_teams_config(force_reload=True)
        
        # 获取原始团队配置
        original_team_config = self.teams_config.get('teams', {}).get(team_id)
        if not original_team_config:
            return None
        
        # 创建配置副本以避免修改原始数据
        team_config = original_team_config.copy()
        
        # 获取基础协作规则
        base_rules = self.teams_config.get('base_collaboration_rules', [])
        team_rules = team_config.get('collaboration_rules', [])
        
        # 🧠 获取工作流类型对应的通用记忆策略
        workflow_type = team_config.get('workflow_type', 'sequential')
        memory_strategy_name = team_config.get('memory_strategy')
        if not memory_strategy_name:
            # 如果没有指定memory_strategy，使用workflow_type作为默认值
            memory_strategy_name = workflow_type
        
        # 从 universal_memory_strategies 获取策略模板
        universal_strategies = self.teams_config.get('universal_memory_strategies', {})
        workflow_memory_strategy = universal_strategies.get(memory_strategy_name, {})
        
        # 构建记忆策略规则列表（将所有core_patterns展开）
        memory_strategy_rules = []
        if workflow_memory_strategy:
            core_patterns = workflow_memory_strategy.get('core_patterns', {})
            # 替换 {{TEAM_ID}} 占位符为实际团队ID
            for pattern_name, pattern_rules in core_patterns.items():
                for rule in pattern_rules:
                    # 替换占位符
                    processed_rule = rule.replace('{{TEAM_ID}}', team_id)
                    memory_strategy_rules.append(processed_rule)
            
            # 添加 XML 示例（如果存在）
            xml_examples = workflow_memory_strategy.get('xml_examples', [])
            for example in xml_examples:
                processed_example = example.replace('{{TEAM_ID}}', team_id)
                memory_strategy_rules.append(processed_example)
        
        # 🔧 添加通用记忆工具（所有团队都需要）
        common_tools = self.teams_config.get('universal_memory_strategies', {}).get('common_tools', {})
        if common_tools:
            # 添加故障排除工具
            troubleshooting_tools = common_tools.get('troubleshooting', [])
            for tool in troubleshooting_tools:
                processed_tool = tool.replace('{{TEAM_ID}}', team_id)
                memory_strategy_rules.append(processed_tool)
            
            # 添加API优势说明
            api_benefits = common_tools.get('api_benefits', [])
            for benefit in api_benefits:
                processed_benefit = benefit.replace('{{TEAM_ID}}', team_id)
                memory_strategy_rules.append(processed_benefit)
        
        # 合并规则：基础规则 + 团队专业规则 + 通用记忆策略规则 + 通用记忆工具
        merged_rules = base_rules + team_rules
        if memory_strategy_rules:
            merged_rules.extend(memory_strategy_rules)
            team_config['collaboration_rules'] = merged_rules
            common_tools_count = len(common_tools.get('troubleshooting', [])) + len(common_tools.get('api_benefits', []))
            logger.debug(f"✅ 团队 {team_id} 完整构建协作规则: {len(base_rules)}条基础规则 + {len(team_rules)}条专业规则 + {len(memory_strategy_rules)}条{memory_strategy_name}策略 + {common_tools_count}条通用工具")
        else:
            team_config['collaboration_rules'] = merged_rules
            logger.debug(f"✅ 团队 {team_id} 合并协作规则: {len(base_rules)}条基础规则 + {len(team_rules)}条专业规则（无策略配置）")
        
        return team_config
        
    def get_all_team_ids(self) -> List[str]:
        """获取所有团队的ID列表"""
        if self.teams_config is None:
            self.load_teams_config(force_reload=True)
        return list(self.teams_config.get('teams', {}).keys())

    def get_context_config(self) -> Dict[str, Any]:
        """获取上下文管理配置"""
        if self.context_config is None:
            self.load_context_config(force_reload=True)
        return self.context_config or {}

    def validate_agents_config(self, config: Dict) -> bool:
        """验证智能体配置文件格式"""
        if 'agents' not in config or not isinstance(config['agents'], dict):
            logger.error("❌ 智能体配置必须包含一个 'agents' 字典")
            return False
        
        for agent_id, agent_config in config['agents'].items():
            required_fields = ['role_name', 'role_definition', 'description']
            for field in required_fields:
                if field not in agent_config:
                    logger.error(f"❌ 智能体 '{agent_id}' 缺少必要字段: {field}")
                    return False
        return True

    def validate_teams_config(self, config: Dict) -> bool:
        """验证团队配置文件格式"""
        if 'teams' not in config or not isinstance(config['teams'], dict):
            logger.error("❌ 团队配置必须包含一个 'teams' 字典")
            return False
            
        valid_workflow_types = ['sequential', 'parallel', 'conditional', 'planning_router']
        
        for team_id, team_config in config['teams'].items():
            required_fields = ['name', 'agents', 'workflow_type']
            for field in required_fields:
                if field not in team_config:
                    logger.error(f"❌ 团队 '{team_id}' 缺少必要字段: {field}")
                    return False
            
            if team_config['workflow_type'] not in valid_workflow_types:
                logger.error(f"❌ 团队 '{team_id}' 的 workflow_type 无效: {team_config['workflow_type']}")
                return False
        return True

    def get_universal_memory_strategies(self) -> Dict[str, Any]:
        """获取通用记忆策略配置"""
        if self.teams_config is None:
            self.load_teams_config(force_reload=True)
        return self.teams_config.get('universal_memory_strategies', {})
    
    def get_memory_strategy_for_team(self, team_id: str) -> Optional[Dict[str, Any]]:
        """获取指定团队的记忆策略配置"""
        team_config = self.get_team_config(team_id)
        if not team_config:
            return None
        
        memory_strategy_name = team_config.get('memory_strategy')
        if not memory_strategy_name:
            # 如果没有指定memory_strategy，使用workflow_type作为默认值
            memory_strategy_name = team_config.get('workflow_type', 'sequential')
        
        universal_strategies = self.get_universal_memory_strategies()
        strategy_config = universal_strategies.get(memory_strategy_name)
        
        if strategy_config:
            # 替换占位符{{TEAM_ID}}为实际团队ID
            strategy_copy = self._replace_team_id_in_strategy(strategy_config, team_id)
            return strategy_copy
        
        return None
    
    def _replace_team_id_in_strategy(self, strategy_config: Dict[str, Any], team_id: str) -> Dict[str, Any]:
        """递归替换策略配置中的{{TEAM_ID}}占位符"""
        import copy
        result = copy.deepcopy(strategy_config)
        
        def replace_in_value(value):
            if isinstance(value, str):
                return value.replace('{{TEAM_ID}}', team_id)
            elif isinstance(value, list):
                return [replace_in_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: replace_in_value(v) for k, v in value.items()}
            else:
                return value
        
        return replace_in_value(result)

    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        universal_strategies = self.get_universal_memory_strategies()
        return {
            "agents_count": len(self.get_all_agent_ids()),
            "teams_count": len(self.get_all_team_ids()),
            "context_config_loaded": self.context_config is not None,
            "universal_memory_strategies_count": len(universal_strategies),
            "available_memory_strategies": list(universal_strategies.keys())
        }

# 单例模式
_config_manager_instance = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager_instance
    if _config_manager_instance is None:
        _config_manager_instance = ConfigManager()
    return _config_manager_instance

__all__ = ['get_config_manager', 'ConfigManager'] 