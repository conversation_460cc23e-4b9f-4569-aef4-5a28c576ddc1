<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 颜色定义 -->
    <style type="text/css">
      /* 核心颜色 */
      .bg-color { fill: #000000; } /* 纯黑背景 */
      .highlight-color { fill: #E31937; } /* 特斯拉红高亮 */
      .text-primary-color { fill: #E0E0E0; } /* 近白色主文本 */
      .text-secondary-color { fill: #A0A0A0; } /* 浅灰色辅助文本 */
      .text-dark-gray { fill: #444444; } /* 深灰色元素 */
      .text-light-gray { fill: #666666; } /* 较浅灰色元素 */

      /* 字体定义 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* 字号和字重 */
      .hero-title { font-size: 140px; font-weight: 700; } /* 超大字体 */
      .main-title { font-size: 64px; font-weight: 700; } /* 主要标题 */
      .section-title { font-size: 48px; font-weight: 600; } /* 章节标题 */
      .content-title { font-size: 32px; font-weight: 500; } /* 内容标题 */
      .body-text { font-size: 24px; font-weight: 400; line-height: 1.5; } /* 正文 */
      .small-text { font-size: 18px; font-weight: 300; } /* 小字 */
      .caption-text { font-size: 16px; font-weight: 300; } /* 标注 */

      /* 文本行高和间距 */
      .line-height-normal { line-height: 1.4; }
      .letter-spacing-normal { letter-spacing: 0em; }

      /* 笔触颜色 */
      .stroke-highlight { stroke: #E31937; stroke-width: 2px; }
      .stroke-light-gray { stroke: #666666; stroke-width: 1px; }

      /* 渐变效果 */
      .gradient-red-fade {
        fill: url(#redFadeGradient);
      }
    </style>

    <!-- 红色透明度渐变 -->
    <linearGradient id="redFadeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#E31937" stop-opacity="0.1"/>
    </linearGradient>

    <!-- 滤镜：用于高亮元素的微弱发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="blur"/>
      <feFlood flood-color="#E31937" flood-opacity="0.6" result="color"/>
      <feComposite in="color" in2="blur" operator="in" result="glow"/>
      <feMerge>
        <feMergeNode in="glow"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 页面页脚的装饰线 -->
    <linearGradient id="footerLineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.0"/>
      <stop offset="25%" stop-color="#E31937" stop-opacity="0.5"/>
      <stop offset="75%" stop-color="#E31937" stop-opacity="0.5"/>
      <stop offset="100%" stop-color="#E31937" stop-opacity="0.0"/>
    </linearGradient>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 顶部导航/页码 -->
  <g id="header">
    <!-- Logo 占位符 -->
    <image x="80" y="60" width="160" height="40" xlink:href="{logo_url}" />
    <!-- 页面序号 -->
    <text x="1840" y="85" text-anchor="end" class="text-secondary-color small-text font-primary">4/10</text>
  </g>

  <!-- 主要内容区域 -->
  <g id="main-content" transform="translate(120, 180)">
    <!-- 标题和副标题 -->
    <text x="0" y="0" class="text-primary-color main-title font-primary" text-anchor="start">
      <tspan x="0" dy="0">{title}</tspan>
      <tspan x="0" dy="70" class="text-secondary-color content-title">{subtitle}</tspan>
    </text>

    <!-- 关键数据/超大数字强调 -->
    <text x="0" y="320" class="highlight-color hero-title font-primary" text-anchor="start" filter="url(#glow)">
      <tspan x="0" dy="0">85%</tspan>
      <tspan x="0" dy="120" class="text-primary-color section-title font-primary" style="font-size: 40px; font-weight: 500;">市场增长潜力</tspan>
      <tspan x="0" dy="50" class="text-secondary-color small-text font-secondary" style="font-size: 20px;">Market Growth Potential</tspan>
    </text>

    <!-- 内容区：正文和要点列表 -->
    <g transform="translate(700, 0)">
      <!-- 正文内容 -->
      <text x="0" y="0" class="text-primary-color body-text font-primary" text-anchor="start">
        <tspan x="0" dy="0">{content}</tspan>
        <tspan x="0" dy="36">我们深入分析了当前的市场趋势和未来预测，</tspan>
        <tspan x="0" dy="36">发现新兴技术和消费者行为的转变，</tspan>
        <tspan x="0" dy="36">为我们的创新解决方案提供了巨大机遇。</tspan>
        <tspan x="0" dy="36">此部分将详细阐述市场规模、增长驱动力</tspan>
        <tspan x="0" dy="36">以及竞争格局，确保您全面了解我们的业务环境。</tspan>
      </text>

      <!-- 要点列表 -->
      <g id="bullet-points" transform="translate(0, 240)">
        <text x="0" y="0" class="text-primary-color content-title font-primary">
          <tspan x="0" dy="0">核心洞察</tspan>
        </text>

        <!-- 第一个要点 -->
        <g transform="translate(0, 60)">
          <circle cx="0" cy="-6" r="6" class="highlight-color"/>
          <text x="20" y="0" class="text-primary-color body-text font-primary">
            <tspan x="20" dy="0">市场规模庞大和持续增长趋势。</tspan>
            <tspan x="20" dy="28" class="text-secondary-color small-text">Vast market size and sustained growth.</tspan>
          </text>
        </g>

        <!-- 第二个要点 -->
        <g transform="translate(0, 140)">
          <circle cx="0" cy="-6" r="6" class="highlight-color"/>
          <text x="20" y="0" class="text-primary-color body-text font-primary">
            <tspan x="20" dy="0">技术创新是主要增长驱动力。</tspan>
            <tspan x="20" dy="28" class="text-secondary-color small-text">Technological innovation as key growth driver.</tspan>
          </text>
        </g>

        <!-- 第三个要点 -->
        <g transform="translate(0, 220)">
          <circle cx="0" cy="-6" r="6" class="highlight-color"/>
          <text x="20" y="0" class="text-primary-color body-text font-primary">
            <tspan x="20" dy="0">竞争格局分析和我们的独特优势。</tspan>
            <tspan x="20" dy="28" class="text-secondary-color small-text">Competitive landscape and our unique advantages.</tspan>
          </text>
        </g>

        <!-- 第四个要点 -->
        <g transform="translate(0, 300)">
          <circle cx="0" cy="-6" r="6" class="highlight-color"/>
          <text x="20" y="0" class="text-primary-color body-text font-primary">
            <tspan x="20" dy="0">财务预测和投资回报率分析。</tspan>
            <tspan x="20" dy="28" class="text-secondary-color small-text">Financial forecasts and ROI analysis.</tspan>
          </text>
        </g>
      </g>
    </g>
  </g>

  <!-- 装饰元素：几何图形和数据图表占位符 -->
  <g id="decorative-elements">
    <!-- 左侧背景抽象线条 -->
    <path d="M-50 100 L 200 0 L 250 200 L 0 300 Z" fill="none" class="stroke-light-gray" opacity="0.3"/>
    <path d="M-80 500 L 150 400 L 200 600 L -30 700 Z" fill="none" class="stroke-light-gray" opacity="0.2"/>

    <!-- 右下角抽象数据图表轮廓 -->
    <g transform="translate(1400, 750)">
      <!-- 简单的折线图轮廓 -->
      <polyline points="0,100 50,40 100,80 150,20 200,60" fill="none" class="stroke-highlight"/>
      <circle cx="0" cy="100" r="4" class="highlight-color"/>
      <circle cx="50" cy="40" r="4" class="highlight-color"/>
      <circle cx="100" cy="80" r="4" class="highlight-color"/>
      <circle cx="150" cy="20" r="4" class="highlight-color"/>
      <circle cx="200" cy="60" r="4" class="highlight-color"/>
      <text x="100" y="150" text-anchor="middle" class="text-secondary-color small-text font-secondary">增长趋势分析</text>
    </g>

    <!-- 底部渐变装饰线 -->
    <rect x="0" y="1070" width="1920" height="10" fill="url(#footerLineGradient)"/>
  </g>

</svg>