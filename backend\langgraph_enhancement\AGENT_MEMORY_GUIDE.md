# 🧠 智能体记忆系统使用指南

## 📋 概述

LangGraph 增强系统为每个智能体提供了**专用记忆能力**，让智能体能够：
- 🧠 主动访问和管理自己的记忆
- 🔍 智能搜索历史信息
- 🔄 与其他智能体共享记忆
- 📊 跟踪交互统计和学习成果

## 🚀 快速开始

### 1. 启用智能体记忆

在 `agents.yaml` 配置文件中启用记忆功能：

```yaml
agents:
  my_agent:
    role_name: "我的智能助手"
    enable_memory_access: true  # 🔥 启用记忆访问
    memory_capacity: 10000     # 可选：设置记忆容量
    # ... 其他配置
```

### 2. 基本记忆操作

```python
# 创建智能体实例
agent = ContextOptimizedAgent(
    agent_id="my_agent",
    config=config,
    system_prompt="你是一个具有记忆能力的智能助手"
)

# 存储记忆
await agent.store_my_memory("user_name", "张三")
await agent.store_my_memory("preferences", {
    "language": "中文",
    "response_style": "详细"
})

# 获取记忆
user_name = await agent.get_my_memory("user_name")
preferences = await agent.get_my_memory("preferences", {})

# 列出所有记忆
memory_keys = await agent.list_my_memories()
print(f"我的记忆: {memory_keys}")
```

## 🧠 记忆管理API

### 基础操作

#### `store_my_memory(key, value)`
存储记忆到智能体专用空间
```python
# 存储简单值
await agent.store_my_memory("last_topic", "人工智能")

# 存储复杂对象
await agent.store_my_memory("user_profile", {
    "name": "李四",
    "interests": ["科技", "阅读"],
    "skill_level": "中级"
})
```

#### `get_my_memory(key, default=None)`
获取指定记忆，支持默认值
```python
# 获取存在的记忆
topic = await agent.get_my_memory("last_topic")

# 获取不存在的记忆，使用默认值
count = await agent.get_my_memory("interaction_count", 0)
```

#### `list_my_memories()`
获取所有记忆键名列表
```python
keys = await agent.list_my_memories()
print(f"共有 {len(keys)} 条记忆: {keys}")
```

#### `clear_my_memory(key=None)`
清除记忆（可选择清除所有或特定记忆）
```python
# 清除特定记忆
await agent.clear_my_memory("temporary_data")

# 清除所有记忆 ⚠️ 谨慎使用
await agent.clear_my_memory()
```

### 高级功能

#### `search_my_memories(pattern)`
基于关键词搜索记忆
```python
# 搜索包含"用户"的记忆
results = await agent.search_my_memories("用户")
print(f"找到 {len(results)} 条相关记忆")

# 搜索包含特定值的记忆
ai_related = await agent.search_my_memories("人工智能")
```

#### `get_my_memory_stats()`
获取记忆使用统计
```python
stats = await agent.get_my_memory_stats()
print(f"记忆统计: {stats}")
# 输出示例:
# {
#   "memory_enabled": True,
#   "total_memories": 15,
#   "memory_keys": ["user_name", "preferences", ...],
#   "most_accessed": ("user_name", 8),
#   "total_access_count": 42
# }
```

## 🔄 记忆增强处理

### 使用记忆增强的处理流程

```python
# 使用记忆增强处理（推荐）
result = await agent.process_with_memory_enhancement(state)

# vs 标准处理（不使用记忆）
result = await agent.process_with_context_optimization(state)
```

### 记忆增强处理的工作流程

1. **自动加载相关记忆**：基于输入内容搜索相关历史信息
2. **增强系统提示**：将记忆信息添加到系统提示中
3. **执行AI处理**：使用增强后的上下文进行处理
4. **更新记忆**：自动保存新的交互记录和学习成果

### 自动记忆管理

记忆增强处理会自动维护以下记忆类型：

- `user_preferences`: 用户偏好设置
- `interaction_history`: 交互历史记录（最近50次）
- `last_context`: 最后一次的完整上下文
- `specialized_knowledge`: 专业知识积累
- `processing_stats`: 处理统计信息

## 🔗 跨智能体记忆共享

### 基本记忆共享

```python
from backend.langgraph_enhancement.context.memory_manager import get_memory_manager

memory_manager = get_memory_manager()

# 在两个智能体间共享记忆
success = await memory_manager.share_memory_between_agents(
    source_agent_id="researcher",
    target_agent_id="writer", 
    memory_key="research_findings"
)
```

### 创建共享记忆

```python
# 为多个智能体创建共享记忆
await memory_manager.create_shared_memory(
    memory_key="project_requirements",
    value={
        "deadline": "2024-12-31",
        "budget": 100000,
        "priority": "high"
    },
    agent_ids=["researcher", "writer", "reviewer"]
)
```

### 团队广播记忆

```python
# 向整个团队广播重要信息
broadcast_count = await memory_manager.broadcast_memory_to_team(
    sender_agent_id="project_manager",
    memory_key="deadline_update", 
    value="项目截止日期提前到本周五",
    team_agent_ids=["researcher", "writer", "reviewer", "tester"]
)

print(f"成功广播给 {broadcast_count} 个团队成员")
```

### 获取共享记忆

```python
# 获取智能体的所有共享记忆
shared_memories = await memory_manager.get_shared_memories_for_agent("writer")
print(f"共享记忆: {shared_memories}")
```

## 💡 实际应用示例

### 示例1：个性化客服智能体

```python
class CustomerServiceAgent(ContextOptimizedAgent):
    
    async def handle_customer_query(self, customer_id: str, query: str):
        """处理客户查询，使用个性化记忆"""
        
        # 获取客户历史记录
        customer_history = await self.get_my_memory(f"customer_{customer_id}", {
            "name": "未知",
            "preference": "标准服务",
            "previous_issues": [],
            "satisfaction_score": 5
        })
        
        # 构建个性化响应
        state = {
            "messages": [
                {
                    "role": "user", 
                    "content": f"客户 {customer_history['name']} 询问: {query}"
                }
            ]
        }
        
        # 使用记忆增强处理
        result = await self.process_with_memory_enhancement(state)
        
        # 更新客户记录
        customer_history["previous_issues"].append({
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "response": result.get("response", "")
        })
        
        # 只保留最近10次交互
        if len(customer_history["previous_issues"]) > 10:
            customer_history["previous_issues"] = customer_history["previous_issues"][-10:]
        
        await self.store_my_memory(f"customer_{customer_id}", customer_history)
        
        return result
```

### 示例2：学习型研究智能体

```python
class ResearchAgent(ContextOptimizedAgent):
    
    async def conduct_research(self, topic: str):
        """进行研究，积累专业知识"""
        
        # 检查是否有相关的历史研究
        previous_research = await self.search_my_memories(topic)
        
        if previous_research:
            print(f"发现 {len(previous_research)} 条相关历史研究")
            
            # 基于历史研究优化查询
            enhanced_prompt = f"基于以往研究经验，深入分析 {topic}"
        else:
            enhanced_prompt = f"全面研究 {topic}"
        
        state = {"messages": [{"role": "user", "content": enhanced_prompt}]}
        result = await self.process_with_memory_enhancement(state)
        
        # 保存研究成果
        research_findings = {
            "topic": topic,
            "findings": result.get("content", ""),
            "timestamp": datetime.now().isoformat(),
            "related_topics": self._extract_related_topics(result)
        }
        
        await self.store_my_memory(f"research_{topic}", research_findings)
        
        # 更新专业知识库
        knowledge_base = await self.get_my_memory("specialized_knowledge", {})
        knowledge_base[topic] = research_findings
        await self.store_my_memory("specialized_knowledge", knowledge_base)
        
        return result
    
    def _extract_related_topics(self, result):
        """从研究结果中提取相关主题"""
        # 简单实现，实际可以使用NLP技术
        content = result.get("content", "").lower()
        topics = []
        # ... 主题提取逻辑
        return topics
```

### 示例3：协作写作团队

```python
async def collaborative_writing_workflow():
    """协作写作工作流示例"""
    
    # 创建写作团队
    researcher = ContextOptimizedAgent("researcher", research_config, "你是研究员")
    writer = ContextOptimizedAgent("writer", writer_config, "你是写作者") 
    editor = ContextOptimizedAgent("editor", editor_config, "你是编辑")
    
    memory_manager = get_memory_manager()
    
    # 1. 研究阶段
    research_result = await researcher.process_with_memory_enhancement({
        "messages": [{"role": "user", "content": "研究人工智能的发展历程"}]
    })
    
    # 2. 共享研究成果给写作者
    await memory_manager.share_memory_between_agents(
        "researcher", "writer", "last_context", "research_input"
    )
    
    # 3. 写作阶段  
    writing_result = await writer.process_with_memory_enhancement({
        "messages": [{"role": "user", "content": "基于研究成果撰写文章"}]
    })
    
    # 4. 编辑阶段
    await memory_manager.share_memory_between_agents(
        "writer", "editor", "last_context", "draft_content"
    )
    
    final_result = await editor.process_with_memory_enhancement({
        "messages": [{"role": "user", "content": "编辑和优化文章"}]
    })
    
    # 5. 团队广播最终成果
    await memory_manager.broadcast_memory_to_team(
        "editor", "project_completion",
        {"status": "completed", "final_article": final_result},
        ["researcher", "writer", "editor"]
    )
    
    return final_result
```

## ⚡ 性能优化建议

### 记忆容量管理

```python
# 在智能体配置中设置合理的记忆容量
agents:
  my_agent:
    enable_memory_access: true
    memory_capacity: 5000  # 根据使用场景调整
```

### 定期清理无用记忆

```python
async def cleanup_old_memories(agent):
    """清理过期记忆"""
    
    stats = await agent.get_my_memory_stats()
    if stats["total_memories"] > 1000:
        
        # 保留重要记忆，清理临时数据
        important_keys = ["user_preferences", "specialized_knowledge"]
        all_keys = await agent.list_my_memories()
        
        for key in all_keys:
            if key.startswith("temp_") or key.startswith("cache_"):
                await agent.clear_my_memory(key)
```

### 批量操作优化

```python
# 避免在循环中频繁访问记忆
# ❌ 低效方式
for item in items:
    value = await agent.get_my_memory(f"item_{item.id}")

# ✅ 高效方式  
all_memories = {}
for key in await agent.list_my_memories():
    if key.startswith("item_"):
        all_memories[key] = await agent.get_my_memory(key)
```

## 🔧 配置选项

### 全局记忆配置

在 `context_config.yaml` 中配置全局记忆设置：

```yaml
context_management:
  # 记忆相关配置
  memory_settings:
    default_capacity: 8000
    cleanup_threshold: 10000
    access_tracking: true
    compression_enabled: true
```

### 智能体特定配置

在 `agents.yaml` 中为每个智能体定制记忆配置：

```yaml
agents:
  data_analyst:
    enable_memory_access: true
    memory_capacity: 15000  # 数据分析需要更大容量
    memory_persistence: true
    auto_cleanup: true
    
  chat_assistant:
    enable_memory_access: true  
    memory_capacity: 5000   # 聊天助手需要较小容量
    memory_sharing: ["team_lead"]  # 可以共享给团队领导
```

## 🚨 注意事项

### 内存管理

- 记忆数据存储在内存中，重启应用会丢失
- 对于持久化需求，可以扩展检查点系统
- 大量记忆会影响性能，建议定期清理

### 隐私和安全

- 敏感信息不要存储在记忆中
- 跨智能体共享时注意数据权限
- 定期审计记忆内容

### 错误处理

```python
# 始终处理记忆访问可能的异常
try:
    value = await agent.get_my_memory("critical_data")
    if value is None:
        # 处理记忆不存在的情况
        value = await load_default_value()
except Exception as e:
    logger.error(f"记忆访问失败: {e}")
    # 提供降级方案
    value = fallback_value
```

## 📚 更多资源

- [LangGraph 集成文档](./README.md)
- [上下文管理指南](./CONTEXT_MANAGEMENT_GUIDE.md)
- [多智能体协作示例](./examples/)
- [性能优化指南](./PERFORMANCE_GUIDE.md)

## 🎯 总结

智能体记忆系统为 LangGraph 多智能体框架提供了强大的个性化和协作能力：

✅ **个性化服务**：记住用户偏好和历史交互  
✅ **知识积累**：持续学习和改进响应质量  
✅ **团队协作**：智能体间无缝信息共享  
✅ **性能优化**：智能压缩和清理机制  

通过合理使用这些记忆功能，您可以构建出真正智能和个性化的AI应用系统。