# -*- coding: utf-8 -*-
"""创建jieba混合词典"""

import json
import jieba
from collections import defaultdict

# 加载jieba词典
jieba_dict = {}
dict_path = jieba.get_dict_file().name

print("📚 加载jieba词典...")
with open(dict_path, 'r', encoding='utf-8') as f:
    for line in f:
        parts = line.strip().split(' ')
        if len(parts) >= 2:
            word = parts[0]
            freq = int(parts[1])
            pos = parts[2] if len(parts) > 2 else 'x'
            jieba_dict[word] = {"freq": freq, "pos": pos}

print(f"✅ 加载完成: {len(jieba_dict)} 词条")

# 构建语义分类
semantic_dict = defaultdict(list)

# 1. 任务动词
print("\n🔍 筛选任务动词...")
task_keywords = ["分析", "生成", "创建", "计算", "设计", "开发", "研究", "评估", "规划", "编写", "处理", "优化"]
for word, info in jieba_dict.items():
    if info["pos"] in ["v", "vn"] and info["freq"] >= 1000:
        for keyword in task_keywords:
            if keyword in word:
                semantic_dict["task_verbs"].append({
                    "word": word,
                    "freq": info["freq"],
                    "pos": info["pos"]
                })
                break

# 2. 问候语
print("🔍 筛选问候语...")
greeting_keywords = ["你好", "您好", "早上好", "晚上好", "再见", "欢迎"]
for word, info in jieba_dict.items():
    if info["freq"] >= 100:
        for keyword in greeting_keywords:
            if keyword == word or keyword in word:
                semantic_dict["greeting_words"].append({
                    "word": word,
                    "freq": info["freq"],
                    "pos": info["pos"]
                })
                break

# 3. 疑问词
print("🔍 筛选疑问词...")
question_keywords = ["什么", "怎么", "为什么", "哪里", "谁", "何时", "如何", "多少"]
for word, info in jieba_dict.items():
    if info["freq"] >= 1000:
        for keyword in question_keywords:
            if keyword == word:
                semantic_dict["question_words"].append({
                    "word": word,
                    "freq": info["freq"],
                    "pos": info["pos"]
                })
                break

# 4. 领域名词
print("🔍 筛选领域名词...")
domain_keywords = ["技术", "市场", "数据", "系统", "产品", "用户", "服务", "人工智能", "机器学习"]
for word, info in jieba_dict.items():
    if info["pos"] in ["n", "nz", "nt"] and info["freq"] >= 2000:
        for keyword in domain_keywords:
            if keyword in word:
                semantic_dict["domain_nouns"].append({
                    "word": word,
                    "freq": info["freq"],
                    "pos": info["pos"]
                })
                break

# 排序并限制数量
for category in semantic_dict:
    semantic_dict[category].sort(key=lambda x: x["freq"], reverse=True)
    semantic_dict[category] = semantic_dict[category][:50]  # 每类保留前50个

# 创建混合词典
hybrid_dict = {
    "metadata": {
        "source": "jieba + semantic mapping",
        "version": "1.0",
        "total_words": sum(len(words) for words in semantic_dict.values())
    },
    "semantic_categories": {}
}

# 转换格式
for semantic_type, word_list in semantic_dict.items():
    hybrid_dict["semantic_categories"][semantic_type] = {
        "words": [w["word"] for w in word_list],
        "word_info": {w["word"]: {"freq": w["freq"], "pos": w["pos"]} for w in word_list},
        "total_count": len(word_list)
    }

# 保存文件
with open("hybrid_dictionary.json", 'w', encoding='utf-8') as f:
    json.dump(hybrid_dict, f, ensure_ascii=False, indent=2)

# 打印统计
print("\n📊 混合词典统计:")
print(f"  总词汇数: {hybrid_dict['metadata']['total_words']}")
for cat, info in hybrid_dict['semantic_categories'].items():
    print(f"  {cat}: {info['total_count']} 词")

print("\n📝 词典示例:")
for cat, info in hybrid_dict['semantic_categories'].items():
    print(f"\n{cat} (前10个):")
    print(f"  {', '.join(info['words'][:10])}")

print("\n✅ 混合词典已保存到 hybrid_dictionary.json") 