<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变定义 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 文本渐变定义 -->
    <linearGradient id="textGradient" x1="0" y1="0" x2="0" y2="100" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E3A8A"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 文本阴影滤镜定义 -->
    <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="8" flood-color="#000" flood-opacity="0.15"/>
    </filter>
  </defs>

  <style>
    /* 全局样式定义 */
    .background {
      fill: url(#backgroundGradient);
    }
    .main-title-cn {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-weight: 700; /* bold */
      font-size: 80px; /* 强调超大字体 */
      fill: url(#textGradient); /* 应用文本渐变 */
    }
    .main-title-en {
      font-family: 'Segoe UI', sans-serif;
      font-weight: 300; /* light */
      font-size: 40px; /* 英文小字作为点缀 */
      fill: #475569; /* 辅助色 */
    }
    .subtitle-text {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-weight: 400; /* normal */
      font-size: 28px; /* 内容标题大小 */
      fill: #64748B; /* 次要文字色 */
    }
    .decorative-shape-primary {
      fill: #1E40AF; /* 主色 */
    }
    .decorative-shape-accent {
      fill: #3B82F6; /* 强调色 */
    }
    .decorative-line-stroke {
      stroke: #7DD3FC; /* 悬停色，用于装饰线 */
      stroke-width: 3; /* 描边宽度 */
      fill: none;
    }
    .icon-stroke {
      stroke: #3B82F6; /* 强调色 */
      stroke-width: 2;
      fill: none;
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="background"/>

  <!-- 顶部和底部装饰元素 - 几何图形装饰 -->
  <rect x="0" y="0" width="400" height="200" class="decorative-shape-primary" opacity="0.05" />
  <rect x="50" y="50" width="300" height="150" class="decorative-shape-accent" opacity="0.03" />

  <rect x="1520" y="880" width="400" height="200" class="decorative-shape-primary" opacity="0.05" />
  <rect x="1570" y="930" width="300" height="150" class="decorative-shape-accent" opacity="0.03" />

  <!-- 中心内容区域 - 章节标题和描述 -->
  <g transform="translate(960, 540)" filter="url(#textShadow)">
    <!-- 主标题 - 中文大字体粗体，英文小字点缀 -->
    <text text-anchor="middle" dominant-baseline="central">
      <tspan x="0" y="-50" class="main-title-cn">{title}</tspan>
      <tspan x="0" y="40" class="main-title-en">{subtitle}</tspan>
    </text>

    <!-- 副标题 / 章节描述 -->
    <text x="0" y="150" text-anchor="middle" class="subtitle-text">
      <tspan x="0" dy="0">{content}</tspan>
    </text>
  </g>

  <!-- 过渡性的装饰元素 - 简洁的勾线图形化 -->
  <!-- 左侧波浪线 -->
  <path d="M0 300 C 200 200, 400 400, 600 300 S 800 200, 960 300" class="decorative-line-stroke" opacity="0.3" />
  <path d="M0 350 C 180 250, 360 450, 540 350 S 720 250, 900 350" class="decorative-line-stroke" opacity="0.2" />

  <!-- 右侧波浪线 -->
  <path d="M1920 780 C 1720 880, 1520 680, 1320 780 S 1120 880, 960 780" class="decorative-line-stroke" opacity="0.3" />
  <path d="M1920 730 C 1740 830, 1560 630, 1380 730 S 1200 830, 1020 730" class="decorative-line-stroke" opacity="0.2" />

  <!-- 抽象图标/几何元素 - 商务图标风格 -->
  <g transform="translate(150, 800)">
    <rect x="0" y="0" width="100" height="80" rx="8" ry="8" class="icon-stroke" />
    <line x1="10" y1="70" x2="30" y2="40" class="icon-stroke" />
    <line x1="30" y1="40" x2="60" y2="60" class="icon-stroke" />
    <line x1="60" y1="60" x2="90" y2="20" class="icon-stroke" />
  </g>

  <g transform="translate(1700, 100) scale(-1, 1)"> <!-- 翻转以用于另一侧 -->
    <rect x="0" y="0" width="100" height="80" rx="8" ry="8" class="icon-stroke" />
    <line x1="10" y1="70" x2="30" y2="40" class="icon-stroke" />
    <line x1="30" y1="40" x2="60" y2="60" class="icon-stroke" />
    <line x1="60" y1="60" x2="90" y2="20" class="icon-stroke" />
  </g>

  <!-- 页面序号和日期 -->
  <text x="1840" y="1020" text-anchor="end" class="subtitle-text">
    <tspan>{date}</tspan> | Page 3/10
  </text>

  <!-- Logo 占位符 (左上角) -->
  <image href="{logo_url}" x="80" y="60" width="120" height="60" />

</svg>