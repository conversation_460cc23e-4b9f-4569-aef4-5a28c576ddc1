<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" lang="zh">
  <defs>
    <!-- Color Palette - Defined as CSS variables for easy editing and consistency -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE; /* From design_guidelines */
        --success-color: #10B981; /* From design_guidelines */
        --error-color: #EF4444; /* From design_guidelines */
      }

      /* Fonts - Using generic fallbacks for broader compatibility */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights - Based on design_guidelines */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.2; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.3; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.5; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Text Colors */
      .text-primary { fill: var(--text-primary); }
      .text-secondary { fill: var(--text-secondary); }
      .text-accent { fill: var(--accent-color); }
      .text-light { fill: var(--text-light); }
      .text-white { fill: #FFFFFF; }

      /* Card Styles - Applies border, background, and shadow */
      .card {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        filter: url(#dropShadow); /* Apply the defined shadow filter */
      }

      /* Icon Styles - Stroke for outline icons, fill for solid accents */
      .icon-stroke { stroke: var(--accent-color); stroke-width: 2; fill: none; }
      .icon-fill { fill: var(--accent-color); } /* Solid fill for icons */

      /* Layout Helpers (for clarity, though direct positioning is used) */
      .margin-h { transform: translateX(80px); }
      .margin-v { transform: translateY(60px); }
    </style>

    <!-- Gradients - Defined as per design_guidelines -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Filter for subtle drop shadow on cards -->
    <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="4"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon Symbols - Reusable SVG icons for consistency and smaller file size -->
    <!-- Checkmark Icon -->
    <symbol id="icon-checkmark" viewBox="0 0 24 24">
      <path d="M5 13l4 4L19 7" class="icon-stroke"/>
    </symbol>
    <!-- Target Icon -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" class="icon-stroke"/>
      <circle cx="12" cy="12" r="6" class="icon-stroke"/>
      <circle cx="12" cy="12" r="2" class="icon-fill"/>
    </symbol>
    <!-- Bar Chart Icon -->
    <symbol id="icon-barchart" viewBox="0 0 24 24">
      <rect x="4" y="12" width="4" height="8" rx="1" class="icon-stroke"/>
      <rect x="10" y="8" width="4" height="12" rx="1" class="icon-stroke"/>
      <rect x="16" y="4" width="4" height="16" rx="1" class="icon-stroke"/>
    </symbol>
    <!-- Lightbulb Icon (for differentiators) -->
    <symbol id="icon-lightbulb" viewBox="0 0 24 24">
      <path d="M9 18h6m-3 0v-2m-3.5-6.5a4.5 4.5 0 117 0V14h-7v-2.5z" class="icon-stroke"/>
      <path d="M12 21a2 2 0 01-2-2h4a2 2 0 01-2 2z" class="icon-stroke"/>
    </symbol>
    <!-- Arrow Right Icon (for central emphasis) -->
    <symbol id="icon-arrow-right" viewBox="0 0 24 24">
      <path d="M5 12h14m-7-7l7 7-7 7" class="icon-stroke"/>
    </symbol>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-color)"/>

  <!-- Decorative Elements - Top Left Corner (subtle geometric shapes with gradients) -->
  <rect x="0" y="0" width="200" height="200" fill="url(#primaryGradient)" opacity="0.1" rx="20"/>
  <circle cx="180" cy="180" r="80" fill="var(--accent-color)" opacity="0.05"/>

  <!-- Main Content Group - Positioned with page margins -->
  <g transform="translate(80, 60)">
    <!-- Header Section -->
    <text x="0" y="30" class="caption-text text-secondary font-secondary">商业计划书 / 页面 7/10</text>
    <text x="1760" y="30" text-anchor="end" class="caption-text text-secondary font-secondary">{date}</text>
    
    <!-- Logo Placeholder (Replace with actual image if needed) -->
    <rect x="0" y="60" width="120" height="40" fill="var(--text-light)" rx="8"/>
    <text x="60" y="87" text-anchor="middle" class="small-text text-white font-primary">LOGO</text>
    <!-- Example for actual logo: <image x="0" y="60" width="120" height="40" xlink:href="{logo_url}"/> -->

    <!-- Main Title and Subtitle -->
    <text x="880" y="100" text-anchor="middle" class="main-title text-primary font-primary">{title}</text>
    <text x="880" y="155" text-anchor="middle" class="content-title text-secondary font-secondary">{subtitle}</text>

    <!-- Comparison Section - Two large cards side-by-side -->
    <g transform="translate(0, 220)">
      <!-- Left Card: Option A -->
      <rect x="0" y="0" width="700" height="600" rx="12" class="card"/>
      <text x="350" y="60" text-anchor="middle" class="section-title text-primary font-primary">方案A</text>
      <text x="350" y="100" text-anchor="middle" class="body-text text-secondary font-secondary">Option A</text>

      <!-- Content for Option A -->
      <g transform="translate(60, 150)">
        <use xlink:href="#icon-checkmark" x="0" y="0" width="32" height="32" class="icon-fill"/>
        <text x="50" y="25" class="body-text text-primary font-primary">核心优势一</text>
        <text x="50" y="55" class="small-text text-secondary font-secondary">Key Advantage One</text>

        <use xlink:href="#icon-checkmark" x="0" y="80" width="32" height="32" class="icon-fill"/>
        <text x="50" y="105" class="body-text text-primary font-primary">市场定位明确</text>
        <text x="50" y="135" class="small-text text-secondary font-secondary">Clear Market Positioning</text>

        <use xlink:href="#icon-checkmark" x="0" y="160" width="32" height="32" class="icon-fill"/>
        <text x="50" y="185" class="body-text text-primary font-primary">技术领先和创新</text>
        <text x="50" y="215" class="small-text text-secondary font-secondary">Leading Technology and Innovation</text>

        <use xlink:href="#icon-checkmark" x="0" y="240" width="32" height="32" class="icon-fill"/>
        <text x="50" y="265" class="body-text text-primary font-primary">稳健的财务结构</text>
        <text x="50" y="295" class="small-text text-secondary font-secondary">Robust Financial Structure</text>

        <use xlink:href="#icon-barchart" x="0" y="320" width="32" height="32" class="icon-stroke"/>
        <text x="50" y="345" class="body-text text-primary font-primary">详细市场数据</text>
        <text x="50" y="375" class="small-text text-secondary font-secondary">Detailed Market Data</text>
      </g>

      <!-- Central Difference Indicator - Visually emphasizes the comparison -->
      <g transform="translate(730, 200)">
        <circle cx="50" cy="50" r="50" fill="url(#accentGradient)" opacity="0.8"/>
        <use xlink:href="#icon-arrow-right" x="25" y="25" width="50" height="50" class="icon-stroke" stroke="white"/>
      </g>

      <!-- Right Card: Option B -->
      <rect x="800" y="0" width="700" height="600" rx="12" class="card"/>
      <text x="1150" y="60" text-anchor="middle" class="section-title text-primary font-primary">方案B</text>
      <text x="1150" y="100" text-anchor="middle" class="body-text text-secondary font-secondary">Option B</text>

      <!-- Content for Option B -->
      <g transform="translate(860, 150)">
        <use xlink:href="#icon-checkmark" x="0" y="0" width="32" height="32" class="icon-fill"/>
        <text x="50" y="25" class="body-text text-primary font-primary">核心优势二</text>
        <text x="50" y="55" class="small-text text-secondary font-secondary">Key Advantage Two</text>

        <use xlink:href="#icon-checkmark" x="0" y="80" width="32" height="32" class="icon-fill"/>
        <text x="50" y="105" class="body-text text-primary font-primary">广泛的客户基础</text>
        <text x="50" y="135" class="small-text text-secondary font-secondary">Broad Customer Base</text>

        <use xlink:href="#icon-checkmark" x="0" y="160" width="32" height="32" class="icon-fill"/>
        <text x="50" y="185" class="body-text text-primary font-primary">高效的运营模式</text>
        <text x="50" y="215" class="small-text text-secondary font-secondary">Efficient Operating Model</text>

        <use xlink:href="#icon-checkmark" x="0" y="240" width="32" height="32" class="icon-fill"/>
        <text x="50" y="265" class="body-text text-primary font-primary">灵活的市场策略</text>
        <text x="50" y="295" class="small-text text-secondary font-secondary">Flexible Market Strategy</text>

        <use xlink:href="#icon-target" x="0" y="320" width="32" height="32" class="icon-stroke"/>
        <text x="50" y="345" class="body-text text-primary font-primary">战略合作展望</text>
        <text x="50" y="375" class="small-text text-secondary font-secondary">Strategic Partnership Outlook</text>
      </g>
    </g>

    <!-- Key Differences / Differentiators Section - Highlighted area for conclusions -->
    <g transform="translate(0, 830)">
      <rect x="0" y="0" width="1760" height="150" rx="12" fill="var(--container-background)" filter="url(#dropShadow)"/>
      <text x="880" y="45" text-anchor="middle" class="content-title text-primary font-primary">核心差异点</text>
      <text x="880" y="75" text-anchor="middle" class="small-text text-secondary font-secondary">Key Differentiators</text>

      <!-- Difference Point 1 -->
      <g transform="translate(80, 105)">
        <use xlink:href="#icon-lightbulb" x="0" y="0" width="24" height="24" class="icon-fill"/>
        <text x="35" y="20" class="body-text text-primary font-primary">在市场渗透方面，方案A更注重深度，方案B更注重广度。</text>
      </g>
      <!-- Difference Point 2 -->
      <g transform="translate(900, 105)">
        <use xlink:href="#icon-lightbulb" x="0" y="0" width="24" height="24" class="icon-fill"/>
        <text x="35" y="20" class="body-text text-primary font-primary">风险评估上，方案A侧重技术风险，方案B侧重运营风险。</text>
      </g>
    </g>
    
    <!-- Footer / Author Information -->
    <text x="0" y="1000" class="caption-text text-secondary font-secondary">© 2023 {author}</text>
    <text x="1760" y="1000" text-anchor="end" class="caption-text text-secondary font-secondary">版权所有</text>
  </g>
  
  <!-- Decorative Elements - Bottom Right Corner (subtle geometric shapes with gradients) -->
  <rect x="1720" y="880" width="200" height="200" fill="url(#accentGradient)" opacity="0.1" rx="20"/>
  <circle cx="1740" cy="900" r="80" fill="var(--primary-color)" opacity="0.05"/>

</svg>