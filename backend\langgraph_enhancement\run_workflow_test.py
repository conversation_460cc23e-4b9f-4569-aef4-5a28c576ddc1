#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 快速工作流测试运行器 
测试三种工作流模式的基本功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from test_team_workflows import TeamWorkflowTester
    print("✅ 成功导入测试模块")
except ImportError as e:
    print(f"❌ 导入测试模块失败: {e}")
    print("📝 创建简化测试...")

async def simple_workflow_test():
    """简化的工作流测试"""
    print("🔥 简化工作流模式测试")
    print("="*50)
    
    try:
        # 模拟导入工作流引擎
        from langgraph_enhancement.core.workflow_engine import execute_team_workflow
        print("✅ 工作流引擎导入成功")
        
        # 测试数据
        test_cases = [
            {
                "name": "Sequential 模式测试",
                "team": "market_intelligence_team",
                "input": {
                    "user_message": "分析AI市场趋势", 
                    "user_id": "test_001",
                    "conversation_id": "test_conv_1",
                    "message_count": 1,
                    "timestamp": datetime.now().isoformat()
                }
            },
            {
                "name": "Parallel 模式测试", 
                "team": "parallel_development_team",
                "input": {
                    "user_message": "开发用户系统",
                    "user_id": "test_002", 
                    "conversation_id": "test_conv_2",
                    "message_count": 2,
                    "timestamp": datetime.now().isoformat()
                }
            },
            {
                "name": "Conditional 模式测试",
                "team": "intelligent_routing_team", 
                "input": {
                    "user_message": "我需要开发Python代码",
                    "user_id": "test_003",
                    "conversation_id": "test_conv_3", 
                    "message_count": 1,
                    "timestamp": datetime.now().isoformat()
                }
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n🧪 {test_case['name']}")
            print("-" * 30)
            
            try:
                start_time = datetime.now()
                
                # 执行工作流
                result = await execute_team_workflow(
                    team_name=test_case["team"],
                    input_data=test_case["input"],
                    config={"test_mode": True}
                )
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if result:
                    print(f"✅ 测试通过 - 执行时间: {execution_time:.2f}s")
                    print(f"📊 工作流类型: {result.get('workflow_type', 'unknown')}")
                    print(f"🤖 执行智能体: {result.get('agents_executed', [])}")
                    
                    results.append({
                        "test": test_case["name"],
                        "status": "成功",
                        "time": execution_time,
                        "details": result
                    })
                else:
                    print("❌ 测试失败 - 无返回结果")
                    results.append({
                        "test": test_case["name"], 
                        "status": "失败",
                        "time": 0,
                        "details": {}
                    })
                    
            except Exception as e:
                print(f"❌ 测试失败: {str(e)}")
                results.append({
                    "test": test_case["name"],
                    "status": f"异常: {str(e)}",
                    "time": 0,
                    "details": {}
                })
        
        # 生成总结报告
        print("\n" + "="*50)
        print("📋 测试总结报告")
        print("="*50)
        
        success_count = len([r for r in results if r["status"] == "成功"])
        total_time = sum([r["time"] for r in results])
        
        for result in results:
            status_icon = "✅" if result["status"] == "成功" else "❌"
            print(f"{status_icon} {result['test']}: {result['status']} ({result['time']:.2f}s)")
        
        print(f"\n🎯 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        print(f"⏱️ 总执行时间: {total_time:.2f}s")
        
        if success_count == len(results):
            print("🎉 所有工作流模式测试通过！")
            return True
        else:
            print("⚠️ 部分测试失败，请检查配置")
            return False
            
    except ImportError as e:
        print(f"❌ 工作流引擎导入失败: {e}")
        print("💡 请确保后端环境正确配置")
        return False
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False


async def check_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查配置文件
    config_files = [
        "config/agents.yaml",
        "config/teams.yaml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} 存在")
        else:
            print(f"❌ {config_file} 不存在")
            return False
    
    # 检查核心模块
    try:
        import langgraph_enhancement.core.workflow_engine
        print("✅ 工作流引擎模块可用")
    except ImportError as e:
        print(f"❌ 工作流引擎模块导入失败: {e}")
        return False
    
    return True


async def main():
    """主函数"""
    print("🔥 JIMU 工作流测试器")
    print("="*50)
    
    # 检查环境
    if not await check_environment():
        print("❌ 环境检查失败，请检查配置")
        return False
    
    # 运行测试
    try:
        # 优先使用完整测试套件
        tester = TeamWorkflowTester()
        success = await tester.run_all_tests()
        return success
        
    except Exception as e:
        print(f"⚠️ 完整测试失败，切换简化测试: {e}")
        # 降级到简化测试
        return await simple_workflow_test()


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n🎉 测试完成！")
            sys.exit(0)
        else:
            print("\n❌ 测试失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试程序异常: {e}")
        sys.exit(1) 