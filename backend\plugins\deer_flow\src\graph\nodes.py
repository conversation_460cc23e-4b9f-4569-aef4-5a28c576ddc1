# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import asyncio
import json
import logging
import os
import time
from typing import Annotated, Literal, Callable, Any

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.types import Command, interrupt
from langchain_mcp_adapters.client import MultiServerMCPClient

from backend.plugins.deer_flow.src.agents import create_agent
from backend.plugins.deer_flow.src.tools.search import LoggedTavilySearch
from backend.plugins.deer_flow.src.tools import (
    crawl_tool,
    get_web_search_tool,
    get_retriever_tool,
    python_repl_tool,
)

from backend.plugins.deer_flow.src.config.agents import AGENT_LLM_MAP
from backend.plugins.deer_flow.src.config.configuration import Configuration
from backend.plugins.deer_flow.src.llms.llm import get_llm_by_type
from backend.plugins.deer_flow.src.prompts.planner_model import Plan
from backend.plugins.deer_flow.src.prompts.template import apply_prompt_template
from backend.plugins.deer_flow.src.utils.json_utils import repair_json_output

from .types import State
from ..config import SELECTED_SEARCH_ENGINE, SearchEngine

logger = logging.getLogger(__name__)


def retry_call(func: Callable, *args, max_retries: int = 3, delay: float = 1.0, **kwargs) -> Any:
    """重试调用函数，适用于同步函数
    
    Args:
        func: 要调用的函数
        *args: 函数参数
        max_retries: 最大重试次数，默认3次
        delay: 重试间隔（秒），默认1秒
        **kwargs: 函数关键字参数
        
    Returns:
        函数调用结果
        
    Raises:
        最后一次调用的异常
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            # 记录请求详情用于调试
            logger.debug(f"尝试调用 {func.__name__}，第 {attempt + 1}/{max_retries + 1} 次")
            if args:
                logger.debug(f"函数参数 args: {args[:2] if len(args) > 2 else args}")  # 只记录前2个参数避免过长
            if kwargs:
                # 过滤敏感信息，只记录关键参数
                safe_kwargs = {k: v for k, v in kwargs.items() if k not in ['api_key', 'token', 'password']}
                logger.debug(f"函数关键字参数 kwargs: {safe_kwargs}")
            
            result = func(*args, **kwargs)
            
            if attempt > 0:
                logger.info(f"调用 {func.__name__} 在第 {attempt + 1} 次尝试成功")
            
            return result
            
        except Exception as e:
            last_exception = e
            error_str = str(e)
            
            # 详细记录错误信息，特别是503错误
            if "503" in error_str or "Service Unavailable" in error_str:
                logger.error(f"服务不可用错误 (503) - 调用 {func.__name__} 失败，第 {attempt + 1}/{max_retries + 1} 次尝试")
                logger.error(f"503错误详情: {error_str}")
                if hasattr(e, 'response') and e.response:
                    logger.error(f"响应状态码: {getattr(e.response, 'status_code', 'N/A')}")
                    logger.error(f"响应头: {getattr(e.response, 'headers', 'N/A')}")
                    logger.error(f"响应内容: {getattr(e.response, 'text', 'N/A')[:500]}")  # 只记录前500字符
            elif "429" in error_str or "rate limit" in error_str.lower():
                logger.warning(f"速率限制错误 - 调用 {func.__name__} 失败，第 {attempt + 1}/{max_retries + 1} 次尝试: {error_str}")
            else:
                logger.warning(f"调用 {func.__name__} 失败，第 {attempt + 1}/{max_retries + 1} 次尝试: {error_str}")
            
            if attempt < max_retries:
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error(f"调用 {func.__name__} 最终失败，已重试 {max_retries} 次: {error_str}")
                raise last_exception


async def async_retry_call(func: Callable, *args, max_retries: int = 3, delay: float = 1.0, **kwargs) -> Any:
    """重试调用异步函数
    
    Args:
        func: 要调用的异步函数
        *args: 函数参数
        max_retries: 最大重试次数，默认3次  
        delay: 重试间隔（秒），默认1秒
        **kwargs: 函数关键字参数
        
    Returns:
        异步函数调用结果
        
    Raises:
        最后一次调用的异常
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            # 记录异步请求详情用于调试
            logger.debug(f"尝试异步调用 {func.__name__}，第 {attempt + 1}/{max_retries + 1} 次")
            if args:
                logger.debug(f"异步函数参数 args: {args[:2] if len(args) > 2 else args}")  # 只记录前2个参数避免过长
            if kwargs:
                # 过滤敏感信息，只记录关键参数
                safe_kwargs = {k: v for k, v in kwargs.items() if k not in ['api_key', 'token', 'password']}
                logger.debug(f"异步函数关键字参数 kwargs: {safe_kwargs}")
            
            result = await func(*args, **kwargs)
            
            if attempt > 0:
                logger.info(f"异步调用 {func.__name__} 在第 {attempt + 1} 次尝试成功")
            
            return result
            
        except Exception as e:
            last_exception = e
            error_str = str(e)
            
            # 详细记录错误信息，特别是503错误
            if "503" in error_str or "Service Unavailable" in error_str:
                logger.error(f"服务不可用错误 (503) - 异步调用 {func.__name__} 失败，第 {attempt + 1}/{max_retries + 1} 次尝试")
                logger.error(f"503错误详情: {error_str}")
                if hasattr(e, 'response') and e.response:
                    logger.error(f"响应状态码: {getattr(e.response, 'status_code', 'N/A')}")
                    logger.error(f"响应头: {getattr(e.response, 'headers', 'N/A')}")
                    logger.error(f"响应内容: {getattr(e.response, 'text', 'N/A')[:500]}")  # 只记录前500字符
            elif "429" in error_str or "rate limit" in error_str.lower():
                logger.warning(f"速率限制错误 - 异步调用 {func.__name__} 失败，第 {attempt + 1}/{max_retries + 1} 次尝试: {error_str}")
            else:
                logger.warning(f"异步调用 {func.__name__} 失败，第 {attempt + 1}/{max_retries + 1} 次尝试: {error_str}")
            
            if attempt < max_retries:
                logger.info(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
            else:
                logger.error(f"异步调用 {func.__name__} 最终失败，已重试 {max_retries} 次: {error_str}")
                raise last_exception


@tool
def handoff_to_planner(
    research_topic: Annotated[str, "The topic of the research task to be handed off."],
    locale: Annotated[str, "The user's detected language locale (e.g., en-US, zh-CN)."],
):
    """Handoff to planner agent to do plan."""
    # This tool is not returning anything: we're just using it
    # as a way for LLM to signal that it needs to hand off to planner agent
    return


def background_investigation_node(state: State, config: RunnableConfig):
    logger.info("background investigation node is running.")
    configurable = Configuration.from_runnable_config(config)
    query = state.get("research_topic")
    
    # 简化的编码检查
    logger.info(f"研究主题: {query}")
    
    background_investigation_results = None
    if SELECTED_SEARCH_ENGINE == SearchEngine.TAVILY.value:
        logger.info("使用Tavily搜索引擎")
        try:
            # 使用重试机制调用Tavily搜索
            searched_content = retry_call(
                LoggedTavilySearch(max_results=configurable.max_search_results).invoke,
                query,
                max_retries=3,
                delay=1.0
            )
            logger.info(f"搜索完成，获得 {len(searched_content) if isinstance(searched_content, list) else 0} 个结果")
            
            # 编码修复处理
            if isinstance(searched_content, list):
                for i, item in enumerate(searched_content):
                    if isinstance(item, dict):
                        for key, value in item.items():
                            if isinstance(value, str):
                                try:
                                    value.encode('gbk')
                                except UnicodeEncodeError as e:
                                    logger.warning(f"搜索结果 {i}.{key} 存在编码问题，正在修复...")
                                    try:
                                        # 修复UTF-8被误解为Latin-1的问题
                                        fixed_bytes = value.encode('latin-1')
                                        fixed_value = fixed_bytes.decode('utf-8')
                                        fixed_value.encode('gbk')  # 验证修复效果
                                        item[key] = fixed_value
                                        logger.info(f"已修复搜索结果 {i}.{key} 的编码问题")
                                    except (UnicodeDecodeError, UnicodeEncodeError):
                                        try:
                                            fixed_value = value.encode('utf-8', errors='replace').decode('utf-8')
                                            item[key] = fixed_value
                                            logger.info(f"已使用替换方式修复搜索结果 {i}.{key}")
                                        except Exception:
                                            item[key] = f"[编码错误，内容已跳过]"
                                            logger.warning(f"无法修复搜索结果 {i}.{key}，已替换为安全内容")
                
                # 处理搜索结果
                background_investigation_results = [
                    f"## {elem['title']}\n\n{elem['content']}" for elem in searched_content
                ]
                
                # 最终合并
                final_result = "\n\n".join(background_investigation_results)
                
                # 最终编码检查
                try:
                    final_result.encode('gbk')
                    logger.info("搜索结果编码检查通过")
                except UnicodeEncodeError as e:
                    logger.warning("最终结果仍有编码问题，进行最后修复...")
                    try:
                        final_result = final_result.encode('utf-8', errors='replace').decode('utf-8')
                        logger.info("最终编码修复完成")
                    except Exception:
                        final_result = "搜索结果包含编码问题，无法正常显示。"
                        logger.error("最终编码修复失败")
                
                return {
                    "background_investigation_results": final_result
                }
            else:
                logger.error(f"Tavily搜索返回格式错误: {searched_content}")
                logger.info(f"使用备用搜索引擎")
                return web_search_node(configurable, query)
        except Exception as e:
            logger.error(f"Tavily搜索过程出错: {e}")
            return {
                "background_investigation_results": f"搜索过程出错: {str(e)}"
            }
    else:
        logger.info("使用备用搜索引擎")
        return web_search_node(configurable, query)

def web_search_node( configurable, query):
    try:
        # 使用重试机制调用备用搜索引擎
        background_investigation_results = retry_call(
            get_web_search_tool(configurable.max_search_results).invoke,
            query,
            max_retries=3,
            delay=1.0
        )
        return {
                "background_investigation_results": str(background_investigation_results)
            }
    except Exception as e:
        logger.error(f"备用搜索引擎出错: {e}")
        return {
                "background_investigation_results": f"搜索过程出错: {str(e)}"
            }

def planner_node(
    state: State, config: RunnableConfig
) -> Command[Literal["human_feedback", "reporter"]]:
    """Planner node that generate the full plan."""
    logger.info("Planner generating full plan")
    configurable = Configuration.from_runnable_config(config)
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    messages = apply_prompt_template("planner", state, configurable)

    if state.get("enable_background_investigation") and state.get(
        "background_investigation_results"
    ):
        messages += [
            {
                "role": "user",
                "content": (
                    "background investigation results of user query:\n"
                    + state["background_investigation_results"]
                    + "\n"
                ),
            }
        ]

    if AGENT_LLM_MAP["planner"] == "basic":
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"]).with_structured_output(
            Plan,
            method="json_mode",
        )
    else:
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"])

    # if the plan iterations is greater than the max plan iterations, return the reporter node
    if plan_iterations >= configurable.max_plan_iterations:
        return Command(goto="reporter")

    # 记录请求详情用于调试503错误
    logger.info(f"准备调用规划器LLM，模型类型: {AGENT_LLM_MAP.get('planner', 'unknown')}")
    logger.debug(f"规划器输入消息数量: {len(messages)}")
    logger.debug(f"规划器迭代次数: {plan_iterations}")
    
    full_response = ""
    if AGENT_LLM_MAP["planner"] == "basic":
        logger.info("使用基础模式调用规划器LLM (structured output)")
        # 使用重试机制调用LLM
        response = retry_call(llm.invoke, messages, max_retries=3, delay=1.0)
        full_response = response.model_dump_json(indent=4, exclude_none=True)
        logger.info(f"规划器LLM (basic) 调用成功，响应长度: {len(full_response)}")
    else:
        logger.info("使用流式模式调用规划器LLM")
        # 对于流式调用，也添加重试机制
        response = retry_call(llm.stream, messages, max_retries=3, delay=1.0)
        chunk_count = 0
        for chunk in response:
            chunk_count += 1
            full_response += chunk.content
        logger.info(f"规划器LLM (stream) 调用成功，处理了 {chunk_count} 个chunks，响应长度: {len(full_response)}")
    logger.debug(f"Current state messages: {state['messages']}")
    logger.info(f"Planner response: {full_response}")

    try:
        curr_plan = json.loads(repair_json_output(full_response))
        
        # 修复缺失的 step_type 字段
        if "steps" in curr_plan and isinstance(curr_plan["steps"], list):
            for step in curr_plan["steps"]:
                if isinstance(step, dict) and "step_type" not in step:
                    # 根据 need_search 字段推断 step_type
                    if step.get("need_search", True):
                        step["step_type"] = "research"
                    else:
                        step["step_type"] = "processing"
                    logger.info(f"自动添加 step_type: {step['step_type']} for step: {step.get('title', 'Unknown')}")
        
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")
    if curr_plan.get("has_enough_context"):
        logger.info("Planner response has enough context.")
        new_plan = Plan.model_validate(curr_plan)
        return Command(
            update={
                "messages": [AIMessage(content=full_response, name="planner")],
                "current_plan": new_plan,
            },
            goto="reporter",
        )
    return Command(
        update={
            "messages": [AIMessage(content=full_response, name="planner")],
            "current_plan": full_response,
        },
        goto="human_feedback",
    )


def human_feedback_node(
    state,
) -> Command[Literal["planner", "research_team", "reporter", "__end__"]]:
    current_plan = state.get("current_plan", "")
    # check if the plan is auto accepted
    auto_accepted_plan = state.get("auto_accepted_plan", False)
    if not auto_accepted_plan:
        feedback = interrupt("Please Review the Plan.")

        # if the feedback is not accepted, return the planner node
        if feedback and str(feedback).upper().startswith("[EDIT_PLAN]"):
            return Command(
                update={
                    "messages": [
                        HumanMessage(content=feedback, name="feedback"),
                    ],
                },
                goto="planner",
            )
        elif feedback and str(feedback).upper().startswith("[ACCEPTED]"):
            logger.info("Plan is accepted by user.")
        else:
            raise TypeError(f"Interrupt value of {feedback} is not supported.")

    # if the plan is accepted, run the following node
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    goto = "research_team"
    try:
        current_plan = repair_json_output(current_plan)
        # increment the plan iterations
        plan_iterations += 1
        # parse the plan
        new_plan = json.loads(current_plan)
        
        # 修复缺失的 step_type 字段
        if "steps" in new_plan and isinstance(new_plan["steps"], list):
            for step in new_plan["steps"]:
                if isinstance(step, dict) and "step_type" not in step:
                    # 根据 need_search 字段推断 step_type
                    if step.get("need_search", True):
                        step["step_type"] = "research"
                    else:
                        step["step_type"] = "processing"
                    logger.info(f"human_feedback_node: 自动添加 step_type: {step['step_type']} for step: {step.get('title', 'Unknown')}")
        
        if new_plan["has_enough_context"]:
            goto = "reporter"
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")

    return Command(
        update={
            "current_plan": Plan.model_validate(new_plan),
            "plan_iterations": plan_iterations,
            "locale": new_plan["locale"],
        },
        goto=goto,
    )


def coordinator_node(
    state: State, config: RunnableConfig
) -> Command[Literal["planner", "background_investigator", "__end__"]]:
    """Coordinator node that communicate with customers."""
    logger.info("Coordinator talking.")
    configurable = Configuration.from_runnable_config(config)
    
    # 简化的状态检查
    research_topic = state.get("research_topic", "")
    logger.info(f"协调器处理研究主题: {research_topic}")
    
    messages = apply_prompt_template("coordinator", state)
    
    # 记录请求详情用于调试503错误
    logger.info(f"准备调用协调器LLM，模型类型: {AGENT_LLM_MAP.get('coordinator', 'unknown')}")
    logger.debug(f"协调器输入消息数量: {len(messages)}")
    logger.debug(f"协调器输入消息预览: {messages[0] if messages else 'No messages'}")
    
    try:
        # 使用重试机制调用协调器LLM
        response = retry_call(
            get_llm_by_type(AGENT_LLM_MAP["coordinator"])
            .bind_tools([handoff_to_planner])
            .invoke,
            messages,
            max_retries=3,
            delay=1.0
        )
        logger.debug(f"Current state messages: {state['messages']}")
        logger.info(f"协调器LLM调用成功，响应类型: {type(response)}")
        logger.debug(f"协调器响应工具调用数量: {len(response.tool_calls) if hasattr(response, 'tool_calls') and response.tool_calls else 0}")
    except Exception as e:
        logger.error(f"协调器LLM调用失败: {e}")
        raise

    goto = "__end__"
    locale = state.get("locale", "en-US")  # Default locale if not specified
    research_topic = state.get("research_topic", "")

    if len(response.tool_calls) > 0:
        goto = "planner"
        if state.get("enable_background_investigation"):
            # if the search_before_planning is True, add the web search tool to the planner agent
            goto = "background_investigator"
        try:
            for tool_call in response.tool_calls:
                if tool_call.get("name", "") != "handoff_to_planner":
                    continue
                if tool_call.get("args", {}).get("locale") and tool_call.get(
                    "args", {}
                ).get("research_topic"):
                    locale = tool_call.get("args", {}).get("locale")
                    research_topic = tool_call.get("args", {}).get("research_topic")
                    logger.info(f"更新研究主题: {research_topic}, 语言: {locale}")
                    break
        except Exception as e:
            logger.error(f"Error processing tool calls: {e}")
    else:
        logger.warning(
            "Coordinator response contains no tool calls. Checking for existing research data..."
        )
        logger.warning(f"Coordinator response: {response}")
        
        # 优雅降级：检查是否有已完成的研究结果可以直接生成报告
        has_research_data = False
        
        # 检查observations字段
        if state.get("observations") and len(state["observations"]) > 0:
            valid_observations = [obs for obs in state["observations"] if obs and isinstance(obs, str) and len(obs.strip()) > 50]
            if valid_observations:
                logger.info(f"Found {len(valid_observations)} valid observations, proceeding to reporter")
                has_research_data = True
        
        # 检查messages字段中的研究内容
        if not has_research_data and state.get("messages"):
            research_messages = []
            for msg in state["messages"]:
                content = ""
                if hasattr(msg, 'content'):
                    content = msg.content
                elif isinstance(msg, dict) and 'content' in msg:
                    content = msg['content']
                
                if content and isinstance(content, str) and len(content.strip()) > 100:
                    # 检查是否包含研究相关的内容
                    research_keywords = ['research', 'analysis', 'findings', 'data', 'study', '研究', '分析', '发现', '数据']
                    if any(keyword.lower() in content.lower() for keyword in research_keywords):
                        research_messages.append(content)
            
            if research_messages:
                logger.info(f"Found {len(research_messages)} research-related messages, proceeding to reporter")
                has_research_data = True
        
        # 检查current_plan是否存在且有已执行的步骤
        if not has_research_data and state.get("current_plan"):
            current_plan = state["current_plan"]
            if hasattr(current_plan, 'steps'):
                executed_steps = [step for step in current_plan.steps if step.execution_res and len(str(step.execution_res).strip()) > 50]
                if executed_steps:
                    logger.info(f"Found {len(executed_steps)} executed steps in current plan, proceeding to reporter")
                    has_research_data = True
        
        if has_research_data:
            logger.info("Found existing research data, proceeding directly to reporter node")
            goto = "reporter"
        else:
            logger.warning("No research data available, terminating workflow")
            goto = "__end__"

    return Command(
        update={
            "locale": locale,
            "research_topic": research_topic,
            "resources": configurable.resources,
        },
        goto=goto,
    )


def reporter_node(state: State, config: RunnableConfig):
    """Reporter node that write a final report."""
    logger.info("Reporter write final report")
    configurable = Configuration.from_runnable_config(config)
    current_plan = state.get("current_plan")
    input_ = {
        "messages": [
            HumanMessage(
                f"# Research Requirements\n\n## Task\n\n{current_plan.title}\n\n## Description\n\n{current_plan.thought}"
            )
        ],
        "locale": state.get("locale", "en-US"),
    }
    invoke_messages = apply_prompt_template("reporter", input_, configurable)
    observations = state.get("observations", [])

    # Add a reminder about the new report format, citation style, and table usage
    invoke_messages.append(
        HumanMessage(
            content="IMPORTANT: Structure your report according to the format in the prompt. Remember to include:\n\n1. Key Points - A bulleted list of the most important findings\n2. Overview - A brief introduction to the topic\n3. Detailed Analysis - Organized into logical sections\n4. Survey Note (optional) - For more comprehensive reports\n5. Key Citations - List all references at the end\n\nFor citations, DO NOT include inline citations in the text. Instead, place all citations in the 'Key Citations' section at the end using the format: `- [Source Title](URL)`. Include an empty line between each citation for better readability.\n\nPRIORITIZE USING MARKDOWN TABLES for data presentation and comparison. Use tables whenever presenting comparative data, statistics, features, or options. Structure tables with clear headers and aligned columns. Example table format:\n\n| Feature | Description | Pros | Cons |\n|---------|-------------|------|------|\n| Feature 1 | Description 1 | Pros 1 | Cons 1 |\n| Feature 2 | Description 2 | Pros 2 | Cons 2 |",
            name="system",
        )
    )

    for observation in observations:
        invoke_messages.append(
            HumanMessage(
                content=f"Below are some observations for the research task:\n\n{observation}",
                name="observation",
            )
        )
    logger.debug(f"Current invoke messages: {invoke_messages}")
    
    # 记录请求详情用于调试503错误
    logger.info(f"准备调用报告器LLM，模型类型: {AGENT_LLM_MAP.get('reporter', 'unknown')}")
    logger.debug(f"报告器输入消息数量: {len(invoke_messages)}")
    logger.debug(f"报告器观察数据数量: {len(observations)}")
    
    # 使用重试机制调用reporter LLM
    response = retry_call(
        get_llm_by_type(AGENT_LLM_MAP["reporter"]).invoke,
        invoke_messages,
        max_retries=3,
        delay=1.0
    )
    response_content = response.content
    logger.info(f"报告器LLM调用成功，响应类型: {type(response)}")
    logger.info(f"报告器响应内容长度: {len(response_content) if response_content else 0}")
    logger.info(f"reporter response: {response_content}")

    # 增强的状态更新逻辑 - 确保final_report正确传递
    if not response_content or not response_content.strip():
        logger.error("Reporter generated empty response!")
        response_content = "报告生成失败，请重试。"
    else:
        # 记录详细的内容信息用于调试
        preview = response_content[:200] + "..." if len(response_content) > 200 else response_content
        logger.info(f"Reporter content preview: {preview}")
    
    # 增加详细的调试日志
    logger.info(f"Reporter node returning: final_report length = {len(response_content)}")
    
    # 确保返回的状态包含正确的final_report
    result_state = {
        "final_report": response_content,
        # 保持其他状态字段不变
        "messages": state.get("messages", []),
        "observations": state.get("observations", []),
        "current_plan": state.get("current_plan"),
        "plan_iterations": state.get("plan_iterations", 0),
        "research_topic": state.get("research_topic", ""),
        "locale": state.get("locale", "en-US"),
        "resources": state.get("resources", []),
        "auto_accepted_plan": state.get("auto_accepted_plan", False),
        "enable_background_investigation": state.get("enable_background_investigation", True),
        "background_investigation_results": state.get("background_investigation_results"),
    }
    
    logger.info(f"Reporter node result type: {type(result_state)}")
    logger.debug(f"Reporter node result keys: {list(result_state.keys())}")
    
    # 验证final_report确实在结果中
    if 'final_report' in result_state and result_state['final_report']:
        logger.info(f"Verified: final_report in result with length {len(result_state['final_report'])}")
    else:
        logger.error("CRITICAL: final_report missing or empty in result state!")
    
    return result_state


def research_team_node(state: State):
    """Research team node that collaborates on tasks."""
    logger.info("Research team is collaborating on tasks.")
    pass


async def _execute_agent_step(
    state: State, agent, agent_name: str
) -> Command[Literal["research_team"]]:
    """Helper function to execute a step using the specified agent."""
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])

    # Find the first unexecuted step
    current_step = None
    completed_steps = []
    for step in current_plan.steps:
        if not step.execution_res:
            current_step = step
            break
        else:
            completed_steps.append(step)

    if not current_step:
        logger.warning("No unexecuted step found")
        return Command(goto="research_team")

    logger.info(f"Executing step: {current_step.title}, agent: {agent_name}")

    # Format completed steps information
    completed_steps_info = ""
    if completed_steps:
        completed_steps_info = "# Existing Research Findings\n\n"
        for i, step in enumerate(completed_steps):
            completed_steps_info += f"## Existing Finding {i + 1}: {step.title}\n\n"
            completed_steps_info += f"<finding>\n{step.execution_res}\n</finding>\n\n"

    # Prepare the input for the agent with completed steps info
    agent_input = {
        "messages": [
            HumanMessage(
                content=f"{completed_steps_info}# Current Task\n\n## Title\n\n{current_step.title}\n\n## Description\n\n{current_step.description}\n\n## Locale\n\n{state.get('locale', 'en-US')}"
            )
        ]
    }

    # Add citation reminder for researcher agent
    if agent_name == "researcher":
        if state.get("resources"):
            resources_info = "**The user mentioned the following resource files:**\n\n"
            for resource in state.get("resources"):
                resources_info += f"- {resource.title} ({resource.description})\n"

            agent_input["messages"].append(
                HumanMessage(
                    content=resources_info
                    + "\n\n"
                    + "You MUST use the **local_search_tool** to retrieve the information from the resource files.",
                )
            )

        agent_input["messages"].append(
            HumanMessage(
                content="IMPORTANT: DO NOT include inline citations in the text. Instead, track all sources and include a References section at the end using link reference format. Include an empty line between each citation for better readability. Use this format for each reference:\n- [Source Title](URL)\n\n- [Another Source](URL)",
                name="system",
            )
        )

    # Invoke the agent
    default_recursion_limit = 100
    try:
        env_value_str = os.getenv("AGENT_RECURSION_LIMIT", str(default_recursion_limit))
        parsed_limit = int(env_value_str)

        if parsed_limit > 0:
            recursion_limit = parsed_limit
            logger.info(f"Recursion limit set to: {recursion_limit}")
        else:
            logger.warning(
                f"AGENT_RECURSION_LIMIT value '{env_value_str}' (parsed as {parsed_limit}) is not positive. "
                f"Using default value {default_recursion_limit}."
            )
            recursion_limit = default_recursion_limit
    except ValueError:
        raw_env_value = os.getenv("AGENT_RECURSION_LIMIT")
        logger.warning(
            f"Invalid AGENT_RECURSION_LIMIT value: '{raw_env_value}'. "
            f"Using default value {default_recursion_limit}."
        )
        recursion_limit = default_recursion_limit

    logger.info(f"Agent input: {agent_input}")
    # 使用异步重试机制调用agent
    try:
        result = await async_retry_call(
            agent.ainvoke,
            input=agent_input,
            config={"recursion_limit": recursion_limit},
            max_retries=3,
            delay=1.0
        )
    except Exception as agent_error:
        error_str = str(agent_error)
        if "recursion limit" in error_str.lower() or "graph_recursion_limit" in error_str.lower():
            logger.error(f"Agent recursion limit exceeded for step '{current_step.title}': {agent_error}")
            # 使用部分结果或默认消息
            partial_result = f"Step '{current_step.title}' was partially completed but exceeded recursion limit. Manual review recommended."
            current_step.execution_res = partial_result
            logger.warning(f"Using partial result for step '{current_step.title}'")
            return Command(
                update={
                    "messages": [
                        HumanMessage(
                            content=partial_result,
                            name=agent_name,
                        )
                    ],
                    "observations": observations + [partial_result],
                },
                goto="research_team",
            )
        else:
            logger.error(f"Agent execution failed for step '{current_step.title}': {agent_error}")
            raise agent_error

    # Process the result
    response_content = result["messages"][-1].content
    logger.debug(f"{agent_name.capitalize()} full response: {response_content}")

    # Update the step with the execution result
    current_step.execution_res = response_content
    logger.info(f"Step '{current_step.title}' execution completed by {agent_name}")

    return Command(
        update={
            "messages": [
                HumanMessage(
                    content=response_content,
                    name=agent_name,
                )
            ],
            "observations": observations + [response_content],
        },
        goto="research_team",
    )


async def _setup_and_execute_agent_step(
    state: State,
    config: RunnableConfig,
    agent_type: str,
    default_tools: list,
) -> Command[Literal["research_team"]]:
    """Helper function to set up an agent with appropriate tools and execute a step.

    This function handles the common logic for both researcher_node and coder_node:
    1. Configures MCP servers and tools based on agent type
    2. Creates an agent with the appropriate tools or uses the default agent
    3. Executes the agent on the current step

    Args:
        state: The current state
        config: The runnable config
        agent_type: The type of agent ("researcher" or "coder")
        default_tools: The default tools to add to the agent

    Returns:
        Command to update state and go to research_team
    """
    configurable = Configuration.from_runnable_config(config)
    mcp_servers = {}
    enabled_tools = {}

    # Extract MCP server configuration for this agent type
    if configurable.mcp_settings:
        for server_name, server_config in configurable.mcp_settings["servers"].items():
            if (
                server_config["enabled_tools"]
                and agent_type in server_config["add_to_agents"]
            ):
                mcp_servers[server_name] = {
                    k: v
                    for k, v in server_config.items()
                    if k in ("transport", "command", "args", "url", "env")
                }
                for tool_name in server_config["enabled_tools"]:
                    enabled_tools[tool_name] = server_name

    # Create and execute agent with MCP tools if available
    if mcp_servers:
        async with MultiServerMCPClient(mcp_servers) as client:
            loaded_tools = default_tools[:]
            for tool in client.get_tools():
                if tool.name in enabled_tools:
                    tool.description = (
                        f"Powered by '{enabled_tools[tool.name]}'.\n{tool.description}"
                    )
                    loaded_tools.append(tool)
            agent = create_agent(agent_type, agent_type, loaded_tools, agent_type)
            return await _execute_agent_step(state, agent, agent_type)
    else:
        # Use default tools if no MCP servers are configured
        agent = create_agent(agent_type, agent_type, default_tools, agent_type)
        return await _execute_agent_step(state, agent, agent_type)


async def researcher_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Researcher node that do research"""
    logger.info("Researcher node is researching.")
    configurable = Configuration.from_runnable_config(config)
    tools = [get_web_search_tool(configurable.max_search_results), crawl_tool]
    retriever_tool = get_retriever_tool(state.get("resources", []))
    if retriever_tool:
        tools.insert(0, retriever_tool)
    logger.info(f"Researcher tools: {tools}")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "researcher",
        tools,
    )


async def coder_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Coder node that do code analysis."""
    logger.info("Coder node is coding.")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "coder",
        [python_repl_tool],
    )
