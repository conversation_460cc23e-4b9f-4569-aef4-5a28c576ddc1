<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css"><![CDATA[
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background-color { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; stroke-width: 1px; }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Specific weights */
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }
      .font-normal { font-weight: 400; }

      /* Icon Styles */
      .icon-stroke { stroke: #3B82F6; stroke-width: 2; fill: none; }
      .icon-fill-primary { fill: #1E40AF; }
      .icon-fill-accent { fill: #3B82F6; }

      /* Card Shadow Filter */
      /* Note: SVG filter syntax is different from CSS box-shadow */
      /* This filter creates a basic drop shadow effect */
    ]]></style>

    <!-- Linear Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF" />
      <stop offset="100%" style="stop-color:#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>
    <linearGradient id="accentGradientTransparent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:0.05" />
    </linearGradient>

    <!-- Filter for card shadow -->
    <filter id="card-shadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="dropShadow1" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2" />
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2" />
      <feColorMatrix result="dropShadow2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" />
      <feMerge>
        <feMergeNode in="dropShadow1" />
        <feMergeNode in="dropShadow2" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icons -->
    <symbol id="icon-check" viewBox="0 0 24 24">
      <path d="M20 6L9 17L4 12" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="icon-lightbulb" viewBox="0 0 24 24">
      <path d="M9 18H15" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 22C14.7614 22 17 19.7614 17 17H7C7 19.7614 9.23858 22 12 22Z" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 17V4C12 3.44772 11.5523 3 11 3C10.4477 3 10 3.44772 10 4V17" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 17C12 14.2386 14.2386 12 17 12C19.7614 12 22 9.76142 22 7C22 4.23858 19.7614 2 17 2C14.2386 2 12 4.23858 12 7" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 17C12 14.2386 9.76142 12 7 12C4.23858 12 2 9.76142 2 7C2 4.23858 4.23858 2 7 2C9.76142 2 12 4.23858 12 7" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="icon-phone" viewBox="0 0 24 24">
      <path d="M22 16.92V21C22 21.5523 21.5523 22 21 22H19C15.6863 22 13 19.3137 13 16V13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13V16C11 19.3137 8.31371 22 5 22H3C2.44772 22 2 21.5523 2 21V16.92C2 16.3677 2.44772 15.92 3 15.92C5.9723 15.92 8.44192 13.4504 8.44192 10.4781C8.44192 7.50577 5.9723 5.03615 3 5.03615C2.44772 5.03615 2 4.58843 2 4.03615V3C2 2.44772 2.44772 2 3 2H5C8.31371 2 11 4.68629 11 8V11C11 11.5523 11.4477 12 12 12C12.5523 12 13 11.5523 13 11V8C13 4.68629 15.6863 2 19 2H21C21.5523 2 22 2.44772 22 3V4.03615C22 4.58843 21.5523 5.03615 21 5.03615C18.0277 5.03615 15.5581 7.50577 15.5581 10.4781C15.5581 13.4504 18.0277 15.92 21 15.92C21.5523 15.92 22 16.3677 22 16.92Z" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="icon-mail" viewBox="0 0 24 24">
      <path d="M4 4H20C21.1046 4 22 4.89543 22 6V18C22 19.1046 21.1046 20 20 20H4C2.89543 20 2 19.1046 2 18V6C2 4.89543 2.89543 4 4 4Z" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M22 6L12 13L2 6" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="icon-globe" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" class="icon-stroke"/>
      <path d="M2 12H22" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 2C14.7614 2 17 4.23858 17 7C17 9.76142 14.7614 12 12 12C9.23858 12 7 9.76142 7 7C7 4.23858 9.23858 2 12 2Z" class="icon-stroke"/>
      <path d="M12 12C14.7614 12 17 14.2386 17 17C17 19.7614 14.7614 22 12 22C9.23858 22 7 19.7614 7 17C7 14.2386 9.23858 12 12 12Z" class="icon-stroke"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative Elements -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#accentGradientTransparent)"/>
  <circle cx="1700" cy="100" r="150" class="primary-color" opacity="0.1"/>
  <rect x="100" y="900" width="300" height="80" class="accent-color" opacity="0.1" rx="10"/>

  <!-- Main Content Group (positioned with page margins) -->
  <g transform="translate(80 60)">
    <!-- Header -->
    <text x="0" y="0" class="text-secondary-color small-text font-primary">
      <tspan x="0" y="0">{logo_url}</tspan>
      <tspan x="1760" y="0" text-anchor="end">10/10</tspan>
    </text>

    <!-- Main Title -->
    <text x="880" y="80" text-anchor="middle" class="text-primary-color main-title font-primary">
      <tspan>核心结论和下一步行动</tspan>
    </text>
    <text x="880" y="140" text-anchor="middle" class="text-secondary-color content-title font-primary">
      <tspan>感谢您的关注和支持</tspan>
    </text>

    <!-- Section 1: 主要结论 (Main Conclusions) -->
    <g transform="translate(0 230)">
      <text x="0" y="0" class="text-primary-color section-title font-primary">
        <tspan>主要结论</tspan>
        <tspan class="text-secondary-color small-text" dx="10" dy="-5"> / Key Takeaways</tspan>
      </text>

      <rect x="0" y="40" width="800" height="200" class="card-background-color" rx="12" filter="url(#card-shadow)"/>
      <rect x="0" y="40" width="800" height="200" class="card-border-color" rx="12"/>

      <g class="body-text text-primary-color font-primary">
        <use xlink:href="#icon-check" x="40" y="80" width="28" height="28" class="icon-fill-primary"/>
        <text x="90" y="100">
          <tspan class="font-bold">市场潜力巨大</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Significant Market Potential</tspan>
          <tspan x="90" dy="30" class="text-secondary-color small-text">详细的市场分析表明，我们目标市场具有巨大的增长空间。</tspan>
        </text>

        <use xlink:href="#icon-check" x="40" y="160" width="28" height="28" class="icon-fill-primary"/>
        <text x="90" y="180">
          <tspan class="font-bold">创新技术领先</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Leading Innovative Technology</tspan>
          <tspan x="90" dy="30" class="text-secondary-color small-text">我们的核心技术拥有专利保护，确保竞争优势。</tspan>
        </text>
      </g>
    </g>

    <!-- Section 2: 行动要点 (Action Points) -->
    <g transform="translate(960 230)">
      <text x="0" y="0" class="text-primary-color section-title font-primary">
        <tspan>行动要点</tspan>
        <tspan class="text-secondary-color small-text" dx="10" dy="-5"> / Next Steps</tspan>
      </text>

      <rect x="0" y="40" width="800" height="200" class="card-background-color" rx="12" filter="url(#card-shadow)"/>
      <rect x="0" y="40" width="800" height="200" class="card-border-color" rx="12"/>

      <g class="body-text text-primary-color font-primary">
        <use xlink:href="#icon-lightbulb" x="40" y="80" width="28" height="28" class="icon-fill-accent"/>
        <text x="90" y="100">
          <tspan class="font-bold">资金到位</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Secure Funding</tspan>
          <tspan x="90" dy="30" class="text-secondary-color small-text">确保本轮融资成功，加速产品开发和市场拓展。</tspan>
        </text>

        <use xlink:href="#icon-lightbulb" x="40" y="160" width="28" height="28" class="icon-fill-accent"/>
        <text x="90" y="180">
          <tspan class="font-bold">战略合作</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Strategic Partnerships</tspan>
          <tspan x="90" dy="30" class="text-secondary-color small-text">寻求行业领先企业进行深度合作，实现共赢。</tspan>
        </text>
      </g>
    </g>

    <!-- Section 3: 联系方式 (Contact Information) -->
    <g transform="translate(0 530)">
      <text x="0" y="0" class="text-primary-color section-title font-primary">
        <tspan>联系方式</tspan>
        <tspan class="text-secondary-color small-text" dx="10" dy="-5"> / Contact Information</tspan>
      </text>

      <rect x="0" y="40" width="1760" height="220" class="card-background-color" rx="12" filter="url(#card-shadow)"/>
      <rect x="0" y="40" width="1760" height="220" class="card-border-color" rx="12"/>

      <g class="body-text text-primary-color font-primary">
        <use xlink:href="#icon-phone" x="80" y="80" width="32" height="32" class="icon-fill-primary"/>
        <text x="130" y="105">
          <tspan class="font-bold">电话</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Phone:</tspan>
          <tspan class="text-secondary-color" dx="10">{phone_number}</tspan>
        </text>

        <use xlink:href="#icon-mail" x="80" y="140" width="32" height="32" class="icon-fill-primary"/>
        <text x="130" y="165">
          <tspan class="font-bold">邮箱</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Email:</tspan>
          <tspan class="text-secondary-color" dx="10">{email_address}</tspan>
        </text>

        <use xlink:href="#icon-globe" x="80" y="200" width="32" height="32" class="icon-fill-primary"/>
        <text x="130" y="225">
          <tspan class="font-bold">网站</tspan>
          <tspan class="text-secondary-color small-text" dx="10"> / Website:</tspan>
          <tspan class="text-secondary-color" dx="10">{website_url}</tspan>
        </text>
      </g>
    </g>

    <!-- Call to Action / Thank You -->
    <g transform="translate(0 800)">
      <text x="880" y="0" text-anchor="middle" class="text-primary-color section-title font-primary">
        <tspan>期待与您携手共创未来！</tspan>
      </text>
      <text x="880" y="50" text-anchor="middle" class="text-secondary-color body-text font-primary">
        <tspan>感谢您的宝贵时间。</tspan>
      </text>
    </g>

    <!-- Footer -->
    <g transform="translate(0 920)">
      <text x="0" y="0" class="text-light-color small-text font-primary">
        <tspan>{author}</tspan>
      </text>
      <text x="1760" y="0" text-anchor="end" class="text-light-color small-text font-primary">
        <tspan>{date}</tspan>
      </text>
    </g>

  </g>
</svg>