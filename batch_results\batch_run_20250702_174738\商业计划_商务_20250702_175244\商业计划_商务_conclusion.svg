<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background-color { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .container-background-color { fill: #E0F2FE; }
      .icon-color { fill: #3B82F6; } /* Using accent_color for icons */

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* Relaxed line height for readability */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.4; }
      .semibold { font-weight: 600; }
      .bold { font-weight: 700; }

      /* Layout & Spacing Helpers */
      .margin-h { transform: translateX(80px); }
      .margin-v { transform: translateY(60px); }

      /* Gradients */
      .primary-gradient-fill { fill: url(#primaryGradient); }
      .accent-gradient-fill { fill: url(#accentGradient); }
    </style>

    <!-- Gradients based on provided scheme -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF" />
      <stop offset="100%" style="stop-color:#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>

    <!-- Subtle background gradient for overall page -->
    <linearGradient id="pageBackgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC" />
      <stop offset="100%" style="stop-color:#E0F2FE" />
    </linearGradient>

    <!-- Card Shadow Filter -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icon for bullet points (simple circle) -->
    <circle id="bulletIcon" cx="0" cy="0" r="6" class="accent-color" />

    <!-- Icon for contact (mail) -->
    <symbol id="iconMail" viewBox="0 0 24 24">
      <path class="icon-color" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
      <polyline class="icon-color" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" points="22,6 12,13 2,6"></polyline>
    </symbol>
    <!-- Icon for contact (phone) -->
    <symbol id="iconPhone" viewBox="0 0 24 24">
      <path class="icon-color" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
    </symbol>
    <!-- Icon for contact (website/globe) -->
    <symbol id="iconGlobe" viewBox="0 0 24 24">
      <circle class="icon-color" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" cx="12" cy="12" r="10"></circle>
      <line class="icon-color" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" x1="2" y1="12" x2="22" y2="12"></line>
      <path class="icon-color" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
    </symbol>

  </defs>

  <!-- Page Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#pageBackgroundGradient)" />

  <!-- Decorative Geometric Shapes (Subtle, professional) -->
  <rect x="0" y="0" width="300" height="300" rx="20" ry="20" class="container-background-color" opacity="0.3" transform="translate(-150, -150) rotate(45)" />
  <circle cx="1920" cy="1080" r="250" class="container-background-color" opacity="0.2" transform="translate(100, 100)" />
  <rect x="1500" y="0" width="400" height="200" rx="15" ry="15" class="primary-color" opacity="0.05" transform="rotate(10 1700 100)" />

  <!-- Header Section -->
  <g class="margin-h">
    <!-- Logo Placeholder -->
    <image x="80" y="60" width="150" height="auto" xlink:href="{logo_url}" />
    <!-- Page Number -->
    <text x="1840" y="90" text-anchor="end" class="small-text text-light-color font-primary">10 / 10</text>
  </g>

  <!-- Main Title -->
  <text x="960" y="160" text-anchor="middle" class="main-title text-primary-color font-primary">{title}</text>
  <text x="960" y="210" text-anchor="middle" class="body-text text-secondary-color font-secondary">{subtitle}</text>

  <!-- Content Area - Bento Grid Inspired Layout -->
  <g transform="translate(80, 270)">
    <!-- Card 1: Main Conclusions -->
    <g filter="url(#cardShadow)">
      <rect x="0" y="0" width="860" height="400" rx="12" ry="12" class="card-background-color" stroke="#BAE6FD" stroke-width="1" />
      <g transform="translate(32, 32)">
        <text x="0" y="0" class="section-title text-primary-color font-primary">核心结论</text>
        <text x="0" y="40" class="small-text text-secondary-color font-secondary">Key Takeaways</text>

        <!-- Key Highlight Number -->
        <text x="430" y="190" text-anchor="middle" class="hero-title accent-color font-primary">150%+</text>
        <text x="430" y="240" text-anchor="middle" class="body-text text-primary-color font-secondary semibold">预计年增长率</text>
        <text x="430" y="275" text-anchor="middle" class="small-text text-secondary-color font-secondary">Projected Annual Growth</text>

        <!-- Conclusion Points -->
        <g transform="translate(32, 290)">
          <use xlink:href="#bulletIcon" x="0" y="0" />
          <text x="20" y="5" class="body-text text-primary-color font-primary"><tspan class="semibold">市场潜力巨大</tspan>和需求持续增长。</text>
          <text x="20" y="35" class="small-text text-secondary-color font-secondary">Vast market potential 和#38; sustained demand growth.</text>

          <use xlink:href="#bulletIcon" x="0" y="70" />
          <text x="20" y="75" class="body-text text-primary-color font-primary"><tspan class="semibold">核心技术优势</tspan>保障产品竞争力。</text>
          <text x="20" y="105" class="small-text text-secondary-color font-secondary">Core technology advantage ensures product competitiveness.</text>
        </g>
      </g>
    </g>

    <!-- Card 2: Action Points -->
    <g filter="url(#cardShadow)" transform="translate(900, 0)">
      <rect x="0" y="0" width="860" height="400" rx="12" ry="12" class="card-background-color" stroke="#BAE6FD" stroke-width="1" />
      <g transform="translate(32, 32)">
        <text x="0" y="0" class="section-title text-primary-color font-primary">行动要点</text>
        <text x="0" y="40" class="small-text text-secondary-color font-secondary">Actionable Next Steps</text>

        <!-- Action Points List -->
        <g transform="translate(0, 80)">
          <g>
            <rect x="-10" y="-10" width="800" height="80" rx="8" ry="8" class="container-background-color" opacity="0.4" />
            <text x="0" y="20" class="content-title text-primary-color font-primary">1. <tspan class="accent-color semibold">深化市场拓展</tspan></text>
            <text x="0" y="50" class="small-text text-secondary-color font-secondary">Expand market reach 和#38; strategic partnerships.</text>
          </g>

          <g transform="translate(0, 100)">
            <rect x="-10" y="-10" width="800" height="80" rx="8" ry="8" class="container-background-color" opacity="0.4" />
            <text x="0" y="20" class="content-title text-primary-color font-primary">2. <tspan class="accent-color semibold">优化产品迭代</tspan></text>
            <text x="0" y="50" class="small-text text-secondary-color font-secondary">Iterate product features based on user feedback.</text>
          </g>

          <g transform="translate(0, 200)">
            <rect x="-10" y="-10" width="800" height="80" rx="8" ry="8" class="container-background-color" opacity="0.4" />
            <text x="0" y="20" class="content-title text-primary-color font-primary">3. <tspan class="accent-color semibold">强化人才建设</tspan></text>
            <text x="0" y="50" class="small-text text-secondary-color font-secondary">Strengthen talent pool 和#38; team capabilities.</text>
          </g>
        </g>
      </g>
    </g>

    <!-- Card 3: Contact Information & CTA -->
    <g filter="url(#cardShadow)" transform="translate(0, 440)">
      <rect x="0" y="0" width="1760" height="240" rx="12" ry="12" class="card-background-color" stroke="#BAE6FD" stroke-width="1" />
      <g transform="translate(32, 32)">
        <text x="0" y="0" class="section-title text-primary-color font-primary">期待与您合作</text>
        <text x="0" y="40" class="small-text text-secondary-color font-secondary">Looking Forward to Collaborating</text>

        <!-- Contact Info -->
        <g transform="translate(0, 80)">
          <use xlink:href="#iconMail" x="0" y="0" width="32" height="32" />
          <text x="40" y="24" class="body-text text-primary-color font-primary">邮箱: <tspan class="text-secondary-color">{content}</tspan></text>

          <use xlink:href="#iconPhone" x="400" y="0" width="32" height="32" />
          <text x="440" y="24" class="body-text text-primary-color font-primary">电话: <tspan class="text-secondary-color">{content}</tspan></text>

          <use xlink:href="#iconGlobe" x="800" y="0" width="32" height="32" />
          <text x="840" y="24" class="body-text text-primary-color font-primary">网址: <tspan class="text-secondary-color">{content}</tspan></text>
        </g>

        <!-- Call to Action Button -->
        <g transform="translate(1300, 100)">
          <rect x="0" y="0" width="300" height="60" rx="30" ry="30" class="accent-color" />
          <text x="150" y="38" text-anchor="middle" class="content-title card-background-color font-primary semibold">立即联系我们</text>
          <text x="150" y="68" text-anchor="middle" class="small-text text-light-color font-secondary">Contact Us Now</text>
        </g>

        <!-- Closing Remark -->
        <text x="0" y="180" class="body-text text-primary-color font-primary">感谢您的时间和关注，我们期待与您共创辉煌。</text>
        <text x="0" y="210" class="small-text text-secondary-color font-secondary">Thank you for your time and attention. We look forward to creating brilliance with you.</text>
      </g>
    </g>
  </g>
</svg>