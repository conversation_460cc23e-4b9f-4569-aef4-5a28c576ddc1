#!/usr/bin/env python3
"""
简单的线程本地存储调试
"""

import threading

def test_thread_local():
    """测试线程本地存储行为"""
    
    print("🧵 测试线程本地存储...")
    
    # 获取当前线程
    current_thread = threading.current_thread()
    print(f"当前线程: {current_thread.name}")
    
    # 设置自定义属性
    test_agent_id = "test_agent_123"
    current_thread.current_agent_id = test_agent_id
    print(f"设置 current_agent_id = {test_agent_id}")
    
    # 检查属性是否存在
    if hasattr(current_thread, 'current_agent_id'):
        retrieved_id = current_thread.current_agent_id
        print(f"✅ 成功检索 current_agent_id = {retrieved_id}")
        print(f"✅ 值匹配: {retrieved_id == test_agent_id}")
    else:
        print("❌ current_agent_id 属性不存在")
    
    # 使用getattr方法
    getattr_result = getattr(current_thread, 'current_agent_id', None)
    print(f"使用getattr检索: {getattr_result}")
    
    # 列出线程属性
    thread_attrs = [attr for attr in dir(current_thread) if not attr.startswith('_')]
    print(f"线程属性: {thread_attrs}")
    
    # 测试删除属性
    print("\n测试删除属性...")
    if hasattr(current_thread, 'current_agent_id'):
        delattr(current_thread, 'current_agent_id')
        print("删除了 current_agent_id 属性")
    
    # 再次检查
    if hasattr(current_thread, 'current_agent_id'):
        print("❌ 属性仍然存在")
    else:
        print("✅ 属性已成功删除")

def test_multiple_threads():
    """测试多线程环境下的线程本地存储"""
    
    def worker(thread_id):
        current_thread = threading.current_thread()
        agent_id = f"agent_{thread_id}"
        
        # 设置线程本地存储
        current_thread.current_agent_id = agent_id
        
        # 检查设置
        retrieved = getattr(current_thread, 'current_agent_id', None)
        print(f"线程 {thread_id}: 设置={agent_id}, 检索={retrieved}")
        
        return retrieved == agent_id
    
    print("\n🧵 测试多线程环境...")
    
    import concurrent.futures
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(worker, i) for i in range(3)]
        
        for i, future in enumerate(futures):
            result = future.result()
            print(f"线程 {i} 结果: {'✅ 成功' if result else '❌ 失败'}")

if __name__ == "__main__":
    test_thread_local()
    test_multiple_threads()