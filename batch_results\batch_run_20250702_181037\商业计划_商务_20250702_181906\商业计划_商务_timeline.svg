<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Color Palette */
    .bg-color { fill: #F8FAFC; }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .text-light { fill: #94A3B8; }
    .card-background { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; }
    .container-background { fill: #E0F2FE; }

    /* Font System */
    .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
    .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
    .font-accent { font-family: "Times New Roman", serif; }

    /* Font Sizes and Weights */
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
    .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
    .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; } /* medium */
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; } /* normal */
    .caption { font-size: 14px; font-weight: 400; line-height: 1.4; } /* normal */
    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }
    .font-medium { font-weight: 500; }
    .font-normal { font-weight: 400; }

    /* Card Style */
    .card {
      fill: #FFFFFF;
      rx: 12px; /* border-radius */
      stroke: #BAE6FD;
      stroke-width: 1px;
    }
    .card-shadow {
      filter: url(#cardShadow);
    }
    
    /* Icon System */
    .icon-stroke {
      stroke: #4A86E8; /* Using a slightly brighter blue for icons as per visual elements */
      stroke-width: 2px;
      fill: none;
    }

    /* Timeline Specifics */
    .timeline-line {
      stroke: #3B82F6; /* Accent color for timeline */
      stroke-width: 4px;
      stroke-linecap: round;
    }
    .timeline-marker {
      fill: #3B82F6; /* Accent color for markers */
    }
    .milestone-circle {
      fill: url(#milestoneGradient); /* Gradient for milestones */
      stroke: #1E40AF; /* Primary color for milestone border */
      stroke-width: 2px;
    }
    .milestone-year {
      fill: #1E293B; /* Text primary for year */
    }
    .milestone-title {
      fill: #1E40AF; /* Primary color for milestone title */
    }
    .milestone-description {
      fill: #64748B; /* Text secondary for description */
    }
    .connector-line {
        stroke: #94A3B8; /* Text light for connector lines */
        stroke-width: 1px;
    }

  </style>

  <defs>
    <!-- Card Shadow Filter -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="shadowMatrix" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode in="shadowMatrix"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Milestone Gradient -->
    <linearGradient id="milestoneGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Background Gradient (Subtle) -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#F8FAFC"/>
        <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Header Section -->
  <g id="header-section">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="160" height="40" fill="#E0F2FE" rx="8"/>
    <text x="160" y="87" text-anchor="middle" class="small-text font-semibold text-primary font-primary">
      <tspan>{logo_url}</tspan>
    </text>

    <!-- Main Title -->
    <text x="960" y="160" text-anchor="middle" class="main-title font-primary text-primary">
      <tspan>{title}</tspan>
    </text>
    <!-- Subtitle -->
    <text x="960" y="210" text-anchor="middle" class="section-title font-primary text-secondary">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Timeline Section -->
  <g id="timeline-section">
    <!-- Central Timeline Line -->
    <line x1="200" y1="540" x2="1720" y2="540" class="timeline-line"/>

    <!-- Timeline Nodes and Milestones -->
    <!-- Milestone 1 -->
    <g id="milestone-1">
      <circle cx="350" cy="540" r="20" class="milestone-circle"/>
      <line x1="350" y1="520" x2="350" y2="440" class="connector-line"/>
      <rect x="250" y="300" width="200" height="120" class="card card-shadow"/>
      <text x="350" y="335" text-anchor="middle" class="content-title font-primary milestone-title">
        <tspan>{date} 年</tspan>
      </text>
      <text x="350" y="365" text-anchor="middle" class="body-text font-primary milestone-description">
        <tspan>市场启动和</tspan>
        <tspan x="350" dy="28">产品发布</tspan>
      </text>
      <text x="350" y="500" text-anchor="middle" class="section-title font-primary milestone-year font-bold">
        <tspan>2018</tspan>
      </text>
    </g>

    <!-- Milestone 2 -->
    <g id="milestone-2">
      <circle cx="700" cy="540" r="20" class="milestone-circle"/>
      <line x1="700" y1="560" x2="700" y2="640" class="connector-line"/>
      <rect x="600" y="660" width="200" height="120" class="card card-shadow"/>
      <text x="700" y="695" text-anchor="middle" class="content-title font-primary milestone-title">
        <tspan>{date} 年</tspan>
      </text>
      <text x="700" y="725" text-anchor="middle" class="body-text font-primary milestone-description">
        <tspan>用户增长和</tspan>
        <tspan x="700" dy="28">市场份额扩大</tspan>
      </text>
      <text x="700" y="580" text-anchor="middle" class="section-title font-primary milestone-year font-bold">
        <tspan>2020</tspan>
      </text>
    </g>

    <!-- Milestone 3 -->
    <g id="milestone-3">
      <circle cx="1050" cy="540" r="20" class="milestone-circle"/>
      <line x1="1050" y1="520" x2="1050" y2="440" class="connector-line"/>
      <rect x="950" y="300" width="200" height="120" class="card card-shadow"/>
      <text x="1050" y="335" text-anchor="middle" class="content-title font-primary milestone-title">
        <tspan>{date} 年</tspan>
      </text>
      <text x="1050" y="365" text-anchor="middle" class="body-text font-primary milestone-description">
        <tspan>产品迭代和</tspan>
        <tspan x="1050" dy="28">技术突破</tspan>
      </text>
      <text x="1050" y="500" text-anchor="middle" class="section-title font-primary milestone-year font-bold">
        <tspan>2022</tspan>
      </text>
    </g>

    <!-- Milestone 4 -->
    <g id="milestone-4">
      <circle cx="1400" cy="540" r="20" class="milestone-circle"/>
      <line x1="1400" y1="560" x2="1400" y2="640" class="connector-line"/>
      <rect x="1300" y="660" width="200" height="120" class="card card-shadow"/>
      <text x="1400" y="695" text-anchor="middle" class="content-title font-primary milestone-title">
        <tspan>{date} 年</tspan>
      </text>
      <text x="1400" y="725" text-anchor="middle" class="body-text font-primary milestone-description">
        <tspan>战略合作和</tspan>
        <tspan x="1400" dy="28">全球扩展</tspan>
      </text>
      <text x="1400" y="580" text-anchor="middle" class="section-title font-primary milestone-year font-bold">
        <tspan>2024</tspan>
      </text>
    </g>

    <!-- Future Milestone (Example) -->
    <g id="milestone-future">
      <circle cx="1650" cy="540" r="12" class="timeline-marker"/>
      <line x1="1650" y1="528" x2="1650" y2="460" class="connector-line"/>
      <text x="1650" y="440" text-anchor="middle" class="small-text font-primary text-secondary">
        <tspan>展望未来</tspan>
      </text>
      <text x="1650" y="500" text-anchor="middle" class="content-title font-primary text-primary">
        <tspan>未来计划</tspan>
      </text>
    </g>

  </g>

  <!-- Footer Section -->
  <g id="footer-section">
    <text x="960" y="1030" text-anchor="middle" class="small-text font-primary text-light">
      <tspan>商业计划书 - 第8页 / 10页</tspan>
    </text>
  </g>

  <!-- Decorative Elements -->
  <g id="decorative-elements">
    <!-- Top-left abstract shape -->
    <path d="M0 0 H200 C150 50 100 100 0 150 V0 Z" fill="#1E40AF" fill-opacity="0.05"/>
    <!-- Bottom-right abstract shape -->
    <path d="M1920 1080 V930 C1870 980 1820 1030 1720 1080 H1920 Z" fill="#3B82F6" fill-opacity="0.05"/>

    <!-- Subtle horizontal divider above footer -->
    <line x1="80" y1="980" x2="1840" y2="980" stroke="#BAE6FD" stroke-width="1"/>
  </g>

</svg>