<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette - Defined as CSS variables for easy editing -->
    <style type="text/css">
      <![CDATA[
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6;
        --hover-color: #7DD3FC;
        --active-color: #1E40AF;
        --disabled-color: #64748B;
      }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes */
      .text-hero-title { font-size: 72px; }
      .text-main-title { font-size: 56px; }
      .text-section-title { font-size: 36px; }
      .text-content-title { font-size: 28px; }
      .text-body { font-size: 22px; }
      .text-small { font-size: 16px; }
      .text-caption { font-size: 14px; }

      /* Font Weights */
      .font-thin { font-weight: 100; }
      .font-light { font-weight: 300; }
      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }
      .font-black { font-weight: 900; }

      /* Text Colors */
      .text-primary-color { fill: var(--text-primary); }
      .text-secondary-color { fill: var(--text-secondary); }
      .text-light-color { fill: var(--text-light); }
      .text-accent-color { fill: var(--accent-color); }
      .text-white { fill: #FFFFFF; }

      /* Card Styles */
      .card-style {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        filter: url(#shadowFilter);
        border-radius: 12px; /* SVG rects don't natively support border-radius property, handled by rx/ry */
      }

      /* Borders */
      .border-card { stroke: var(--card-border); stroke-width: 1px; }

      /* General Styles */
      .bg-background { fill: var(--background-color); }
      .bg-container { fill: var(--container-background); }
      .fill-primary { fill: var(--primary-color); }
      .fill-accent { fill: var(--accent-color); }
      .stroke-accent { stroke: var(--accent-color); stroke-width: 2px; fill: none; }
      .stroke-primary { stroke: var(--primary-color); stroke-width: 2px; fill: none; }
      .stroke-secondary { stroke: var(--secondary-color); stroke-width: 1px; fill: none; }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#4A86E8"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>
    <!-- Accent Gradient with Transparency for progress bar -->
    <linearGradient id="accentGradientTransparent" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.8"/>
    </linearGradient>

    <!-- Shadow Filter for card-style -->
    <filter id="shadowFilter" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feFlood flood-color="rgba(0, 0, 0, 0.1)" result="colorOut"/>
      <feComposite in="colorOut" in2="blurOut" operator="in" result="shadowEffect"/>
      <feMerge>
        <feMergeNode in="shadowEffect"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Reusable Icon: Checkmark -->
    <symbol id="icon-checkmark" viewBox="0 0 24 24">
      <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- Reusable Icon: Folder (for chapters) -->
    <symbol id="icon-folder" viewBox="0 0 24 24">
      <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- Reusable Icon: Chart (for data) -->
    <symbol id="icon-chart" viewBox="0 0 24 24">
      <path d="M12 20V10M18 20V4M6 20V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- Reusable Icon: Lightbulb (for innovation) -->
    <symbol id="icon-lightbulb" viewBox="0 0 24 24">
      <path d="M9 18a3 3 0 0 1 6 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M10 21h4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 2c-3.31371 0-6 2.68629-6 6 0 2.91574 1.75887 5.42084 4.29828 6.55169L12 16l1.70172-1.44831C16.2411 13.4208 18 10.9157 18 8c0-3.31371-2.68629-6-6-6z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- Reusable Icon: Target (for strategy) -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="12" r="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

  </defs>

  <!-- Overall Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-background"/>

  <!-- Content Container (subtle lighter background) -->
  <rect x="80" y="60" width="1760" height="960" rx="12" ry="12" class="bg-container"/>

  <!-- Header -->
  <g id="header">
    <!-- Logo Placeholder -->
    <text x="100" y="110" class="font-primary font-bold text-content-title text-primary-color">
      {logo_url}
    </text>

    <!-- Page Number -->
    <text x="1840" y="110" text-anchor="end" class="font-primary font-semibold text-body text-secondary-color">
      2/10
    </text>
  </g>

  <!-- Main Content Area -->
  <g id="main-content">
    <!-- Left Section: Table of Contents / Navigation -->
    <g id="table-of-contents">
      <text x="160" y="240" class="font-primary font-bold text-main-title text-primary-color">
        {title}
      </text>
      <text x="160" y="290" class="font-primary font-normal text-content-title text-secondary-color">
        {subtitle}
      </text>

      <!-- Chapter 1: Executive Summary -->
      <g transform="translate(160, 360)">
        <rect x="0" y="0" width="600" height="80" rx="12" ry="12" class="card-style"/>
        <rect x="0" y="0" width="8" height="80" rx="4" ry="4" class="fill-accent"/> <!-- Active indicator -->
        <use xlink:href="#icon-checkmark" x="32" y="28" width="24" height="24" class="text-accent-color"/>
        <text x="80" y="48" class="font-primary font-semibold text-content-title text-accent-color">
          01. 执行摘要
        </text>
        <text x="80" y="68" class="font-primary font-normal text-small text-secondary-color">
          Executive Summary
        </text>
      </g>

      <!-- Chapter 2: Market Analysis -->
      <g transform="translate(160, 360 + 32 + 80)">
        <rect x="0" y="0" width="600" height="80" rx="12" ry="12" class="card-style"/>
        <use xlink:href="#icon-target" x="32" y="28" width="24" height="24" class="text-primary-color"/>
        <text x="80" y="48" class="font-primary font-semibold text-content-title text-primary-color">
          02. 市场分析
        </text>
        <text x="80" y="68" class="font-primary font-normal text-small text-secondary-color">
          Market Analysis
        </text>
      </g>

      <!-- Chapter 3: Business Model -->
      <g transform="translate(160, 360 + 2*(32 + 80))">
        <rect x="0" y="0" width="600" height="80" rx="12" ry="12" class="card-style"/>
        <use xlink:href="#icon-lightbulb" x="32" y="28" width="24" height="24" class="text-primary-color"/>
        <text x="80" y="48" class="font-primary font-semibold text-content-title text-primary-color">
          03. 商业模式
        </text>
        <text x="80" y="68" class="font-primary font-normal text-small text-secondary-color">
          Business Model
        </text>
      </g>

      <!-- Chapter 4: Financial Projections -->
      <g transform="translate(160, 360 + 3*(32 + 80))">
        <rect x="0" y="0" width="600" height="80" rx="12" ry="12" class="card-style"/>
        <use xlink:href="#icon-chart" x="32" y="28" width="24" height="24" class="text-primary-color"/>
        <text x="80" y="48" class="font-primary font-semibold text-content-title text-primary-color">
          04. 财务预测
        </text>
        <text x="80" y="68" class="font-primary font-normal text-small text-secondary-color">
          Financial Projections
        </text>
      </g>

      <!-- Chapter 5: Risk Assessment -->
      <g transform="translate(160, 360 + 4*(32 + 80))">
        <rect x="0" y="0" width="600" height="80" rx="12" ry="12" class="card-style"/>
        <use xlink:href="#icon-folder" x="32" y="28" width="24" height="24" class="text-primary-color"/>
        <text x="80" y="48" class="font-primary font-semibold text-content-title text-primary-color">
          05. 风险评估
        </text>
        <text x="80" y="68" class="font-primary font-normal text-small text-secondary-color">
          Risk Assessment
        </text>
      </g>
    </g>

    <!-- Right Section: Key Insights / Bento Grid Style -->
    <g id="key-insights" transform="translate(800, 240)">
      <!-- Large Number / Highlight -->
      <text x="0" y="100" class="font-primary font-black text-hero-title text-accent-color" style="opacity:0.1; letter-spacing: -0.05em;">
        02
      </text>
      <text x="0" y="100" class="font-primary font-black text-main-title text-primary-color" transform="translate(0, -20)">
        洞察
      </text>
      <text x="0" y="140" class="font-primary font-semibold text-content-title text-secondary-color">
        Key Insights
      </text>

      <!-- Bento Grid Elements -->
      <!-- Grid Item 1: Market Opportunity -->
      <g transform="translate(0, 180)">
        <rect x="0" y="0" width="480" height="200" rx="12" ry="12" class="card-style"/>
        <text x="32" y="60" class="font-primary font-semibold text-section-title text-primary-color">
          市场潜力
        </text>
        <text x="32" y="90" class="font-primary font-normal text-small text-secondary-color">
          Market Opportunity
        </text>
        <text x="32" y="130" class="font-primary font-normal text-body text-primary-color">
          我们识别了巨大的市场空白
          <tspan x="32" dy="28">
            和增长潜力。
          </tspan>
        </text>
        <!-- Decorative outline element (example: wave line) -->
        <path d="M400 150 C420 160, 440 140, 460 150" class="stroke-accent" stroke-opacity="0.6" stroke-width="3"/>
        <circle cx="400" cy="150" r="5" class="fill-accent" fill-opacity="0.6"/>
        <circle cx="460" cy="150" r="5" class="fill-accent" fill-opacity="0.6"/>
      </g>

      <!-- Grid Item 2: Core Strengths -->
      <g transform="translate(512, 180)">
        <rect x="0" y="0" width="480" height="200" rx="12" ry="12" class="card-style"/>
        <text x="32" y="60" class="font-primary font-semibold text-section-title text-primary-color">
          核心优势
        </text>
        <text x="32" y="90" class="font-primary font-normal text-small text-secondary-color">
          Core Strengths
        </text>
        <text x="32" y="130" class="font-primary font-normal text-body text-primary-color">
          创新的技术和经验丰富的
          <tspan x="32" dy="28">
            团队是我们成功的基石。
          </tspan>
        </text>
        <!-- Decorative outline element (example: overlapping shapes) -->
        <rect x="380" y="130" width="80" height="40" rx="8" ry="8" class="stroke-primary" stroke-opacity="0.6" stroke-width="3"/>
        <line x1="380" y1="150" x2="460" y2="150" class="stroke-accent" stroke-width="2" stroke-opacity="0.8"/>
      </g>

      <!-- Grid Item 3: Investment Highlights -->
      <g transform="translate(0, 180 + 200 + 32)">
        <rect x="0" y="0" width="992" height="200" rx="12" ry="12" class="card-style"/>
        <text x="32" y="60" class="font-primary font-semibold text-section-title text-primary-color">
          投资亮点
        </text>
        <text x="32" y="90" class="font-primary font-normal text-small text-secondary-color">
          Investment Highlights
        </text>
        <text x="32" y="130" class="font-primary font-normal text-body text-primary-color">
          预计五年内实现三倍增长，拥有强劲的现金流和清晰的退出策略。
          <tspan x="32" dy="28">
            详细财务模型可在财务预测章节查阅。
          </tspan>
        </text>
        <!-- Simple Bar Chart Placeholder (stylized) -->
        <g transform="translate(700, 80)">
          <line x1="0" y1="100" x2="250" y2="100" class="stroke-secondary"/>
          <line x1="0" y1="0" x2="0" y2="100" class="stroke-secondary"/>
          <rect x="20" y="70" width="30" height="30" class="fill-primary" rx="4" ry="4"/>
          <rect x="70" y="40" width="30" height="60" class="fill-primary" rx="4" ry="4"/>
          <rect x="120" y="10" width="30" height="90" class="fill-accent" rx="4" ry="4"/>
          <rect x="170" y="50" width="30" height="50" class="fill-primary" rx="4" ry="4"/>
          <text x="120" y="5" text-anchor="middle" class="font-primary text-small text-accent-color font-semibold">↑300%</text>
        </g>
      </g>
    </g>
  </g>

  <!-- Footer -->
  <g id="footer">
    <!-- Progress Indicator (Simple Bar) -->
    <rect x="80" y="1020" width="1760" height="4" rx="2" ry="2" fill="var(--card-border)"/>
    <rect x="80" y="1020" width="352" height="4" rx="2" ry="2" fill="url(#accentGradientTransparent)"/> <!-- 20% progress (1760 * 0.2 = 352) -->

    <!-- Date and Author -->
    <text x="160" y="980" class="font-primary font-normal text-small text-secondary-color">
      {date}
    </text>
    <text x="1760" y="980" text-anchor="end" class="font-primary font-normal text-small text-secondary-color">
      {author}
    </text>
  </g>

</svg>