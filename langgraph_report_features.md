## 3. 核心特性

### 3.1 有状态性与循环工作流
LangGraph 最显著的优势是其对**有状态性**和**循环工作流**的显式支持。与 LangChain 等框架主要处理非循环的、有向无环图（DAG）不同，LangGraph 允许开发者定义包含循环的图，这对于实现类智能体行为至关重要。这意味着 LLM 可以在一个循环中被反复调用，根据前一个步骤的结果决定下一步的行动，从而实现迭代式推理和自我修正。这种能力是构建能够深入理解上下文、执行复杂任务并动态调整行为的 AI 系统的关键。

### 3.2 多智能体协调
LangGraph 提供了强大的多智能体系统编排能力。它通过其图谱式架构，能够精确控制不同智能体之间的交互方式和时机。开发者可以定义复杂的工作流，其中包含多个智能体作为图中的节点，它们之间通过边进行信息传递和协作。这使得可以构建一个“智能体团队”，它们可以在工作流的某些阶段并行工作，在另一些阶段则暂停等待特定决策或输入。这种精细的协调机制极大地提升了 AI 应用处理复杂任务的能力，使其能够模拟团队协作解决问题的场景。

### 3.3 人类介入 (Human-in-the-loop, HIL)
LangGraph 原生支持**人类介入（Human-in-the-loop, HIL）**工作流。这意味着在复杂的 LLM 应用中，可以在特定节点或决策点集成人工干预。例如，在智能体需要进行关键决策、处理模糊信息或在自动化流程中进行验证时，可以暂停工作流，等待人类用户提供指导或确认。这种能力对于需要高可靠性、高准确性或涉及敏感信息的应用场景至关重要，它确保了 AI 系统在提供便利的同时，也能受到人类的监督和控制。

### 3.4 图谱式架构
LangGraph 的核心是其**图谱式架构**，它将复杂的 LLM 应用逻辑建模为由节点（Nodes）和边（Edges）组成的有向图。节点代表智能体、工具调用或其他处理步骤，而边定义了信息流和控制流在这些节点之间的传递方式。这种架构提供了极高的灵活性和可控性，使得开发者能够：
*   **清晰地可视化和管理复杂流程：** 将多步骤的 AI 任务分解为离散的、可管理的部分。
*   **实现精细的状态管理：** 允许在图中的每个节点维护和更新应用程序的状态。
*   **支持动态路由和分支：** 根据中间结果或条件判断，灵活地将执行路径路由到不同的节点。
*   **促进模块化和可复用性：** 每个节点都可以是一个独立的、可复用的组件。