<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css"><![CDATA[
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .container-background-color { fill: #E0F2FE; }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Text Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* For main Chinese title */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* For English subtitle */
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; }

      /* General text styles for fill */
      .text-dark { fill: #1E293B; }
      .text-medium { fill: #64748B; }
      .text-light { fill: #94A3B8; }

      /* Custom Styles for this template */
      .shadow-effect { filter: url(#drop-shadow); }
      .title-gradient-fill { fill: url(#title-gradient); }
      .accent-shape-fill { fill: url(#accent-gradient); }
      .primary-shape-fill { fill: url(#primary-gradient); }

      /* Specific element styles for hover/focus (for future interaction) */
      .hover-effect:hover { opacity: 0.8; transition: opacity 0.3s ease; }
    ]]></style>

    <!-- Gradients -->
    <linearGradient id="primary-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accent-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="title-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="background-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Drop Shadow Filter for subtle depth -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="8" result="blur" />
      <feOffset dx="0" dy="4" result="offsetBlur" />
      <feFlood flood-color="rgba(0,0,0,0.1)" flood-opacity="1" result="offsetColor" />
      <feComposite in="offsetColor" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Simple Icon for decoration: Growth/Upward Trend -->
    <symbol id="icon-growth" viewBox="0 0 24 24">
      <path d="M16 6H22V12M22 6L12 16L7 11L2 16" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- Simple Icon for decoration: Chart/Data -->
    <symbol id="icon-chart" viewBox="0 0 24 24">
      <path d="M12 20V10M18 20V4M6 20V16" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <rect x="2" y="2" width="20" height="20" rx="2" stroke="#3B82F6" stroke-width="2" fill="none"/>
    </symbol>
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />
  <!-- Subtle background gradient overlay -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#background-gradient)" />

  <!-- Decorative Elements - Geometric shapes for visual impact -->
  <!-- Large semi-transparent primary color rectangle, rotated -->
  <rect x="1300" y="-50" width="800" height="600" rx="60" class="primary-shape-fill" opacity="0.08" transform="rotate(15 1300 -50)" />
  <!-- Smaller semi-transparent accent color rectangle, rotated -->
  <rect x="-100" y="600" width="700" height="500" rx="60" class="accent-shape-fill" opacity="0.08" transform="rotate(-10 -100 600)" />
  <!-- Abstract "growth" lines for dynamism -->
  <g stroke="#3B82F6" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" opacity="0.2">
    <path d="M150 900 L300 850 L450 920 L600 880" fill="none"/>
    <path d="M1700 150 L1550 200 L1400 130 L1250 170" fill="none"/>
  </g>

  <!-- Main Content Group, offset by page margins -->
  <g transform="translate(80, 60)">
    <!-- Logo Placeholder - Positioned top-left -->
    <image x="0" y="0" width="200" height="60" href="{logo_url}" />

    <!-- Main Title and Subtitle - Centered for prominence -->
    <g transform="translate(960, 400)" text-anchor="middle">
      <!-- Main Title (Chinese, large, bold, gradient) -->
      <text x="0" y="0" class="hero-title font-primary text-dark title-gradient-fill">
        <tspan x="0" dy="0">{title}</tspan>
      </text>
      <!-- Subtitle (English or supporting Chinese, smaller, medium weight) -->
      <text x="0" y="100" class="section-title font-primary text-medium">
        <tspan x="0" dy="0">{subtitle}</tspan>
      </text>
    </g>

    <!-- Decorative Icon (Chart/Data) - Subtle background element -->
    <g transform="translate(890, 680)" opacity="0.15">
      <use xlink:href="#icon-chart" width="140" height="140" class="accent-color" />
    </g>

    <!-- Bottom Left Information: Author -->
    <g transform="translate(0, 900)">
      <text x="0" y="0" class="body-text font-primary text-secondary-color">
        <tspan>{author}</tspan>
      </text>
    </g>

    <!-- Bottom Right Information: Date -->
    <g transform="translate(1760, 900)" text-anchor="end">
      <text x="0" y="0" class="body-text font-primary text-secondary-color">
        <tspan>{date}</tspan>
      </text>
    </g>

  </g>
</svg>