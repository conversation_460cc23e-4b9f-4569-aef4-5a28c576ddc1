<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .node { 
        fill: #lightblue; 
        stroke: #333; 
        stroke-width: 2; 
        rx: 10; 
      }
      .node-text { 
        font-family: Arial, sans-serif; 
        font-size: 12px; 
        text-anchor: middle; 
        dominant-baseline: middle; 
      }
      .edge { 
        stroke: #666; 
        stroke-width: 2; 
        marker-end: url(#arrowhead); 
      }
      .title { 
        font-family: Arial, sans-serif; 
        font-size: 18px; 
        font-weight: bold; 
        text-anchor: middle; 
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="title">🔥 LangGraph 增强工作流可视化</text>
  
  <!-- 简单顺序工作流 -->
  <text x="400" y="70" class="title" style="font-size: 14px;">简单顺序工作流</text>
  
  <!-- 节点 -->
  <rect x="50" y="100" width="100" height="40" class="node"/>
  <text x="100" y="120" class="node-text">开始</text>
  
  <rect x="200" y="100" width="100" height="40" class="node"/>
  <text x="250" y="120" class="node-text">数据收集</text>
  
  <rect x="350" y="100" width="100" height="40" class="node"/>
  <text x="400" y="120" class="node-text">数据分析</text>
  
  <rect x="500" y="100" width="100" height="40" class="node"/>
  <text x="550" y="120" class="node-text">报告生成</text>
  
  <rect x="650" y="100" width="100" height="40" class="node"/>
  <text x="700" y="120" class="node-text">结束</text>
  
  <!-- 边 -->
  <line x1="150" y1="120" x2="200" y2="120" class="edge"/>
  <line x1="300" y1="120" x2="350" y2="120" class="edge"/>
  <line x1="450" y1="120" x2="500" y2="120" class="edge"/>
  <line x1="600" y1="120" x2="650" y2="120" class="edge"/>
  
  <!-- 并行工作流 -->
  <text x="400" y="220" class="title" style="font-size: 14px;">并行多智能体工作流</text>
  
  <!-- 协调器 -->
  <rect x="350" y="250" width="100" height="40" class="node"/>
  <text x="400" y="270" class="node-text">协调器</text>
  
  <!-- 并行智能体 -->
  <rect x="150" y="340" width="100" height="40" class="node" style="fill: #lightgreen;"/>
  <text x="200" y="360" class="node-text">研究智能体</text>
  
  <rect x="350" y="340" width="100" height="40" class="node" style="fill: #lightgreen;"/>
  <text x="400" y="360" class="node-text">分析智能体</text>
  
  <rect x="550" y="340" width="100" height="40" class="node" style="fill: #lightgreen;"/>
  <text x="600" y="360" class="node-text">写作智能体</text>
  
  <!-- 合并器 -->
  <rect x="350" y="430" width="100" height="40" class="node" style="fill: #lightyellow;"/>
  <text x="400" y="450" class="node-text">结果合并器</text>
  
  <!-- 输出 -->
  <rect x="350" y="520" width="100" height="40" class="node" style="fill: #lightcoral;"/>
  <text x="400" y="540" class="node-text">最终输出</text>
  
  <!-- 并行工作流的边 -->
  <line x1="400" y1="290" x2="200" y2="340" class="edge"/>
  <line x1="400" y1="290" x2="400" y2="340" class="edge"/>
  <line x1="400" y1="290" x2="600" y2="340" class="edge"/>
  
  <line x1="200" y1="380" x2="400" y2="430" class="edge"/>
  <line x1="400" y1="380" x2="400" y2="430" class="edge"/>
  <line x1="600" y1="380" x2="400" y2="430" class="edge"/>
  
  <line x1="400" y1="470" x2="400" y2="520" class="edge"/>
  
  <!-- 图例 -->
  <text x="50" y="590" style="font-size: 10px; font-family: Arial;">
    🔵 普通节点  🟢 智能体  🟡 处理器  🔴 输出节点
  </text>
  
</svg>