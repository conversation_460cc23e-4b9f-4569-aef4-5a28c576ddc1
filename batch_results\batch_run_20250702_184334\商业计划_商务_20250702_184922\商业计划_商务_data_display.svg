<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 样式定义 -->
    <style type="text/css">
      <![CDATA[
        /* 颜色方案 */
        .bg-color { fill: #F8FAFC; } /* 背景色 */
        .primary-color { fill: #1E40AF; } /* 主色 */
        .secondary-color { fill: #475569; } /* 辅助色 */
        .accent-color { fill: #3B82F6; } /* 强调色 */
        .text-primary { fill: #1E293B; } /* 主要文字色 */
        .text-secondary { fill: #64748B; } /* 次要文字色 */
        .card-bg { fill: #FFFFFF; } /* 卡片背景色 */
        .card-border { stroke: #BAE6FD; } /* 卡片边框色 */
        .success-color { fill: #10B981; } /* 成功色 */
        .warning-color { fill: #F59E0B; } /* 警告色 */
        .error-color { fill: #EF4444; } /* 错误色 */

        /* 字体系统 */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        /* 字体大小和字重 */
        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
        .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
        .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
        .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
        .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

        /* 文字对齐 */
        .text-center { text-anchor: middle; }
        .text-left { text-anchor: start; }
        .text-right { text-anchor: end; }

        /* 卡片阴影 */
        .card-shadow { filter: url(#drop-shadow); }

        /* 图表标签 */
        .chart-label { font-size: 18px; font-weight: 500; fill: #64748B; }
        .chart-value { font-size: 24px; font-weight: 600; fill: #1E293B; }
        .chart-axis-label { font-size: 16px; fill: #64748B; }

        /* 关键指标超大数字 */
        .metric-value-large { font-size: 80px; font-weight: 700; fill: #1E40AF; }
        .metric-label-small { font-size: 24px; font-weight: 500; fill: #475569; }

        /* 分割线 */
        .divider-line { stroke: #BAE6FD; stroke-width: 1px; }

        /* 图标样式 */
        .icon-outline { stroke: #3B82F6; stroke-width: 2px; fill: none; }
        .icon-fill { fill: #3B82F6; }
      ]]>
    </style>

    <!-- 渐变定义 -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.2" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.2" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 可复用图标定义 -->
    <g id="icon-chart">
      <rect x="0" y="10" width="6" height="10" class="icon-fill" />
      <rect x="10" y="5" width="6" height="15" class="icon-fill" />
      <rect x="20" y="0" width="6" height="20" class="icon-fill" />
    </g>

    <g id="icon-target">
      <circle cx="13" cy="13" r="12" class="icon-outline" />
      <circle cx="13" cy="13" r="8" class="icon-outline" />
      <circle cx="13" cy="13" r="4" class="icon-fill" />
    </g>

    <g id="icon-growth">
      <polyline points="0,20 8,10 16,15 26,0" class="icon-outline" />
      <polygon points="26,0 20,0 26,6" class="icon-fill" />
    </g>

    <g id="icon-finance">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v4h-2V7zm0 6h2v2h-2v-2z" class="icon-fill"/>
    </g>

    <g id="icon-warning">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm0-4c-.55 0-1-.45-1-1V7c0-.55.45-1 1-1s1 .45 1 1v5c0 .55-.45 1-1 1z" fill="#EF4444"/>
    </g>

  </defs>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- 装饰性几何图形 -->
  <circle cx="1800" cy="100" r="150" fill="#3B82F6" fill-opacity="0.1" />
  <rect x="1600" y="850" width="300" height="150" fill="#1E40AF" fill-opacity="0.05" transform="rotate(15 1750 925)" rx="12" ry="12" />
  <polygon points="100,0 250,0 100,150" fill="#475569" fill-opacity="0.05" />

  <!-- 顶部区域：页眉、Logo、页码 -->
  <g id="header">
    <text x="80" y="90" class="text-secondary small-text font-primary text-left">商业计划书</text>
    <text x="1840" y="90" class="text-secondary small-text font-primary text-right">页面 6/10</text>
    <!-- Logo 占位符 -->
    <rect x="900" y="60" width="120" height="40" fill="#1E40AF" fill-opacity="0.1" rx="8" ry="8" />
    <text x="960" y="88" class="text-primary content-title font-primary text-center">公司标识</text>
    <!-- 如果需要实际Logo图片，请取消注释并替换 {logo_url} -->
    <!-- <image xlink:href="{logo_url}" x="900" y="60" width="120" height="40" /> -->
  </g>

  <!-- 主内容区域 -->
  <g id="main-content" transform="translate(80, 160)">
    <!-- 标题和副标题 -->
    <text x="0" y="0" class="main-title font-primary text-primary">
      <tspan font-weight="700">市场与财务洞察</tspan>
      <tspan x="0" dy="60" class="content-title font-secondary text-secondary">Market and Financial Insights</tspan>
    </text>

    <!-- 分割线 -->
    <line x1="0" y1="100" x2="1760" y2="100" class="divider-line" />

    <!-- 数据可视化区域 (Bento Grid 风格) -->
    <g id="data-visuals" transform="translate(0, 140)">
      <!-- 左侧图表卡片 -->
      <rect x="0" y="0" width="1150" height="600" rx="12" ry="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
      <text x="40" y="50" class="section-title font-primary text-primary">市场增长趋势</text>
      <text x="40" y="90" class="small-text font-secondary text-secondary">Market Growth Trend</text>

      <!-- 折线图占位符 -->
      <g transform="translate(80, 150)">
        <!-- 坐标轴 -->
        <line x1="0" y1="350" x2="1000" y2="350" stroke="#E0E0E0" stroke-width="1" /> <!-- X轴 -->
        <line x1="0" y1="0" x2="0" y2="350" stroke="#E0E0E0" stroke-width="1" /> <!-- Y轴 -->

        <!-- 示例折线路径 (使用强调色渐变) -->
        <path d="M0,300 C200,150 400,250 600,100 800,50 1000,10" fill="none" stroke="url(#accentGradient)" stroke-width="4" stroke-linecap="round" />

        <!-- 数据点 (圆形) -->
        <circle cx="0" cy="300" r="6" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" />
        <circle cx="200" cy="150" r="6" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" />
        <circle cx="400" cy="250" r="6" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" />
        <circle cx="600" cy="100" r="6" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" />
        <circle cx="800" cy="50" r="6" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" />
        <circle cx="1000" cy="10" r="6" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" />

        <!-- X轴标签 -->
        <text x="0" y="370" class="chart-axis-label text-left">2020</text>
        <text x="200" y="370" class="chart-axis-label text-center">2021</text>
        <text x="400" y="370" class="chart-axis-label text-center">2022</text>
        <text x="600" y="370" class="chart-axis-label text-center">2023</text>
        <text x="800" y="370" class="chart-axis-label text-center">2024</text>
        <text x="1000" y="370" class="chart-axis-label text-right">2025</text>

        <!-- Y轴标签 -->
        <text x="-10" y="350" class="chart-axis-label text-right" dy="5">0%</text>
        <text x="-10" y="260" class="chart-axis-label text-right" dy="5">25%</text>
        <text x="-10" y="170" class="chart-axis-label text-right" dy="5">50%</text>
        <text x="-10" y="80" class="chart-axis-label text-right" dy="5">75%</text>
        <text x="-10" y="0" class="chart-axis-label text-right" dy="5">100%</text>

        <!-- 当前值标注 -->
        <rect x="900" y="-30" width="120" height="40" rx="6" ry="6" fill="#3B82F6" />
        <text x="960" y="-5" class="small-text font-primary text-center" fill="#FFFFFF">预测增长 85%</text>
      </g>

      <!-- 右侧数据卡片 (Bento Grid 风格) -->
      <g transform="translate(1180, 0)">
        <!-- 卡片 1: 关键指标 1 -->
        <rect x="0" y="0" width="580" height="290" rx="12" ry="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
        <use xlink:href="#icon-chart" transform="translate(40, 40) scale(1.5)" class="icon-fill" />
        <text x="40" y="100" class="content-title font-primary text-primary">总收入预测</text>
        <text x="40" y="140" class="small-text font-secondary text-secondary">Total Revenue Forecast</text>
        <text x="40" y="240" class="metric-value-large font-primary text-primary">$1.2B</text>
        <text x="280" y="240" class="metric-label-small font-primary text-secondary">2025年目标</text>

        <!-- 卡片 2: 关键指标 2 -->
        <rect x="0" y="310" width="580" height="290" rx="12" ry="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
        <use xlink:href="#icon-target" transform="translate(40, 350) scale(1.5)" class="icon-fill" />
        <text x="40" y="410" class="content-title font-primary text-primary">市场占有率</text>
        <text x="40" y="450" class="small-text font-secondary text-secondary">Market Share</text>
        <text x="40" y="550" class="metric-value-large font-primary text-primary">25.3%</text>
        <text x="280" y="550" class="metric-label-small font-primary text-secondary">行业领先</text>
      </g>
    </g>

    <!-- 底部统计与风险评估区域 -->
    <g id="bottom-stats" transform="translate(0, 780)">
      <text x="0" y="0" class="section-title font-primary text-primary">关键统计与风险评估</text>
      <text x="0" y="40" class="small-text font-secondary text-secondary">Key Statistics and Risk Assessment</text>

      <!-- 统计卡片 (水平布局) -->
      <g transform="translate(0, 80)">
        <!-- 统计卡片 1 -->
        <rect x="0" y="0" width="560" height="160" rx="12" ry="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
        <use xlink:href="#icon-growth" transform="translate(40, 40) scale(1.2)" class="icon-fill" />
        <text x="100" y="60" class="content-title font-primary text-primary">用户增长</text>
        <text x="100" y="90" class="body-text font-primary success-color">+150%</text>
        <text x="100" y="120" class="small-text font-secondary text-secondary">过去12个月</text>

        <!-- 统计卡片 2 -->
        <rect x="600" y="0" width="560" height="160" rx="12" ry="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
        <use xlink:href="#icon-finance" transform="translate(640, 40) scale(1.2)" class="icon-fill" />
        <text x="700" y="60" class="content-title font-primary text-primary">盈利能力</text>
        <text x="700" y="90" class="body-text font-primary text-primary">持续增长</text>
        <text x="700" y="120" class="small-text font-secondary text-secondary">EBITDA 利润率 20%</text>

        <!-- 统计卡片 3 (风险占位符) -->
        <rect x="1200" y="0" width="560" height="160" rx="12" ry="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
        <use xlink:href="#icon-warning" transform="translate(1240, 40) scale(1.2)" class="error-color" />
        <text x="1300" y="60" class="content-title font-primary text-primary">潜在风险</text>
        <text x="1300" y="90" class="body-text font-primary text-secondary">市场波动性</text>
        <text x="1300" y="120" class="small-text font-secondary text-secondary">竞争加剧</text>
      </g>
    </g>
  </g>

  <!-- 底部页脚 -->
  <g id="footer">
    <text x="960" y="1040" class="text-secondary small-text font-primary text-center">
      © 2024 {公司名称}。保留所有权利。
    </text>
  </g>

</svg>