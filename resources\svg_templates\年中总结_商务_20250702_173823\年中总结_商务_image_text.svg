<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette & Text Styles -->
    <style type="text/css">
      /* Core Aesthetic Colors (Prioritizing enhanced requirements) */
      .color-background { fill: #0A0A0A; } /* Very Dark Gray/Black for Bento Grid style */
      .color-highlight { fill: #E31937; } /* Tesla Red for primary accent */

      /* Original Blue Palette for subtle elements, borders, and secondary accents */
      .color-primary-blue { fill: #1E40AF; } /* Deep Blue */
      .color-secondary-blue { fill: #475569; } /* Slate Gray */
      .color-accent-blue { fill: #3B82F6; } /* Vivid Blue */
      .color-card-border { stroke: #BAE6FD; } /* Light Blue for card borders */

      /* Text Colors adapted for dark background */
      .text-primary-darkbg { fill: #F8FAFC; } /* Off-white for main text */
      .text-secondary-darkbg { fill: #94A3B8; } /* Medium gray for secondary text */
      .text-light-darkbg { fill: #64748B; } /* Darker gray for very subtle text */
      .text-highlight { fill: #E31937; } /* Tesla Red for emphasized text */

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes & Weights */
      .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* Bold, for super large numbers */
      .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* Bold */
      .text-section-title { font-size: 36px; font-weight: 700; line-height: 1.1; } /* Bold */
      .text-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* Semibold */
      .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; } /* Normal */
      .text-small { font-size: 16px; font-weight: 400; line-height: 1.4; } /* Normal */
      .text-caption { font-size: 14px; font-weight: 300; line-height: 1.4; } /* Light */
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }
      .font-normal { font-weight: 400; }
      .font-light { font-weight: 300; }
    </style>

    <!-- Gradients for decorative elements (Tesla Red and Blue, with transparency) -->
    <linearGradient id="gradientHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E31937;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#E31937;stop-opacity:0.1" />
    </linearGradient>

    <linearGradient id="gradientAccentBlue" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:0.1" />
    </linearGradient>

    <!-- Card Shadow Effect (Filter) -->
    <filter id="cardShadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset dx="0" dy="4" result="offsetblur"/>
      <feGaussianBlur in="offsetblur" stdDeviation="8" result="shadowblur"/>
      <feColorMatrix in="shadowblur" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" result="shadowcolor"/>
      <feMerge>
        <feMergeNode in="shadowcolor"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon System (outline style, Tesla Red for consistency with highlight) -->
    <symbol id="icon_arrow_right" viewBox="0 0 24 24">
      <path d="M5 12h14M12 5l7 7-7 7" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <symbol id="icon_chart" viewBox="0 0 24 24">
      <path d="M3 3v18h18M18 10l-4 4-6-6-4 4" stroke="#E31937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="color-background"/>

  <!-- Decorative Bento Grid style elements -->
  <g id="bento_decorations">
    <!-- Large, subtle rectangles with gradients -->
    <rect x="80" y="60" width="800" height="400" rx="20" ry="20" fill="url(#gradientHighlight)" opacity="0.05"/>
    <rect x="1040" y="580" width="800" height="400" rx="20" ry="20" fill="url(#gradientAccentBlue)" opacity="0.05"/>

    <!-- Geometric shapes with outline style -->
    <rect x="1700" y="80" width="120" height="120" rx="15" ry="15" stroke="#E31937" stroke-width="2" fill="none" opacity="0.2"/>
    <circle cx="150" cy="950" r="80" stroke="#3B82F6" stroke-width="2" fill="none" opacity="0.15"/>

    <!-- Subtle line patterns -->
    <path d="M80 500 L 400 500 M80 550 L 350 550" stroke="#E31937" stroke-width="1" opacity="0.08"/>
    <path d="M1840 500 L 1500 500 M1840 550 L 1550 550" stroke="#3B82F6" stroke-width="1" opacity="0.08"/>
  </g>

  <!-- Header Section -->
  <g id="header_section">
    <!-- Logo Placeholder (Top Left) -->
    <rect x="80" y="60" width="120" height="40" fill="none" stroke="#94A3B8" stroke-width="1" rx="5"/>
    <text x="140" y="85" class="text-small font-light text-secondary-darkbg" text-anchor="middle">
      <tspan>{logo_url}</tspan>
    </text>

    <!-- Page Number (Top Right) -->
    <text x="1840" y="85" class="text-small font-light text-secondary-darkbg" text-anchor="end">5/10</text>
  </g>

  <!-- Main Content Area - Image and Text Balance -->
  <g id="main_content" transform="translate(80, 120)">
    <!-- Left Section: Image Display (Card Style) -->
    <g id="image_section">
      <!-- Main Image Container (Larger) -->
      <rect x="0" y="0" width="800" height="480" rx="12" ry="12" class="color-card-border" stroke-width="2" fill="#1A1A1A" filter="url(#cardShadow)"/>
      <text x="400" y="240" class="text-content-title text-secondary-darkbg font-normal" text-anchor="middle" dominant-baseline="middle">{image_url} (主图)</text>

      <!-- Smaller Supporting Image (Bottom Left) -->
      <rect x="0" y="520" width="380" height="240" rx="12" ry="12" class="color-card-border" stroke-width="1" fill="#1A1A1A" filter="url(#cardShadow)"/>
      <text x="190" y="640" class="text-small text-secondary-darkbg font-normal" text-anchor="middle" dominant-baseline="middle">{image_url} (详情)</text>

      <!-- Another Smaller Supporting Image (Bottom Right of Left Section) -->
      <rect x="420" y="520" width="380" height="240" rx="12" ry="12" class="color-card-border" stroke-width="1" fill="#1A1A1A" filter="url(#cardShadow)"/>
      <text x="610" y="640" class="text-small text-secondary-darkbg font-normal" text-anchor="middle" dominant-baseline="middle">{image_url} (分析)</text>
    </g>

    <!-- Right Section: Text Content -->
    <g id="text_section" transform="translate(880, 0)">
      <!-- Super Large Number/Text Highlight -->
      <g id="key_metric">
        <text x="0" y="80" class="font-primary text-hero-title text-highlight">
          <tspan>85%</tspan>
        </text>
        <text x="200" y="60" class="font-primary text-content-title text-primary-darkbg font-semibold">
          <tspan dx="10">增长</tspan>
        </text>
        <text x="200" y="100" class="font-primary text-small text-secondary-darkbg font-light">
          <tspan dx="10">Year-over-Year Growth</tspan>
        </text>
        <use xlink:href="#icon_chart" x="700" y="20" width="64" height="64" class="color-highlight" opacity="0.4"/>
      </g>

      <!-- Main Title -->
      <text x="0" y="180" class="font-primary text-main-title text-primary-darkbg">
        <tspan>年中总结：</tspan>
        <tspan class="font-bold">成果和展望</tspan>
      </text>
      <text x="0" y="225" class="font-primary text-content-title text-secondary-darkbg">
        <tspan>Mid-Year Review: Achievements 和#38; Outlook</tspan>
      </text>

      <!-- Subtitle/Introduction -->
      <text x="0" y="290" class="font-primary text-body text-primary-darkbg">
        <tspan>本年度上半年，我们在各项业务中取得了显著进展，</tspan>
        <tspan x="0" dy="1.6em">特别是在市场拓展和技术创新方面表现突出。此页将</tspan>
        <tspan x="0" dy="1.6em">详细展示关键成果，并结合数据分析。</tspan>
      </text>
      <text x="0" y="380" class="font-primary text-small text-secondary-darkbg">
        <tspan>In the first half of this year, we achieved significant progress in various businesses, </tspan>
        <tspan x="0" dy="1.4em">especially in market expansion and technological innovation. This page will detail </tspan>
        <tspan x="0" dy="1.4em">key achievements and provide data analysis.</tspan>
      </text>

      <!-- Content sections with bullet points or key takeaways -->
      <g id="content_points">
        <text x="0" y="480" class="font-primary text-content-title text-primary-darkbg font-semibold">
          <tspan>核心突破</tspan>
          <tspan class="text-small text-secondary-darkbg font-light" dx="10">Key Breakthroughs</tspan>
        </text>
        <g transform="translate(0, 520)">
          <use xlink:href="#icon_arrow_right" x="0" y="-14" width="24" height="24"/>
          <text x="32" y="0" class="font-primary text-body text-primary-darkbg">
            <tspan>市场份额提升：成功进入新市场，实现</tspan>
            <tspan class="text-highlight font-bold">30%</tspan>
            <tspan>的市场份额增长。</tspan>
          </text>
          <text x="32" y="28" class="font-primary text-small text-secondary-darkbg">
            <tspan>Market Share Growth: Successfully entered new markets, achieving a </tspan>
            <tspan class="text-highlight font-bold">30%</tspan>
            <tspan> increase.</tspan>
          </text>
        </g>
        <g transform="translate(0, 590)">
          <use xlink:href="#icon_arrow_right" x="0" y="-14" width="24" height="24"/>
          <text x="32" y="0" class="font-primary text-body text-primary-darkbg">
            <tspan>产品创新：发布两款革命性新产品，获得用户</tspan>
            <tspan class="text-highlight font-bold">高评价</tspan>
            <tspan>。</tspan>
          </text>
          <text x="32" y="28" class="font-primary text-small text-secondary-darkbg">
            <tspan>Product Innovation: Launched two revolutionary new products, receiving </tspan>
            <tspan class="text-highlight font-bold">high user ratings</tspan>
            <tspan>.</tspan>
          </text>
        </g>
        <g transform="translate(0, 660)">
          <use xlink:href="#icon_arrow_right" x="0" y="-14" width="24" height="24"/>
          <text x="32" y="0" class="font-primary text-body text-primary-darkbg">
            <tspan>团队协作：跨部门协作效率提升</tspan>
            <tspan class="text-highlight font-bold">15%</tspan>
            <tspan>，项目交付周期缩短。</tspan>
          </text>
          <text x="32" y="28" class="font-primary text-small text-secondary-darkbg">
            <tspan>Team Collaboration: Cross-departmental efficiency increased by </tspan>
            <tspan class="text-highlight font-bold">15%</tspan>
            <tspan>, shortening project delivery cycles.</tspan>
          </text>
        </g>
      </g>
    </g>
  </g>

  <!-- Footer Section -->
  <g id="footer_section">
    <!-- Decorative Divider Line (Blue from original palette for business touch) -->
    <line x1="80" y1="1020" x2="1840" y2="1020" stroke="#1E40AF" stroke-width="1" opacity="0.3"/>
    <circle cx="180" cy="1020" r="5" fill="#3B82F6" opacity="0.5"/>
    <circle cx="1740" cy="1020" r="5" fill="#E31937" opacity="0.5"/>

    <!-- Date and Author Placeholder -->
    <text x="80" y="1050" class="text-caption text-secondary-darkbg font-light">
      <tspan>{date}</tspan>
      <tspan dx="20">| {author}</tspan>
    </text>
  </g>
</svg>