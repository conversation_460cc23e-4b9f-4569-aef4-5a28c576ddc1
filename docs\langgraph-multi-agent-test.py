#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangGraph信息搜集-评估-循环-总结工作流测试 - 基于JIMU MCP工具调用方式
实现信息搜集、质量评估、条件循环、结果总结的智能体协作

运行要求：
1. 确保已安装LangGraph: pip install langgraph
2. 确保后端服务正在运行 (http://************:9999)
3. 确保OpenAI兼容接口可用 (http://jimu.ffa.chat/v1)
"""

import asyncio
import json
import logging
import os
import time
import re
from datetime import datetime
from typing import TypedDict, List, Dict, Optional, Any, Literal
import requests
import sys
from pathlib import Path

# 添加后端路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from langchain_openai import ChatOpenAI
    from langgraph.graph import StateGraph, END, START
    from langgraph.checkpoint.memory import MemorySaver
except ImportError as e:
    print(f"缺少依赖库: {e}")
    print("请安装: pip install langgraph langchain langchain-openai")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# JIMU项目配置
JIMU_SEARCH_BASE_URL = "http://************:9999/api/v1"
JIMU_LLM_BASE_URL = "http://jimu.ffa.chat/v1"
JIMU_API_KEY = "sk-Z0MdU0NAXCmiwYF_iz-gu4aqoEg8XSYGUL3IR32geJ7ZlaflLmzJVENtrEk"
SYSTEM_TOKEN = "sk-RMerNTL9uP4lmHh_QhIY-eX5bQyCOoVTxOJTfx3B4O1FJ1Yb83mqbcT1dWU"

class EnhancedMCPToolParser:
    """增强的MCP工具解析器，支持更多工具类型"""
    
    def __init__(self):
        self.supported_tool_types = [
            'use_mcp_tool',
            'access_mcp_resource',
            'write_file',
            'read_file',
            'generate_image',
            'process_text'
        ]
    
    def parse_tool_calls(self, text: str) -> List[Dict]:
        """解析文本中的工具调用"""
        tool_calls = []
        
        # 解析 use_mcp_tool
        tool_pattern = r'<use_mcp_tool>\s*<server_name>(.*?)</server_name>\s*<tool_name>(.*?)</tool_name>\s*<arguments>\s*(.*?)\s*</arguments>\s*</use_mcp_tool>'
        matches = re.findall(tool_pattern, text, re.DOTALL)
        
        for match in matches:
            try:
                tool_call = {
                    'type': 'use_mcp_tool',
                    'server_name': match[0].strip(),
                    'tool_name': match[1].strip(),
                    'arguments': json.loads(match[2].strip()) if match[2].strip() else {}
                }
                tool_calls.append(tool_call)
            except Exception as e:
                logger.error(f"解析工具调用失败: {e}")
        
        # 解析文件操作工具
        file_patterns = [
            (r'<write_file>\s*<path>(.*?)</path>\s*<content>(.*?)</content>\s*</write_file>', 'write_file'),
            (r'<read_file>\s*<path>(.*?)</path>\s*</read_file>', 'read_file')
        ]
        
        for pattern, tool_type in file_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                if tool_type == 'write_file':
                    tool_call = {
                        'type': tool_type,
                        'path': match[0].strip(),
                        'content': match[1].strip()
                    }
                else:  # read_file
                    tool_call = {
                        'type': tool_type,
                        'path': match[0].strip()
                    }
                tool_calls.append(tool_call)
        
        return tool_calls
    
    def has_tool_calls(self, text: str) -> bool:
        """检查文本是否包含工具调用"""
        return bool(self.parse_tool_calls(text))

class MultiAgentLLMManager:
    """多智能体LLM管理器 - 搜索评估循环版"""
    
    def __init__(self):
        self.base_url = JIMU_LLM_BASE_URL
        self.api_key = JIMU_API_KEY
        self.models = [
            "gemini-2.5-flash-preview-05-20", "deepseek-chat", "claude-3-5-sonnet-20241022"
        ]
        self.current_model = "gemini-2.5-flash-preview-05-20"
        self.cache = {}
        self.tool_parser = EnhancedMCPToolParser()
    
    def call_llm(self, messages: List[Dict], model: Optional[str] = None, 
                 temperature: float = 0.7, agent_type: str = "general") -> str:
        """调用LLM接口，支持不同智能体类型"""
        model = model or self.current_model
        
        # 根据智能体类型添加特定的系统提示词
        enhanced_messages = messages.copy()
        system_prompt = self._get_agent_system_prompt(agent_type)
        
        if not any(msg.get("role") == "system" for msg in enhanced_messages):
            enhanced_messages.insert(0, {"role": "system", "content": system_prompt})
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model,
                "messages": enhanced_messages,
                "temperature": temperature,
                "max_tokens": 20480
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                return f"LLM调用失败: {response.status_code}"
                
        except Exception as e:
            return f"LLM调用异常: {str(e)}"
    
    def _get_agent_system_prompt(self, agent_type: str) -> str:
        """获取不同智能体类型的系统提示词"""
        prompts = {
            "info_collector": """
你是信息搜集专家。你的任务是使用多种搜索工具收集全面、准确的信息。

当需要搜索时，使用以下格式：

<use_mcp_tool>
<server_name>search</server_name>
<tool_name>tavily_search</tool_name>
<arguments>
{
  "query": "搜索关键词",
  "service": "tavily"
}
</arguments>
</use_mcp_tool>

搜索策略：
1. 使用不同的关键词组合进行多轮搜索
2. 尝试不同的搜索服务（tavily, exa, jina, firecrawl）
3. 从不同角度收集信息（基础概念、应用案例、最新发展等）
4. 每次搜索后简要总结收集到的信息

务必进行多次搜索以获得全面的信息覆盖。
""",
            
            "info_evaluator": """
你是信息质量评估专家。你的任务是评估搜索收集到的信息是否足够全面和准确。

评估标准：
1. 信息完整性：是否覆盖了主题的主要方面
2. 信息质量：来源是否可靠，内容是否准确
3. 信息深度：是否有足够的细节和深度
4. 信息时效性：是否包含最新的发展动态
5. 信息多样性：是否从多个角度获取了信息

评估结果格式：
- 质量分数：0-100分
- 评估结论：PASS（通过）或 NEED_MORE（需要更多信息）
- 不足之处：列出需要补充的信息类型
- 改进建议：具体的搜索建议

请提供详细的评估分析。
""",
            
            "result_summarizer": """
你是结果总结专家。你的任务是基于所有收集到的信息生成全面、准确的总结报告。

总结要求：
1. 结构清晰：使用标准的报告格式
2. 内容全面：涵盖所有重要信息点
3. 逻辑严密：信息组织有条理
4. 语言专业：使用准确的专业术语
5. 观点平衡：客观呈现不同观点

总结格式：
# 主题总结报告

## 概述
简要介绍主题的核心内容

## 主要内容
### 1. 基础信息
### 2. 核心特点
### 3. 应用场景
### 4. 发展趋势

## 关键信息汇总
- 重要数据和统计
- 专家观点
- 典型案例

## 结论与展望

生成高质量的综合报告。
"""
        }
        
        return prompts.get(agent_type, "你是AI助手，根据用户需求智能选择工具完成任务。")

class EnhancedSearchManager:
    """增强的搜索管理器，支持多搜索源和质量评估"""
    
    def __init__(self):
        self.base_url = JIMU_SEARCH_BASE_URL
        self.token = SYSTEM_TOKEN
        self.services = ["tavily", "exa", "jina", "firecrawl"]
        self.search_history = []
    
    def multi_source_search(self, query: str, max_results: int = 5, services: Optional[List[str]] = None) -> Dict[str, List[Dict]]:
        """多源搜索，返回分类结果"""
        services = services or self.services[:2]  # 默认使用前两个服务
        results = {}
        
        for service in services:
            try:
                service_results = self.search(query, service, max_results)
                results[service] = service_results
                
                # 记录搜索历史
                self.search_history.append({
                    "query": query,
                    "service": service,
                    "results_count": len(service_results),
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                results[service] = [{"error": f"{service}搜索失败: {str(e)}"}]
        
        return results
    
    def search(self, query: str, service: str = "tavily", max_results: int = 5) -> List[Dict]:
        """执行单一搜索，使用正确的JIMU API格式"""
        try:
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }
            
            # 修正API参数格式，使用service而不是provider
            payload = {
                "query": query,
                "service": service,
                "options": {
                    "max_results": max_results
                }
            }
            
            response = requests.post(
                f"{self.base_url}/search",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                # 适配不同服务的响应格式
                if service == "tavily" and "results" in data:
                    return data.get("results", [])
                elif service == "exa" and "results" in data:
                    return data.get("results", [])
                elif service == "jina" and "results" in data:
                    return data.get("results", [])
                elif service == "firecrawl":
                    if "success" in data and data["success"] and "data" in data:
                        return data.get("data", [])
                    elif "results" in data:
                        return data.get("results", [])
                
                # 通用处理
                return data.get("results", data.get("data", []))
            else:
                return [{"error": f"搜索失败: {response.status_code}"}]
                
        except Exception as e:
            return [{"error": f"搜索异常: {str(e)}"}]
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        total_searches = len(self.search_history)
        service_counts = {}
        total_results = 0
        
        for search in self.search_history:
            service = search["service"]
            service_counts[service] = service_counts.get(service, 0) + 1
            total_results += search.get("results_count", 0)
        
        return {
            "total_searches": total_searches,
            "total_results": total_results,
            "service_distribution": service_counts,
            "average_results_per_search": total_results / total_searches if total_searches > 0 else 0
        }

class FileManager:
    """文件管理工具"""
    
    def __init__(self, base_dir: str = "./generated_content"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    def write_file(self, filename: str, content: str) -> Dict[str, Any]:
        """写入文件"""
        try:
            file_path = self.base_dir / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return {
                "success": True,
                "path": str(file_path),
                "message": f"文件已保存: {filename}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"文件写入失败: {str(e)}"
            }
    
    def read_file(self, filename: str) -> Dict[str, Any]:
        """读取文件"""
        try:
            file_path = self.base_dir / filename
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {filename}"
                }
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "success": True,
                "content": content,
                "message": f"文件已读取: {filename}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"文件读取失败: {str(e)}"
            }

# 搜索评估循环状态定义
class SearchEvalState(TypedDict):
    topic: str                          # 主题
    messages: List[Dict[str, str]]      # 消息历史
    search_results: List[Dict[str, Any]]  # 累积搜索结果
    current_round: int                  # 当前搜索轮次
    max_rounds: int                     # 最大搜索轮次
    evaluation_score: float             # 信息质量评分
    evaluation_result: str              # 评估结论 (PASS/NEED_MORE)
    evaluation_feedback: str            # 评估反馈
    search_queries: List[str]           # 已使用的搜索查询
    search_services: List[str]          # 已使用的搜索服务
    final_summary: str                  # 最终总结
    current_agent: str                  # 当前智能体
    workflow_stage: str                 # 工作流阶段
    tool_usage: List[Dict]              # 工具使用记录
    search_statistics: Dict[str, Any]   # 搜索统计信息
    metadata: Dict[str, Any]            # 元数据

class SearchEvalTestRunner:
    """搜索评估循环测试运行器"""
    
    def __init__(self):
        self.llm_manager = MultiAgentLLMManager()
        self.search_manager = EnhancedSearchManager()
        self.file_manager = FileManager()
        self.test_results = []
        
        # 工作流配置
        self.max_search_rounds = 3
        self.quality_threshold = 75.0
        self.search_services_rotation = ["tavily", "exa", "jina", "firecrawl"]
    
    def log_test(self, test_name: str, result: str, success: bool = True):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "result": result,
            "success": success,
            "timestamp": datetime.now().isoformat()
        })
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {result}")
    
    def execute_tool_calls(self, text: str, agent_type: str) -> List[Dict]:
        """执行工具调用，支持多种搜索服务"""
        tool_calls = self.llm_manager.tool_parser.parse_tool_calls(text)
        results = []
        
        for tool_call in tool_calls:
            try:
                if tool_call["type"] == "use_mcp_tool":
                    if tool_call["tool_name"] == "tavily_search":
                        query = tool_call["arguments"].get("query", "")
                        service = tool_call["arguments"].get("service", "tavily")
                        max_results = tool_call["arguments"].get("max_results", 5)
                        
                        search_results = self.search_manager.search(query, service, max_results)
                        results.extend(search_results)
                    else:
                        results.append({"info": f"模拟执行工具: {tool_call['tool_name']}"})
                
                elif tool_call["type"] == "write_file":
                    file_result = self.file_manager.write_file(
                        tool_call["path"], 
                        tool_call["content"]
                    )
                    results.append(file_result)
                
                elif tool_call["type"] == "read_file":
                    file_result = self.file_manager.read_file(tool_call["path"])
                    results.append(file_result)
                
            except Exception as e:
                results.append({"error": f"工具执行失败: {str(e)}"})
        
        return results 

    def info_collector_agent(self, state: SearchEvalState) -> SearchEvalState:
        """信息搜集智能体 - 多轮多源搜索"""
        topic = state["topic"]
        current_round = state["current_round"]
        search_queries = state["search_queries"] if "search_queries" in state else []
        search_services = state["search_services"] if "search_services" in state else []
        
        # 根据轮次选择搜索策略
        if current_round == 1:
            # 第一轮：基础搜索
            queries = [
                f"{topic} 基础概念",
                f"{topic} 定义和原理",
                f"什么是{topic}"
            ]
            service = "tavily"
        elif current_round == 2:
            # 第二轮：深度搜索
            queries = [
                f"{topic} 最新发展",
                f"{topic} 应用案例",
                f"{topic} 技术趋势"
            ]
            service = "exa"
        else:
            # 第三轮：补充搜索
            queries = [
                f"{topic} 详细分析",
                f"{topic} 专家观点",
                f"{topic} 行业报告"
            ]
            service = "jina"
        
        # 构建搜索提示词
        search_prompt = f"""
主题: {topic}
当前轮次: {current_round}
已使用查询: {', '.join(search_queries) if search_queries else '无'}

请执行多个相关搜索以收集全面信息：

"""
        
        # 为每个查询添加搜索工具调用
        for query in queries:
            search_prompt += f"""
<use_mcp_tool>
<server_name>search</server_name>
<tool_name>tavily_search</tool_name>
<arguments>
{{
  "query": "{query}",
  "service": "{service}",
  "max_results": 5
}}
</arguments>
</use_mcp_tool>

"""
        
        search_prompt += "请在每次搜索后简要总结收集到的信息。"
        
        # 调用LLM
        response = self.llm_manager.call_llm([
            {"role": "user", "content": search_prompt}
        ], agent_type="info_collector")
        
        # 执行工具调用
        tool_results = self.execute_tool_calls(response, "info_collector")
        
        # 更新累积搜索结果
        accumulated_results = state["search_results"] if "search_results" in state else []
        
        # 添加当前轮次的结果
        for result in tool_results:
            if isinstance(result, dict) and not result.get("error"):
                result["search_round"] = current_round
                result["search_service"] = service
                accumulated_results.append(result)
        
        # 更新搜索查询和服务记录
        updated_queries = search_queries + queries
        updated_services = search_services + [service] * len(queries)
        
        return {
            **state,
            "search_results": accumulated_results,
            "search_queries": updated_queries,
            "search_services": updated_services,
            "current_agent": "info_collector",
            "workflow_stage": f"search_round_{current_round}",
            "tool_usage": (state["tool_usage"] if "tool_usage" in state else []) + [
                {
                    "agent": "info_collector", 
                    "round": current_round,
                    "tools_used": len(tool_results), 
                    "service": service,
                    "queries": queries,
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
    
    def info_evaluator_agent(self, state: SearchEvalState) -> SearchEvalState:
        """信息评估智能体 - 评估搜索结果质量和完整性"""
        topic = state["topic"]
        search_results = state["search_results"] if "search_results" in state else []
        current_round = state["current_round"]
        search_queries = state["search_queries"] if "search_queries" in state else []
        
        # 分析搜索结果
        total_results = len(search_results)
        valid_results = [r for r in search_results if isinstance(r, dict) and not r.get("error")]
        error_results = [r for r in search_results if isinstance(r, dict) and r.get("error")]
        
        # 统计不同服务的结果
        service_stats = {}
        for result in valid_results:
            if isinstance(result, dict):
                service = result.get("search_service", "unknown")
                service_stats[service] = service_stats.get(service, 0) + 1
        
        # 构建评估提示词
        eval_prompt = f"""
主题: {topic}
当前轮次: {current_round}
总搜索结果数: {total_results}
有效结果数: {len(valid_results)}
错误结果数: {len(error_results)}
已使用搜索查询: {', '.join(search_queries)}
服务使用统计: {service_stats}

请评估当前收集的信息质量：

评估标准：
1. 信息完整性（25分）：是否覆盖了{topic}的主要方面
2. 信息质量（25分）：来源是否可靠，内容是否准确
3. 信息深度（25分）：是否有足够的细节和深度
4. 信息时效性（15分）：是否包含最新的发展动态
5. 信息多样性（10分）：是否从多个角度获取了信息

基于以上标准，请提供：
1. 质量分数（0-100分）
2. 评估结论（PASS/NEED_MORE）
3. 不足之处（如果有）
4. 改进建议（如果需要更多搜索）

评估详细分析：
"""
        
        # 添加搜索结果摘要
        if valid_results:
            eval_prompt += "\n搜索结果摘要:\n"
            for i, result in enumerate(valid_results[:10]):  # 只显示前10个结果
                title = result.get("title", "无标题")
                content = result.get("content", result.get("summary", ""))[:100]
                service = result.get("search_service", "unknown")
                eval_prompt += f"{i+1}. [{service}] {title}: {content}...\n"
        
        # 调用LLM进行评估
        response = self.llm_manager.call_llm([
            {"role": "user", "content": eval_prompt}
        ], agent_type="info_evaluator")
        
        # 解析评估结果
        evaluation_score = 50.0  # 默认分数
        evaluation_result = "NEED_MORE"  # 默认结论
        
        # 简单解析评估结果（实际应用中可以使用更复杂的解析逻辑）
        if "PASS" in response.upper():
            evaluation_result = "PASS"
        
        # 尝试提取分数
        import re
        score_match = re.search(r'(\d+)(?:\.\d+)?(?:\s*分|\s*points?)', response)
        if score_match:
            try:
                evaluation_score = float(score_match.group(1))
            except:
                pass
        
        # 如果分数很低，强制设为NEED_MORE
        if evaluation_score < self.quality_threshold:
            evaluation_result = "NEED_MORE"
        
        return {
            **state,
            "evaluation_score": evaluation_score,
            "evaluation_result": evaluation_result,
            "evaluation_feedback": response,
            "current_agent": "info_evaluator",
            "workflow_stage": "evaluated",
            "tool_usage": (state["tool_usage"] if "tool_usage" in state else []) + [
                {
                    "agent": "info_evaluator",
                    "round": current_round,
                    "score": evaluation_score,
                    "result": evaluation_result,
                    "total_results": total_results,
                    "valid_results": len(valid_results),
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
    
    def result_summarizer_agent(self, state: SearchEvalState) -> SearchEvalState:
        """结果总结智能体 - 基于所有搜索结果生成综合总结"""
        topic = state["topic"]
        search_results = state["search_results"] if "search_results" in state else []
        search_queries = state["search_queries"] if "search_queries" in state else []
        search_services = state["search_services"] if "search_services" in state else []
        evaluation_score = state["evaluation_score"] if "evaluation_score" in state else 0
        evaluation_feedback = state["evaluation_feedback"] if "evaluation_feedback" in state else ""
        
        # 分析搜索结果统计
        total_results = len(search_results)
        valid_results = [r for r in search_results if isinstance(r, dict) and not r.get("error")]
        
        # 按服务分类结果
        service_results = {}
        for result in valid_results:
            if isinstance(result, dict):
                service = result.get("search_service", "unknown")
                if service not in service_results:
                    service_results[service] = []
                service_results[service].append(result)
        
        # 构建总结提示词
        summary_prompt = f"""
主题: {topic}
搜索统计: 
- 总搜索轮次: {state['current_round'] if 'current_round' in state else 1}
- 使用的搜索查询: {', '.join(search_queries)}
- 使用的搜索服务: {set(search_services)}
- 总搜索结果: {total_results}条
- 有效结果: {len(valid_results)}条
- 信息质量评分: {evaluation_score}/100

请基于以下搜索结果生成关于"{topic}"的综合总结报告：

"""
        
        # 添加各服务的搜索结果摘要
        for service, results in service_results.items():
            summary_prompt += f"\n## {service.upper()}搜索结果 ({len(results)}条):\n"
            for i, result in enumerate(results[:5]):  # 每个服务最多显示5条
                title = result.get("title", "无标题")
                content = result.get("content", result.get("summary", ""))
                if content:
                    content = content[:300]  # 限制长度
                summary_prompt += f"{i+1}. **{title}**\n   {content}...\n\n"
        
        summary_prompt += f"""
评估反馈:
{evaluation_feedback}

请按以下格式生成综合总结报告：

# {topic} - 综合研究报告

## 概述
简要介绍{topic}的核心概念和重要性

## 主要发现
### 1. 基础信息
### 2. 核心特点  
### 3. 应用场景
### 4. 发展趋势

## 关键信息汇总
- 重要数据和统计
- 专家观点
- 典型案例

## 结论与展望

## 搜索过程总结
- 搜索策略和方法
- 信息来源分析
- 数据质量评估

请生成专业、全面、结构清晰的研究报告。
"""
        
        # 调用LLM生成总结
        summary_response = self.llm_manager.call_llm([
            {"role": "user", "content": summary_prompt}
        ], agent_type="result_summarizer", temperature=0.7)
        
        # 如果响应为空，提供备用总结
        if not summary_response or len(summary_response.strip()) == 0:
            summary_response = f"""# {topic} - 综合研究报告

## 概述
本报告基于{total_results}条搜索结果，对{topic}进行了多维度的信息收集和分析。

## 主要发现
### 1. 基础信息
通过{len(set(search_services))}个搜索服务收集了{topic}的基础信息。

### 2. 核心特点
{topic}具有重要的技术价值和应用前景。

### 3. 应用场景
{topic}在多个领域有着广泛的应用。

### 4. 发展趋势
{topic}呈现出持续发展的趋势。

## 关键信息汇总
- 搜索结果数量: {total_results}条
- 有效信息: {len(valid_results)}条
- 质量评分: {evaluation_score}/100分

## 结论与展望
{topic}是一个值得持续关注的重要主题。

## 搜索过程总结
- 使用了{len(set(search_services))}个搜索服务
- 执行了{len(search_queries)}次搜索查询
- 信息质量达到{evaluation_score}分

---
*本报告由JIMU搜索评估循环系统自动生成*
"""
        
        # 保存总结报告
        filename = f"summary_report_{topic.replace(' ', '_')}.md"
        file_result = self.file_manager.write_file(filename, summary_response)
        
        # 生成搜索统计报告
        search_stats = self.search_manager.get_search_statistics()
        
        return {
            **state,
            "final_summary": summary_response,
            "search_statistics": search_stats,
            "current_agent": "result_summarizer",
            "workflow_stage": "completed",
            "tool_usage": (state["tool_usage"] if "tool_usage" in state else []) + [
                {
                    "agent": "result_summarizer",
                    "summary_length": len(summary_response),
                    "file_saved": file_result.get("success", False),
                    "final_score": evaluation_score,
                    "total_rounds": state["current_round"] if "current_round" in state else 1,
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
    
    def should_continue_search(self, state: SearchEvalState) -> Literal["continue_search", "summarize"]:
        """条件判断：是否需要继续搜索"""
        evaluation_result = state["evaluation_result"] if "evaluation_result" in state else "NEED_MORE"
        current_round = state["current_round"] if "current_round" in state else 1
        max_rounds = state["max_rounds"] if "max_rounds" in state else self.max_search_rounds
        
        # 如果评估通过或已达到最大轮次，则进入总结阶段
        if evaluation_result == "PASS" or current_round >= max_rounds:
            return "summarize"
        else:
            return "continue_search"
    
    def prepare_next_round(self, state: SearchEvalState) -> SearchEvalState:
        """准备下一轮搜索"""
        return {
            **state,
            "current_round": state["current_round"] + 1,
            "current_agent": "info_collector",
            "workflow_stage": f"preparing_round_{state['current_round'] + 1}"
        }
    
    def test_search_eval_workflow(self):
        """测试搜索评估循环工作流"""
        print("\n=== 搜索评估循环工作流测试 ===")
        
        # 构建工作流
        workflow = StateGraph(SearchEvalState)
        
        # 添加三个智能体节点
        workflow.add_node("info_collector", self.info_collector_agent)
        workflow.add_node("info_evaluator", self.info_evaluator_agent)
        workflow.add_node("result_summarizer", self.result_summarizer_agent)
        workflow.add_node("prepare_next_round", self.prepare_next_round)
        
        # 设置工作流连接
        workflow.set_entry_point("info_collector")
        workflow.add_edge("info_collector", "info_evaluator")
        
        # 条件边：根据评估结果决定是否继续搜索
        workflow.add_conditional_edges(
            "info_evaluator",
            self.should_continue_search,
            {
                "continue_search": "prepare_next_round",
                "summarize": "result_summarizer"
            }
        )
        
        # 继续搜索的循环
        workflow.add_edge("prepare_next_round", "info_collector")
        workflow.add_edge("result_summarizer", END)
        
        # 测试用例
        test_topics = [
            "人工智能在教育领域的应用",
            "量子计算技术发展现状",
            "区块链在金融科技中的创新"
        ]
        
        try:
            app = workflow.compile()
            
            for topic in test_topics:
                print(f"\n🔍 开始搜索评估主题: {topic}")
                
                initial_state: SearchEvalState = {
                    "topic": topic,
                    "messages": [{"role": "user", "content": f"请深入研究{topic}主题"}],
                    "search_results": [],
                    "current_round": 1,
                    "max_rounds": self.max_search_rounds,
                    "evaluation_score": 0.0,
                    "evaluation_result": "NEED_MORE",
                    "evaluation_feedback": "",
                    "search_queries": [],
                    "search_services": [],
                    "final_summary": "",
                    "current_agent": "",
                    "workflow_stage": "started",
                    "tool_usage": [],
                    "search_statistics": {},
                    "metadata": {"start_time": datetime.now().isoformat()}
                }
                
                start_time = time.time()
                result = app.invoke(initial_state)
                execution_time = time.time() - start_time
                
                # 分析结果
                tool_usage_count = len(result["tool_usage"] if "tool_usage" in result else [])
                total_rounds = result["current_round"] if "current_round" in result else 1
                final_score = result["evaluation_score"] if "evaluation_score" in result else 0
                search_count = len(result["search_results"] if "search_results" in result else [])
                summary_length = len(result["final_summary"] if "final_summary" in result else "")
                
                self.log_test(
                    f"搜索评估循环 - {topic}",
                    f"用时{execution_time:.1f}s, {total_rounds}轮搜索, 得分{final_score}, {search_count}条结果, 总结{summary_length}字符"
                )
                
        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            print(f"详细错误信息:\n{error_detail}")
            self.log_test("搜索评估循环工作流", f"失败: {str(e)}", False)
    
    def test_agent_performance(self):
        """测试智能体性能"""
        print("\n=== 智能体性能测试 ===")
        
        test_cases = [
            ("信息搜集", "人工智能技术", "info_collector"),
            ("信息评估", "机器学习算法", "info_evaluator"), 
            ("结果总结", "云计算服务", "result_summarizer")
        ]
        
        for test_name, topic, agent_type in test_cases:
            start_time = time.time()
            
            # 模拟单个智能体测试
            if agent_type == "info_evaluator":
                test_prompt = f"请评估关于{topic}的搜索结果质量"
            elif agent_type == "result_summarizer":
                test_prompt = f"请总结关于{topic}的研究结果"
            else:
                test_prompt = f"请搜集关于{topic}的信息"
                
            response = self.llm_manager.call_llm([
                {"role": "user", "content": test_prompt}
            ], agent_type=agent_type)
            
            execution_time = time.time() - start_time
            
            self.log_test(
                f"{test_name}智能体性能",
                f"用时{execution_time:.2f}s, 响应长度{len(response)}字符"
            )
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始搜索评估循环工作流测试")
        print(f"使用LLM服务: {JIMU_LLM_BASE_URL}")
        print(f"使用搜索服务: {JIMU_SEARCH_BASE_URL}")
        print(f"文件输出目录: {self.file_manager.base_dir}")
        print(f"最大搜索轮次: {self.max_search_rounds}")
        print(f"质量阈值: {self.quality_threshold}")
        print("-" * 60)
        
        # 运行测试
        self.test_agent_performance()
        self.test_search_eval_workflow()
        
        # 生成测试报告
        print("\n" + "="*60)
        print("🏁 测试完成！测试报告:")
        print("-" * 60)
        
        success_count = sum(1 for result in self.test_results if result["success"])
        total_count = len(self.test_results)
        
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 保存详细报告
        report = {
            "test_summary": {
                "total": total_count,
                "success": success_count,
                "failure": total_count - success_count,
                "success_rate": success_count/total_count*100,
                "test_time": datetime.now().isoformat()
            },
            "workflow_config": {
                "max_search_rounds": self.max_search_rounds,
                "quality_threshold": self.quality_threshold,
                "search_services": self.search_services_rotation
            },
            "agent_capabilities": [
                "信息搜集智能体：多轮多源搜索和信息聚合",
                "信息评估智能体：搜索结果质量评估和完整性分析", 
                "结果总结智能体：综合研究报告生成"
            ],
            "workflow_features": [
                "搜索-评估-循环的智能工作流",
                "基于JIMU MCP工具调用机制",
                "条件循环和质量控制",
                "多维度搜索统计和分析"
            ],
            "test_details": self.test_results
        }
        
        # 保存报告
        report_file = self.file_manager.write_file(
            "search_eval_test_report.json",
            json.dumps(report, ensure_ascii=False, indent=2)
        )
        
        print(f"\n📋 详细报告已保存: {report_file.get('path', '保存失败')}")
        print("\n🎯 搜索评估循环工作流优势:")
        for feature in report["workflow_features"]:
            print(f"  ✅ {feature}")

if __name__ == "__main__":
    print("🔧 初始化搜索评估循环测试环境...")
    
    # 检查依赖
    try:
        import langgraph
        print("✅ LangGraph已安装")
    except ImportError:
        print("❌ 请先安装LangGraph: pip install langgraph")
        exit(1)
    
    # 运行测试
    runner = SearchEvalTestRunner()
    runner.run_all_tests() 