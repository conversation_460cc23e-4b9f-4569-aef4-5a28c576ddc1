<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <style>
    /* Color Palette - Adjusted for Dark Background and Tesla Red Accent */
    :root {
      --bg-dark: #1A1A1A; /* Pure black base as per enhanced aesthetics */
      --accent-red: #E31937; /* Tesla Red for highlight and emphasis */
      --primary-blue: #1E40AF; /* Original primary blue for data elements */
      --secondary-blue: #3B82F6; /* Original accent blue for data elements */
      --tertiary-gray: #475569; /* Original secondary gray for lines/less important text */
      --text-light: #F8FAFC; /* Light text for contrast on dark background */
      --text-muted: #94A3B8; /* Muted text for secondary information */
      --card-bg-dark: #2A2A2A; /* Darker card background to fit theme */
      --card-border-blue: #3B82F6; /* Blue border for cards, subtly visible */
      --shadow-color: rgba(0, 0, 0, 0.5); /* Shadow color */
    }

    /* Font System */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Font Sizes */
    .text-hero-title { font-size: 72px; } /* Used for large numbers */
    .text-main-title { font-size: 56px; }
    .text-section-title { font-size: 36px; }
    .text-content-title { font-size: 28px; }
    .text-body { font-size: 22px; line-height: 1.4; }
    .text-small { font-size: 16px; }
    .text-caption { font-size: 14px; }

    /* Font Weights */
    .font-normal { font-weight: 400; }
    .font-medium { font-weight: 500; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }

    /* General Styles */
    .fill-bg-dark { fill: var(--bg-dark); }
    .fill-accent-red { fill: var(--accent-red); }
    .fill-primary-blue { fill: var(--primary-blue); }
    .fill-secondary-blue { fill: var(--secondary-blue); }
    .fill-tertiary-gray { fill: var(--tertiary-gray); }
    .fill-text-light { fill: var(--text-light); }
    .fill-text-muted { fill: var(--text-muted); }
    .fill-card-bg-dark { fill: var(--card-bg-dark); }

    .stroke-accent-red { stroke: var(--accent-red); }
    .stroke-primary-blue { stroke: var(--primary-blue); }
    .stroke-secondary-blue { stroke: var(--secondary-blue); }
    .stroke-card-border-blue { stroke: var(--card-border-blue); }
    .stroke-text-muted { stroke: var(--text-muted); }

    .rounded-md { border-radius: 12px; } /* For rect elements, visual cue */
    .shadow-sm { filter: url(#drop-shadow); } /* Apply shadow filter */
  </style>

  <defs>
    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="8"/>
      <feGaussianBlur stdDeviation="16"/>
      <feFlood flood-color="var(--shadow-color)"/>
      <feComposite in2="SourceAlpha" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Linear Gradient for Red Highlight (transparency) -->
    <!-- Used for large numbers, creating a techy glow effect -->
    <linearGradient id="gradient-red-transparent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--accent-red)" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="var(--accent-red)" stop-opacity="0.3"/>
    </linearGradient>

    <!-- Linear Gradient for Blue Highlight (transparency) -->
    <!-- Used for smaller data points, providing depth -->
    <linearGradient id="gradient-blue-transparent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--secondary-blue)" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="var(--secondary-blue)" stop-opacity="0.3"/>
    </linearGradient>

    <!-- Reusable icon for data point (simple outline graphic) -->
    <g id="icon-data-point">
      <circle cx="0" cy="0" r="4" fill="var(--secondary-blue)"/>
      <circle cx="0" cy="0" r="8" stroke="var(--secondary-blue)" stroke-width="2" fill="none"/>
    </g>

    <!-- Reusable arrow icon (outline style) -->
    <g id="icon-arrow">
      <path d="M12 4L20 12L12 20M4 12H20" stroke="var(--accent-red)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>

    <!-- Reusable statistical icon (up trend) -->
    <g id="icon-trend-up">
      <path d="M18 8L12 14L8 10L2 16" stroke="var(--accent-red)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M18 8H14M18 8V12" stroke="var(--accent-red)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    </g>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="fill-bg-dark"/>

  <!-- Subtle Decorative Geometric Shapes (low opacity, dark on dark) -->
  <g opacity="0.05">
    <path d="M-100 0L500 0L400 300L-200 300L-100 0Z" fill="var(--primary-blue)"/>
    <circle cx="1800" cy="900" r="150" fill="var(--secondary-blue)"/>
    <rect x="1500" y="-50" width="400" height="200" rx="20" fill="var(--tertiary-gray)"/>
  </g>

  <!-- Main Content Group - Padded by page margins (80px horizontal, 60px vertical) -->
  <g transform="translate(80 60)">
    <!-- Header: Logo and Page Number -->
    <g class="font-primary">
      <!-- Logo Placeholder - use text for simplicity as per requirement -->
      <text x="0" y="40" class="text-content-title font-bold fill-text-light">
        <tspan>{logo_url}</tspan>
      </text>
      <!-- Page Number -->
      <text x="1760" y="40" text-anchor="end" class="text-small fill-text-muted">6/10</tspan>
    </g>

    <!-- Title and Subtitle -->
    <g transform="translate(0, 100)" class="font-primary">
      <text x="0" y="0" class="text-section-title font-bold fill-text-light">
        <tspan>年中总结：{title}</tspan>
      </text>
      <text x="0" y="50" class="text-body font-normal fill-text-muted">
        <tspan>{subtitle}</tspan>
      </text>
    </g>

    <!-- Bento Grid Layout for Data Display -->
    <!-- Grid dimensions: Total content width: 1920 - 80*2 = 1760px -->
    <!-- Total content height: 1080 - 60*2 = 960px (approx. 720px for content below title) -->
    <g transform="translate(0, 240)">
      <!-- Row 1: Large Metric Card (Left) and Chart Area (Right) -->
      <g>
        <!-- Large Metric Card (Left - ~1/3 width) -->
        <rect x="0" y="0" width="560" height="380" rx="12" class="fill-card-bg-dark stroke-card-border-blue" stroke-width="1"/>
        <g transform="translate(40, 40)" class="font-primary">
          <text x="0" y="0" class="text-content-title font-semibold fill-text-light">
            <tspan>核心增长指标</tspan>
          </text>
          <text x="0" y="40" class="text-small fill-text-muted">
            <tspan>Core Growth Metric</tspan>
          </text>
          <!-- Super large number emphasized with red gradient -->
          <text x="0" y="160" class="text-hero-title font-bold" fill="url(#gradient-red-transparent)">
            <tspan>+32.5%</tspan>
          </text>
          <text x="0" y="200" class="text-body font-normal fill-text-muted">
            <tspan>Overall Revenue Growth</tspan>
          </text>
          <text x="0" y="240" class="text-small fill-text-muted">
            <tspan>Compared to last period, exceeding targets.</tspan>
          </text>
          <!-- Icon for trend -->
          <use xlink:href="#icon-trend-up" transform="translate(480, 20) scale(1.5)"/>
        </g>

        <!-- Chart Area (Right - ~2/3 width) -->
        <rect x="580" y="0" width="1180" height="380" rx="12" class="fill-card-bg-dark stroke-card-border-blue" stroke-width="1"/>
        <g transform="translate(620, 40)" class="font-primary">
          <text x="0" y="0" class="text-content-title font-semibold fill-text-light">
            <tspan>产品线贡献分析</tspan>
          </text>
          <text x="0" y="40" class="text-small fill-text-muted">
            <tspan>Revenue Contribution by Product Line</tspan>
          </text>

          <!-- Simulated Bar Chart - Simplified for SVG -->
          <g transform="translate(0, 80)">
            <!-- X-axis labels -->
            <text x="50" y="230" text-anchor="middle" class="text-small fill-text-muted">A</text>
            <text x="200" y="230" text-anchor="middle" class="text-small fill-text-muted">B</text>
            <text x="350" y="230" text-anchor="middle" class="text-small fill-text-muted">C</text>
            <text x="500" y="230" text-anchor="middle" class="text-small fill-text-muted">D</text>
            <text x="650" y="230" text-anchor="middle" class="text-small fill-text-muted">E</text>

            <!-- Y-axis labels -->
            <text x="-10" y="180" text-anchor="end" class="text-small fill-text-muted">0</text>
            <text x="-10" y="120" text-anchor="end" class="text-small fill-text-muted">50M</text>
            <text x="-10" y="60" text-anchor="end" class="text-small fill-text-muted">100M</text>

            <!-- Axis lines (subtle gray) -->
            <line x1="0" y1="190" x2="700" y2="190" stroke="var(--tertiary-gray)" stroke-width="1"/>
            <line x1="0" y1="190" x2="0" y2="0" stroke="var(--tertiary-gray)" stroke-width="1"/>

            <!-- Bars - using primary and secondary blue, with one highlighted in accent red -->
            <rect x="25" y="100" width="50" height="90" rx="4" class="fill-primary-blue"/>
            <text x="50" y="90" text-anchor="middle" class="text-small fill-text-light font-bold">90</text>

            <rect x="175" y="60" width="50" height="130" rx="4" class="fill-primary-blue"/>
            <text x="200" y="50" text-anchor="middle" class="text-small fill-text-light font-bold">130</text>

            <rect x="325" y="120" width="50" height="70" rx="4" class="fill-primary-blue"/>
            <text x="350" y="110" text-anchor="middle" class="text-small fill-text-light font-bold">70</text>

            <rect x="475" y="30" width="50" height="160" rx="4" class="fill-secondary-blue"/> <!-- Highlighted bar in secondary blue -->
            <text x="500" y="20" text-anchor="middle" class="text-content-title font-bold fill-accent-red">160</text> <!-- Number in red -->

            <rect x="625" y="140" width="50" height="50" rx="4" class="fill-primary-blue"/>
            <text x="650" y="130" text-anchor="middle" class="text-small fill-text-light font-bold">50</text>

            <!-- Annotation for highlighted bar -->
            <path d="M500 20 L500 10" stroke="var(--accent-red)" stroke-width="2" stroke-linecap="round"/>
            <text x="500" y="-5" text-anchor="middle" class="text-small fill-accent-red font-bold">Top Performer</tspan>
          </g>
          <!-- End Simulated Bar Chart -->

          <!-- Key Insights for Chart -->
          <g transform="translate(750, 80)">
            <text x="0" y="0" class="text-body font-semibold fill-text-light">
              <tspan>关键洞察</tspan>
            </text>
            <text x="0" y="40" class="text-small fill-text-muted">
              <tspan>Product D is the strongest growth driver.</tspan>
            </text>
            <text x="0" y="70" class="text-small fill-text-muted">
              <tspan>需要关注产品C和E的增长策略。</tspan>
            </text>
            <text x="0" y="100" class="text-small fill-text-muted">
              <tspan>Future plans focus on optimizing product mix.</tspan>
            </text>
            <use xlink:href="#icon-arrow" transform="translate(10, 150) scale(0.8)"/>
            <text x="50" y="165" class="text-small fill-accent-red font-bold">
              <tspan>深入分析</tspan>
            </text>
          </g>
        </g>
      </g>

      <!-- Row 2: Three Data Cards (equally spaced) -->
      <g transform="translate(0, 420)">
        <!-- Card 1: User Activity -->
        <rect x="0" y="0" width="560" height="260" rx="12" class="fill-card-bg-dark stroke-card-border-blue" stroke-width="1"/>
        <g transform="translate(40, 40)" class="font-primary">
          <text x="0" y="0" class="text-content-title font-semibold fill-text-light">
            <tspan>用户活跃度</tspan>
          </text>
          <text x="0" y="40" class="text-small fill-text-muted">
            <tspan>Active User Growth</tspan>
          </text>
          <text x="0" y="120" class="text-main-title font-bold" fill="url(#gradient-blue-transparent)">
            <tspan>+18%</tspan>
          </text>
          <text x="0" y="160" class="text-small fill-text-muted">
            <tspan>Increased user engagement and retention.</tspan>
          </text>
          <use xlink:href="#icon-data-point" transform="translate(500, 20)"/>
        </g>

        <!-- Card 2: Market Share -->
        <rect x="580" y="0" width="560" height="260" rx="12" class="fill-card-bg-dark stroke-card-border-blue" stroke-width="1"/>
        <g transform="translate(620, 40)" class="font-primary">
          <text x="0" y="0" class="text-content-title font-semibold fill-text-light">
            <tspan>市场份额</tspan>
          </text>
          <text x="0" y="40" class="text-small fill-text-muted">
            <tspan>Market Share Gain</tspan>
          </text>
          <text x="0" y="120" class="text-main-title font-bold" fill="url(#gradient-blue-transparent)">
            <tspan>25.3%</tspan>
          </text>
          <text x="0" y="160" class="text-small fill-text-muted">
            <tspan>Strong position in key markets.</tspan>
          </text>
          <!-- Simple graphic for market share -->
          <path d="M480 30 L500 50 L480 70 M480 50 H520" stroke="var(--primary-blue)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </g>

        <!-- Card 3: Operational Efficiency -->
        <rect x="1160" y="0" width="600" height="260" rx="12" class="fill-card-bg-dark stroke-card-border-blue" stroke-width="1"/>
        <g transform="translate(1200, 40)" class="font-primary">
          <text x="0" y="0" class="text-content-title font-semibold fill-text-light">
            <tspan>运营效率提升</tspan>
          </text>
          <text x="0" y="40" class="text-small fill-text-muted">
            <tspan>Operational Efficiency Improvement</tspan>
          </text>
          <text x="0" y="120" class="text-main-title font-bold" fill="url(#gradient-blue-transparent)">
            <tspan>15天</tspan>
          </text>
          <text x="0" y="160" class="text-small fill-text-muted">
            <tspan>Average cycle time reduced by 15 days.</tspan>
          </text>
          <!-- Simple graphic for efficiency -->
          <path d="M480 30 C490 10, 510 10, 520 30 C530 50, 510 70, 500 50 C490 30, 470 30, 480 50 Z" fill="var(--primary-blue)"/>
        </g>
      </g>

      <!-- Row 3: Call to Action / Summary (bottom full width) -->
      <g transform="translate(0, 720)">
        <rect x="0" y="0" width="1760" height="120" rx="12" class="fill-card-bg-dark stroke-accent-red" stroke-width="2"/>
        <g transform="translate(40, 30)" class="font-primary">
          <text x="0" y="0" class="text-content-title font-bold fill-accent-red">
            <tspan>未来展望和行动计划</tspan>
          </text>
          <text x="0" y="50" class="text-body font-normal fill-text-light">
            <tspan>基于当前成果，我们将继续深化数据分析和市场拓展，确保下半年持续增长和创新。</tspan>
          </text>
        </g>
        <use xlink:href="#icon-arrow" transform="translate(1650, 50) scale(1.2)"/>
      </g>
    </g>
  </g>

  <!-- Decorative elements - light grid lines (subtle, low opacity) -->
  <g opacity="0.15">
    <line x1="0" y1="100" x2="1920" y2="100" stroke="var(--text-muted)" stroke-width="1"/>
    <line x1="0" y1="200" x2="1920" y2="200" stroke="var(--text-muted)" stroke-width="1"/>
    <line x1="0" y1="900" x2="1920" y2="900" stroke="var(--text-muted)" stroke-width="1"/>
    <line x1="100" y1="0" x2="100" y2="1080" stroke="var(--text-muted)" stroke-width="1"/>
    <line x1="1820" y1="0" x2="1820" y2="1080" stroke="var(--text-muted)" stroke-width="1"/>
  </g>

  <!-- Author and Date Placeholder (Bottom Right) -->
  <g transform="translate(1840, 1020)" class="font-primary">
    <text x="0" y="0" text-anchor="end" class="text-caption fill-text-muted">
      <tspan>{author} | {date}</tspan>
    </text>
  </g>

</svg>