# -*- coding: utf-8 -*-
"""
LangGraph 增强系统 - MCP 桥接器

零修改复用现有 MCP 系统
"""

import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class MCPBridge:
    """MCP 桥接器 - 完全复用现有系统"""
    
    def __init__(self):
        """初始化 MCP 桥接器"""
        try:
            from backend.mcp.server_manager import mcp_server
            from backend.mcp.tool_parser import mcp_tool_parser
            
            self.mcp_server = mcp_server
            self.tool_parser = mcp_tool_parser
            self.is_initialized = True
            logger.info("✅ MCP 桥接器初始化成功")
        except ImportError as e:
            logger.error(f"❌ 导入 MCP 组件失败: {str(e)}")
            self.mcp_server = None
            self.tool_parser = None
            self.is_initialized = False
    
    async def list_tools_from_servers(self, server_names: List[str]) -> List[Dict[str, Any]]:
        """
        从指定的服务器获取工具列表
        
        Args:
            server_names: MCP 服务器名称列表
        
        Returns:
            List[Dict[str, Any]]: 工具列表
        """
        if not self.is_initialized:
            logger.error("❌ MCP 系统未初始化")
            return []
        
        try:
            tools = await self.mcp_server.list_tools_from_servers(server_names)
            logger.info(f"✅ 从 {len(server_names)} 个服务器获取到 {len(tools)} 个工具")
            return tools
        except Exception as e:
            logger.error(f"❌ 获取工具列表失败: {str(e)}")
            return []
    
    async def list_all_tools(self) -> List[Dict[str, Any]]:
        """获取所有可用工具"""
        if not self.is_initialized:
            return []
        
        try:
            tools = await self.mcp_server.list_all_tools()
            logger.info(f"✅ 获取到 {len(tools)} 个可用工具")
            return tools
        except Exception as e:
            logger.error(f"❌ 获取所有工具失败: {str(e)}")
            return []
    
    async def get_server_status(self, server_name: str) -> Dict[str, Any]:
        """
        获取服务器状态
        
        Args:
            server_name: 服务器名称
        
        Returns:
            Dict[str, Any]: 服务器状态信息
        """
        if not self.is_initialized:
            return {"status": "error", "message": "MCP 系统未初始化"}
        
        try:
            status = await self.mcp_server.get_server_status(server_name)
            return status
        except Exception as e:
            logger.error(f"❌ 获取服务器状态失败: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def execute_tool(self, 
                          server_name: str, 
                          tool_name: str, 
                          arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行 MCP 工具
        
        Args:
            server_name: 服务器名称
            tool_name: 工具名称
            arguments: 工具参数
        
        Returns:
            Dict[str, Any]: 执行结果
        """
        if not self.is_initialized:
            return {"error": "MCP 系统未初始化"}
        
        try:
            result = await self.mcp_server.execute_tool(server_name, tool_name, arguments)
            logger.debug(f"✅ 工具执行成功: {server_name}.{tool_name}")
            return result
        except Exception as e:
            logger.error(f"❌ 工具执行失败: {str(e)}")
            return {"error": str(e)}
    
    def parse_tool_calls(self, content: str) -> Optional[List[Dict[str, Any]]]:
        """
        解析 AI 响应中的工具调用
        
        Args:
            content: AI 响应内容
        
        Returns:
            Optional[List[Dict[str, Any]]]: 工具调用列表
        """
        if not self.is_initialized or not self.tool_parser:
            return None
        
        try:
            tool_calls = self.tool_parser.parse_xml_tool_calls(content)
            if tool_calls:
                logger.debug(f"✅ 解析到 {len(tool_calls)} 个工具调用")
            return tool_calls
        except Exception as e:
            logger.error(f"❌ 解析工具调用失败: {str(e)}")
            return None
    
    async def handle_tool_calls(self, 
                              tool_calls: List[Dict[str, Any]], 
                              selected_servers: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        处理多个工具调用
        
        Args:
            tool_calls: 工具调用列表
            selected_servers: 限定的服务器列表
        
        Returns:
            List[Dict[str, Any]]: 执行结果列表
        """
        results = []
        
        for tool_call in tool_calls:
            server_name = tool_call.get('server_name')
            tool_name = tool_call.get('tool_name')
            arguments = tool_call.get('arguments', {})
            
            # 如果指定了服务器列表，检查是否在列表中
            if selected_servers and server_name not in selected_servers:
                results.append({
                    "error": f"服务器 {server_name} 不在允许列表中"
                })
                continue
            
            # 执行工具
            result = await self.execute_tool(server_name, tool_name, arguments)
            results.append(result)
        
        return results
    
    def get_available_servers(self) -> List[str]:
        """获取可用的 MCP 服务器列表"""
        if not self.is_initialized:
            return []
        
        try:
            # 从 mcp_server 获取服务器列表
            if hasattr(self.mcp_server, 'get_available_servers'):
                return self.mcp_server.get_available_servers()
            else:
                # 返回默认服务器列表
                return ["filesystem", "web_search", "tavily", "jina", "calculator"]
        except Exception as e:
            logger.error(f"获取服务器列表失败: {str(e)}")
            return []
    
    def validate_connection(self) -> bool:
        """验证 MCP 连接是否正常"""
        return self.is_initialized and self.mcp_server is not None


# 全局 MCP 桥接器实例
_mcp_bridge: Optional[MCPBridge] = None


def get_mcp_bridge() -> MCPBridge:
    """获取全局 MCP 桥接器实例"""
    global _mcp_bridge
    if _mcp_bridge is None:
        _mcp_bridge = MCPBridge()
    return _mcp_bridge


# 便捷函数
async def get_tools_for_agent(server_names: List[str]) -> List[Dict[str, Any]]:
    """便捷函数：为智能体获取工具列表"""
    bridge = get_mcp_bridge()
    return await bridge.list_tools_from_servers(server_names)


async def execute_mcp_tool(server_name: str, 
                         tool_name: str, 
                         arguments: Dict[str, Any]) -> Dict[str, Any]:
    """便捷函数：执行 MCP 工具"""
    bridge = get_mcp_bridge()
    return await bridge.execute_tool(server_name, tool_name, arguments) 