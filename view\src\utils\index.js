/**
 * 格式化时间戳为可读格式
 * @param {number|string} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const now = new Date();
  const isToday = 
    date.getDate() === now.getDate() && 
    date.getMonth() === now.getMonth() && 
    date.getFullYear() === now.getFullYear();
  
  // 对于今天的消息，只显示时间
  if (isToday) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
  
  // 对于非今天的消息，显示日期和时间
  return date.toLocaleString([], { 
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

/**
 * 格式化用户消息和AI响应为聊天记录格式
 * @param {string} userMessage - 用户消息
 * @param {string} aiResponse - AI响应
 * @param {string} service - 使用的AI服务
 * @param {object} functionCall - 函数调用信息(如果有)
 * @param {object} functionResult - 函数结果(如果有)
 * @returns {Array} 格式化后的消息数组
 */
export const formatMessages = (
  userMessage, 
  aiResponse, 
  service = 'openai',
  functionCall = null,
  functionResult = null
) => {
  const messages = [];
  
  // 添加用户消息
  messages.push({
    role: 'user',
    content: userMessage,
    timestamp: Date.now()
  });
  
  // 添加AI响应
  messages.push({
    role: 'assistant',
    content: aiResponse,
    timestamp: Date.now(),
    service,
    ...(functionCall && { function_call: functionCall }),
    ...(functionResult && { function_result: functionResult })
  });
  
  return messages;
};

/**
 * 解析WebSocket消息
 * @param {string} messageText - WebSocket消息文本
 * @returns {object} 解析后的消息对象
 */
export const parseWSMessage = (messageText) => {
  try {
    return JSON.parse(messageText);
  } catch (error) {
    console.error('解析WebSocket消息失败:', error);
    return { error: '消息解析失败' };
  }
};

/**
 * 创建新的会话对象
 * @param {string} title - 会话标题
 * @param {string} service - AI服务名称
 * @param {string} model - 模型名称
 * @param {object} options - 其他选项
 * @returns {object} 新会话对象
 */
export const createNewConversation = (title = '新会话', service = 'openai', model = '', options = {}) => {
  return {
    id: generateId(),
    title,
    messages: [],
    createdAt: Date.now(),
    updatedAt: Date.now(),
    service,
    model,
    role_id: options.role_id || null,
    team_id: options.team_id || null, // 添加team_id支持
    is_web_search_enabled: options.is_web_search_enabled || false,
    isAgentMode: options.isAgentMode || false,
    loading: options.loading || false,
    systemPrompt: options.systemPrompt || '',
    maxTokens: options.maxTokens || ''
  };
};

/**
 * 使用指定的AI服务创建系统消息
 * @param {string} service - AI服务名称
 * @param {string} customPrompt - 自定义系统提示词，如果提供则替换默认内容
 * @returns {object} 系统消息
 */
export const createSystemMessage = (service = 'openai', customPrompt = '') => {
  // 不同服务的系统提示可能有所不同
  let content = 'hello。';
  
  // 如果提供了自定义提示词，则使用它
  if (customPrompt) {
    content = customPrompt;
  }
  
  return {
    role: 'system',
    content
  };
}; 