# -*- coding: utf-8 -*-
"""
jieba词典分析工具 - 解析和统计jieba词典信息
"""

import jieba
import json
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Tuple

class JiebaDictAnalyzer:
    """jieba词典分析器"""
    
    def __init__(self):
        self.dict_path = jieba.get_dict_file().name
        self.word_dict = {}  # {word: (freq, pos)}
        self.pos_groups = defaultdict(list)  # {pos: [words]}
        self.freq_distribution = Counter()  # 词频分布统计
        
    def load_dict(self):
        """加载jieba词典"""
        print(f"📚 加载jieba词典: {self.dict_path}")
        
        with open(self.dict_path, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split(' ')
                if len(parts) >= 2:
                    word = parts[0]
                    freq = int(parts[1])
                    pos = parts[2] if len(parts) > 2 else 'x'
                    
                    self.word_dict[word] = (freq, pos)
                    self.pos_groups[pos].append((word, freq))
                    
                    # 统计词频分布
                    if freq >= 10000:
                        self.freq_distribution['10000+'] += 1
                    elif freq >= 1000:
                        self.freq_distribution['1000-9999'] += 1
                    elif freq >= 100:
                        self.freq_distribution['100-999'] += 1
                    else:
                        self.freq_distribution['<100'] += 1
        
        print(f"✅ 加载完成: {len(self.word_dict)} 个词条")
        
    def analyze_pos_distribution(self) -> Dict[str, int]:
        """分析词性分布"""
        pos_stats = {}
        for pos, words in self.pos_groups.items():
            pos_stats[pos] = len(words)
        
        # 按数量排序
        sorted_pos = sorted(pos_stats.items(), key=lambda x: x[1], reverse=True)
        
        print("\n📊 词性分布统计:")
        for pos, count in sorted_pos[:20]:  # 显示前20个
            print(f"  {pos}: {count} 词")
            
        return dict(sorted_pos)
    
    def extract_high_freq_words(self, min_freq: int = 1000) -> Dict[str, List[Tuple[str, int]]]:
        """提取高频词汇"""
        high_freq_by_pos = defaultdict(list)
        
        for word, (freq, pos) in self.word_dict.items():
            if freq >= min_freq:
                high_freq_by_pos[pos].append((word, freq))
        
        # 按词频排序
        for pos in high_freq_by_pos:
            high_freq_by_pos[pos].sort(key=lambda x: x[1], reverse=True)
        
        print(f"\n📈 高频词汇统计 (频率>={min_freq}):")
        total_high_freq = sum(len(words) for words in high_freq_by_pos.values())
        print(f"  总计: {total_high_freq} 个高频词")
        
        return dict(high_freq_by_pos)
    
    def analyze_existing_json_words(self, json_dict_path: str) -> Dict[str, List[Tuple[str, int, str]]]:
        """分析现有JSON词典在jieba中的信息"""
        json_words_info = {}
        dict_path = Path(json_dict_path)
        
        print(f"\n🔍 分析JSON词典词汇在jieba中的信息:")
        
        for json_file in dict_path.glob("*.json"):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            file_words = []
            # 递归提取所有词汇
            def extract_words(obj):
                if isinstance(obj, list):
                    return obj
                elif isinstance(obj, dict):
                    words = []
                    for v in obj.values():
                        words.extend(extract_words(v))
                    return words
                return []
            
            all_words = extract_words(data)
            
            # 查找每个词在jieba中的信息
            for word in all_words:
                if word in self.word_dict:
                    freq, pos = self.word_dict[word]
                    file_words.append((word, freq, pos))
                else:
                    file_words.append((word, 0, 'unknown'))
            
            json_words_info[json_file.stem] = file_words
            
            # 统计
            found = sum(1 for _, freq, _ in file_words if freq > 0)
            print(f"  {json_file.stem}: {found}/{len(file_words)} 在jieba词典中")
        
        return json_words_info
    
    def generate_report(self) -> Dict:
        """生成分析报告"""
        report = {
            "total_words": len(self.word_dict),
            "freq_distribution": dict(self.freq_distribution),
            "pos_distribution": self.analyze_pos_distribution(),
            "high_freq_words_count": len([1 for _, (freq, _) in self.word_dict.items() if freq >= 1000])
        }
        
        print(f"\n📋 词频分布:")
        for range_name, count in self.freq_distribution.items():
            print(f"  {range_name}: {count} 词")
            
        return report


def main():
    """主函数"""
    analyzer = JiebaDictAnalyzer()
    
    # 加载词典
    analyzer.load_dict()
    
    # 生成报告
    report = analyzer.generate_report()
    
    # 提取高频词
    high_freq_words = analyzer.extract_high_freq_words(min_freq=5000)
    
    # 分析现有JSON词典
    json_analysis = analyzer.analyze_existing_json_words("dictionaries")
    
    # 保存分析结果
    analysis_result = {
        "report": report,
        "high_freq_sample": {pos: words[:10] for pos, words in high_freq_words.items()},
        "json_words_analysis": {
            name: [(w, f, p) for w, f, p in words[:5]]  # 每个文件保存前5个示例
            for name, words in json_analysis.items()
        }
    }
    
    with open("jieba_analysis_result.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print("\n✅ 分析完成，结果已保存到 jieba_analysis_result.json")
    
    return analyzer


if __name__ == "__main__":
    main() 