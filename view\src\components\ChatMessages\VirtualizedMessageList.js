import React, { useEffect, useRef } from 'react';
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { Typography, Avatar, Button, Tooltip } from 'antd';
import { UserOutlined, RobotOutlined, SyncOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { formatTimestamp } from '../../utils';

const { Text } = Typography;

// 单个消息项组件
const MessageItem = React.memo(({ data, index, style }) => {
  const { messages, onRegenerate, loading } = data;
  const message = messages[index];
  const isUser = message.role === 'user';
  const isLast = index === messages.length - 1;

  // 渲染Markdown代码块
  const renderCodeBlock = ({ node, inline, className, children, ...props }) => {
    const match = /language-(\w+)/.exec(className || '');
    return !inline && match ? (
      <SyntaxHighlighter
        style={vscDarkPlus}
        language={match[1]}
        PreTag="div"
        {...props}
      >
        {String(children).replace(/\n$/, '')}
      </SyntaxHighlighter>
    ) : (
      <code className={className} {...props}>
        {children}
      </code>
    );
  };

  return (
    <div style={style}>
      <div className={`message-item ${isUser ? 'user-message' : 'assistant-message'}`}>
        <div className="message-avatar">
          <Avatar 
            icon={isUser ? <UserOutlined /> : <RobotOutlined />} 
            size="large"
            className={isUser ? 'user-avatar' : 'assistant-avatar'}
          />
        </div>
        
        <div className="message-content">
          <div className="message-header">
            <Text strong>{isUser ? '用户' : '助手'}</Text>
            {message.timestamp && (
              <Text type="secondary" className="message-time">
                {formatTimestamp(message.timestamp)}
              </Text>
            )}
            {isLast && !isUser && !loading && (
              <div className="message-actions">
                <Tooltip title="重新生成">
                  <Button 
                    type="text" 
                    icon={<SyncOutlined />} 
                    size="small"
                    onClick={() => onRegenerate && onRegenerate()}
                  />
                </Tooltip>
              </div>
            )}
          </div>
          
          <div className={`message-body ${isUser ? 'user-content' : 'assistant-content'} ${message.isLoading ? 'loading' : ''} ${message.isError ? 'error' : ''}`}>
            {isUser ? (
              <div>{message.content}</div>
            ) : (
              <ReactMarkdown
                components={{
                  code: renderCodeBlock
                }}
              >
                {message.content || ''}
              </ReactMarkdown>
            )}
            {message.isLoading && !isUser && (
              <span className="typing-indicator">●</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

const VirtualizedMessageList = ({ messages = [], loading = false, onRegenerate }) => {
  const listRef = useRef();
  const prevMessagesLengthRef = useRef(messages.length);

  // 当消息列表更新时，滚动到底部
  useEffect(() => {
    if (messages.length > prevMessagesLengthRef.current && listRef.current) {
      listRef.current.scrollToItem(messages.length - 1);
    }
    prevMessagesLengthRef.current = messages.length;
  }, [messages.length]);

  // 如果没有消息，显示空状态
  if (!messages.length) {
    return (
      <div className="empty-messages">
        暂无消息
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <AutoSizer>
        {({ height, width }) => (
          <List
            ref={listRef}
            height={height}
            width={width}
            itemCount={messages.length}
            itemSize={150} // 预估的每条消息高度，可以根据实际情况调整
            itemData={{
              messages,
              onRegenerate,
              loading
            }}
          >
            {MessageItem}
          </List>
        )}
      </AutoSizer>
    </div>
  );
};

export default VirtualizedMessageList; 