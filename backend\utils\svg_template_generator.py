#!/usr/bin/env python3
"""
SVG模板生成器 - 增强版
基于现有svg_generator.py创建功能完整的SVG模板生成系统
生成10张一组的统一风格SVG模板，支持图片、logo、背景等，可直接转换为PPT

功能特点：
- 生成10种不同类型的SVG模板
- 保持样式一致性
- 支持图片、logo、背景元素
- 集成PPT生成功能
- 一键生成完整演示文稿

Created: 2025-07-02
Enhanced by: <PERSON> AI Assistant
"""

import asyncio
import json
import logging
import os
import re
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# 导入必要的模块
from backend.ai.openai_service import OpenAIService
from backend.utils.prompts import PPT_SVG

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class TemplateConfig:
    """模板配置数据类"""
    style: str = "business"  # modern, business, creative, academic, future
    color_theme: str = "blue"  # blue, green, orange, purple, gradient, future
    layout: str = "standard"  # standard(16:9), wide(21:9), square(1:1)
    canvas_width: int = 1920
    canvas_height: int = 1080
    scenario: str = "商业计划"  # 应用场景
    custom_requirements: str = ""  # 自定义需求
    
    # 视觉元素配置
    enable_images: bool = True  # 支持图片
    enable_logos: bool = True   # 支持logo
    enable_backgrounds: bool = True  # 支持背景
    
    # AI生成配置
    model: str = "gemini-2.5-flash"
    temperature: float = 0.7
    max_retries: int = 3

@dataclass 
class DesignSpecification:
    """设计规范数据类"""
    theme_selection: Dict[str, Any]
    color_palette: Dict[str, str]
    layout_principles: Dict[str, Any]
    typography_system: Dict[str, Any]
    visual_elements: Dict[str, Any]
    template_consistency: Dict[str, Any]
    content_outline: List[Dict[str, Any]]
    canvas_size: Dict[str, int]
    created_at: str
    raw_spec: str

@dataclass
class SVGTemplate:
    """SVG模板数据类"""
    template_id: str
    template_type: str
    title: str
    description: str
    svg_code: str
    placeholders: List[str]
    layout_zones: Dict[str, Dict[str, Any]]
    style_elements: List[str]
    created_at: str
    page_number: int

class SVGTemplateGenerator:
    """增强的SVG模板生成器
    
    生成10张统一风格的SVG模板，支持图片、logo、背景等元素
    可直接转换为完整的PPT演示文稿
    """
    
    def __init__(self, config: Optional[TemplateConfig] = None):
        """初始化生成器
        
        Args:
            config: 模板配置，为None时使用默认配置
        """
        self.config = config or TemplateConfig()
        self.ai_service = OpenAIService()
        
        # 设置AI模型
        self.ai_service.model = self.config.model
        
        # 内部状态
        self.design_spec: Optional[DesignSpecification] = None
        self.generated_templates: List[SVGTemplate] = []
        self.current_stage = "initial"
        
        # 设置保存路径
        self.templates_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            "resources", "svg_templates"
        )
        os.makedirs(self.templates_dir, exist_ok=True)
        
        # 定义10种模板类型
        self.template_types = [
            {
                "id": "cover",
                "type": "封面页",
                "title": "封面模板",
                "description": "主标题 + 副标题 + logo + 装饰元素",
                "page_number": 1,
                "content_focus": "标题展示",
                "visual_priority": "品牌识别"
            },
            {
                "id": "agenda",
                "type": "目录页", 
                "title": "目录模板",
                "description": "内容概览 + 章节导航 + 进度指示",
                "page_number": 2,
                "content_focus": "结构导航",
                "visual_priority": "层级清晰"
            },
            {
                "id": "section_divider",
                "type": "章节分隔页",
                "title": "章节分隔模板", 
                "description": "章节标题 + 装饰背景 + 过渡设计",
                "page_number": 3,
                "content_focus": "章节切换",
                "visual_priority": "视觉冲击"
            },
            {
                "id": "title_content",
                "type": "标题内容页",
                "title": "标题内容模板",
                "description": "页面标题 + 主要内容 + 要点列表",
                "page_number": 4,
                "content_focus": "信息传达",
                "visual_priority": "内容层次"
            },
            {
                "id": "image_text",
                "type": "图文混排页",
                "title": "图文混排模板",
                "description": "图片展示 + 文字说明 + 图文平衡",
                "page_number": 5,
                "content_focus": "图文结合",
                "visual_priority": "视觉平衡"
            },
            {
                "id": "data_display",
                "type": "数据展示页",
                "title": "数据展示模板",
                "description": "图表区域 + 数据卡片 + 统计信息",
                "page_number": 6,
                "content_focus": "数据可视化", 
                "visual_priority": "数据突出"
            },
            {
                "id": "comparison",
                "type": "对比分析页",
                "title": "对比分析模板",
                "description": "左右对比 + 差异展示 + 结论总结",
                "page_number": 7,
                "content_focus": "对比分析",
                "visual_priority": "对比清晰"
            },
            {
                "id": "timeline",
                "type": "时间线页",
                "title": "时间线模板", 
                "description": "时间轴 + 里程碑 + 发展历程",
                "page_number": 8,
                "content_focus": "时间流程",
                "visual_priority": "流程连贯"
            },
            {
                "id": "quote",
                "type": "引用页",
                "title": "引用模板",
                "description": "重要引用 + 来源信息 + 视觉强调",
                "page_number": 9,
                "content_focus": "重点强调",
                "visual_priority": "情感共鸣"
            },
            {
                "id": "conclusion",
                "type": "结尾页",
                "title": "结尾模板",
                "description": "总结要点 + 行动呼吁 + 联系信息",
                "page_number": 10,
                "content_focus": "总结呼吁",
                "visual_priority": "行动导向"
            }
        ]
        
        logger.info(f"SVG模板生成器初始化完成 - 风格: {self.config.style}, 主题: {self.config.color_theme}")
    
    def _build_style_config_description(self) -> str:
        """构建风格配置描述"""
        style_descriptions = {
            'modern': "现代简约风格：采用简洁线条、充足留白、无衬线字体，强调功能性和可读性",
            'business': "商务专业风格：使用正式布局、传统配色、清晰层次结构，体现专业性和权威性", 
            'creative': "创意活泼风格：运用不规则形状、渐变色彩、创新布局，营造活泼有趣氛围",
            'academic': "学术严谨风格：采用规整布局、稳重配色、清晰逻辑结构，确保信息准确传达",
            'future': "科技未来感风格：采用未来感布局、科技感配色、科技感字体，营造未来感和科技感"
        }
        
        color_descriptions = {
            'blue': "商务蓝色系：主色调使用蓝色系（#4A86E8, #1E3A8A, #3B82F6），营造专业可信氛围",
            'green': "清新绿色系：主色调使用绿色系（#10B981, #065F46, #059669），传达自然和成长理念",
            'orange': "活力橙色系：主色调使用橙色系（#F59E0B, #C2410C, #EA580C），展现活力和创新精神",
            'purple': "典雅紫色系：主色调使用紫色系（#8B5CF6, #553C9A, #7C3AED），体现优雅和创意",
            'gradient': "渐变彩色：使用多色渐变效果，营造现代和时尚的视觉效果",
            'future': "科技未来感：使用科技感配色，营造未来感和科技感"
        }
        
        layout_descriptions = {
            'standard': "标准16:9比例布局（1920x1080），适合大多数演示场景",
            'wide': "宽屏21:9比例布局（2560x1080），适合超宽屏显示",
            'square': "方形1:1比例布局（1080x1080），适合社交媒体分享"
        }
        
        # 更新画布尺寸
        if self.config.layout == 'wide':
            self.config.canvas_width, self.config.canvas_height = 2560, 1080
        elif self.config.layout == 'square':
            self.config.canvas_width, self.config.canvas_height = 1080, 1080
        else:
            self.config.canvas_width, self.config.canvas_height = 1920, 1080
        
        return f"""
风格配置要求：
- 设计风格：{self.config.style} - {style_descriptions.get(self.config.style, '')}
- 配色主题：{self.config.color_theme} - {color_descriptions.get(self.config.color_theme, '')}
- 布局规格：{self.config.layout} - {layout_descriptions.get(self.config.layout, '')}
- 应用场景：{self.config.scenario}
- 自定义要求：{self.config.custom_requirements if self.config.custom_requirements else '无特殊要求'}
- 视觉元素支持：图片({self.config.enable_images})、Logo({self.config.enable_logos})、背景({self.config.enable_backgrounds})
"""
    
    async def generate_design_specification(self, content_context: str = "通用演示文稿模板") -> Dict[str, Any]:
        """生成统一的设计规范
        
        Args:
            content_context: 内容上下文描述
            
        Returns:
            设计规范生成结果
        """
        try:
            logger.info("🎨 开始生成统一设计规范...")
            self.current_stage = "design_spec"
            
            # 构建设计规范提示词
            design_prompt = self._build_design_specification_prompt(content_context)
            
            # 调用AI生成设计规范
            messages = [{"role": "user", "content": design_prompt}]
            response = await self.ai_service.chat_completion(
                messages=messages,
                temperature=self.config.temperature
            )
            
            if "error" in response:
                logger.error(f"设计规范生成失败: {response['error']}")
                return {"success": False, "error": response["error"]}
            
            # 解析设计规范
            design_spec = self._parse_design_specification(response["content"])
            if not design_spec:
                return {"success": False, "error": "设计规范解析失败"}
            
            self.design_spec = design_spec
            logger.info("✅ 设计规范生成完成")
            
            return {
                "success": True,
                "design_spec": asdict(design_spec),
                "message": "设计规范生成成功"
            }
            
        except Exception as e:
            error_msg = f"生成设计规范异常: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    def _build_design_specification_prompt(self, content_context: str) -> str:
        """构建设计规范生成提示词"""
        
        style_config_desc = self._build_style_config_description()
        
        prompt = f"""# SVG模板集合设计规范生成器

## 任务目标
为10张统一风格的SVG模板生成一套完整的设计规范，确保所有模板在视觉风格上高度一致。

## 用户配置要求
{style_config_desc}

## 内容上下文
{content_context}

## 需要生成的10种模板类型
1. **封面模板 (Cover)**: 主标题 + 副标题 + logo + 装饰元素
2. **目录模板 (Agenda)**: 内容概览 + 章节导航 + 进度指示
3. **章节分隔模板 (Section Divider)**: 章节标题 + 装饰背景 + 过渡设计
4. **标题内容模板 (Title Content)**: 页面标题 + 主要内容 + 要点列表
5. **图文混排模板 (Image Text)**: 图片展示 + 文字说明 + 图文平衡
6. **数据展示模板 (Data Display)**: 图表区域 + 数据卡片 + 统计信息
7. **对比分析模板 (Comparison)**: 左右对比 + 差异展示 + 结论总结
8. **时间线模板 (Timeline)**: 时间轴 + 里程碑 + 发展历程
9. **引用模板 (Quote)**: 重要引用 + 来源信息 + 视觉强调
10. **结尾模板 (Conclusion)**: 总结要点 + 行动呼吁 + 联系信息

## 特殊要求
- 支持图片元素：使用SVG的<image>标签，支持外部URL和本地路径
- 支持Logo展示：为品牌logo预留专门区域和占位符
- 支持背景设计：可使用纯色、渐变或图片背景
- 确保模板间的视觉一致性：统一的色彩、字体、间距、装饰元素
- 适应不同内容类型：每种模板都要体现其特定的功能特点

## 输出要求
请按以下JSON格式输出完整的设计规范：

```json
{{
    "theme_selection": {{
        "primary_style": "用户选择的风格类型",
        "visual_theme": "基于场景的主题定位", 
        "design_philosophy": "设计理念描述",
        "scenario_adaptation": "场景适配策略"
    }},
    "color_palette": {{
        "primary_color": "#主色HEX值",
        "secondary_color": "#辅助色HEX值", 
        "accent_color": "#强调色HEX值",
        "background_color": "#背景色HEX值",
        "text_primary": "#主要文字色HEX值",
        "text_secondary": "#次要文字色HEX值",
        "card_background": "#卡片背景色HEX值",
        "gradient_start": "#渐变起始色HEX值",
        "gradient_end": "#渐变结束色HEX值"
    }},
    "layout_principles": {{
        "canvas_size": {{"width": {self.config.canvas_width}, "height": {self.config.canvas_height}}},
        "page_margins": {{"horizontal": 80, "vertical": 60}},
        "content_spacing": {{"module_gap": 24, "section_gap": 40}},
        "grid_system": "网格布局规则",
        "image_guidelines": "图片处理指导",
        "logo_positioning": "Logo放置规则"
    }},
    "typography_system": {{
        "primary_font": "主要字体名称",
        "font_sizes": {{
            "main_title": 56,
            "section_title": 36,
            "content_title": 28,
            "body_text": 22,
            "small_text": 16
        }},
        "font_weights": {{
            "title": "bold",
            "content": "normal",
            "emphasis": "600"
        }},
        "line_heights": {{
            "title": 1.2,
            "content": 1.6,
            "dense": 1.4
        }}
    }},
    "visual_elements": {{
        "card_style": {{
            "background": "卡片背景样式",
            "border_radius": 12,
            "border_style": "边框样式描述",
            "shadow": "阴影效果描述"
        }},
        "decorative_elements": [
            "装饰元素1描述",
            "装饰元素2描述", 
            "装饰元素3描述"
        ],
        "gradients": {{
            "primary_gradient": "主要渐变定义",
            "accent_gradient": "强调渐变定义",
            "background_gradient": "背景渐变定义"
        }},
        "image_effects": {{
            "border_radius": 8,
            "shadow_style": "图片阴影样式",
            "overlay_style": "图片遮罩样式"
        }},
        "logo_effects": {{
            "positioning": "Logo位置规则",
            "size_guidelines": "Logo尺寸指导",
            "background_treatment": "Logo背景处理"
        }}
    }},
    "template_consistency": {{
        "title_positioning": "标题位置一致性规则",
        "content_alignment": "内容对齐规则", 
        "spacing_rules": "间距一致性规则",
        "visual_hierarchy": "视觉层级规则",
        "color_usage": "色彩使用规则",
        "element_positioning": "元素定位规则"
    }}
}}
```

请确保生成的设计规范：
1. 严格遵循用户的风格和配色配置
2. 为10种不同模板类型提供统一的视觉框架
3. 包含详细的图片、logo、背景处理指导
4. 确保模板间的高度一致性
5. 适应指定的应用场景需求

请生成完整的设计规范JSON。"""
        
        return prompt
    
    def _parse_design_specification(self, ai_response: str) -> Optional[DesignSpecification]:
        """解析AI返回的设计规范"""
        try:
            # 提取JSON内容
            json_match = re.search(r'```json\s*(.*?)\s*```', ai_response, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
            else:
                # 尝试直接解析
                json_content = ai_response
            
            spec_data = json.loads(json_content)
            
            # 创建内容大纲（基于10种模板类型）
            content_outline = []
            for i, template_type in enumerate(self.template_types, 1):
                content_outline.append({
                    "page_number": i,
                    "template_id": template_type["id"],
                    "title": template_type["title"],
                    "type": template_type["type"],
                    "description": template_type["description"],
                    "content_focus": template_type["content_focus"],
                    "visual_priority": template_type["visual_priority"]
                })
            
            return DesignSpecification(
                theme_selection=spec_data.get("theme_selection", {}),
                color_palette=spec_data.get("color_palette", {}),
                layout_principles=spec_data.get("layout_principles", {}),
                typography_system=spec_data.get("typography_system", {}),
                visual_elements=spec_data.get("visual_elements", {}),
                template_consistency=spec_data.get("template_consistency", {}),
                content_outline=content_outline,
                canvas_size={"width": self.config.canvas_width, "height": self.config.canvas_height},
                created_at=datetime.now().isoformat(),
                raw_spec=ai_response
            )
            
        except Exception as e:
            logger.error(f"解析设计规范失败: {str(e)}")
            return None
    
    async def generate_template_set(self, content_context: str = "通用演示文稿模板") -> Dict[str, Any]:
        """生成完整的10张模板集合
        
        Args:
            content_context: 内容上下文描述
            
        Returns:
            模板集合生成结果
        """
        try:
            logger.info("🚀 开始生成10张SVG模板集合...")
            
            # 第一步：生成设计规范
            if not self.design_spec:
                spec_result = await self.generate_design_specification(content_context)
                if not spec_result["success"]:
                    return spec_result
            
            # 第二步：逐个生成10张模板
            self.current_stage = "generating_templates"
            generated_templates = []
            
            for i, template_type in enumerate(self.template_types):
                try:
                    logger.info(f"📄 生成第 {i+1}/10 张模板: {template_type['title']}")
                    
                    template_result = await self._generate_single_template(template_type)
                    
                    if template_result["success"]:
                        template = SVGTemplate(
                            template_id=f"{template_type['id']}_{int(time.time())}_{i+1}",
                            template_type=template_type["id"],
                            title=template_type["title"],
                            description=template_type["description"],
                            svg_code=template_result["svg_code"],
                            placeholders=template_result.get("placeholders", []),
                            layout_zones=template_result.get("layout_zones", {}),
                            style_elements=template_result.get("style_elements", []),
                            created_at=datetime.now().isoformat(),
                            page_number=template_type["page_number"]
                        )
                        generated_templates.append(template)
                        logger.info(f"✅ 第 {i+1} 张模板生成成功")
                    else:
                        logger.warning(f"❌ 第 {i+1} 张模板生成失败: {template_result.get('error')}")
                        
                except Exception as e:
                    logger.error(f"生成第 {i+1} 张模板时异常: {str(e)}")
                    continue
            
            # 第三步：保存模板集合
            if generated_templates:
                self.generated_templates = generated_templates
                save_result = self._save_template_set(generated_templates)
                
                logger.info(f"🎉 模板集合生成完成！成功生成 {len(generated_templates)}/10 张模板")
                
                return {
                    "success": True,
                    "templates": [asdict(t) for t in generated_templates],
                    "design_spec": asdict(self.design_spec),
                    "save_info": save_result,
                    "total_templates": len(generated_templates),
                    "template_types": [t.template_type for t in generated_templates],
                    "generated_at": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "所有模板生成都失败了",
                    "templates": []
                }
                
        except Exception as e:
            error_msg = f"生成模板集合异常: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def _generate_single_template(self, template_type: Dict[str, Any]) -> Dict[str, Any]:
        """生成单个SVG模板"""
        try:
            # 构建模板生成提示词
            template_prompt = self._build_template_generation_prompt(template_type)
            
            # 调用AI生成SVG
            messages = [{"role": "user", "content": template_prompt}]
            
            for attempt in range(self.config.max_retries):
                try:
                    response = await self.ai_service.chat_completion(
                        messages=messages,
                        temperature=self.config.temperature
                    )
                    
                    if "error" not in response:
                        # 提取SVG代码
                        svg_code = self._extract_svg_code(response["content"])
                        if svg_code:
                            return {
                                "success": True,
                                "svg_code": svg_code,
                                "raw_response": response["content"],
                                "placeholders": self._extract_placeholders(svg_code),
                                "layout_zones": self._extract_layout_zones(template_type, svg_code),
                                "style_elements": self._extract_style_elements(svg_code)
                            }
                    
                    logger.warning(f"第 {attempt + 1} 次尝试失败: {response.get('error', '未知错误')}")
                    
                except Exception as e:
                    logger.warning(f"第 {attempt + 1} 次尝试异常: {str(e)}")
                
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(1)  # 重试前等待
            
            return {"success": False, "error": f"经过 {self.config.max_retries} 次尝试后仍然失败"}
            
        except Exception as e:
            return {"success": False, "error": f"生成模板异常: {str(e)}"}
    
    def _build_template_generation_prompt(self, template_type: Dict[str, Any]) -> str:
        """构建模板生成提示词"""
        
        # 基础提示词（来自prompts.py）
        base_prompt = PPT_SVG
        
        # 设计规范信息
        design_info = f"""
## 当前设计规范
**主题风格**: {self.design_spec.theme_selection.get('primary_style', '商务专业')}
**配色方案**: 
- 主色: {self.design_spec.color_palette.get('primary_color', '#4A86E8')}
- 辅助色: {self.design_spec.color_palette.get('secondary_color', '#1E3A8A')}
- 强调色: {self.design_spec.color_palette.get('accent_color', '#3B82F6')}
- 背景色: {self.design_spec.color_palette.get('background_color', '#FFFFFF')}
- 文字色: {self.design_spec.color_palette.get('text_primary', '#333333')}

**布局规范**:
- 画布尺寸: {self.config.canvas_width} x {self.config.canvas_height}
- 页面边距: 水平 {self.design_spec.layout_principles.get('page_margins', {}).get('horizontal', 80)}px, 垂直 {self.design_spec.layout_principles.get('page_margins', {}).get('vertical', 60)}px
- 模块间距: {self.design_spec.layout_principles.get('content_spacing', {}).get('module_gap', 24)}px

**字体系统**:
- 主字体: {self.design_spec.typography_system.get('primary_font', 'Microsoft YaHei, sans-serif')}
- 主标题: {self.design_spec.typography_system.get('font_sizes', {}).get('main_title', 56)}px
- 章节标题: {self.design_spec.typography_system.get('font_sizes', {}).get('section_title', 36)}px
- 正文: {self.design_spec.typography_system.get('font_sizes', {}).get('body_text', 22)}px
"""
        
        # 特定模板要求
        template_specific = self._get_template_specific_requirements(template_type)
        
        # 组合提示词
        full_prompt = f"""{base_prompt}

{design_info}

## 当前任务：生成 {template_type['title']}

**模板类型**: {template_type['type']} (第 {template_type['page_number']} 页)
**功能描述**: {template_type['description']}
**内容焦点**: {template_type['content_focus']}
**视觉优先级**: {template_type['visual_priority']}

{template_specific}

## 生成要求
1. **严格遵循设计规范**: 使用上述指定的颜色、字体、布局参数
2. **模板一致性**: 确保与同套模板的其他页面风格完全一致
3. **功能特性**: 充分体现该模板类型的特定功能特点
4. **占位符系统**: 为动态内容设置清晰的占位符，如 {{{{TITLE}}}}、{{{{CONTENT}}}}、{{{{IMAGE_URL}}}}等
5. **响应式设计**: 确保在指定画布尺寸下的最佳显示效果
6. **可用性**: 生成的SVG必须是完整的、语法正确的、可直接使用的

## 输出格式
请直接输出完整的SVG代码，包含：
- XML声明和SVG根元素
- 完整的样式定义
- 所有视觉元素和内容区域
- 适当的占位符标记
- 符合XML规范的格式

开始生成SVG代码："""
        
        return full_prompt
    
    def _get_template_specific_requirements(self, template_type: Dict[str, Any]) -> str:
        """获取特定模板的专门要求"""
        
        template_id = template_type["id"]
        
        specific_requirements = {
            "cover": """
**封面页特殊要求**:
- Logo区域: 左上角预留 200x80px 的Logo占位符 {{LOGO_URL}}
- 主标题: 页面中央突出显示，使用最大字号
- 副标题: 主标题下方，使用次级字号
- 装饰元素: 添加符合风格的装饰图形，增强视觉冲击力
- 背景设计: 可使用渐变或图片背景 {{BACKGROUND_IMAGE}}
- 日期/作者: 底部显示演示日期和作者信息 {{DATE}}, {{AUTHOR}}
""",
            "agenda": """
**目录页特殊要求**:
- 页面标题: "内容概览" 或 "目录"
- 章节列表: 6-8个章节项，使用数字编号
- 进度指示: 每个章节前添加圆点或序号
- 布局: 左侧章节列表，右侧可添加装饰图形
- 占位符: {{PAGE_TITLE}}, {{ITEM_1}}, {{ITEM_2}}, {{ITEM_3}}, {{ITEM_4}}, {{ITEM_5}}, {{ITEM_6}}
""",
            "section_divider": """
**章节分隔页特殊要求**:
- 大标题: 章节名称，使用最大字号居中显示
- 副标题: 章节描述或序号
- 背景设计: 使用渐变或几何图形作为背景
- 装饰元素: 添加符合主题的装饰图案
- 页码: 右下角显示页码
- 占位符: {{SECTION_TITLE}}, {{SECTION_SUBTITLE}}, {{PAGE_NUMBER}}
""",
            "title_content": """
**标题内容页特殊要求**:
- 页面标题: 顶部突出显示
- 主要内容区: 使用卡片或容器承载内容
- 要点列表: 3-5个要点，使用项目符号
- 布局层次: 清晰的信息层级
- 占位符: {{PAGE_TITLE}}, {{MAIN_CONTENT}}, {{POINT_1}}, {{POINT_2}}, {{POINT_3}}, {{POINT_4}}
""",
            "image_text": """
**图文混排页特殊要求**:
- 图片区域: 左侧或右侧预留图片区域，尺寸约 600x400px
- 图片占位符: {{IMAGE_URL}} 使用 <image> 标签
- 文字区域: 与图片平衡的文字说明区域
- 标题: 页面顶部标题
- 图片说明: 图片下方添加说明文字
- 占位符: {{PAGE_TITLE}}, {{IMAGE_URL}}, {{IMAGE_CAPTION}}, {{TEXT_CONTENT}}
""",
            "data_display": """
**数据展示页特殊要求**:
- 图表区域: 主要区域预留给图表显示
- 数据卡片: 3-4个关键数据指标卡片
- 数值突出: 重要数字使用大字号和强调色
- 网格布局: 数据卡片使用网格排列
- 占位符: {{PAGE_TITLE}}, {{CHART_AREA}}, {{METRIC_1}}, {{VALUE_1}}, {{METRIC_2}}, {{VALUE_2}}, {{METRIC_3}}, {{VALUE_3}}
""",
            "comparison": """
**对比分析页特殊要求**:
- 左右布局: 明确的左右对比区域
- VS 标识: 中央添加对比标识
- 对比项目: 每侧 3-4 个对比要点
- 颜色区分: 左右使用不同的强调色
- 结论区域: 底部总结对比结论
- 占位符: {{TITLE}}, {{LEFT_TITLE}}, {{LEFT_ITEM_1}}, {{LEFT_ITEM_2}}, {{RIGHT_TITLE}}, {{RIGHT_ITEM_1}}, {{RIGHT_ITEM_2}}, {{CONCLUSION}}
""",
            "timeline": """
**时间线页特殊要求**:
- 时间轴: 垂直或水平时间线
- 时间节点: 5-6个关键时间点
- 里程碑: 每个节点的事件描述
- 连接线: 节点间的连接设计
- 时间标注: 每个节点的时间标记
- 占位符: {{TIMELINE_TITLE}}, {{EVENT_1}}, {{DATE_1}}, {{EVENT_2}}, {{DATE_2}}, {{EVENT_3}}, {{DATE_3}}
""",
            "quote": """
**引用页特殊要求**:
- 引用标记: 大号引号装饰
- 引用内容: 突出显示的引用文字
- 来源信息: 引用来源和作者
- 背景设计: 柔和的背景色或图案
- 中央布局: 内容居中对齐
- 占位符: {{QUOTE_CONTENT}}, {{QUOTE_AUTHOR}}, {{QUOTE_SOURCE}}
""",
            "conclusion": """
**结尾页特殊要求**:
- 总结标题: "总结" 或 "结论"
- 关键要点: 3-4个核心总结点
- 行动呼吁: 明确的下一步行动
- 联系信息: 底部联系方式
- 感谢语: 感谢观众的话语
- 占位符: {{CONCLUSION_TITLE}}, {{SUMMARY_POINT_1}}, {{SUMMARY_POINT_2}}, {{CALL_TO_ACTION}}, {{CONTACT_INFO}}
"""
        }
        
        return specific_requirements.get(template_id, "")
    
    def _extract_svg_code(self, response_content: str) -> Optional[str]:
        """从AI响应中提取SVG代码"""
        # 尝试提取SVG代码块
        svg_patterns = [
            r'```svg\s*(.*?)\s*```',
            r'```xml\s*(.*?)\s*```',
            r'```\s*(.*?)\s*```',
            r'<svg.*?</svg>'
        ]
        
        for pattern in svg_patterns:
            match = re.search(pattern, response_content, re.DOTALL)
            if match:
                svg_content = match.group(1)
                # 确保SVG标签完整
                if svg_content.strip().startswith('<?xml') or svg_content.strip().startswith('<svg'):
                    return svg_content.strip()
        
        # 如果没有找到代码块，检查是否整个响应就是SVG
        if '<svg' in response_content and '</svg>' in response_content:
            start = response_content.find('<svg')
            if start != -1:
                # 找到完整的SVG内容
                svg_start = response_content.rfind('<?xml', 0, start)
                if svg_start == -1:
                    svg_start = start
                svg_end = response_content.find('</svg>') + 6
                return response_content[svg_start:svg_end].strip()
        
        logger.warning("未能从AI响应中提取有效的SVG代码")
        return None
    
    def _extract_placeholders(self, svg_code: str) -> List[str]:
        """从SVG代码中提取占位符"""
        placeholder_pattern = r'\{\{([^}]+)\}\}'
        placeholders = re.findall(placeholder_pattern, svg_code)
        return list(set(placeholders))  # 去重
    
    def _extract_layout_zones(self, template_type: Dict[str, Any], svg_code: str) -> Dict[str, Dict[str, Any]]:
        """提取布局区域信息"""
        # 这里可以解析SVG中的区域定义，简化处理返回基本布局信息
        return {
            "header": {"x": 0, "y": 0, "width": self.config.canvas_width, "height": 100},
            "content": {"x": 80, "y": 120, "width": self.config.canvas_width-160, "height": self.config.canvas_height-200},
            "footer": {"x": 0, "y": self.config.canvas_height-80, "width": self.config.canvas_width, "height": 80}
        }
    
    def _extract_style_elements(self, svg_code: str) -> List[str]:
        """提取样式元素"""
        elements = []
        if 'gradient' in svg_code.lower():
            elements.append("渐变效果")
        if 'shadow' in svg_code.lower():
            elements.append("阴影效果")
        if 'image' in svg_code.lower():
            elements.append("图片支持")
        if 'pattern' in svg_code.lower():
            elements.append("图案填充")
        return elements
    
    def _save_template_set(self, templates: List[SVGTemplate]) -> Dict[str, Any]:
        """保存模板集合到文件"""
        try:
            # 创建时间戳目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            set_name = f"{self.config.style}_{self.config.color_theme}_{timestamp}"
            save_dir = os.path.join(self.templates_dir, set_name)
            os.makedirs(save_dir, exist_ok=True)
            
            saved_files = []
            
            # 保存每个模板的SVG文件
            for template in templates:
                filename = f"{template.page_number:02d}_{template.template_type}.svg"
                file_path = os.path.join(save_dir, filename)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(template.svg_code)
                
                saved_files.append(filename)
            
            # 保存模板集合元数据
            metadata = {
                "set_info": {
                    "name": set_name,
                    "style": self.config.style,
                    "color_theme": self.config.color_theme,
                    "layout": self.config.layout,
                    "total_templates": len(templates),
                    "created_at": timestamp
                },
                "config": asdict(self.config),
                "design_spec": asdict(self.design_spec) if self.design_spec else None,
                "templates": [asdict(t) for t in templates]
            }
            
            metadata_file = os.path.join(save_dir, "template_set.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📁 模板集合已保存到: {save_dir}")
            
            return {
                "success": True,
                "save_dir": save_dir,
                "set_name": set_name,
                "saved_files": saved_files,
                "metadata_file": metadata_file
            }
            
        except Exception as e:
            logger.error(f"保存模板集合失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def apply_content_to_template(self, template_id: str, content_mapping: Dict[str, str]) -> Dict[str, Any]:
        """将具体内容应用到模板
        
        Args:
            template_id: 模板ID
            content_mapping: 内容映射 {占位符: 实际内容}
            
        Returns:
            应用结果
        """
        try:
            # 查找模板
            template = None
            for t in self.generated_templates:
                if t.template_id == template_id or t.template_type == template_id:
                    template = t
                    break
            
            if not template:
                return {"success": False, "error": f"未找到模板: {template_id}"}
            
            # 应用内容替换
            svg_code = template.svg_code
            
            for placeholder, content in content_mapping.items():
                # 处理占位符格式
                if not placeholder.startswith('{{'):
                    placeholder = f"{{{{{placeholder}}}}}"
                
                # 替换占位符
                svg_code = svg_code.replace(placeholder, str(content))
            
            return {
                "success": True,
                "svg_code": svg_code,
                "template_type": template.template_type,
                "applied_content": content_mapping
            }
            
        except Exception as e:
            return {"success": False, "error": f"应用内容失败: {str(e)}"}
    
    async def generate_complete_presentation(self, presentation_content: Dict[str, Any]) -> Dict[str, Any]:
        """生成完整的演示文稿（10张SVG -> PPT）
        
        Args:
            presentation_content: 演示文稿内容
            格式: {
                "title": "演示文稿标题",
                "author": "作者",
                "date": "日期",
                "slides": [
                    {"template_type": "cover", "content": {...}},
                    {"template_type": "agenda", "content": {...}},
                    ...
                ]
            }
            
        Returns:
            完整演示文稿生成结果
        """
        try:
            logger.info("🎯 开始生成完整演示文稿...")
            
            # 1. 确保有模板集合
            if not self.generated_templates:
                logger.info("📋 模板集合不存在，开始生成...")
                template_result = await self.generate_template_set()
                if not template_result["success"]:
                    return template_result
            
            # 2. 为每张幻灯片应用内容
            applied_slides = []
            
            for i, slide_data in enumerate(presentation_content.get("slides", [])):
                template_type = slide_data.get("template_type")
                content = slide_data.get("content", {})
                
                logger.info(f"📄 应用内容到第 {i+1} 张幻灯片: {template_type}")
                
                apply_result = await self.apply_content_to_template(template_type, content)
                
                if apply_result["success"]:
                    applied_slides.append({
                        "slide_number": i + 1,
                        "template_type": template_type,
                        "svg_code": apply_result["svg_code"],
                        "content": content
                    })
                else:
                    logger.warning(f"❌ 第 {i+1} 张幻灯片内容应用失败: {apply_result['error']}")
            
            # 3. 保存应用内容后的SVG文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            presentation_dir = os.path.join(self.templates_dir, f"presentation_{timestamp}")
            os.makedirs(presentation_dir, exist_ok=True)
            
            svg_files = []
            for slide in applied_slides:
                filename = f"slide_{slide['slide_number']:02d}_{slide['template_type']}.svg"
                file_path = os.path.join(presentation_dir, filename)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(slide['svg_code'])
                
                svg_files.append(file_path)
            
            # 4. 生成演示文稿配置文件
            presentation_config = {
                "presentation_info": presentation_content,
                "slides": applied_slides,
                "generation_info": {
                    "total_slides": len(applied_slides),
                    "style": self.config.style,
                    "color_theme": self.config.color_theme,
                    "created_at": timestamp
                }
            }
            
            config_file = os.path.join(presentation_dir, "presentation.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(presentation_config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"🎉 完整演示文稿生成完成！共 {len(applied_slides)} 张幻灯片")
            
            return {
                "success": True,
                "presentation_dir": presentation_dir,
                "svg_files": svg_files,
                "config_file": config_file,
                "total_slides": len(applied_slides),
                "slides": applied_slides,
                "message": f"成功生成 {len(applied_slides)} 张幻灯片"
            }
            
        except Exception as e:
            error_msg = f"生成完整演示文稿异常: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def load_existing_template_set(self, set_name: str) -> Dict[str, Any]:
        """加载现有的模板集合
        
        Args:
            set_name: 模板集合名称
            
        Returns:
            加载结果
        """
        try:
            set_dir = os.path.join(self.templates_dir, set_name)
            if not os.path.exists(set_dir):
                return {"success": False, "error": f"模板集合不存在: {set_name}"}
            
            metadata_file = os.path.join(set_dir, "template_set.json")
            if not os.path.exists(metadata_file):
                return {"success": False, "error": "模板集合元数据文件不存在"}
            
            # 加载元数据
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 重构模板对象
            templates = []
            for template_data in metadata.get("templates", []):
                template = SVGTemplate(
                    template_id=template_data["template_id"],
                    template_type=template_data["template_type"],
                    title=template_data["title"],
                    description=template_data["description"],
                    svg_code=template_data["svg_code"],
                    placeholders=template_data["placeholders"],
                    layout_zones=template_data["layout_zones"],
                    style_elements=template_data["style_elements"],
                    created_at=template_data["created_at"],
                    page_number=template_data["page_number"]
                )
                templates.append(template)
            
            # 重构设计规范
            if metadata.get("design_spec"):
                spec_data = metadata["design_spec"]
                self.design_spec = DesignSpecification(
                    theme_selection=spec_data["theme_selection"],
                    color_palette=spec_data["color_palette"],
                    layout_principles=spec_data["layout_principles"],
                    typography_system=spec_data["typography_system"],
                    visual_elements=spec_data["visual_elements"],
                    template_consistency=spec_data["template_consistency"],
                    content_outline=spec_data["content_outline"],
                    canvas_size=spec_data["canvas_size"],
                    created_at=spec_data["created_at"],
                    raw_spec=spec_data["raw_spec"]
                )
            
            self.generated_templates = templates
            
            logger.info(f"✅ 成功加载模板集合: {set_name}, 共 {len(templates)} 个模板")
            
            return {
                "success": True,
                "templates": [asdict(t) for t in templates],
                "design_spec": asdict(self.design_spec) if self.design_spec else None,
                "set_info": metadata.get("set_info", {}),
                "message": f"成功加载 {len(templates)} 个模板"
            }
            
        except Exception as e:
            error_msg = f"加载模板集合失败: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    def list_template_sets(self) -> List[Dict[str, Any]]:
        """列出所有可用的模板集合"""
        try:
            if not os.path.exists(self.templates_dir):
                return []
            
            template_sets = []
            
            for item in os.listdir(self.templates_dir):
                item_path = os.path.join(self.templates_dir, item)
                if os.path.isdir(item_path):
                    metadata_file = os.path.join(item_path, "template_set.json")
                    if os.path.exists(metadata_file):
                        try:
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                            
                            set_info = metadata.get("set_info", {})
                            template_sets.append({
                                "name": item,
                                "display_name": set_info.get("name", item),
                                "style": set_info.get("style", "unknown"),
                                "color_theme": set_info.get("color_theme", "unknown"),
                                "total_templates": set_info.get("total_templates", 0),
                                "created_at": set_info.get("created_at", "unknown"),
                                "path": item_path
                            })
                        except Exception as e:
                            logger.warning(f"读取模板集合元数据失败: {item}, {str(e)}")
            
            return sorted(template_sets, key=lambda x: x.get("created_at", ""), reverse=True)
            
        except Exception as e:
            logger.error(f"列出模板集合失败: {str(e)}")
            return []


# 便捷函数
async def create_template_set(
    style: str = "business",
    color_theme: str = "blue",
    layout: str = "standard",
    scenario: str = "商业计划",
    custom_requirements: str = ""
) -> Dict[str, Any]:
    """创建新的模板集合（便捷函数）
    
    Args:
        style: 设计风格
        color_theme: 配色主题
        layout: 布局规格
        scenario: 应用场景
        custom_requirements: 自定义需求
        
    Returns:
        模板集合创建结果
    """
    config = TemplateConfig(
        style=style,
        color_theme=color_theme,
        layout=layout,
        scenario=scenario,
        custom_requirements=custom_requirements
    )
    
    generator = SVGTemplateGenerator(config)
    return await generator.generate_template_set()


async def create_complete_presentation(
    presentation_content: Dict[str, Any],
    style: str = "business",
    color_theme: str = "blue"
) -> Dict[str, Any]:
    """创建完整演示文稿（便捷函数）
    
    Args:
        presentation_content: 演示文稿内容
        style: 设计风格
        color_theme: 配色主题
        
    Returns:
        完整演示文稿创建结果
    """
    config = TemplateConfig(style=style, color_theme=color_theme)
    generator = SVGTemplateGenerator(config)
    
    return await generator.generate_complete_presentation(presentation_content)


if __name__ == "__main__":
    # 测试代码
    async def test_template_generator():
        """测试模板生成器"""
        print("🧪 测试SVG模板生成器...")
        
        # 创建生成器
        config = TemplateConfig(
            style="business",
            color_theme="blue",
            layout="standard",
            scenario="商业计划"
        )
        
        generator = SVGTemplateGenerator(config)
        
        # 生成模板集合
        result = await generator.generate_template_set("智能办公解决方案演示")
        
        if result["success"]:
            print(f"✅ 成功生成 {result['total_templates']} 个模板")
            print(f"📁 保存位置: {result['save_info']['save_dir']}")
        else:
            print(f"❌ 生成失败: {result['error']}")
    
    # 运行测试
    asyncio.run(test_template_generator())