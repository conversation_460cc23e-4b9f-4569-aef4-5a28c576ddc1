# LangGraph 实战教程 - 第一部分：快速入门与核心概念

## 1. 什么是 LangGraph？

LangGraph 是 LangChain 生态系统中的一个强大框架，专门用于构建基于图结构的语言模型应用。它允许开发者创建复杂的、带有循环和条件分支的工作流，是构建多代理系统和复杂AI应用的理想选择。

### 1.1 核心特性

- **图结构工作流**：支持复杂的分支、循环和条件逻辑
- **状态管理**：自动处理应用状态的传递和更新
- **多代理协作**：原生支持多个AI代理的协作
- **人机交互**：支持人工干预和批准机制
- **可视化调试**：提供图形化的工作流可视化工具

### 1.2 LangGraph vs LangChain

| 特性 | LangChain | LangGraph |
|------|-----------|-----------|
| 工作流类型 | 线性链式 | 图结构/循环 |
| 状态管理 | 手动传递 | 自动管理 |
| 条件分支 | 有限支持 | 原生支持 |
| 循环处理 | 困难 | 原生支持 |
| 多代理 | 需要额外工作 | 原生支持 |
| 复杂度 | 简单场景 | 复杂场景 |

## 2. 核心概念

### 2.1 状态（State）

状态是在图中所有节点之间共享的数据结构。每个节点可以读取和修改状态。

```python
from typing import TypedDict
from langgraph.graph import StateGraph

class GraphState(TypedDict):
    """
    图状态定义
    """
    question: str
    context: str
    answer: str
    iteration: int
```

### 2.2 节点（Nodes）

节点是图中的处理单元，每个节点执行特定的功能。

```python
def research_node(state: GraphState) -> GraphState:
    """
    研究节点：收集相关信息
    """
    question = state["question"]
    # 执行研究逻辑
    context = f"研究结果：{question}"
    
    return {
        **state,
        "context": context,
        "iteration": state.get("iteration", 0) + 1
    }
```

### 2.3 边（Edges）

边定义了节点之间的连接和流转条件。

```python
# 条件边示例
def should_continue(state: GraphState) -> str:
    """
    决定是否继续处理
    """
    if state["iteration"] < 3:
        return "continue"
    else:
        return "end"
```

## 3. 环境配置与安装

### 3.1 安装 LangGraph

```bash
# 基础安装
pip install langgraph

# 完整安装（包含可选依赖）
pip install "langgraph[complete]"

# 开发版本
pip install git+https://github.com/langchain-ai/langgraph.git
```

### 3.2 必要依赖

```bash
# 核心依赖
pip install langchain
pip install langchain-openai  # 如果使用OpenAI
pip install langchain-anthropic  # 如果使用Anthropic

# 可视化依赖
pip install jupyter
pip install matplotlib
pip install graphviz

# 其他常用依赖
pip install python-dotenv
pip install pydantic
```

### 3.3 环境变量配置

创建 `.env` 文件：

```bash
# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# Anthropic API
ANTHROPIC_API_KEY=your_anthropic_api_key

# Tavily搜索（可选）
TAVILY_API_KEY=your_tavily_api_key

# LangSmith追踪（可选）
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key
LANGCHAIN_PROJECT=your_project_name
```

### 3.4 基础项目结构

```
langgraph_project/
├── .env
├── requirements.txt
├── src/
│   ├── __init__.py
│   ├── graphs/
│   │   ├── __init__.py
│   │   └── main_graph.py
│   ├── nodes/
│   │   ├── __init__.py
│   │   └── processing_nodes.py
│   └── utils/
│       ├── __init__.py
│       └── helpers.py
├── tests/
│   ├── __init__.py
│   └── test_graphs.py
└── notebooks/
    └── exploration.ipynb
```

## 4. 第一个LangGraph应用

### 4.1 创建简单的问答机器人

```python
import os
from typing import TypedDict
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 定义状态
class QAState(TypedDict):
    question: str
    answer: str
    
# 初始化LLM
llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0)

def answer_question(state: QAState) -> QAState:
    """
    回答问题节点
    """
    question = state["question"]
    
    # 调用LLM生成回答
    messages = [HumanMessage(content=question)]
    response = llm.invoke(messages)
    
    return {
        **state,
        "answer": response.content
    }

# 创建图
workflow = StateGraph(QAState)

# 添加节点
workflow.add_node("answer", answer_question)

# 设置入口点
workflow.set_entry_point("answer")

# 添加边
workflow.add_edge("answer", END)

# 编译图
app = workflow.compile()
```

### 4.2 运行应用

```python
# 运行示例
result = app.invoke({"question": "什么是人工智能？"})
print(f"问题：{result['question']}")
print(f"回答：{result['answer']}")
```

### 4.3 可视化工作流

```python
# 生成工作流图像
try:
    from IPython.display import Image, display
    display(Image(app.get_graph().draw_mermaid_png()))
except Exception as e:
    print("无法显示图像，请确保安装了相关依赖")
    print(app.get_graph().draw_ascii())
```

## 5. 核心组件详解

### 5.1 StateGraph 类

```python
from langgraph.graph import StateGraph

# 创建状态图
graph = StateGraph(YourStateType)

# 核心方法
graph.add_node(name, function)      # 添加节点
graph.add_edge(from_node, to_node)  # 添加边
graph.add_conditional_edges(...)    # 添加条件边
graph.set_entry_point(node_name)    # 设置入口
graph.set_finish_point(node_name)   # 设置终点
```

### 5.2 条件分支

```python
def route_decision(state: GraphState) -> str:
    """
    路由决策函数
    """
    if state["score"] > 0.8:
        return "high_confidence"
    elif state["score"] > 0.5:
        return "medium_confidence"
    else:
        return "low_confidence"

# 添加条件边
workflow.add_conditional_edges(
    "evaluation",
    route_decision,
    {
        "high_confidence": "final_answer",
        "medium_confidence": "research_more",
        "low_confidence": "ask_human"
    }
)
```

### 5.3 循环处理

```python
def check_completion(state: GraphState) -> str:
    """
    检查是否完成
    """
    if state["attempts"] >= 3:
        return "end"
    elif state["quality_score"] >= 0.9:
        return "end"
    else:
        return "retry"

# 创建循环
workflow.add_conditional_edges(
    "process",
    check_completion,
    {
        "retry": "process",  # 循环回到自己
        "end": END
    }
)
```

## 下一步

这是LangGraph教程的第一部分，我们已经覆盖了：
- LangGraph的基本概念和特性
- 环境配置和安装
- 第一个简单应用
- 核心组件介绍

在接下来的部分中，我们将深入实战项目：
- **第二部分**：智能问答机器人项目
- **第三部分**：多代理协作系统
- **第四部分**：复杂工作流自动化
- **第五部分**：最佳实践与进阶技巧

继续阅读下一部分来学习更多实战技能！ 