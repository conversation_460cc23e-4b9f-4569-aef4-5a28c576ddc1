/* 团队选择器按钮样式 */
.team-selector-button {
  min-width: 150px;
  height: 32px;
  display: flex;
  align-items: center;
  border-radius: 4px;
}

.team-button-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

/* 团队选择模态框样式 */
.team-selector-modal .ant-modal-header {
  border-bottom: none;
  padding-bottom: 0;
}

.team-selector-modal .ant-modal-title {
  font-size: 18px;
  font-weight: 600;
}

.team-selector-modal .ant-modal-body {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.team-selector-modal .ant-modal-footer {
  border-top: none;
  padding-top: 0;
}

/* 团队列表样式 */
.team-list {
  padding: 0;
}

.team-list-item {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.team-list-item:hover {
  background-color: #f5f5f5;
}

/* 选中的团队项样式 */
.team-selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.team-selected:hover {
  background-color: #e6f7ff;
}

/* 团队标题样式 */
.team-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.team-name {
  font-weight: 500;
  font-size: 15px;
}

.team-workflow-type {
  font-size: 12px;
  color: #8c8c8c;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

/* 团队描述样式 */
.team-description {
  margin-top: 4px;
}

.team-desc-text {
  font-size: 12px;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 36px;
}

.team-agents-info {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

/* 团队详情按钮样式 */
.team-detail-button {
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
}

/* 响应式样式 */
@media (max-width: 576px) {
  .team-selector-button {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .team-button-text {
    max-width: 100%;
  }
}

/* 在首页的团队选择器样式 */
.home-team-selector .team-selector-button {
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
}

.home-team-selector .team-selector-button:hover {
  background: rgba(0, 0, 0, 0.04);
}

/* 团队详情模态框中的样式 */
.ant-descriptions-item-label {
  font-weight: 600;
  color: #262626;
}

.ant-descriptions-item-content {
  color: #595959;
}

/* 优化团队选择器在工具栏中的外观 */
.ant-btn.team-selector-button {
  min-height: 32px;
}

.ant-btn.team-selector-button:not(.home-team-selector .team-selector-button) {
  border-radius: 4px;
  padding: 0 11px;
}

/* 团队成员列表的样式 */
.ant-list-item-meta-title {
  margin-bottom: 4px;
}

.ant-list-item-meta-description {
  font-size: 12px;
  line-height: 1.5;
}

/* 路由条件列表样式 */
.ant-list-item .ant-space-vertical {
  width: 100%;
}

.ant-list-item .ant-space-vertical .ant-typography {
  margin-bottom: 0;
} 