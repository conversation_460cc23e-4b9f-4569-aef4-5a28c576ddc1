<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette - Defined using CSS variables for readability and reusability -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --hover-color: #7DD3FC; /* From design norms */
        --container-background: #E0F2FE; /* From design norms */

        /* Font System */
        --primary-font: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        --secondary-font: 'Source <PERSON> Sans CN', 'Noto Sans CJK SC', sans-serif;
        --accent-font: 'Times New Roman', serif; /* Not used extensively here, but defined */
        --hero-title-size: 72px;
        --main-title-size: 56px;
        --section-title-size: 36px;
        --content-title-size: 28px;
        --body-text-size: 22px;
        --small-text-size: 16px;
        --caption-size: 14px;
        --font-weight-normal: 400;
        --font-weight-semibold: 600;
        --font-weight-bold: 700;
        --line-height-normal: 1.4;
      }
    </style>

    <!-- Gradients for background and accents -->
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="var(--background-color)"/>
      <stop offset="1" stop-color="var(--container-background)"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="var(--accent-color)"/>
      <stop offset="1" stop-color="var(--primary-color)"/>
    </linearGradient>

    <!-- Card border gradient for subtle highlight on hover/active feel -->
    <linearGradient id="cardBorderGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop stop-color="var(--card-border)"/>
      <stop offset="1" stop-color="var(--hover-color)"/>
    </linearGradient>

    <!-- Drop Shadow Filter for Cards -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Unified Icon System - Outline Style -->
    <symbol id="icon-achievements" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM12 12v4M12 8h.01"/>
    </symbol>
    <symbol id="icon-analysis" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 20v-6M18 20v-4M6 20v-2M3 9l9-7 9 7-9 7-9-7z"/>
    </symbol>
    <symbol id="icon-strategy" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-3 3a5 5 0 0 0-.54 7.54zM14 10L21 3M3 3l7.07 7.07"/>
    </symbol>
    <symbol id="icon-outlook" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 2v20M17 15l-5 5-5-5M7 9l5-5 5 5"/>
    </symbol>
    <symbol id="icon-summary" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8zM14 2v6h6M16 13H8M16 17H8M10 9H8"/>
    </symbol>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative Geometric Elements (Subtle, Transparent) -->
  <circle cx="1700" cy="150" r="80" fill="var(--accent-color)" opacity="0.05"/>
  <rect x="100" y="900" width="150" height="80" rx="12" fill="var(--primary-color)" opacity="0.03"/>
  <path d="M0 0 L 300 0 L 0 300 Z" fill="var(--accent-color)" opacity="0.02"/>
  <path d="M1920 1080 L 1620 1080 L 1920 780 Z" fill="var(--primary-color)" opacity="0.02"/>


  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="160" height="40" fill="var(--primary-color)" rx="8"/>
    <text x="160" y="88" text-anchor="middle" font-family="var(--primary-font)" font-size="20" font-weight="var(--font-weight-bold)" fill="var(--card-background)">
      {logo_url}
    </text>

    <!-- Page Number -->
    <text x="1840" y="88" text-anchor="end" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-secondary)">
      2 / 10
    </text>
  </g>

  <!-- Main Content - Table of Contents -->
  <g id="table-of-contents">
    <!-- Main Title -->
    <text x="960" y="200" text-anchor="middle" font-family="var(--primary-font)" font-size="var(--hero-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-primary)">
      {title}
    </text>
    <text x="960" y="260" text-anchor="middle" font-family="var(--secondary-font)" font-size="var(--content-title-size)" fill="var(--text-secondary)">
      Mid-Year Summary - Content Overview
    </text>

    <!-- Chapter Navigation (Bento Grid Style) -->
    <!-- Grid Layout: 2 columns, 3 rows for a total of 5 chapters + 1 placeholder -->
    <!-- Card dimensions: 690px width, 180px height -->
    <!-- Horizontal positioning: (1920 - (690 * 2 + 32 (module_gap))) / 2 = 244px -->
    <!-- Vertical positioning starts at 340px, then adds 180 (card height) + 32 (module_gap) = 212px per row -->
    <g transform="translate(244, 340)">
      <!-- Row 1 -->
      <g id="chapter-1-achievements" filter="url(#cardShadow)">
        <rect x="0" y="0" width="690" height="180" rx="12" fill="var(--card-background)" stroke="url(#cardBorderGradient)" stroke-width="1"/>
        <text x="40" y="70" font-family="var(--primary-font)" font-size="var(--main-title-size)" font-weight="var(--font-weight-bold)" fill="var(--accent-color)">
          01
        </text>
        <text x="140" y="70" font-family="var(--primary-font)" font-size="var(--section-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-primary)">
          {content} 成果回顾
        </text>
        <text x="140" y="105" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-secondary)">
          Achievements Review and Key Milestones
        </text>
        <use xlink:href="#icon-achievements" x="610" y="60" width="60" height="60" fill="none" stroke="var(--accent-color)"/>
      </g>

      <g id="chapter-2-data-analysis" transform="translate(742, 0)" filter="url(#cardShadow)">
        <rect x="0" y="0" width="690" height="180" rx="12" fill="var(--card-background)" stroke="url(#cardBorderGradient)" stroke-width="1"/>
        <text x="40" y="70" font-family="var(--primary-font)" font-size="var(--main-title-size)" font-weight="var(--font-weight-bold)" fill="var(--accent-color)">
          02
        </text>
        <text x="140" y="70" font-family="var(--primary-font)" font-size="var(--section-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-primary)">
          {content} 数据分析
        </text>
        <text x="140" y="105" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-secondary)">
          In-depth Data Analysis and Insights
        </text>
        <use xlink:href="#icon-analysis" x="610" y="60" width="60" height="60" fill="none" stroke="var(--accent-color)"/>
      </g>

      <!-- Row 2 -->
      <g id="chapter-3-strategy-optimization" transform="translate(0, 212)" filter="url(#cardShadow)">
        <rect x="0" y="0" width="690" height="180" rx="12" fill="var(--card-background)" stroke="url(#cardBorderGradient)" stroke-width="1"/>
        <text x="40" y="70" font-family="var(--primary-font)" font-size="var(--main-title-size)" font-weight="var(--font-weight-bold)" fill="var(--accent-color)">
          03
        </text>
        <text x="140" y="70" font-family="var(--primary-font)" font-size="var(--section-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-primary)">
          {content} 策略优化
        </text>
        <text x="140" y="105" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-secondary)">
          Strategy Optimization and Adjustments
        </text>
        <use xlink:href="#icon-strategy" x="610" y="60" width="60" height="60" fill="none" stroke="var(--accent-color)"/>
      </g>

      <g id="chapter-4-future-outlook" transform="translate(742, 212)" filter="url(#cardShadow)">
        <rect x="0" y="0" width="690" height="180" rx="12" fill="var(--card-background)" stroke="url(#cardBorderGradient)" stroke-width="1"/>
        <text x="40" y="70" font-family="var(--primary-font)" font-size="var(--main-title-size)" font-weight="var(--font-weight-bold)" fill="var(--accent-color)">
          04
        </text>
        <text x="140" y="70" font-family="var(--primary-font)" font-size="var(--section-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-primary)">
          {content} 未来展望
        </text>
        <text x="140" y="105" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-secondary)">
          Future Outlook and Next Steps
        </text>
        <use xlink:href="#icon-outlook" x="610" y="60" width="60" height="60" fill="none" stroke="var(--accent-color)"/>
      </g>

      <!-- Row 3 -->
      <g id="chapter-5-summary" transform="translate(0, 424)" filter="url(#cardShadow)">
        <rect x="0" y="0" width="690" height="180" rx="12" fill="var(--card-background)" stroke="url(#cardBorderGradient)" stroke-width="1"/>
        <text x="40" y="70" font-family="var(--primary-font)" font-size="var(--main-title-size)" font-weight="var(--font-weight-bold)" fill="var(--accent-color)">
          05
        </text>
        <text x="140" y="70" font-family="var(--primary-font)" font-size="var(--section-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-primary)">
          {content} 总结和致谢
        </text>
        <text x="140" y="105" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-secondary)">
          Summary and Acknowledgements
        </text>
        <use xlink:href="#icon-summary" x="610" y="60" width="60" height="60" fill="none" stroke="var(--accent-color)"/>
      </g>

      <!-- Placeholder for more chapters or a general summary card -->
      <g id="chapter-more" transform="translate(742, 424)" filter="url(#cardShadow)">
        <rect x="0" y="0" width="690" height="180" rx="12" fill="var(--card-background)" stroke="var(--card-border)" stroke-width="1"/>
        <text x="345" y="90" text-anchor="middle" font-family="var(--secondary-font)" font-size="var(--section-title-size)" font-weight="var(--font-weight-bold)" fill="var(--text-light)">
          ...
        </text>
        <text x="345" y="130" text-anchor="middle" font-family="var(--secondary-font)" font-size="var(--body-text-size)" fill="var(--text-light)">
          More Sections To Be Added
        </text>
      </g>
    </g>
  </g>

  <!-- Progress Indicator (Bottom Bar) -->
  <g id="progress-indicator">
    <!-- Background bar -->
    <rect x="80" y="1000" width="1760" height="8" rx="4" fill="var(--card-border)"/>
    <!-- Current progress: 2/10 = 20% of 1760px width -->
    <rect x="80" y="1000" width="352" height="8" rx="4" fill="url(#accentGradient)"/>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1040" font-family="var(--secondary-font)" font-size="var(--small-text-size)" fill="var(--text-light)">
      {date}
    </text>
    <text x="1840" y="1040" text-anchor="end" font-family="var(--secondary-font)" font-size="var(--small-text-size)" fill="var(--text-light)">
      {author}
    </text>
  </g>

</svg>