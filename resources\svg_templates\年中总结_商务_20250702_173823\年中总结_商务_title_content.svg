<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
  <defs>
    <!-- Color Palette - Defined as CSS Variables for reusability and clarity -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6; /* Matches accent-color */
        --hover-color: #7DD3FC;
        --active-color: #1E40AF; /* Matches primary-color */
        --disabled-color: #64748B; /* Matches text-secondary */
      }

      /* Font System */
      .font-primary { font-family: '<PERSON> YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes */
      .text-hero-title { font-size: 72px; }
      .text-main-title { font-size: 56px; }
      .text-section-title { font-size: 36px; }
      .text-content-title { font-size: 28px; }
      .text-body { font-size: 22px; }
      .text-small { font-size: 16px; }
      .text-caption { font-size: 14px; }

      /* Font Weights */
      .font-thin { font-weight: 100; }
      .font-light { font-weight: 300; }
      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }
      .font-black { font-weight: 900; }

      /* Text Colors */
      .text-primary-color { fill: var(--text-primary); }
      .text-secondary-color { fill: var(--text-secondary); }
      .text-light-color { fill: var(--text-light); }
      .text-accent-color { fill: var(--accent-color); }
      .text-white-color { fill: #FFFFFF; } /* For contrast on dark elements */

      /* Shapes & Fills */
      .fill-background { fill: var(--background-color); }
      .fill-primary { fill: var(--primary-color); }
      .fill-secondary { fill: var(--secondary-color); }
      .fill-accent { fill: var(--accent-color); }
      .fill-card-background { fill: var(--card-background); }
      .fill-container-background { fill: var(--container-background); }

      /* Strokes */
      .stroke-primary { stroke: var(--primary-color); }
      .stroke-accent { stroke: var(--accent-color); }
      .stroke-card-border { stroke: var(--card-border); }
      .stroke-divider { stroke: var(--card-border); } /* Using card border for dividers */

      /* General Styles */
      .line-height-normal { line-height: 1.4; }
      .letter-spacing-normal { letter-spacing: 0em; }
    </style>

    <!-- Gradients -->
    <!-- Primary Gradient (linear-gradient(135deg, #1E40AF, #475569)) -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:var(--primary-color);stop-opacity:1" />
      <stop offset="100%" style="stop-color:var(--secondary-color);stop-opacity:1" />
    </linearGradient>
    <!-- Accent Gradient (linear-gradient(45deg, #3B82F6, #1E40AF)) -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:var(--accent-color);stop-opacity:1" />
      <stop offset="100%" style="stop-color:var(--primary-color);stop-opacity:1" />
    </linearGradient>
    <!-- Accent Transparent Gradient (for techy emphasis) -->
    <linearGradient id="gradientAccentTransparent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:var(--accent-color);stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:var(--accent-color);stop-opacity:0.2" />
    </linearGradient>
    <!-- Subtle Background Gradient (linear-gradient(180deg, #F8FAFC, #E0F2FE)) -->
    <linearGradient id="gradientBackground" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:var(--background-color);stop-opacity:1" />
      <stop offset="100%" style="stop-color:var(--container-background);stop-opacity:1" />
    </linearGradient>

    <!-- Filter for Card Shadow (0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)) -->
    <filter id="cardShadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset result="offOut1" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut1" in="offOut1" stdDeviation="3" />
      <feColorMatrix result="shadowMatrix1" in="blurOut1" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" />
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2" />
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2" />
      <feColorMatrix result="shadowMatrix2" in="blurOut2" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0" />
      <feMerge>
        <feMergeNode in="shadowMatrix1" />
        <feMergeNode in="shadowMatrix2" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gradientBackground)" />

  <!-- Decorative elements - Subtle background patterns (geometric shapes) -->
  <g id="decorative-background-elements">
    <circle cx="1800" cy="100" r="80" class="fill-primary" opacity="0.05"/>
    <rect x="1700" y="950" width="200" height="150" class="fill-accent" opacity="0.03" transform="rotate(15 1700 950)"/>
    <!-- Large, subtle waves/shapes mimicking container_background -->
    <path d="M-50 800 Q 200 700 400 850 T 800 900 Q 1000 950 1200 800 L 1200 1080 L -50 1080 Z" class="fill-container-background" opacity="0.3"/>
    <path d="M1970 200 Q 1700 300 1500 150 T 1100 100 Q 900 50 700 200 L 700 -50 L 1970 -50 Z" class="fill-container-background" opacity="0.2"/>
  </g>

  <!-- Main Content Area - Margins: 80px horizontal, 60px vertical -->
  <g transform="translate(80 60)">
    <!-- Page Header (Top Left) -->
    <g id="page-header">
      <text x="0" y="24" class="text-small font-normal text-light-color font-primary">4/10</text>
      <!-- Logo Placeholder (example rect with text, replace with actual image) -->
      <image x="0" y="40" width="120" height="40" xlink:href="{logo_url}" />
      <rect x="0" y="40" width="120" height="40" rx="6" class="fill-primary" opacity="0.1"/>
      <text x="60" y="65" text-anchor="middle" class="text-small font-semibold text-primary-color font-primary">Logo Here</text>
    </g>

    <!-- Content Card/Container - Bento Grid style interpretation: a large, structured card -->
    <rect x="0" y="120" width="1760" height="840" rx="12" class="fill-card-background stroke-card-border" style="filter:url(#cardShadow);"/>

    <g transform="translate(100 180)"> <!-- Inner padding for the card: 100px horizontal, 60px vertical from card top -->

      <!-- Main Title -->
      <text x="0" y="0" class="text-section-title font-bold text-primary-color font-primary">
        <tspan>{title}</tspan>
        <tspan x="0" dy="48">年中总结：核心成就和未来展望</tspan>
      </text>
      <text x="0" y="100" class="text-content-title font-normal text-secondary-color font-primary">
        <tspan>{subtitle}</tspan>
        <tspan x="0" dy="36">深入分析业务表现和战略规划</tspan>
      </text>

      <!-- Horizontal Divider -->
      <line x1="0" y1="180" x2="1560" y2="180" class="stroke-divider" stroke-width="1"/>

      <!-- Content Layout: Main content paragraph on left, key metrics & bullet points on right -->
      <g id="content-layout" transform="translate(0 220)">
        <!-- Left Section: Main Content Paragraph -->
        <g id="main-content-paragraph">
          <text x="0" y="0" class="text-body font-normal text-primary-color font-primary" style="white-space: pre-line;">
            <tspan x="0" dy="0">{content}</tspan>
            <tspan x="0" dy="32">本部分将深入探讨项目背景、核心目标，以及我们在过去半年中面临的挑战和取得的突破。我们致力于提供一个清晰的概述，以便所有利益相关者都能理解我们的进展和未来方向。</tspan>
            <tspan x="0" dy="32">通过详尽的数据分析和案例研究，我们将展示各项举措如何协同作用，共同推动了业务增长和效率提升。我们的策略是基于市场洞察和内部能力的精确匹配。</tspan>
            <tspan x="0" dy="32">未来，我们将继续优化流程，拓展新市场，并投资于人才发展，以确保可持续的竞争力。重点关注创新和客户体验的提升，确保在竞争激烈的市场中保持领先地位。</tspan>
          </text>
        </g>

        <!-- Right Section: Key Metrics / Visual Highlight & Bullet Points -->
        <g transform="translate(800 0)">
          <!-- Key Metric Highlight (Super Large Font) -->
          <g id="key-metric">
            <text x="760" y="80" class="text-hero-title font-black text-accent-color font-accent" text-anchor="end">
              <tspan fill="url(#gradientAccentTransparent)">+35%</tspan>
            </text>
            <text x="760" y="120" class="text-content-title font-bold text-primary-color font-primary" text-anchor="end">
              <tspan>业绩增长</tspan>
            </text>
            <text x="760" y="150" class="text-small font-normal text-secondary-color font-primary" text-anchor="end">
              <tspan>Year-on-Year Growth</tspan>
            </text>
            <!-- Simple outline graphic for data visualization -->
            <path d="M500 170 L500 100 L550 100 L550 140 L600 140 L600 80 L650 80 L650 120 L700 120 L700 50"
                  fill="none" stroke="var(--accent-color)" stroke-width="3" opacity="0.6" stroke-linecap="round" stroke-linejoin="round"/>
          </g>


          <!-- Bullet Points List -->
          <g id="bullet-points" transform="translate(0 200)">
            <text x="0" y="0" class="text-content-title font-semibold text-primary-color font-primary">
              <tspan>核心成果要点:</tspan>
            </text>

            <g transform="translate(0 40)">
              <!-- Bullet 1 -->
              <circle cx="-10" cy="-6" r="4" class="fill-accent"/>
              <text x="10" y="0" class="text-body font-normal text-primary-color font-primary">
                <tspan>市场份额提升</tspan>
                <tspan class="text-small font-normal text-secondary-color" dx="10">Market Share Growth</tspan>
              </text>
            </g>

            <g transform="translate(0 80)">
              <!-- Bullet 2 -->
              <circle cx="-10" cy="-6" r="4" class="fill-accent"/>
              <text x="10" y="0" class="text-body font-normal text-primary-color font-primary">
                <tspan>运营效率优化</tspan>
                <tspan class="text-small font-normal text-secondary-color" dx="10">Operational Efficiency Improved</tspan>
              </text>
            </g>

            <g transform="translate(0 120)">
              <!-- Bullet 3 -->
              <circle cx="-10" cy="-6" r="4" class="fill-accent"/>
              <text x="10" y="0" class="text-body font-normal text-primary-color font-primary">
                <tspan>客户满意度显著提升</tspan>
                <tspan class="text-small font-normal text-secondary-color" dx="10">Customer Satisfaction Boost</tspan>
              </text>
            </g>

            <g transform="translate(0 160)">
              <!-- Bullet 4 -->
              <circle cx="-10" cy="-6" r="4" class="fill-accent"/>
              <text x="10" y="0" class="text-body font-normal text-primary-color font-primary">
                <tspan>新产品成功推出</tspan>
                <tspan class="text-small font-normal text-secondary-color" dx="10">New Product Launch Success</tspan>
              </text>
            </g>

            <g transform="translate(0 200)">
              <!-- Bullet 5 -->
              <circle cx="-10" cy="-6" r="4" class="fill-accent"/>
              <text x="10" y="0" class="text-body font-normal text-primary-color font-primary">
                <tspan>团队协作和创新能力增强</tspan>
                <tspan class="text-small font-normal text-secondary-color" dx="10">Enhanced Team Collaboration</tspan>
              </text>
            </g>

          </g>
        </g>
      </g>
    </g>
  </g>

  <!-- Footer (Bottom Right) -->
  <g id="page-footer" transform="translate(1640 1000)">
    <text x="0" y="0" text-anchor="end" class="text-small font-normal text-secondary-color font-primary">
      <tspan>{date} | {author}</tspan>
      <tspan x="0" dy="20">2023年年中总结 | 市场部</tspan>
    </text>
  </g>

  <!-- Additional Decorative elements - Subtle icons/patterns for techy feel -->
  <g id="decorative-icons">
    <!-- Abstract 'network' or 'connection' lines -->
    <path d="M120 900 Q 200 850 300 900 T 500 850" fill="none" stroke="var(--accent-color)" stroke-width="1.5" opacity="0.2" stroke-linecap="round"/>
    <path d="M1700 200 Q 1650 150 1600 200 T 1500 150" fill="none" stroke="var(--primary-color)" stroke-width="1.5" opacity="0.15" stroke-linecap="round"/>
    <!-- Small accent circles -->
    <circle cx="150" cy="1000" r="10" class="fill-primary" opacity="0.08"/>
    <circle cx="1700" cy="80" r="15" class="fill-accent" opacity="0.08"/>
  </g>

</svg>