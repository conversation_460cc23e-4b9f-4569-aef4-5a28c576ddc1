#!/usr/bin/env python3
"""
调试线程本地存储中的智能体ID问题
"""

import threading
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.langgraph_enhancement.agents.context_optimized_agent import get_agent_pool, ContextOptimizedAgent
from backend.ai.manager import AIManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ThreadLocalDebugger:
    """线程本地存储调试器"""
    
    def __init__(self):
        self.ai_manager = AIManager()
    
    def check_thread_local_storage(self):
        """检查当前线程的本地存储"""
        current_thread = threading.current_thread()
        
        print(f"🧵 当前线程: {current_thread.name}")
        print(f"🧵 线程ID: {current_thread.ident}")
        
        # 检查是否有current_agent_id属性
        if hasattr(current_thread, 'current_agent_id'):
            agent_id = current_thread.current_agent_id
            print(f"✅ 线程本地存储中找到 agent_id: {agent_id}")
        else:
            print("❌ 线程本地存储中没有 current_agent_id 属性")
        
        # 列出所有线程本地属性
        thread_attrs = [attr for attr in dir(current_thread) if not attr.startswith('_')]
        print(f"🔍 线程本地属性: {thread_attrs}")
        
        return getattr(current_thread, 'current_agent_id', None)
    
    def simulate_agent_call(self, agent_id: str):
        """模拟智能体调用过程"""
        print(f"\n🎯 模拟智能体 {agent_id} 的调用过程")
        
        # 模拟ContextOptimizedAgent设置线程本地存储
        current_thread = threading.current_thread()
        old_agent_id = getattr(current_thread, 'current_agent_id', None)
        current_thread.current_agent_id = agent_id
        
        print(f"📝 设置 current_agent_id = {agent_id}")
        
        try:
            # 检查设置后的状态
            self.check_thread_local_storage()
            
            # 测试AI Manager的获取方法
            print(f"\n🔍 测试 AI Manager 的 _get_current_agent_instance 方法")
            
            # 创建模拟上下文
            mock_ctx = {}
            
            # 调用获取方法
            agent_instance = asyncio.run(self.ai_manager._get_current_agent_instance(mock_ctx))
            
            if agent_instance:
                print(f"✅ AI Manager 成功获取智能体: {agent_instance.agent_id}")
            else:
                print("❌ AI Manager 无法获取智能体实例")
                
        finally:
            # 恢复线程本地存储
            if old_agent_id is not None:
                current_thread.current_agent_id = old_agent_id
            elif hasattr(current_thread, 'current_agent_id'):
                delattr(current_thread, 'current_agent_id')
    
    def test_agent_pool(self):
        """测试智能体池"""
        print(f"\n🔍 测试智能体池状态")
        
        agent_pool = get_agent_pool()
        all_agents = agent_pool.get_all_agents()
        
        print(f"📊 智能体池中的智能体数量: {len(all_agents)}")
        
        for agent_id, agent in all_agents.items():
            print(f"  - {agent_id}: {type(agent).__name__}")
            if hasattr(agent, 'last_processing_time'):
                print(f"    最后处理时间: {agent.last_processing_time}")
        
        # 测试get_agent方法
        if all_agents:
            test_agent_id = next(iter(all_agents.keys()))
            retrieved_agent = agent_pool.get_agent(test_agent_id)
            
            if retrieved_agent:
                print(f"✅ 成功从池中检索智能体: {test_agent_id}")
            else:
                print(f"❌ 无法从池中检索智能体: {test_agent_id}")
        
        return all_agents

def main():
    """主测试函数"""
    print("🚀 开始线程本地存储调试...")
    
    debugger = ThreadLocalDebugger()
    
    # 1. 检查初始状态
    print("1️⃣ 检查初始线程本地存储状态")
    debugger.check_thread_local_storage()
    
    # 2. 测试智能体池
    print("\n2️⃣ 测试智能体池")
    agents = debugger.test_agent_pool()
    
    # 3. 模拟智能体调用
    print("\n3️⃣ 模拟智能体调用")
    debugger.simulate_agent_call("test_agent_001")
    
    # 4. 如果有真实智能体，测试真实场景
    if agents:
        real_agent_id = next(iter(agents.keys()))
        print(f"\n4️⃣ 使用真实智能体 {real_agent_id} 测试")
        debugger.simulate_agent_call(real_agent_id)
    
    print("\n🏁 调试完成")

if __name__ == "__main__":
    main()