<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Scheme Variables -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --hover-color: #7DD3FC;
        --active-color: #1E40AF;
        --disabled-color: #64748B;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6;
      }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; letter-spacing: -0.025em; }
      .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; letter-spacing: -0.025em; }
      .text-section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .text-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .text-small { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .text-caption { font-size: 14px; font-weight: 400; line-height: 1.4; }
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }

      /* Common Fills */
      .fill-primary { fill: var(--primary-color); }
      .fill-accent { fill: var(--accent-color); }
      .fill-background { fill: var(--background-color); }
      .fill-card-background { fill: var(--card-background); }
      .fill-text-primary { fill: var(--text-primary); }
      .fill-text-secondary { fill: var(--text-secondary); }
      .fill-text-light { fill: var(--text-light); }

      /* Common Strokes */
      .stroke-card-border { stroke: var(--card-border); }
      .stroke-accent { stroke: var(--accent-color); }

      /* Card Style */
      .card-style {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        rx: 12px;
        filter: url(#shadow);
      }
    </style>

    <!-- Shadows -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4" result="offOut"/>
      <feGaussianBlur in="offOut" stdDeviation="3" result="blurOut"/>
      <feColorMatrix in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" result="shadowColor"/>
      <feMerge>
        <feMergeNode in="shadowColor"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="background-gradient" x1="0" y1="0" x2="0" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="var(--background-color)"/>
      <stop offset="1" stop-color="var(--container-background)"/>
    </linearGradient>

    <linearGradient id="accent-gradient-transparent" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="var(--accent-color)" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="var(--accent-color)" stop-opacity="0"/>
    </linearGradient>

    <!-- Icon System: Example Line Chart Icon -->
    <g id="icon-chart-line" stroke-width="2" stroke="var(--accent-color)" fill="none" stroke-linecap="round" stroke-linejoin="round">
      <polyline points="3 18 10 10 16 16 21 8" />
      <circle cx="3" cy="18" r="1" />
      <circle cx="10" cy="10" r="1" />
      <circle cx="16" cy="16" r="1" />
      <circle cx="21" cy="8" r="1" />
    </g>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#background-gradient)"/>

  <!-- Page Header -->
  <g id="page-header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="160" height="40" fill="var(--primary-color)" rx="8"/>
    <text x="160" y="87" text-anchor="middle" class="font-primary text-small" fill="white">
      <tspan>{logo_url}</tspan>
    </text>
    <!-- Page Number -->
    <text x="1760" y="87" text-anchor="end" class="font-primary text-small fill-text-secondary">
      <tspan>5/10</tspan>
    </text>
  </g>

  <!-- Main Content Area -->
  <g id="main-content" transform="translate(80 160)">
    <!-- Main Title -->
    <text x="880" y="0" text-anchor="middle" class="font-primary text-main-title fill-text-primary">
      <tspan>
        <tspan class="font-bold">市场洞察</tspan>
        <tspan class="fill-text-secondary">和</tspan>
        <tspan class="font-semibold">增长机遇</tspan>
      </tspan>
    </text>
    <text x="880" y="70" text-anchor="middle" class="font-secondary text-content-title fill-text-secondary">
      <tspan>{subtitle}</tspan>
    </text>

    <!-- Content Modules - Two Column Layout for Image and Text Balance -->
    <g id="content-modules" transform="translate(0 140)">
      <!-- Left Column: Image Card -->
      <g id="image-card" transform="translate(0 0)">
        <rect x="0" y="0" width="860" height="580" class="card-style" rx="12"/>
        <!-- Image Placeholder -->
        <rect x="20" y="20" width="820" height="540" fill="var(--container-background)" rx="8"/>
        <text x="430" y="290" text-anchor="middle" alignment-baseline="middle" class="font-secondary text-content-title fill-text-light">
          <tspan>图片展示</tspan>
        </text>
        <text x="430" y="330" text-anchor="middle" alignment-baseline="middle" class="font-secondary text-small fill-text-light">
          <tspan>{image_url}</tspan>
        </text>
        <!-- Decorative Overlay (Subtle Techy Gradient) -->
        <rect x="20" y="20" width="820" height="540" rx="8" fill="url(#accent-gradient-transparent)"/>
      </g>

      <!-- Right Column: Text and Data Card -->
      <g id="text-data-card" transform="translate(900 0)">
        <rect x="0" y="0" width="860" height="580" class="card-style" rx="12"/>

        <!-- Section Title for Text -->
        <text x="40" y="60" class="font-primary text-content-title fill-text-primary">
          <tspan class="font-bold">核心市场分析</tspan>
        </text>

        <!-- Body Text Content -->
        <g class="font-secondary text-body fill-text-secondary">
          <text x="40" y="120" width="780">
            <tspan x="40" dy="0">
              <tspan class="font-semibold" fill="var(--primary-color)">市场规模和趋势:</tspan> 预计未来五年，全球市场将以年均增长率 {growth_rate}% 持续扩张，主要驱动力来自技术创新和新兴市场需求。
            </tspan>
            <tspan x="40" dy="40">
              <tspan class="font-semibold" fill="var(--primary-color)">竞争格局:</tspan> 市场集中度逐渐提高，领先企业在技术、品牌和渠道方面拥有显著优势。但新进入者仍有机会通过差异化战略取得成功。
            </tspan>
            <tspan x="40" dy="40">
              <tspan class="font-semibold" fill="var(--primary-color)">用户画像:</tspan> 我们的目标用户群体以年轻专业人士为主，注重效率、创新和可持续性。他们对数字化解决方案的接受度高。
            </tspan>
            <tspan x="40" dy="40">
              <tspan class="font-semibold" fill="var(--primary-color)">风险评估:</tspan> 主要风险包括宏观经济波动、政策法规变化和技术迭代加速。我们已制定相应的应对策略。
            </tspan>
          </text>
        </g>

        <!-- Placeholder for Data/Chart (Outline Graphic) -->
        <g transform="translate(40 430)">
          <rect x="0" y="0" width="780" height="120" fill="var(--container-background)" rx="8"/>
          <text x="390" y="60" text-anchor="middle" alignment-baseline="middle" class="font-secondary text-small fill-text-light">
            <tspan>数据图表占位符 (在线图表组件兼容样式)</tspan>
          </text>
          <!-- Simple Outline Graph example (can be replaced by an actual chart SVG) -->
          <use href="#icon-chart-line" transform="translate(370 40) scale(2)" />
        </g>
      </g>
    </g>

    <!-- Decorative Element: Large Accent Number/Text (Subtle, for visual interest) -->
    <g id="decorative-element">
      <text x="880" y="640" text-anchor="middle" class="font-primary text-hero-title" fill="var(--accent-color)" opacity="0.1">
        <tspan>洞察</tspan>
      </text>
      <text x="880" y="700" text-anchor="middle" class="font-secondary text-main-title" fill="var(--accent-color)" opacity="0.05">
        <tspan>INSIGHT</tspan>
      </text>
    </g>

  </g>

  <!-- Page Footer -->
  <g id="page-footer">
    <text x="80" y="1020" class="font-primary text-small fill-text-secondary">
      <tspan>{date}</tspan>
    </text>
    <text x="1760" y="1020" text-anchor="end" class="font-primary text-small fill-text-secondary">
      <tspan>{author}</tspan>
    </text>
  </g>

</svg>