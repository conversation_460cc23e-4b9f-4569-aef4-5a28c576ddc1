.welcome-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.greeting {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.question {
  font-size: 2.5rem;
  margin-top: 0;
}

.highlight {
  color: #1890ff;
}

.description {
  margin-top: 1rem;
  color: rgba(0, 0, 0, 0.6);
}

.prompt-cards {
  margin-top: 1.5rem;
}

.refresh-button {
  margin-top: 1rem;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
}

.refresh-button:hover {
  color: #1890ff;
}

.message-input-wrapper {
  margin-top: 2rem;
  max-width: 800px;
}

/* Enhanced model selector styles */
.model-selector-container {
  display: flex;
  align-items: center;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
}

.model-selector-label {
  margin-right: 10px;
  font-weight: 500;
}

.model-selector {
  width: 260px;
}

/* Enhanced MCP tool button styles */
.mcp-button {
  margin-top: 1rem;
  display: flex;
  align-items: center;
}

/* MCP工具模态框样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.tools-container {
  margin-top: 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.tool-card {
  height: 100%;
}

.tool-details {
  margin-top: 10px;
}

@media (max-width: 768px) {
  .welcome-content {
    padding: 1.5rem;
  }
  
  .greeting, .question {
    font-size: 2rem;
  }
  
  .model-selector-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .model-selector-label {
    margin-bottom: 8px;
  }
  
  .model-selector {
    width: 100%;
  }
  
  .tools-container {
    grid-template-columns: 1fr;
  }
} 