<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="chartGradient1" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2"/>
    </linearGradient>
    <linearGradient id="chartGradient2" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.2"/>
    </linearGradient>

    <!-- 滤镜定义：用于卡片阴影效果 -->
    <filter id="cardShadow" x="-10" y="-10" width="100%" height="100%" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#000000" flood-opacity="0.1"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.06"/>
    </filter>
  </defs>

  <style>
    /* 字体定义 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 文本颜色 */
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .text-light { fill: #94A3B8; }
    .text-accent { fill: #3B82F6; }
    .text-white { fill: #FFFFFF; }

    /* 字体大小和粗细 */
    .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* Bold */
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* Bold */
    .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* Semibold */
    .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* Semibold */
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* Normal */
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* Normal */
    .caption { font-size: 14px; font-weight: 400; line-height: 1.6; } /* Normal */
    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }

    /* 卡片样式 */
    .card-background { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; stroke-width: 1px; }
    .card-rounded { rx: 12px; ry: 12px; }

    /* 图标样式 */
    .icon-color { stroke: #4A86E8; stroke-width: 2px; }

    /* 图表样式 */
    .chart-line { stroke: #3B82F6; stroke-width: 4px; fill: none; }
    .chart-area { fill: url(#chartGradient1); }
    .chart-grid-line { stroke: #E0F2FE; stroke-width: 1px; }
    .chart-label { fill: #64748B; font-size: 16px; font-family: 'Microsoft YaHei', sans-serif; }
    .chart-point { fill: #3B82F6; stroke: #FFFFFF; stroke-width: 2px; }

    /* 装饰元素样式 */
    .divider-line { stroke: #BAE6FD; stroke-width: 1px; }
  </style>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="#F8FAFC"/>

  <!-- 页眉 -->
  <g id="header">
    <!-- Logo 占位符 -->
    <rect x="80" y="60" width="160" height="40" fill="#E0F2FE" rx="8" ry="8"/>
    <text x="160" y="87" text-anchor="middle" class="small-text text-secondary font-semibold font-primary">
      {logo_url}
    </text>
    <!-- 页码 -->
    <text x="1760" y="87" text-anchor="end" class="small-text text-secondary font-primary">
      页面 6/10
    </text>
  </g>

  <!-- 主内容区域 -->
  <g id="main-content" transform="translate(80, 160)">
    <!-- 标题和副标题 -->
    <text x="0" y="0" class="main-title text-primary font-bold font-primary">
      {title}
    </text>
    <text x="0" y="66" class="section-title text-secondary font-primary">
      {subtitle}
    </text>

    <!-- 主图表区域 (左侧 - 仿 Bento Grid 风格) -->
    <g id="chart-area" transform="translate(0, 120)">
      <rect x="0" y="0" width="1080" height="700" class="card-background card-rounded" filter="url(#cardShadow)"/>
      <rect x="0" y="0" width="1080" height="700" class="card-border card-rounded"/>

      <!-- 图表标题 -->
      <text x="40" y="50" class="content-title text-primary font-semibold font-primary">
        市场增长趋势分析
        <tspan x="40" dy="30" class="small-text text-secondary font-primary">Market Growth Trend Analysis</tspan>
      </text>

      <!-- 图表示例 - 区域折线图 -->
      <g transform="translate(80, 120)">
        <!-- X轴 -->
        <line x1="0" y1="450" x2="900" y2="450" class="chart-grid-line"/>
        <text x="0" y="470" class="chart-label">2021</text>
        <text x="225" y="470" class="chart-label">2022</text>
        <text x="450" y="470" class="chart-label">2023</text>
        <text x="675" y="470" class="chart-label">2024</text>
        <text x="900" y="470" class="chart-label">2025</text>

        <!-- Y轴 -->
        <line x1="0" y1="0" x2="0" y2="450" class="chart-grid-line"/>
        <text x="-30" y="450" text-anchor="end" class="chart-label">0%</text>
        <text x="-30" y="337.5" text-anchor="end" class="chart-label">25%</text>
        <text x="-30" y="225" text-anchor="end" class="chart-label">50%</text>
        <text x="-30" y="112.5" text-anchor="end" class="chart-label">75%</text>
        <text x="-30" y="0" text-anchor="end" class="chart-label">100%</text>

        <!-- 图表数据路径 (示例数据) -->
        <path d="M0 400 C225 300, 450 150, 675 200, 900 100" class="chart-line"/>
        <path d="M0 400 L0 450 L900 450 L900 100 C675 200, 450 150, 225 300, 0 400 Z" class="chart-area"/>

        <!-- 数据点 (示例) -->
        <circle cx="0" cy="400" r="6" class="chart-point"/>
        <circle cx="225" cy="300" r="6" class="chart-point"/>
        <circle cx="450" cy="150" r="6" class="chart-point"/>
        <circle cx="675" cy="200" r="6" class="chart-point"/>
        <circle cx="900" cy="100" r="6" class="chart-point"/>
      </g>
    </g>

    <!-- 数据卡片区域 (右侧 - 仿 Bento Grid 风格) -->
    <g id="data-cards" transform="translate(1120, 120)">
      <!-- 卡片 1: 关键指标 -->
      <rect x="0" y="0" width="560" height="220" class="card-background card-rounded" filter="url(#cardShadow)"/>
      <rect x="0" y="0" width="560" height="220" class="card-border card-rounded"/>
      <text x="40" y="60" class="small-text text-secondary font-primary">
        年度营收增长
        <tspan x="40" dy="24" class="caption text-light font-primary">Annual Revenue Growth</tspan>
      </text>
      <text x="40" y="140" class="hero-title text-accent font-bold font-primary">
        +35%
      </text>
      <text x="40" y="180" class="body-text text-secondary font-primary">
        同比去年同期
        <tspan x="40" dy="24" class="small-text text-light font-primary">Year-over-year</tspan>
      </text>

      <!-- 卡片 2: 市场占有率 -->
      <rect x="0" y="240" width="270" height="220" class="card-background card-rounded" filter="url(#cardShadow)"/>
      <rect x="0" y="240" width="270" height="220" class="card-border card-rounded"/>
      <text x="40" y="290" class="small-text text-secondary font-primary">
        市场占有率
        <tspan x="40" dy="20" class="caption text-light font-primary">Market Share</tspan>
      </text>
      <text x="40" y="370" class="section-title text-primary font-bold font-primary">
        22.5%
      </text>

      <!-- 卡片 3: 新增用户 -->
      <rect x="290" y="240" width="270" height="220" class="card-background card-rounded" filter="url(#cardShadow)"/>
      <rect x="290" y="240" width="270" height="220" class="card-border card-rounded"/>
      <text x="330" y="290" class="small-text text-secondary font-primary">
        新增用户数
        <tspan x="330" dy="20" class="caption text-light font-primary">New Users</tspan>
      </text>
      <text x="330" y="370" class="section-title text-primary font-bold font-primary">
        1.2M
      </text>

      <!-- 卡片 4: 项目投资回报率 -->
      <rect x="0" y="480" width="560" height="220" class="card-background card-rounded" filter="url(#cardShadow)"/>
      <rect x="0" y="480" width="560" height="220" class="card-border card-rounded"/>
      <text x="40" y="530" class="small-text text-secondary font-primary">
        项目投资回报率
        <tspan x="40" dy="20" class="caption text-light font-primary">Project ROI</tspan>
      </text>
      <text x="40" y="610" class="main-title text-accent font-bold font-primary">
        180%
      </text>
      <text x="40" y="650" class="small-text text-light font-primary">
        预期五年内实现
        <tspan x="40" dy="20" class="caption text-light font-primary">Expected within 5 years</tspan>
      </text>
    </g>
  </g>

  <!-- 装饰元素 -->
  <g id="decorations">
    <!-- 右上角抽象图案 -->
    <path d="M1840 0 L1920 0 L1920 180 C1880 120, 1840 60, 1840 0 Z" fill="url(#primaryGradient)" fill-opacity="0.1"/>
    <path d="M1700 0 L1760 0 L1760 120 C1730 80, 1700 40, 1700 0 Z" fill="url(#accentGradient)" fill-opacity="0.08"/>

    <!-- 左下角抽象图案 -->
    <path d="M0 1000 L0 1080 L80 1080 C40 1040, 0 1000, 0 1000 Z" fill="url(#primaryGradient)" fill-opacity="0.1"/>
    <path d="M0 920 L0 980 L60 980 C30 950, 0 920, 0 920 Z" fill="url(#accentGradient)" fill-opacity="0.08"/>

    <!-- 标题下的分隔线 -->
    <line x1="80" y1="140" x2="1840" y2="140" class="divider-line"/>
  </g>

  <!-- 页脚 -->
  <g id="footer">
    <text x="960" y="1030" text-anchor="middle" class="caption text-light font-primary">
      {date} | {author}
    </text>
  </g>
</svg>