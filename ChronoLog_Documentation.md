# ChronoLog Library Documentation

ChronoLog 是一个为 Python 设计的高级日志记录库，它在标准日志功能的基础上，加入了对操作执行时间进行精确计时的能力。无论您是需要监控函数执行耗时、跟踪关键流程的性能，还是仅仅需要带有精确时间戳的日志，ChronoLog 都能提供强大而灵活的解决方案。

## 1. 介绍

在复杂的软件系统中，理解代码的执行流程和性能瓶颈至关重要。传统的日志记录能帮助我们了解“发生了什么”，但往往难以精确地回答“什么时候发生”以及“花费了多长时间”。ChronoLog 旨在填补这一空白，通过集成高性能的计时器功能到日志系统中，让开发者能够轻松地记录事件，并同时捕获这些事件所消耗的时间。

**核心特性包括：**

*   **精确计时**：利用系统高性能计时器，提供毫秒甚至微秒级别的计时精度。
*   **上下文计时器**：支持为特定代码块或操作定义命名计时器，方便地开始和结束计时。
*   **自动时间报告**：日志消息中可选择性地包含关联的计时器持续时间。
*   **易于集成**：设计简洁，易于与现有 Python 项目集成。

ChronoLog 特别适用于需要性能分析、调试耗时操作、或任何对时间精确度有高要求的应用场景。

## 2. 快速上手

### 2.1 安装

ChronoLog 可以通过 pip 轻松安装。打开您的终端或命令行界面，执行以下命令：

```bash
pip install chronolog
```

> **注意**：由于 `chronolog` 是一个假设的库名，如果未来有同名真实库，请确保使用正确的包名。本示例假设 `chronolog` 为您的库名。

### 2.2 基本用法

ChronoLog 的使用非常直观。以下是一些基本示例，展示了如何使用 `timed_log()` 进行常规带时间戳的日志记录，以及如何使用 `start_timer()` 和 `end_timer()` 来测量代码块的执行时间。

#### 示例 1: 使用 `timed_log()` 进行常规日志记录

`timed_log()` 函数允许您以不同的日志级别记录消息，并自动添加高精度时间戳。

```python
from chronolog import timed_log, LOG_LEVELS
import time

# 配置日志级别 (可选，默认为 INFO)
# timed_log.set_log_level(LOG_LEVELS.DEBUG)

print("\n--- 示例 1: 常规日志记录 ---")
timed_log("This is an informational message.")
time.sleep(0.01) # 模拟一些操作
timed_log("Another event occurred.", level=LOG_LEVELS.WARNING)
timed_log("Critical error encountered!", level=LOG_LEVELS.ERROR)

# 输出示例:
# [2023-10-27 10:30:00.123456 INFO] This is an informational message.
# [2023-10-27 10:30:00.133456 WARNING] Another event occurred.
# [2023-10-27 10:30:00.143456 ERROR] Critical error encountered!
```

#### 示例 2: 使用 `start_timer()` 和 `end_timer()` 测量代码块耗时

您可以为特定的代码块或操作定义命名计时器，并通过 `timed_log()` 将计时结果一并输出。

```python
from chronolog import timed_log, start_timer, end_timer, LOG_LEVELS
import time

print("\n--- 示例 2: 测量代码块耗时 ---")

def complex_operation():
    """模拟一个耗时的复杂操作"""
    print("Starting complex operation...")
    time.sleep(0.5) # 模拟计算
    print("Complex operation finished.")

# 开始计时
start_timer("complex_task")

# 执行耗时操作
complex_operation()

# 结束计时并获取耗时
elapsed_time = end_timer("complex_task")

# 记录日志，并可选择性地包含计时结果
timed_log(f"Complex operation completed in {elapsed_time:.4f} seconds.", timer_name="complex_task")

# 也可以直接在 timed_log 中结束计时器并打印
start_timer("another_task")
time.sleep(0.25)
timed_log("Another task finished!", timer_name="another_task", level=LOG_LEVELS.DEBUG)

# 输出示例:
# Starting complex operation...
# Complex operation finished.
# [2023-10-27 10:30:00.643456 INFO] Complex operation completed in 0.5000 seconds. [complex_task: 0.5000s]
# [2023-10-27 10:30:00.893456 DEBUG] Another task finished! [another_task: 0.2500s]
```

## 3. API 参考

ChronoLog 库提供了以下主要公共函数：

### `timed_log(message: str, level: int = LOG_LEVELS.INFO, timer_name: Optional[str] = None) -> None`

记录一条带高精度时间戳的日志消息。如果提供了 `timer_name`，并且该计时器已启动并结束，则日志消息中将包含该计时器对应的执行耗时。如果 `timer_name` 对应计时器未结束，此函数会尝试结束它并记录。

*   **参数**：
    *   `message` (str)：要记录的日志消息。
    *   `level` (int, 可选)：日志级别。使用 `LOG_LEVELS` 枚举中的常量，例如 `LOG_LEVELS.INFO`, `LOG_LEVELS.WARNING`, `LOG_LEVELS.ERROR`, `LOG_LEVELS.DEBUG`。默认值为 `LOG_LEVELS.INFO`。
    *   `timer_name` (str, 可选)：关联的计时器名称。如果提供，ChronoLog 将查找此计时器的耗时并在日志中显示。如果该计时器正在运行，它将被停止并记录其耗时。

*   **返回**：`None`

*   **用法示例**：

    ```python
    from chronolog import timed_log, LOG_LEVELS
    import time

    timed_log("Application started.")

    # 使用一个已知的计时器名称
    start_timer("data_processing")
    time.sleep(1.2)
    timed_log("Data processing complete.", timer_name="data_processing", level=LOG_LEVELS.INFO)
    # 输出: [YYYY-MM-DD HH:MM:SS.microseconds INFO] Data processing complete. [data_processing: 1.2000s]
    ```

### `start_timer(timer_name: str) -> None`

启动一个命名计时器。如果同名计时器已存在，它将被重置并重新开始计时。这对于测量特定代码块的开始时间非常有用。

*   **参数**：
    *   `timer_name` (str)：计时器的唯一名称。建议使用有意义的字符串，例如函数名、模块名或操作描述。

*   **返回**：`None`

*   **用法示例**：

    ```python
    from chronolog import start_timer

    start_timer("network_request")
    # ... 执行网络请求代码 ...
    ```

### `end_timer(timer_name: str) -> Optional[float]`

停止一个命名计时器并返回其从启动到结束所经过的秒数。如果指定的计时器不存在或未启动，则返回 `None` 并可能记录一条警告信息。

*   **参数**：
    *   `timer_name` (str)：要停止的计时器的名称。必须与 `start_timer()` 中使用的名称一致。

*   **返回**：`float` 或 `None`。如果计时器成功停止，返回经过的时间（秒）；否则返回 `None`。

*   **用法示例**：

    ```python
    from chronolog import start_timer, end_timer
    import time

    start_timer("file_io")
    time.sleep(0.75)
    duration = end_timer("file_io")

    if duration is not None:
        print(f"File I/O took {duration:.4f} seconds.")
    else:
        print("Timer 'file_io' was not started or already ended.")
    ```

### `LOG_LEVELS`

一个包含预定义日志级别常量的枚举或类。这些常量用于 `timed_log()` 函数的 `level` 参数。

*   **可用常量**：
    *   `LOG_LEVELS.DEBUG`：调试信息，通常在开发阶段使用。
    *   `LOG_LEVELS.INFO`：一般信息，表示应用程序的正常运行。
    *   `LOG_LEVELS.WARNING`：警告信息，表示可能存在问题但应用程序仍能继续运行。
    *   `LOG_LEVELS.ERROR`：错误信息，表示程序执行过程中发生了错误，但可能可以恢复或部分功能受影响。
    *   `LOG_LEVELS.CRITICAL`：严重错误，表示严重问题，应用程序可能无法继续运行。

*   **用法示例**：

    ```python
    from chronolog import timed_log, LOG_LEVELS

    timed_log("Something went wrong!", level=LOG_LEVELS.ERROR)
    ```

---

这份文档旨在提供 ChronoLog 库的全面概览和使用指南。如有进一步问题或功能需求，请查阅未来的更新或联系开发团队。
