<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Styles: Colors, Fonts, and General Element Styling -->
    <style type="text/css">
      /* Main Color Palette */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .container-bg { fill: #E0F2FE; } /* Used for a subtle background element */
      .card-border-color { stroke: #BAE6FD; } /* For borders of structured elements */

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* tight line height */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; }

      /* General Styles for elements */
      .subtle-shadow { filter: url(#subtle-drop-shadow); }
      .outline-stroke { stroke-width: 2; }
    </style>

    <!-- Gradients -->
    <!-- Primary Gradient: linear-gradient(135deg, #1E40AF, #475569) -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <!-- Accent Gradient: linear-gradient(45deg, #3B82F6, #1E40AF) -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <!-- Background Gradient for subtle depth -->
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>
    <!-- Tech Accent Gradient for highlight with transparency -->
    <linearGradient id="techAccentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.1"/>
    </linearGradient>

    <!-- Filters for shadows -->
    <filter id="subtle-drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.05 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Main Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>
  <!-- Subtle background gradient overlay for depth and "Bento Grid" base -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" opacity="0.5"/>

  <!-- Decorative Elements: Geometric shapes and implied panels for "Bento Grid" style -->
  <!-- Main content panel background with rounded corners and border -->
  <rect x="80" y="60" width="1760" height="960" rx="20" ry="20" class="container-bg" opacity="0.4"/>
  <rect x="80" y="60" width="1760" height="960" rx="20" ry="20" class="card-border-color outline-stroke" fill="none"/>

  <!-- Abstract wave shape at the bottom using primary gradient -->
  <path d="M0 750 C 480 650, 960 850, 1440 750 C 1700 680, 1920 800, 1920 1080 L 0 1080 L 0 750 Z" fill="url(#primaryGradient)" opacity="0.15"/>
  <!-- Another wave shape with accent gradient for layered effect -->
  <path d="M0 700 C 450 600, 900 800, 1350 700 C 1650 630, 1920 750, 1920 1080 L 0 1080 L 0 700 Z" fill="url(#accentGradient)" opacity="0.1"/>

  <!-- Subtle large circles with tech accent gradient for visual depth -->
  <circle cx="1700" cy="200" r="150" fill="url(#techAccentGradient)" opacity="0.1"/>
  <circle cx="200" cy="900" r="100" fill="url(#techAccentGradient)" opacity="0.1"/>

  <!-- Main Content Area - Centered for Titles -->
  <g transform="translate(960, 500)" text-anchor="middle">
    <!-- Main Title (Chinese) - Large and bold -->
    <text y="-100" class="hero-title font-primary text-primary">
      <tspan x="0">商业计划书</tspan>
    </text>
    <!-- Subtitle (English) - Slightly smaller, professional font -->
    <text y="0" class="main-title font-accent text-primary">
      <tspan x="0">Business Proposal</tspan>
    </text>
    <!-- Supporting tagline (Chinese and English) -->
    <text y="80" class="body-text font-secondary text-secondary">
      <tspan x="0">市场洞察 和 战略展望</tspan>
    </text>
    <text y="120" class="small-text font-secondary text-light">
      <tspan x="0">Market Insights 和#38; Strategic Outlook</tspan>
    </text>
  </g>

  <!-- Logo Placeholder - Top Left Corner -->
  <g id="logo-area" transform="translate(120, 100)">
    <!-- Placeholder for Logo. Replace this rect/text with an <image> tag if {logo_url} is available. -->
    <!-- Example: <image href="{logo_url}" x="0" y="0" width="auto" height="80" class="subtle-shadow"/> -->
    <rect x="0" y="0" width="180" height="50" rx="8" ry="8" fill="#1E40AF" class="subtle-shadow"/>
    <text x="90" y="32" class="content-title font-primary" fill="#FFFFFF" text-anchor="middle" alignment-baseline="middle">
      <tspan>LOGO</tspan>
    </text>
  </g>

  <!-- Date and Author - Bottom Right Corner -->
  <g id="info-area" transform="translate(1800, 980)" text-anchor="end">
    <text class="small-text font-secondary text-secondary">
      <tspan x="0">{date}</tspan>
    </text>
    <text y="20" class="small-text font-secondary text-secondary">
      <tspan x="0">Presented by {author}</tspan>
    </text>
  </g>

  <!-- Page Number - Bottom Center -->
  <text x="960" y="980" class="caption-text font-secondary text-light" text-anchor="middle">
    <tspan>Page 1/10</tspan>
  </text>

  <!-- Example of simple outlined graphic elements (Icons hinting at data/progress) -->
  <!-- Growth icon (top right decorative) -->
  <g transform="translate(1500, 300)">
    <rect x="0" y="0" width="100" height="100" rx="15" ry="15" stroke="#3B82F6" class="outline-stroke" fill="none" opacity="0.6"/>
    <path d="M25 75 L50 50 L75 75 M50 50 L50 25" stroke="#3B82F6" stroke-width="3" fill="none" opacity="0.8" stroke-linecap="round"/>
  </g>
  <!-- Target/Focus icon (bottom left decorative) -->
  <g transform="translate(320, 680)">
    <rect x="0" y="0" width="100" height="100" rx="15" ry="15" stroke="#1E40AF" class="outline-stroke" fill="none" opacity="0.6"/>
    <circle cx="50" cy="50" r="30" stroke="#1E40AF" stroke-width="3" fill="none" opacity="0.8"/>
    <line x1="20" y1="50" x2="80" y2="50" stroke="#1E40AF" stroke-width="3" opacity="0.8" stroke-linecap="round"/>
    <line x1="50" y1="20" x2="50" y2="80" stroke="#1E40AF" stroke-width="3" opacity="0.8" stroke-linecap="round"/>
  </g>

</svg>