# -*- coding: utf-8 -*-
"""
🎯 相关性评估器 - 替代AI评估的确定性算法

核心功能：
- 分层模式匹配算法
- 加权关键词重叠度计算
- 请求类型自动识别
- 毫秒级评估响应
"""

import re
import logging
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """评估结果数据类"""
    result: str  # "quality_approved" 或 "quality_failed"
    score: float  # 相关性得分 0-1
    request_type: str  # 请求类型
    details: Dict  # 详细评分信息

class RelevanceEvaluator:
    """
    🎯 相关性评估器
    
    使用确定性算法评估用户请求与智能体回答的相关性
    """
    
    def __init__(self):
        """初始化评估器"""
        # 关键词字典
        self.greeting_patterns = [
            "你好", "hello", "hi", "早上好", "下午好", "晚上好", 
            "怎么样", "好吗", "在吗", "在不在"
        ]
        
        self.task_patterns = [
            "分析", "生成", "创建", "计算", "规划", "设计", "研究", 
            "写一个", "帮我", "制作", "开发", "实现", "搜索", "查找"
        ]
        
        self.simple_query_patterns = [
            "是什么", "怎么", "为什么", "在哪里", "什么时候", "如何",
            "什么意思", "怎么办", "可以吗", "能够"
        ]
        
        # 停用词
        self.stop_words = {
            "的", "了", "在", "是", "有", "和", "就", "都", "而", "及", 
            "与", "或", "但", "如果", "因为", "所以", "然后", "这", "那",
            "a", "an", "the", "is", "are", "was", "were", "be", "been",
            "have", "has", "had", "do", "does", "did", "will", "would",
            "could", "should", "may", "might", "can", "to", "of", "in",
            "on", "at", "by", "for", "with", "from"
        }
        
        # 评估阈值配置
        self.thresholds = {
            "GREETING": 0.1,
            "SIMPLE_QUERY": 0.3,
            "TASK": 0.6,
            "GENERAL": 0.4
        }
        
        logger.info("🎯 相关性评估器初始化完成")
    
    def evaluate(self, user_request: str, agent_response: str) -> EvaluationResult:
        """
        主评估方法
        
        Args:
            user_request: 用户请求
            agent_response: 智能体回答
            
        Returns:
            EvaluationResult: 评估结果
        """
        try:
            # 第一步：处理边界情况
            edge_case_result = self._handle_edge_cases(user_request, agent_response)
            if edge_case_result:
                return edge_case_result
            
            # 第二步：识别请求类型
            request_type = self._classify_request(user_request)
            
            # 第三步：根据类型进行评估
            if request_type == "GREETING":
                return self._evaluate_greeting(user_request, agent_response)
            elif request_type == "SIMPLE_QUERY":
                return self._evaluate_simple_query(user_request, agent_response)
            elif request_type == "TASK":
                return self._evaluate_task(user_request, agent_response)
            else:  # GENERAL
                return self._evaluate_general(user_request, agent_response)
                
        except Exception as e:
            logger.error(f"❌ 评估过程异常: {e}")
            return EvaluationResult(
                result="quality_failed",
                score=0.0,
                request_type="ERROR",
                details={"error": str(e)}
            )
    
    def _handle_edge_cases(self, user_request: str, agent_response: str) -> EvaluationResult:
        """处理边界情况"""
        # 用户请求过短
        if len(user_request.strip()) < 2:
            if len(agent_response.strip()) > 0:
                return EvaluationResult("quality_approved", 1.0, "EMPTY_REQUEST", {"reason": "空请求有回应"})
            else:
                return EvaluationResult("quality_failed", 0.0, "EMPTY_REQUEST", {"reason": "空请求无回应"})
        
        # 智能体无回应
        if len(agent_response.strip()) < 5:
            return EvaluationResult("quality_failed", 0.0, "NO_RESPONSE", {"reason": "无有效回应"})
        
        # 明显的错误回应
        error_indicators = ["抱歉", "无法", "不知道", "错误", "失败", "sorry", "error", "failed"]
        if any(indicator in agent_response.lower() for indicator in error_indicators):
            return EvaluationResult("quality_failed", 0.0, "ERROR_RESPONSE", {"reason": "包含错误指示词"})
        
        return None
    
    def _classify_request(self, user_request: str) -> str:
        """
        分类用户请求类型
        
        Returns:
            str: GREETING/SIMPLE_QUERY/TASK/GENERAL
        """
        request_lower = user_request.lower().strip()
        request_length = len(user_request.strip())
        
        # 1. 问候类检查
        if any(pattern in request_lower for pattern in self.greeting_patterns):
            return "GREETING"
        
        # 2. 简单询问类检查
        if (request_length < 30 and 
            any(pattern in request_lower for pattern in self.simple_query_patterns)):
            return "SIMPLE_QUERY"
        
        # 3. 任务类检查
        if any(pattern in request_lower for pattern in self.task_patterns):
            return "TASK"
        
        # 4. 默认为一般类型
        return "GENERAL"
    
    def _evaluate_greeting(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估问候类请求"""
        response_lower = agent_response.lower()
        
        # 检查礼貌回应
        polite_patterns = [
            "你好", "hello", "hi", "很高兴", "为您服务", "帮助", 
            "有什么", "需要", "可以", "欢迎"
        ]
        
        has_polite_response = any(pattern in response_lower for pattern in polite_patterns)
        
        # 检查是否过度回应
        is_over_response = len(agent_response) > 300
        
        if has_polite_response and not is_over_response:
            score = 0.9
            result = "quality_approved"
        elif has_polite_response:
            score = 0.6  # 有礼貌但过长
            result = "quality_approved"
        else:
            score = 0.3
            result = "quality_approved"  # 问候类比较宽松
        
        return EvaluationResult(
            result=result,
            score=score,
            request_type="GREETING",
            details={
                "has_polite_response": has_polite_response,
                "is_over_response": is_over_response,
                "response_length": len(agent_response)
            }
        )
    
    def _evaluate_simple_query(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估简单询问类请求"""
        # 提取关键词
        user_keywords = self._extract_keywords(user_request)
        response_keywords = self._extract_keywords(agent_response)
        
        # 计算关键词重叠度
        overlap_score = self._calculate_keyword_overlap(user_keywords, response_keywords)
        
        # 检查回答长度适中性
        length_score = self._evaluate_response_length(user_request, agent_response, "SIMPLE_QUERY")
        
        # 综合评分
        final_score = overlap_score * 0.7 + length_score * 0.3
        
        result = "quality_approved" if final_score >= self.thresholds["SIMPLE_QUERY"] else "quality_failed"
        
        return EvaluationResult(
            result=result,
            score=final_score,
            request_type="SIMPLE_QUERY",
            details={
                "keyword_overlap": overlap_score,
                "length_score": length_score,
                "user_keywords": list(user_keywords),
                "common_keywords": list(user_keywords & response_keywords)
            }
        )
    
    def _evaluate_task(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估任务类请求"""
        # 使用完整的加权关键词重叠度算法
        return self._calculate_comprehensive_relevance(user_request, agent_response, "TASK")
    
    def _evaluate_general(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估一般类型请求"""
        return self._calculate_comprehensive_relevance(user_request, agent_response, "GENERAL")
    
    def _calculate_comprehensive_relevance(self, user_request: str, agent_response: str, request_type: str) -> EvaluationResult:
        """
        计算综合相关性（加权关键词重叠度算法）
        """
        # 1. 实体匹配度 (权重: 0.3)
        user_entities = self._extract_entities(user_request)
        response_entities = self._extract_entities(agent_response)
        entity_score = self._calculate_set_overlap(user_entities, response_entities)
        
        # 2. 关键动词匹配度 (权重: 0.2)
        user_verbs = self._extract_action_verbs(user_request)
        response_verbs = self._extract_action_verbs(agent_response)
        verb_score = self._calculate_set_overlap(user_verbs, response_verbs)
        
        # 3. 主题词匹配度 (权重: 0.4) - 提高权重，这是最重要的
        user_topics = self._extract_topic_words(user_request)
        response_topics = self._extract_topic_words(agent_response)
        topic_score = self._calculate_set_overlap(user_topics, response_topics)
        
        # 4. 数字匹配度 (权重: 0.1)
        user_numbers = self._extract_numbers(user_request)
        response_numbers = self._extract_numbers(agent_response)
        number_score = 1.0 if any(num in response_numbers for num in user_numbers) else 0.0
        
        # 计算加权总分
        final_score = (
            entity_score * 0.3 + 
            verb_score * 0.2 + 
            topic_score * 0.4 + 
            number_score * 0.1
        )
        
        # 如果主题词匹配度很高，可以适当降低阈值
        threshold = self.thresholds[request_type]
        if topic_score >= 0.5:
            threshold = max(0.2, threshold - 0.1)
        
        result = "quality_approved" if final_score >= threshold else "quality_failed"
        
        return EvaluationResult(
            result=result,
            score=final_score,
            request_type=request_type,
            details={
                "entity_score": entity_score,
                "verb_score": verb_score,
                "topic_score": topic_score,
                "number_score": number_score,
                "threshold": threshold,
                "user_entities": list(user_entities),
                "user_verbs": list(user_verbs),
                "user_topics": list(user_topics),
                "common_entities": list(user_entities & response_entities),
                "common_verbs": list(user_verbs & response_verbs),
                "common_topics": list(user_topics & response_topics)
            }
        )
    
    def _extract_keywords(self, text: str) -> Set[str]:
        """提取关键词（去除停用词）"""
        # 中英文分词
        # 中文：提取连续的中文字符
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,}', text)
        # 英文：提取单词
        english_words = re.findall(r'\b[a-zA-Z]{2,}\b', text.lower())
        # 数字
        numbers = re.findall(r'\d+', text)
        
        all_words = chinese_words + english_words + numbers
        
        # 过滤停用词和短词
        keywords = {
            word for word in all_words 
            if len(word) > 1 and word not in self.stop_words
        }
        
        return keywords
    
    def _extract_entities(self, text: str) -> Set[str]:
        """提取实体（人名、地名、机构名等）"""
        # 简化版实体识别：查找大写开头的词汇和特定模式
        entities = set()
        
        # 大写开头的词（可能是人名、地名）
        capitalized_words = re.findall(r'\b[A-Z][a-z]+\b', text)
        entities.update(capitalized_words)
        
        # 年份（4位数字）
        years = re.findall(r'\b(19|20)\d{2}\b', text)
        entities.update(years)
        
        # 百分比
        percentages = re.findall(r'\d+%', text)
        entities.update(percentages)
        
        # 中文实体识别：专有名词、地名等
        chinese_entities = re.findall(r'[\u4e00-\u9fff]{2,}', text)
        # 过滤掉常见的非实体词
        non_entities = {"什么", "怎么", "为什么", "在哪里", "什么时候", "如何", "可以", "需要", "帮助", "服务"}
        chinese_entities = [e for e in chinese_entities if e not in non_entities and len(e) >= 2]
        entities.update(chinese_entities)
        
        return entities
    
    def _extract_action_verbs(self, text: str) -> Set[str]:
        """提取动作动词"""
        # 常见动作动词列表
        action_verbs = {
            "分析", "研究", "计算", "生成", "创建", "设计", "规划", "制作",
            "开发", "实现", "搜索", "查找", "比较", "评估", "预测", "建议",
            "写", "做", "帮", "处理", "解决", "完成", "执行", "运行", "构建",
            "analyze", "research", "calculate", "generate", "create", "design",
            "develop", "implement", "search", "find", "compare", "evaluate",
            "write", "make", "help", "process", "solve", "complete", "execute"
        }
        
        # 提取所有词汇
        words = re.findall(r'[\u4e00-\u9fff]+|\b\w+\b', text.lower())
        text_words = set(words)
        
        # 匹配动词
        matched_verbs = text_words & action_verbs
        
        # 额外检查：寻找包含动作词的短语
        for verb in action_verbs:
            if verb in text.lower():
                matched_verbs.add(verb)
        
        return matched_verbs
    
    def _extract_topic_words(self, text: str) -> Set[str]:
        """提取主题词汇"""
        # 去除停用词后的所有词汇都可能是主题词
        return self._extract_keywords(text)
    
    def _extract_numbers(self, text: str) -> Set[str]:
        """提取数字信息"""
        numbers = set()
        
        # 整数
        integers = re.findall(r'\b\d+\b', text)
        numbers.update(integers)
        
        # 小数
        decimals = re.findall(r'\b\d+\.\d+\b', text)
        numbers.update(decimals)
        
        # 百分比
        percentages = re.findall(r'\d+(?:\.\d+)?%', text)
        numbers.update(percentages)
        
        return numbers
    
    def _calculate_keyword_overlap(self, set1: Set[str], set2: Set[str]) -> float:
        """计算两个关键词集合的重叠度"""
        if not set1:
            return 1.0  # 如果没有关键词要求，认为匹配
        
        if not set2:
            return 0.0
        
        overlap = len(set1 & set2)
        return overlap / len(set1)
    
    def _calculate_set_overlap(self, set1: Set[str], set2: Set[str]) -> float:
        """计算集合重叠度（通用方法）"""
        return self._calculate_keyword_overlap(set1, set2)
    
    def _evaluate_response_length(self, user_request: str, agent_response: str, request_type: str) -> float:
        """评估回答长度的适中性"""
        request_length = len(user_request)
        response_length = len(agent_response)
        
        # 根据请求类型设定期望长度
        if request_type == "SIMPLE_QUERY":
            min_length = 20
            optimal_length = request_length * 2
            max_length = request_length * 5
        else:
            min_length = 50
            optimal_length = max(100, request_length * 3)
            max_length = request_length * 10
        
        if response_length < min_length:
            return 0.3  # 太短
        elif response_length <= optimal_length:
            return 1.0  # 合适
        elif response_length <= max_length:
            return 0.8  # 稍长但可接受
        else:
            return 0.5  # 太长

# 全局实例
_evaluator_instance = None

def get_relevance_evaluator() -> RelevanceEvaluator:
    """获取全局相关性评估器实例"""
    global _evaluator_instance
    if _evaluator_instance is None:
        _evaluator_instance = RelevanceEvaluator()
    return _evaluator_instance 