# LangGraph 实战教程 - 第二部分：智能问答机器人项目

## 项目概述

在这个项目中，我们将构建一个智能问答机器人，具备以下功能：
- 理解用户问题
- 搜索相关信息  
- 生成高质量答案
- 支持多轮对话
- 质量评估和改进

## 1. 项目架构设计

### 1.1 状态定义

```python
from typing import TypedDict, List, Dict, Optional
from datetime import datetime

class ChatBotState(TypedDict):
    """
    聊天机器人状态定义
    """
    # 用户输入
    user_question: str
    conversation_history: List[Dict[str, str]]
    
    # 处理过程
    processed_question: str
    search_results: List[Dict]
    context: str
    
    # 答案生成
    answer: str
    confidence_score: float
    
    # 控制流程
    iteration: int
    max_iterations: int
    error_message: Optional[str]
    
    # 元数据
    timestamp: str
    session_id: str
```

## 2. 核心节点实现

### 2.1 问题理解节点

```python
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, SystemMessage

class QuestionProcessor:
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0)
        
    def process_question(self, state: ChatBotState) -> ChatBotState:
        """
        理解和优化用户问题
        """
        question = state["user_question"]
        history = state.get("conversation_history", [])
        
        # 构建上下文提示
        system_prompt = """
        你是一个问题理解专家。你的任务是：
        1. 分析用户问题的意图
        2. 结合对话历史优化问题表述
        3. 提取关键信息和搜索词
        4. 确保问题清晰具体
        
        对话历史：{history}
        当前问题：{question}
        
        请输出优化后的问题，使其更适合信息检索。
        """
        
        # 格式化历史对话
        history_text = "\n".join([
            f"用户: {turn.get('user', '')}\n助手: {turn.get('assistant', '')}"
            for turn in history[-3:]  # 只保留最近3轮对话
        ])
        
        messages = [
            SystemMessage(content=system_prompt.format(
                history=history_text,
                question=question
            ))
        ]
        
        response = self.llm.invoke(messages)
        processed_question = response.content.strip()
        
        return {
            **state,
            "processed_question": processed_question,
            "iteration": state.get("iteration", 0) + 1,
            "timestamp": datetime.now().isoformat()
        }
```

### 2.2 信息检索节点

```python
import requests
from typing import List, Dict

class InformationRetriever:
    def __init__(self, tavily_api_key: Optional[str] = None):
        self.tavily_api_key = tavily_api_key or os.getenv("TAVILY_API_KEY")
        
    def search_information(self, state: ChatBotState) -> ChatBotState:
        """
        搜索相关信息
        """
        question = state["processed_question"]
        
        search_results = []
        
        # 使用Tavily搜索
        if self.tavily_api_key:
            search_results.extend(self._tavily_search(question))
        
        # 可以添加其他搜索源
        # search_results.extend(self._wikipedia_search(question))
        # search_results.extend(self._custom_kb_search(question))
        
        # 整理搜索结果
        context = self._format_search_results(search_results)
        
        return {
            **state,
            "search_results": search_results,
            "context": context
        }
    
    def _tavily_search(self, query: str, max_results: int = 5) -> List[Dict]:
        """
        使用Tavily API搜索
        """
        if not self.tavily_api_key:
            return []
            
        url = "https://api.tavily.com/search"
        headers = {"Content-Type": "application/json"}
        data = {
            "api_key": self.tavily_api_key,
            "query": query,
            "max_results": max_results,
            "search_depth": "advanced"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            
            results = response.json().get("results", [])
            return [
                {
                    "title": result.get("title", ""),
                    "content": result.get("content", ""),
                    "url": result.get("url", ""),
                    "score": result.get("score", 0.0)
                }
                for result in results
            ]
        except Exception as e:
            print(f"Tavily搜索错误: {e}")
            return []
    
    def _format_search_results(self, results: List[Dict]) -> str:
        """
        格式化搜索结果为上下文
        """
        if not results:
            return "未找到相关信息。"
        
        context_parts = []
        for i, result in enumerate(results[:3], 1):  # 只使用前3个结果
            context_parts.append(
                f"信息源 {i}:\n"
                f"标题: {result['title']}\n"
                f"内容: {result['content'][:500]}...\n"
                f"来源: {result['url']}\n"
            )
        
        return "\n".join(context_parts)
```

### 2.3 答案生成节点

```python
class AnswerGenerator:
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-4", temperature=0.1)
        
    def generate_answer(self, state: ChatBotState) -> ChatBotState:
        """
        基于上下文生成答案
        """
        question = state["user_question"]
        context = state["context"]
        history = state.get("conversation_history", [])
        
        system_prompt = """
        你是一个知识渊博的AI助手。请基于提供的上下文信息回答用户问题。

        要求：
        1. 答案要准确、全面、有条理
        2. 基于提供的上下文信息作答
        3. 如果信息不足，明确说明
        4. 保持客观中立的态度
        5. 适当引用信息源
        
        上下文信息：
        {context}
        
        对话历史：
        {history}
        
        用户问题：{question}
        
        请提供详细的回答：
        """
        
        # 格式化历史对话
        history_text = "\n".join([
            f"用户: {turn.get('user', '')}\n助手: {turn.get('assistant', '')}"
            for turn in history[-2:]  # 保留最近2轮对话
        ])
        
        messages = [
            SystemMessage(content=system_prompt.format(
                context=context,
                history=history_text,
                question=question
            ))
        ]
        
        response = self.llm.invoke(messages)
        answer = response.content.strip()
        
        return {
            **state,
            "answer": answer
        }
```

### 2.4 质量评估节点

```python
class QualityEvaluator:
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0)
        
    def evaluate_answer(self, state: ChatBotState) -> ChatBotState:
        """
        评估答案质量
        """
        question = state["user_question"]
        answer = state["answer"]
        context = state["context"]
        
        evaluation_prompt = """
        请评估这个问答的质量。评分标准：
        
        1. 相关性 (0-25分): 答案是否直接回答了问题
        2. 准确性 (0-25分): 答案是否基于提供的上下文且事实正确
        3. 完整性 (0-25分): 答案是否全面覆盖了问题的各个方面
        4. 清晰度 (0-25分): 答案是否表述清晰、逻辑清楚
        
        问题: {question}
        
        上下文: {context}
        
        答案: {answer}
        
        请给出总分(0-100)和简要评价。
        格式: 
        总分: [分数]
        评价: [简要说明]
        """
        
        messages = [
            HumanMessage(content=evaluation_prompt.format(
                question=question,
                answer=answer,
                context=context
            ))
        ]
        
        response = self.llm.invoke(messages)
        evaluation = response.content.strip()
        
        # 提取分数
        import re
        score_match = re.search(r'总分[：:]\s*(\d+)', evaluation)
        confidence_score = float(score_match.group(1)) / 100 if score_match else 0.5
        
        return {
            **state,
            "confidence_score": confidence_score
        }
```

## 3. 工作流构建

### 3.1 创建完整的聊天机器人图

```python
from langgraph.graph import StateGraph, END

class ChatBot:
    def __init__(self, tavily_api_key: Optional[str] = None):
        self.processor = QuestionProcessor()
        self.retriever = InformationRetriever(tavily_api_key)
        self.generator = AnswerGenerator()
        self.evaluator = QualityEvaluator()
        
        # 构建工作流
        self.workflow = self._build_workflow()
        self.app = self.workflow.compile()
    
    def _build_workflow(self) -> StateGraph:
        """
        构建聊天机器人工作流
        """
        workflow = StateGraph(ChatBotState)
        
        # 添加节点
        workflow.add_node("process_question", self.processor.process_question)
        workflow.add_node("search_info", self.retriever.search_information)
        workflow.add_node("generate_answer", self.generator.generate_answer)
        workflow.add_node("evaluate_quality", self.evaluator.evaluate_answer)
        workflow.add_node("improve_answer", self._improve_answer)
        
        # 设置入口点
        workflow.set_entry_point("process_question")
        
        # 添加边
        workflow.add_edge("process_question", "search_info")
        workflow.add_edge("search_info", "generate_answer")
        workflow.add_edge("generate_answer", "evaluate_quality")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "evaluate_quality",
            self._should_improve,
            {
                "improve": "improve_answer",
                "accept": END,
                "fail": END
            }
        )
        
        workflow.add_edge("improve_answer", "generate_answer")
        
        return workflow
    
    def _should_improve(self, state: ChatBotState) -> str:
        """
        决定是否需要改进答案
        """
        confidence = state["confidence_score"]
        iteration = state.get("iteration", 0)
        max_iterations = state.get("max_iterations", 3)
        
        if iteration >= max_iterations:
            return "fail"
        elif confidence >= 0.7:
            return "accept"
        else:
            return "improve"
    
    def _improve_answer(self, state: ChatBotState) -> ChatBotState:
        """
        改进答案的策略
        """
        # 这里可以实现不同的改进策略
        # 比如：重新搜索、调整提示词、使用不同模型等
        
        return {
            **state,
            "processed_question": f"更详细地解释：{state['processed_question']}",
            "iteration": state.get("iteration", 0) + 1
        }
```

### 3.2 自定义LLM配置

```python
from langchain.llms.base import LLM
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

class CustomLLMConfig:
    """
    自定义LLM配置管理
    """
    
    @staticmethod
    def get_llm(provider: str = "openai", model: str = None, **kwargs):
        """
        获取配置好的LLM实例
        """
        if provider.lower() == "openai":
            model = model or "gpt-3.5-turbo"
            return ChatOpenAI(
                model=model,
                temperature=kwargs.get("temperature", 0.1),
                max_tokens=kwargs.get("max_tokens", 1000)
            )
        elif provider.lower() == "anthropic":
            model = model or "claude-3-sonnet-20240229"
            return ChatAnthropic(
                model=model,
                temperature=kwargs.get("temperature", 0.1),
                max_tokens=kwargs.get("max_tokens", 1000)
            )
        else:
            raise ValueError(f"不支持的LLM提供商: {provider}")

# 使用自定义配置
class EnhancedChatBot(ChatBot):
    def __init__(self, llm_config: Dict = None, **kwargs):
        self.llm_config = llm_config or {"provider": "openai", "model": "gpt-4"}
        
        # 使用自定义LLM配置
        custom_llm = CustomLLMConfig.get_llm(**self.llm_config)
        
        # 重新初始化组件
        self.processor = QuestionProcessor()
        self.processor.llm = custom_llm
        
        self.generator = AnswerGenerator()
        self.generator.llm = custom_llm
        
        super().__init__(**kwargs)
```

### 3.3 自定义系统提示词

```python
class PromptManager:
    """
    提示词管理器
    """
    
    def __init__(self):
        self.prompts = {
            "question_processing": """
            你是一个问题理解专家。分析用户问题并优化表述。
            
            任务：
            1. 理解问题意图
            2. 提取关键信息
            3. 优化搜索关键词
            4. 保持问题的核心含义
            
            用户问题：{question}
            对话历史：{history}
            
            输出优化后的问题：
            """,
            
            "answer_generation": """
            你是一个专业的AI助手。基于提供的信息回答用户问题。
            
            原则：
            - 准确性第一
            - 基于事实
            - 逻辑清晰
            - 承认不确定性
            
            上下文：{context}
            问题：{question}
            
            回答：
            """,
            
            "quality_evaluation": """
            评估问答质量，关注：
            1. 相关性 (25%)
            2. 准确性 (25%) 
            3. 完整性 (25%)
            4. 清晰度 (25%)
            
            问题：{question}
            答案：{answer}
            上下文：{context}
            
            总分(0-100)：
            评价：
            """
        }
    
    def get_prompt(self, prompt_type: str, **kwargs) -> str:
        """
        获取格式化的提示词
        """
        template = self.prompts.get(prompt_type)
        if not template:
            raise ValueError(f"未找到提示词类型: {prompt_type}")
        
        return template.format(**kwargs)
    
    def update_prompt(self, prompt_type: str, new_prompt: str):
        """
        更新提示词
        """
        self.prompts[prompt_type] = new_prompt

# 使用自定义提示词的聊天机器人
class PromptAwareChatBot(EnhancedChatBot):
    def __init__(self, custom_prompts: Dict = None, **kwargs):
        self.prompt_manager = PromptManager()
        
        # 更新自定义提示词
        if custom_prompts:
            for prompt_type, prompt_text in custom_prompts.items():
                self.prompt_manager.update_prompt(prompt_type, prompt_text)
        
        super().__init__(**kwargs)
```

## 4. 使用示例

### 4.1 基础使用

```python
# 创建聊天机器人
chatbot = ChatBot(tavily_api_key="your_tavily_key")

# 单次问答
initial_state = {
    "user_question": "什么是量子计算？",
    "conversation_history": [],
    "max_iterations": 3,
    "session_id": "session_001"
}

result = chatbot.app.invoke(initial_state)
print(f"答案：{result['answer']}")
print(f"置信度：{result['confidence_score']}")
```

### 4.2 多轮对话

```python
class ConversationManager:
    def __init__(self, chatbot: ChatBot):
        self.chatbot = chatbot
        self.history = []
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def chat(self, user_input: str) -> Dict:
        """
        进行一轮对话
        """
        state = {
            "user_question": user_input,
            "conversation_history": self.history.copy(),
            "max_iterations": 3,
            "session_id": self.session_id
        }
        
        result = self.chatbot.app.invoke(state)
        
        # 更新对话历史
        self.history.append({
            "user": user_input,
            "assistant": result["answer"],
            "confidence": result["confidence_score"]
        })
        
        return result

# 使用对话管理器
conversation = ConversationManager(chatbot)

# 多轮对话示例
questions = [
    "什么是机器学习？",
    "它和人工智能有什么区别？",
    "请举几个实际应用的例子"
]

for question in questions:
    result = conversation.chat(question)
    print(f"Q: {question}")
    print(f"A: {result['answer']}")
    print(f"置信度: {result['confidence_score']:.2f}")
    print("-" * 50)
```

## 5. 性能优化和监控

### 5.1 添加缓存机制

```python
import hashlib
import json
from functools import wraps

class ResponseCache:
    def __init__(self):
        self.cache = {}
    
    def get_cache_key(self, question: str, context: str = "") -> str:
        """
        生成缓存键
        """
        content = f"{question}:{context}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Dict]:
        """
        获取缓存
        """
        return self.cache.get(key)
    
    def set(self, key: str, value: Dict):
        """
        设置缓存
        """
        self.cache[key] = value

# 带缓存的答案生成器
class CachedAnswerGenerator(AnswerGenerator):
    def __init__(self):
        super().__init__()
        self.cache = ResponseCache()
    
    def generate_answer(self, state: ChatBotState) -> ChatBotState:
        # 检查缓存
        cache_key = self.cache.get_cache_key(
            state["processed_question"], 
            state["context"]
        )
        
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return {
                **state,
                **cached_result
            }
        
        # 生成新答案
        result = super().generate_answer(state)
        
        # 缓存结果
        self.cache.set(cache_key, {
            "answer": result["answer"]
        })
        
        return result
```

### 5.2 性能监控

```python
import time
import logging
from functools import wraps

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "avg_response_time": 0,
            "error_rate": 0,
            "node_performance": {}
        }
    
    def track_node_performance(self, node_name: str):
        """
        节点性能跟踪装饰器
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    self._record_success(node_name, time.time() - start_time)
                    return result
                except Exception as e:
                    self._record_error(node_name, time.time() - start_time)
                    raise
            return wrapper
        return decorator
    
    def _record_success(self, node_name: str, duration: float):
        if node_name not in self.metrics["node_performance"]:
            self.metrics["node_performance"][node_name] = {
                "total_calls": 0,
                "total_time": 0,
                "errors": 0
            }
        
        node_metrics = self.metrics["node_performance"][node_name]
        node_metrics["total_calls"] += 1
        node_metrics["total_time"] += duration
    
    def get_metrics(self) -> Dict:
        """
        获取性能指标
        """
        return self.metrics

# 带监控的聊天机器人
class MonitoredChatBot(ChatBot):
    def __init__(self, **kwargs):
        self.monitor = PerformanceMonitor()
        super().__init__(**kwargs)
    
    def _build_workflow(self) -> StateGraph:
        # 为所有节点添加性能监控
        original_processor = self.processor.process_question
        self.processor.process_question = self.monitor.track_node_performance("process_question")(original_processor)
        
        original_search = self.retriever.search_information
        self.retriever.search_information = self.monitor.track_node_performance("search_info")(original_search)
        
        return super()._build_workflow()
```

## 总结

在这一部分中，我们构建了一个完整的智能问答机器人，包括：

1. **模块化设计**：将功能分解为独立的处理节点
2. **质量控制**：实现答案质量评估和改进机制
3. **灵活配置**：支持自定义LLM和提示词
4. **性能优化**：添加缓存和监控机制
5. **多轮对话**：支持对话历史和上下文管理

在下一部分中，我们将学习如何构建多代理协作系统，让多个AI代理协同工作解决复杂问题。 