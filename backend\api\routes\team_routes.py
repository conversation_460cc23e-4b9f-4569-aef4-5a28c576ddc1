"""
团队管理相关路由处理模块
"""
import json
import logging
import time
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Query

# 从其他模块导入
from backend.utils.team_utils import TeamManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 初始化团队管理器
team_manager = TeamManager()

@router.get("/teams")
async def get_all_teams():
    """
    获取所有团队信息

    Returns:
        List[Dict]: 所有团队的信息列表，每个团队包含完整的智能体详细信息
    """
    try:
        # 获取所有团队信息
        teams_json = team_manager.get_all_teams_info()
        teams = json.loads(teams_json)
        
        return {
            "status": "success",
            "data": teams
        }
    except Exception as e:
        logger.error(f"获取所有团队信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取所有团队信息失败: {str(e)}")

@router.get("/teams/{team_id}")
async def get_team_info(team_id: str):
    """
    获取指定团队的信息

    Args:
        team_id (str): 团队ID

    Returns:
        Dict: 团队信息，包含所有智能体的详细信息
    """
    try:
        # 获取团队信息
        team_info_json = team_manager.get_team_info(team_id)
        
        if not team_info_json:
            raise HTTPException(status_code=404, detail=f"未找到团队: {team_id}")
        
        team_info = json.loads(team_info_json)
        
        return {
            "status": "success",
            "data": team_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取团队 {team_id} 信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取团队信息失败: {str(e)}")

@router.get("/teams-summary")
async def get_teams_summary():
    """
    获取团队概要统计信息

    Returns:
        Dict: 团队概要统计信息
    """
    try:
        summary = team_manager.get_teams_summary()
        
        return {
            "status": "success",
            "data": summary
        }
    except Exception as e:
        logger.error(f"获取团队概要信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取团队概要信息失败: {str(e)}")

@router.post("/teams/{team_id}/execute")
async def execute_team_workflow(team_id: str, input_data: Dict[str, Any]):
    """
    直接执行指定团队的工作流（用于测试和调试）

    Args:
        team_id (str): 团队ID
        input_data (Dict): 输入数据，包含messages等

    Returns:
        Dict: 工作流执行结果
    """
    try:
        # 验证团队是否存在
        team_info_json = team_manager.get_team_info(team_id)
        if not team_info_json:
            raise HTTPException(status_code=404, detail=f"未找到团队: {team_id}")

        # 导入工作流引擎
        from backend.langgraph_enhancement.core.workflow_engine import DynamicWorkflowEngine
        workflow_engine = DynamicWorkflowEngine()

        # 执行工作流
        workflow_result = await workflow_engine.execute_workflow(
            team_name=team_id,
            input_data=input_data,
            config={
                "configurable": {
                    "thread_id": f"direct_execution_{team_id}_{int(time.time())}"
                }
            }
        )

        return {
            "status": "success",
            "data": workflow_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行团队 {team_id} 工作流时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"执行团队工作流失败: {str(e)}")

@router.get("/teams/{team_id}/workflow-info")  
async def get_team_workflow_info(team_id: str):
    """
    获取团队的工作流配置信息

    Args:
        team_id (str): 团队ID

    Returns:
        Dict: 团队工作流配置信息
    """
    try:
        # 获取团队完整信息
        team_info_json = team_manager.get_team_info(team_id)
        if not team_info_json:
            raise HTTPException(status_code=404, detail=f"未找到团队: {team_id}")

        team_info = json.loads(team_info_json)

        # 提取工作流相关信息
        workflow_info = {
            "team_id": team_id,
            "name": team_info.get("name", ""),
            "description": team_info.get("description", ""),
            "workflow_type": team_info.get("workflow_type", "sequential"),
            "context_sharing_strategy": team_info.get("context_sharing_strategy", "selective"),
            "collaboration_rules": team_info.get("collaboration_rules", []),
            "routing_conditions": team_info.get("routing_conditions", {}),
            "agents_count": len(team_info.get("agents", [])),
            "agents_summary": [
                {
                    "agent_id": agent.get("agent_id", ""),
                    "role_name": agent.get("role_name", ""),
                    "model": agent.get("model", ""),
                    "associated_servers": agent.get("associated_servers", [])
                }
                for agent in team_info.get("agents", [])
            ]
        }

        return {
            "status": "success",
            "data": workflow_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取团队 {team_id} 工作流信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取团队工作流信息失败: {str(e)}") 