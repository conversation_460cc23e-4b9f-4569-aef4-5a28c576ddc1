# -*- coding: utf-8 -*-
"""
📚 词典配置管理器

推荐的外部词典库和配置管理
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Set

logger = logging.getLogger(__name__)

class DictionaryManager:
    """
    📚 词典管理器
    
    管理各种词典文件和外部库集成
    """
    
    def __init__(self, dict_base_path: str = None):
        """初始化词典管理器"""
        self.dict_base_path = dict_base_path or self._get_default_path()
        self.ensure_dict_directory()
    
    def _get_default_path(self) -> str:
        """获取默认词典路径"""
        current_dir = Path(__file__).parent
        return str(current_dir / "dictionaries")
    
    def ensure_dict_directory(self):
        """确保词典目录存在"""
        dict_path = Path(self.dict_base_path)
        dict_path.mkdir(exist_ok=True)
    
    def create_jieba_requirements(self) -> str:
        """创建jieba相关依赖说明"""
        requirements = """
# 推荐的NLP库安装命令

## 1. jieba分词库 (最推荐 - 中文分词)
pip install jieba

## 2. pkuseg (北大出品 - 更准确的中文分词)  
pip install pkuseg-python

## 3. NLTK (英文处理强大)
pip install nltk

## 4. spaCy (工业级NLP)
pip install spacy
python -m spacy download zh_core_web_sm

## 5. HanLP (专业中文NLP)
pip install hanlp

## 使用示例：
# import jieba
# import jieba.posseg as pseg
# words = list(jieba.cut("我要分析2024年市场趋势"))
# pos_words = list(pseg.cut("我要分析2024年市场趋势"))
"""
        return requirements
    
    def create_enhanced_dictionaries(self):
        """创建增强版词典文件"""
        dict_path = Path(self.dict_base_path)
        
        # 1. 问候词典
        greeting_dict = {
            "chinese": ["你好", "您好", "早上好", "下午好", "晚上好", "晚安", "再见", "怎么样", "好吗", "在吗"],
            "english": ["hello", "hi", "hey", "good morning", "good afternoon", "good evening", "goodbye", "bye"],
            "informal": ["哈喽", "嗨", "咋样", "干嘛呢", "忙啥呢"]
        }
        
        # 2. 任务动词词典
        task_verbs_dict = {
            "analysis": {
                "chinese": ["分析", "研究", "调研", "探索", "调查", "考察", "梳理", "总结"],
                "english": ["analyze", "research", "investigate", "explore", "study", "examine"]
            },
            "creation": {
                "chinese": ["生成", "创建", "制作", "编写", "撰写", "设计", "构建", "开发"],
                "english": ["generate", "create", "make", "write", "design", "build", "develop"]
            },
            "calculation": {
                "chinese": ["计算", "统计", "核算", "估算", "测算"],
                "english": ["calculate", "compute", "count", "estimate"]
            },
            "planning": {
                "chinese": ["规划", "计划", "安排", "设计", "策划", "布局"],
                "english": ["plan", "schedule", "arrange", "design", "organize"]
            }
        }
        
        # 3. 领域关键词词典
        domain_keywords_dict = {
            "technology": {
                "ai_ml": ["人工智能", "AI", "机器学习", "深度学习", "神经网络", "算法", "模型"],
                "programming": ["编程", "代码", "程序", "开发", "软件", "系统", "框架"],
                "data": ["数据", "数据库", "大数据", "数据分析", "可视化", "统计"]
            },
            "business": {
                "marketing": ["市场", "营销", "推广", "品牌", "客户", "用户", "竞争"],
                "finance": ["财务", "投资", "收益", "利润", "成本", "预算", "资金"],
                "management": ["管理", "运营", "策略", "决策", "流程", "效率"]
            },
            "travel": {
                "planning": ["旅游", "行程", "路线", "攻略", "目的地", "景点"],
                "booking": ["酒店", "机票", "车票", "预订", "住宿", "交通"],
                "experience": ["体验", "美食", "文化", "风景", "特色", "推荐"]
            }
        }
        
        # 4. 停用词词典
        stop_words_dict = {
            "chinese": [
                "的", "了", "在", "是", "有", "和", "就", "都", "而", "及", "与", "或", "但",
                "如果", "因为", "所以", "然后", "这", "那", "一个", "我", "你", "他", "她", "它",
                "我们", "你们", "他们", "什么", "怎么", "为什么", "哪里", "什么时候", "如何"
            ],
            "english": [
                "a", "an", "the", "is", "are", "was", "were", "be", "been", "have", "has", "had",
                "do", "does", "did", "will", "would", "could", "should", "may", "might", "can",
                "to", "of", "in", "on", "at", "by", "for", "with", "from", "up", "about", "into",
                "through", "during", "before", "after", "above", "below", "between", "among"
            ]
        }
        
        # 5. 简单询问模式词典
        simple_query_dict = {
            "what": ["是什么", "什么是", "what is", "what are"],
            "how": ["怎么", "如何", "怎样", "how to", "how can", "how do"],
            "why": ["为什么", "为啥", "why", "why do", "why is"],
            "where": ["在哪里", "哪里", "where", "where is", "where can"],
            "when": ["什么时候", "何时", "when", "when is", "when can"],
            "who": ["谁", "who", "who is", "who can"]
        }
        
        # 保存所有词典
        dictionaries = {
            "greeting_patterns.json": greeting_dict,
            "task_patterns.json": task_verbs_dict,
            "domain_keywords.json": domain_keywords_dict,
            "stop_words.json": stop_words_dict,
            "simple_query_patterns.json": simple_query_dict
        }
        
        for filename, content in dictionaries.items():
            file_path = dict_path / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(content, f, ensure_ascii=False, indent=2)
            logger.info(f"📝 创建词典文件: {filename}")
    
    def create_requirements_file(self):
        """创建requirements.txt"""
        requirements_content = """# NLP和分词相关库
jieba>=0.42.1
pkuseg-python>=0.0.25
nltk>=3.8.1
spacy>=3.7.0

# 可选的高级NLP库
# hanlp>=2.1.0b4
# transformers>=4.30.0
# sentence-transformers>=2.2.0
"""
        
        req_path = Path(self.dict_base_path) / "requirements_nlp.txt"
        with open(req_path, 'w', encoding='utf-8') as f:
            f.write(requirements_content)
        
        logger.info(f"📝 创建NLP依赖文件: {req_path}")
    
    def load_dictionary(self, dict_name: str) -> Dict:
        """加载指定词典"""
        dict_path = Path(self.dict_base_path) / f"{dict_name}.json"
        
        if dict_path.exists():
            with open(dict_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"⚠️ 词典文件不存在: {dict_path}")
            return {}
    
    def update_dictionary(self, dict_name: str, new_content: Dict):
        """更新词典内容"""
        dict_path = Path(self.dict_base_path) / f"{dict_name}.json"
        
        with open(dict_path, 'w', encoding='utf-8') as f:
            json.dump(new_content, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📝 更新词典: {dict_name}")
    
    def get_installation_guide(self) -> str:
        """获取安装指南"""
        guide = """
🎯 外部词典库安装指南

## 推荐安装顺序：

### 1. 基础中文分词 (必装)
```bash
pip install jieba
```

### 2. 高级中文分词 (可选)
```bash
pip install pkuseg-python
```

### 3. 英文处理 (可选)
```bash
pip install nltk
# 安装后需要下载数据
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

### 4. 工业级NLP (高级用户)
```bash
pip install spacy
python -m spacy download zh_core_web_sm  # 中文模型
python -m spacy download en_core_web_sm  # 英文模型
```

## 使用示例：

### jieba分词
```python
import jieba
import jieba.posseg as pseg

# 基础分词
words = list(jieba.cut("我要分析2024年市场趋势"))
# ['我', '要', '分析', '2024年', '市场', '趋势']

# 词性标注
words_with_pos = list(pseg.cut("我要分析2024年市场趋势"))
# [('我', 'r'), ('要', 'v'), ('分析', 'v'), ('2024年', 'm'), ('市场', 'n'), ('趋势', 'n')]
```

### 自定义词典
```python
# 添加自定义词汇
jieba.add_word("机器学习")
jieba.add_word("人工智能")

# 从文件加载词典
jieba.load_userdict("custom_dict.txt")
```

## 词典文件结构：
```
dictionaries/
├── greeting_patterns.json      # 问候语词典
├── task_patterns.json         # 任务动词词典  
├── domain_keywords.json       # 领域关键词
├── stop_words.json           # 停用词词典
├── simple_query_patterns.json # 简单询问模式
└── requirements_nlp.txt      # NLP库依赖
```

## 性能对比：
- 基础分词: ~1ms
- jieba分词: ~3ms  
- pkuseg分词: ~5ms
- spaCy处理: ~10ms
"""
        return guide

# 全局实例
def get_dictionary_manager(dict_path: str = None) -> DictionaryManager:
    """获取词典管理器"""
    return DictionaryManager(dict_path)

if __name__ == "__main__":
    # 创建词典管理器并初始化所有词典
    manager = get_dictionary_manager()
    
    print("📚 创建增强版词典文件...")
    manager.create_enhanced_dictionaries()
    
    print("📝 创建NLP依赖文件...")
    manager.create_requirements_file()
    
    print("📖 安装指南:")
    print(manager.get_installation_guide())
    
    print("✅ 词典初始化完成！") 