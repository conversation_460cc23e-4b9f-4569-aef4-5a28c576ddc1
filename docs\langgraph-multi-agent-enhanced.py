#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangGraph多智能体协调工作流测试 - 基于JIMU MCP工具调用方式
实现信息收集、信息检索、文章编写、多媒体输出四智能体协作

运行要求：
1. 确保已安装LangGraph: pip install langgraph
2. 确保后端服务正在运行 (http://************:9999)
3. 确保OpenAI兼容接口可用 (http://jimu.ffa.chat/v1)
"""

import asyncio
import json
import logging
import os
import time
import re
from datetime import datetime
from typing import TypedDict, List, Dict, Optional, Any, Literal
import requests
import sys
from pathlib import Path

# 添加后端路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from langchain_openai import ChatOpenAI
    from langgraph.graph import StateGraph, END, START
    from langgraph.checkpoint.memory import MemorySaver
except ImportError as e:
    print(f"缺少依赖库: {e}")
    print("请安装: pip install langgraph langchain langchain-openai")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# JIMU项目配置
JIMU_SEARCH_BASE_URL = "http://************:9999/api/v1"
JIMU_LLM_BASE_URL = "http://jimu.ffa.chat/v1"
JIMU_API_KEY = "sk-Z0MdU0NAXCmiwYF_iz-gu4aqoEg8XSYGUL3IR32geJ7ZlaflLmzJVENtrEk"
SYSTEM_TOKEN = "jimu_system_2024"

class EnhancedMCPToolParser:
    """增强的MCP工具解析器，支持更多工具类型"""
    
    def __init__(self):
        self.supported_tool_types = [
            'use_mcp_tool',
            'access_mcp_resource',
            'write_file',
            'read_file',
            'generate_image',
            'process_text'
        ]
    
    def parse_tool_calls(self, text: str) -> List[Dict]:
        """解析文本中的工具调用"""
        tool_calls = []
        
        # 解析 use_mcp_tool
        tool_pattern = r'<use_mcp_tool>\s*<server_name>(.*?)</server_name>\s*<tool_name>(.*?)</tool_name>\s*<arguments>\s*(.*?)\s*</arguments>\s*</use_mcp_tool>'
        matches = re.findall(tool_pattern, text, re.DOTALL)
        
        for match in matches:
            try:
                tool_call = {
                    'type': 'use_mcp_tool',
                    'server_name': match[0].strip(),
                    'tool_name': match[1].strip(),
                    'arguments': json.loads(match[2].strip()) if match[2].strip() else {}
                }
                tool_calls.append(tool_call)
            except Exception as e:
                logger.error(f"解析工具调用失败: {e}")
        
        # 解析文件操作工具
        file_patterns = [
            (r'<write_file>\s*<path>(.*?)</path>\s*<content>(.*?)</content>\s*</write_file>', 'write_file'),
            (r'<read_file>\s*<path>(.*?)</path>\s*</read_file>', 'read_file')
        ]
        
        for pattern, tool_type in file_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                if tool_type == 'write_file':
                    tool_call = {
                        'type': tool_type,
                        'path': match[0].strip(),
                        'content': match[1].strip()
                    }
                else:  # read_file
                    tool_call = {
                        'type': tool_type,
                        'path': match[0].strip()
                    }
                tool_calls.append(tool_call)
        
        return tool_calls
    
    def has_tool_calls(self, text: str) -> bool:
        """检查文本是否包含工具调用"""
        return bool(self.parse_tool_calls(text))

class MultiAgentLLMManager:
    """多智能体LLM管理器 - 增强版"""
    
    def __init__(self):
        self.base_url = JIMU_LLM_BASE_URL
        self.api_key = JIMU_API_KEY
        self.models = [
            "gemini-2.5-flash-preview-05-20", "deepseek-chat", "claude-3-5-sonnet-20241022"
        ]
        self.current_model = "gemini-2.5-flash-preview-05-20"
        self.cache = {}
        self.tool_parser = EnhancedMCPToolParser()
    
    def call_llm(self, messages: List[Dict], model: Optional[str] = None, 
                 temperature: float = 0.7, agent_type: str = "general") -> str:
        """调用LLM接口，支持不同智能体类型"""
        model = model or self.current_model
        
        # 根据智能体类型添加特定的系统提示词
        enhanced_messages = messages.copy()
        system_prompt = self._get_agent_system_prompt(agent_type)
        
        if not any(msg.get("role") == "system" for msg in enhanced_messages):
            enhanced_messages.insert(0, {"role": "system", "content": system_prompt})
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model,
                "messages": enhanced_messages,
                "temperature": temperature,
                "max_tokens": 1500
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                return f"LLM调用失败: {response.status_code}"
                
        except Exception as e:
            return f"LLM调用异常: {str(e)}"
    
    def _get_agent_system_prompt(self, agent_type: str) -> str:
        """获取不同智能体类型的系统提示词"""
        prompts = {
            "info_collector": """
你是信息收集专家。当需要搜索信息时，使用以下格式：

<use_mcp_tool>
<server_name>search</server_name>
<tool_name>tavily_search</tool_name>
<arguments>
{
  "query": "搜索关键词"
}
</arguments>
</use_mcp_tool>

收集全面、准确的信息，关注权威来源。
""",
            
            "data_retriever": """
你是数据检索专家。可以使用文件操作工具：

读取文件：
<read_file>
<path>/path/to/file</path>
</read_file>

写入文件：
<write_file>
<path>/path/to/file</path>
<content>文件内容</content>
</write_file>

专注于结构化数据处理和文件管理。
""",
            
            "content_writer": """
你是专业的内容创作者。基于提供的信息创作高质量文章，包括：
- 清晰的文章结构
- 引人入胜的标题和开头  
- 逻辑严密的论述
- 专业准确的表述

最终输出完整的Markdown格式文章。
""",
            
            "media_generator": """
你是多媒体内容生成专家。基于文章内容生成：
- 配图建议和描述
- 音频脚本大纲
- 视频制作脚本
- 交互式元素设计

提供具体可执行的多媒体方案。
"""
        }
        
        return prompts.get(agent_type, "你是AI助手，根据用户需求智能选择工具完成任务。")

class EnhancedSearchManager:
    """增强的搜索管理器，支持多搜索源"""
    
    def __init__(self):
        self.base_url = JIMU_SEARCH_BASE_URL
        self.token = SYSTEM_TOKEN
        self.providers = ["tavily", "exa", "jina", "firecrawl"]
    
    def multi_source_search(self, query: str, max_results: int = 5) -> Dict[str, List[Dict]]:
        """多源搜索，返回分类结果"""
        results = {}
        
        for provider in self.providers[:2]:  # 使用前两个提供商
            try:
                provider_results = self.search(query, provider, max_results)
                results[provider] = provider_results
            except Exception as e:
                results[provider] = [{"error": f"{provider}搜索失败: {str(e)}"}]
        
        return results
    
    def search(self, query: str, provider: str = "tavily", max_results: int = 5) -> List[Dict]:
        """执行单一搜索"""
        try:
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "query": query,
                "max_results": max_results,
                "provider": provider
            }
            
            response = requests.post(
                f"{self.base_url}/search",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json().get("results", [])
            else:
                return [{"error": f"搜索失败: {response.status_code}"}]
                
        except Exception as e:
            return [{"error": f"搜索异常: {str(e)}"}]

class FileManager:
    """文件管理工具"""
    
    def __init__(self, base_dir: str = "./generated_content"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    def write_file(self, filename: str, content: str) -> Dict[str, Any]:
        """写入文件"""
        try:
            file_path = self.base_dir / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return {
                "success": True,
                "path": str(file_path),
                "message": f"文件已保存: {filename}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"文件写入失败: {str(e)}"
            }
    
    def read_file(self, filename: str) -> Dict[str, Any]:
        """读取文件"""
        try:
            file_path = self.base_dir / filename
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {filename}"
                }
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "success": True,
                "content": content,
                "message": f"文件已读取: {filename}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"文件读取失败: {str(e)}"
            }

# 多智能体状态定义
class MultiAgentState(TypedDict):
    topic: str                          # 主题
    messages: List[Dict[str, str]]      # 消息历史
    search_results: Dict[str, Any]      # 搜索结果
    retrieved_data: Dict[str, Any]      # 检索数据
    article_content: str                # 文章内容
    media_content: Dict[str, Any]       # 多媒体内容
    current_agent: str                  # 当前智能体
    workflow_stage: str                 # 工作流阶段
    tool_usage: List[Dict]              # 工具使用记录
    metadata: Dict[str, Any]            # 元数据

class MultiAgentTestRunner:
    """多智能体测试运行器"""
    
    def __init__(self):
        self.llm_manager = MultiAgentLLMManager()
        self.search_manager = EnhancedSearchManager()
        self.file_manager = FileManager()
        self.test_results = []
    
    def log_test(self, test_name: str, result: str, success: bool = True):
        """记录测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "result": result,
            "success": success,
            "timestamp": datetime.now().isoformat()
        })
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {result}")
    
    def execute_tool_calls(self, text: str, agent_type: str) -> List[Dict]:
        """执行工具调用"""
        tool_calls = self.llm_manager.tool_parser.parse_tool_calls(text)
        results = []
        
        for tool_call in tool_calls:
            try:
                if tool_call["type"] == "use_mcp_tool":
                    if tool_call["tool_name"] == "tavily_search":
                        query = tool_call["arguments"].get("query", "")
                        search_results = self.search_manager.search(query, max_results=3)
                        results.extend(search_results)
                    else:
                        results.append({"info": f"模拟执行工具: {tool_call['tool_name']}"})
                
                elif tool_call["type"] == "write_file":
                    file_result = self.file_manager.write_file(
                        tool_call["path"], 
                        tool_call["content"]
                    )
                    results.append(file_result)
                
                elif tool_call["type"] == "read_file":
                    file_result = self.file_manager.read_file(tool_call["path"])
                    results.append(file_result)
                
            except Exception as e:
                results.append({"error": f"工具执行失败: {str(e)}"})
        
        return results 

    def info_collector_agent(self, state: MultiAgentState) -> MultiAgentState:
        """信息收集智能体 - 多源信息搜索"""
        topic = state["topic"]
        
        # 构建搜索提示词
        search_prompt = f"""
主题: {topic}

请使用搜索工具收集相关信息。执行多个搜索以获得全面覆盖：

<use_mcp_tool>
<server_name>search</server_name>
<tool_name>tavily_search</tool_name>
<arguments>
{{
  "query": "{topic} 基础知识"
}}
</arguments>
</use_mcp_tool>
"""
        
        # 调用LLM
        response = self.llm_manager.call_llm([
            {"role": "user", "content": search_prompt}
        ], agent_type="info_collector")
        
        # 执行工具调用
        tool_results = self.execute_tool_calls(response, "info_collector")
        
        # 额外搜索：趋势和应用
        trend_results = self.search_manager.search(f"{topic} 最新发展趋势", max_results=3)
        application_results = self.search_manager.search(f"{topic} 实际应用案例", max_results=3)
        
        search_results = {
            "basic_info": tool_results,
            "trends": trend_results,
            "applications": application_results,
            "collection_summary": f"收集了{len(tool_results + trend_results + application_results)}条信息"
        }
        
        return {
            **state,
            "search_results": search_results,
            "current_agent": "info_collector",
            "workflow_stage": "information_collected",
            "tool_usage": state.get("tool_usage", []) + [
                {"agent": "info_collector", "tools_used": len(tool_results), "timestamp": datetime.now().isoformat()}
            ]
        }
    
    def data_retriever_agent(self, state: MultiAgentState) -> MultiAgentState:
        """信息检索智能体 - 文件系统和结构化数据处理"""
        topic = state["topic"]
        search_results = state.get("search_results", {})
        
        # 构建数据整理提示词
        data_prompt = f"""
主题: {topic}
已收集信息摘要: {search_results.get('collection_summary', '')}

请整理和结构化收集到的信息，并保存为文件：

<write_file>
<path>research_data_{topic.replace(' ', '_')}.json</path>
<content>
{{
  "topic": "{topic}",
  "research_summary": "基于搜索结果的研究摘要",
  "key_points": ["要点1", "要点2", "要点3"],
  "data_sources": "数据来源统计",
  "processing_date": "{datetime.now().isoformat()}"
}}
</content>
</write_file>
"""
        
        # 调用LLM
        response = self.llm_manager.call_llm([
            {"role": "user", "content": data_prompt}
        ], agent_type="data_retriever")
        
        # 执行工具调用
        tool_results = self.execute_tool_calls(response, "data_retriever")
        
        # 模拟结构化数据处理
        basic_info = search_results.get("basic_info", [])
        structured_data = {
            "processed_articles": len(basic_info),
            "key_concepts": [f"概念{i+1}" for i in range(min(5, len(basic_info)))],
            "data_quality_score": 0.85,
            "processing_method": "自动化信息提取和结构化"
        }
        
        return {
            **state,
            "retrieved_data": {
                "structured_data": structured_data,
                "file_operations": tool_results,
                "data_summary": f"处理了{len(basic_info)}条信息，生成结构化数据"
            },
            "current_agent": "data_retriever", 
            "workflow_stage": "data_retrieved",
            "tool_usage": state.get("tool_usage", []) + [
                {"agent": "data_retriever", "tools_used": len(tool_results), "timestamp": datetime.now().isoformat()}
            ]
        }
    
    def content_writer_agent(self, state: MultiAgentState) -> MultiAgentState:
        """文章编写智能体 - 基于收集信息创作文章"""
        topic = state["topic"]
        search_results = state.get("search_results", {})
        retrieved_data = state.get("retrieved_data", {})
        
        # 构建文章写作上下文
        context_info = []
        for source, results in search_results.items():
            if isinstance(results, list):
                for result in results[:2]:  # 每个源取前2条
                    if not result.get('error'):
                        context_info.append(f"- {result.get('title', '')}: {result.get('content', '')[:200]}")
        
        context = "\n".join(context_info[:10])  # 限制上下文长度
        
        writing_prompt = f"""
作为专业的内容创作者，请围绕主题"{topic}"创作一篇完整的技术文章。

参考资料摘要:
{context if context else "基于您的专业知识创作"}

数据分析结果: {retrieved_data.get('data_summary', '暂无')}

文章要求：
1. 标题：吸引人且专业
2. 引言：简明扼要介绍主题背景和重要性
3. 主体内容：分3-4个章节详细阐述
4. 结论：总结要点并展望未来

请使用标准Markdown格式编写，确保内容结构清晰、表述准确。

现在请开始创作这篇关于"{topic}"的文章：
"""
        
        # 调用LLM生成文章
        article_response = self.llm_manager.call_llm([
            {"role": "user", "content": writing_prompt}
        ], agent_type="content_writer", temperature=0.7)
        
        # 如果响应为空，提供备用内容
        if not article_response or len(article_response.strip()) == 0:
            article_response = f"""# {topic}

## 引言

本文将深入探讨{topic}的相关内容，为读者提供全面的理解和实用的指导。

## 主要内容

### 1. 基础概念
{topic}是一个重要的技术主题，具有广泛的应用价值。

### 2. 核心原理
深入理解{topic}的基本原理对于掌握相关技术至关重要。

### 3. 实际应用
{topic}在实际工作中有着丰富的应用场景和实现方式。

### 4. 最佳实践
遵循最佳实践可以帮助我们更好地运用{topic}相关技术。

## 总结

通过本文的学习，相信读者对{topic}有了更深入的理解。随着技术的不断发展，{topic}将在未来发挥更加重要的作用。

---
*本文由JIMU多智能体系统自动生成*
"""
        
        # 直接使用FileManager保存文章，而不是通过LLM
        filename = f"article_{topic.replace(' ', '_')}.md"
        file_result = self.file_manager.write_file(filename, article_response)
        
        return {
            **state,
            "article_content": article_response,
            "current_agent": "content_writer",
            "workflow_stage": "article_written", 
            "tool_usage": state.get("tool_usage", []) + [
                {"agent": "content_writer", "article_length": len(article_response), "file_saved": file_result.get("success", False), "timestamp": datetime.now().isoformat()}
            ]
        }
    
    def media_generator_agent(self, state: MultiAgentState) -> MultiAgentState:
        """多媒体输出智能体 - 生成配套多媒体内容"""
        topic = state["topic"]
        article_content = state.get("article_content", "")
        
        media_prompt = f"""
主题: {topic}
文章长度: {len(article_content)}字符

基于文章内容，生成配套的多媒体方案：

1. 配图建议（3-5张）
2. 音频脚本大纲  
3. 视频制作脚本
4. 交互式元素设计

提供具体、可执行的方案。
"""
        
        # 调用LLM生成多媒体方案
        media_response = self.llm_manager.call_llm([
            {"role": "user", "content": media_prompt}
        ], agent_type="media_generator", temperature=0.9)
        
        # 生成多媒体文件大纲
        media_outline = {
            "images": [
                {"type": "concept_diagram", "description": f"{topic}概念图"},
                {"type": "flowchart", "description": "流程图示意"},
                {"type": "infographic", "description": "信息图表"}
            ],
            "audio": {
                "duration": "10-15分钟",
                "sections": ["引言", "核心内容", "总结"],
                "style": "专业解说"
            },
            "video": {
                "duration": "5-8分钟", 
                "format": "教育类短视频",
                "elements": ["标题动画", "内容展示", "总结画面"]
            },
            "interactive": {
                "quiz": f"{topic}相关测试题",
                "demo": "实际操作演示",
                "discussion": "讨论话题"
            }
        }
        
        # 直接保存多媒体方案
        filename = f"media_plan_{topic.replace(' ', '_')}.json"
        file_result = self.file_manager.write_file(
            filename, 
            json.dumps(media_outline, ensure_ascii=False, indent=2)
        )
        
        return {
            **state,
            "media_content": {
                "generation_plan": media_response,
                "structured_outline": media_outline,
                "file_operations": [file_result]
            },
            "current_agent": "media_generator",
            "workflow_stage": "completed",
            "tool_usage": state.get("tool_usage", []) + [
                {"agent": "media_generator", "media_elements": len(media_outline), "file_saved": file_result.get("success", False), "timestamp": datetime.now().isoformat()}
            ]
        }
    
    def test_multi_agent_workflow(self):
        """测试完整的多智能体工作流"""
        print("\n=== 多智能体协调工作流测试 ===")
        
        # 构建工作流
        workflow = StateGraph(MultiAgentState)
        
        # 添加四个智能体节点
        workflow.add_node("info_collector", self.info_collector_agent)
        workflow.add_node("data_retriever", self.data_retriever_agent)  
        workflow.add_node("content_writer", self.content_writer_agent)
        workflow.add_node("media_generator", self.media_generator_agent)
        
        # 设置工作流连接
        workflow.set_entry_point("info_collector")
        workflow.add_edge("info_collector", "data_retriever")
        workflow.add_edge("data_retriever", "content_writer")
        workflow.add_edge("content_writer", "media_generator")
        workflow.add_edge("media_generator", END)
        
        # 测试用例
        test_topics = [
            "Python编程基础教程",
            "人工智能在医疗行业的应用",
            "区块链技术原理及应用"
        ]
        
        try:
            app = workflow.compile()
            
            for topic in test_topics:
                print(f"\n📝 开始处理主题: {topic}")
                
                initial_state: MultiAgentState = {
                    "topic": topic,
                    "messages": [{"role": "user", "content": f"请创作关于{topic}的完整内容"}],
                    "search_results": {},
                    "retrieved_data": {},
                    "article_content": "",
                    "media_content": {},
                    "current_agent": "",
                    "workflow_stage": "started",
                    "tool_usage": [],
                    "metadata": {"start_time": datetime.now().isoformat()}
                }
                
                start_time = time.time()
                result = app.invoke(initial_state)
                execution_time = time.time() - start_time
                
                # 分析结果
                tool_usage_count = len(result.get("tool_usage", []))
                article_length = len(result.get("article_content", ""))
                media_elements = len(result.get("media_content", {}).get("structured_outline", {}))
                
                self.log_test(
                    f"多智能体工作流 - {topic}",
                    f"用时{execution_time:.1f}s, 工具调用{tool_usage_count}次, 文章{article_length}字符, 多媒体{media_elements}项"
                )
                
        except Exception as e:
            self.log_test("多智能体工作流", f"失败: {str(e)}", False)
    
    def test_agent_performance(self):
        """测试智能体性能"""
        print("\n=== 智能体性能测试 ===")
        
        test_cases = [
            ("信息收集", "量子计算技术", "info_collector"),
            ("数据检索", "机器学习算法", "data_retriever"), 
            ("内容编写", "云计算服务", "content_writer"),
            ("多媒体生成", "物联网应用", "media_generator")
        ]
        
        for test_name, topic, agent_type in test_cases:
            start_time = time.time()
            
            # 模拟单个智能体测试
            test_prompt = f"请处理主题: {topic}"
            response = self.llm_manager.call_llm([
                {"role": "user", "content": test_prompt}
            ], agent_type=agent_type)
            
            execution_time = time.time() - start_time
            
            self.log_test(
                f"{test_name}智能体性能",
                f"用时{execution_time:.2f}s, 响应长度{len(response)}字符"
            )
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始多智能体协调工作流测试")
        print(f"使用LLM服务: {JIMU_LLM_BASE_URL}")
        print(f"使用搜索服务: {JIMU_SEARCH_BASE_URL}")
        print(f"文件输出目录: {self.file_manager.base_dir}")
        print("-" * 60)
        
        # 运行测试
        self.test_agent_performance()
        self.test_multi_agent_workflow()
        
        # 生成测试报告
        print("\n" + "="*60)
        print("🏁 测试完成！测试报告:")
        print("-" * 60)
        
        success_count = sum(1 for result in self.test_results if result["success"])
        total_count = len(self.test_results)
        
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 保存详细报告
        report = {
            "test_summary": {
                "total": total_count,
                "success": success_count,
                "failure": total_count - success_count,
                "success_rate": success_count/total_count*100,
                "test_time": datetime.now().isoformat()
            },
            "agent_capabilities": [
                "信息收集智能体：多源搜索和信息聚合",
                "数据检索智能体：文件操作和结构化数据处理", 
                "内容编写智能体：专业文章创作和格式化",
                "多媒体生成智能体：配套媒体内容设计"
            ],
            "workflow_features": [
                "四智能体顺序协作工作流",
                "基于JIMU MCP工具调用机制",
                "完整的文件读写和管理",
                "多维度性能监控和分析"
            ],
            "test_details": self.test_results
        }
        
        # 保存报告
        report_file = self.file_manager.write_file(
            "multi_agent_test_report.json",
            json.dumps(report, ensure_ascii=False, indent=2)
        )
        
        print(f"\n📋 详细报告已保存: {report_file.get('path', '保存失败')}")
        print("\n🎯 多智能体协作优势:")
        for feature in report["workflow_features"]:
            print(f"  ✅ {feature}")

if __name__ == "__main__":
    print("🔧 初始化多智能体测试环境...")
    
    # 检查依赖
    try:
        import langgraph
        print("✅ LangGraph已安装")
    except ImportError:
        print("❌ 请先安装LangGraph: pip install langgraph")
        exit(1)
    
    # 运行测试
    runner = MultiAgentTestRunner()
    runner.run_all_tests() 