{"mcpServers": {"exa": {"command": "npx", "args": ["-y", "exa-mcp-server"], "env": {"EXA_API_KEY": "ff9553d1-4ab5-42db-abd2-9fe3e692ac88"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-adf965d649a44894841192daceae0d15"}}, "time-mcp": {"command": "npx", "args": ["-y", "time-mcp"]}}}