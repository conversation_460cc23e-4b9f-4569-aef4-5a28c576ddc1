"""
团队管理相关工具模块
基于 ConfigManager 实现团队配置读取和智能体信息整合
"""
import json
import logging
from typing import Dict, List, Optional, Any

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from backend.langgraph_enhancement.config.config_manager import get_config_manager

# 设置日志
logger = logging.getLogger(__name__)

class TeamManager:
    """团队管理器 - 负责团队配置和智能体信息的整合"""
    
    def __init__(self):
        """初始化团队管理器"""
        self.config_manager = get_config_manager()
        logger.info("团队管理器初始化完成")

    def get_team_info(self, team_id: str) -> Optional[str]:
        """
        获取指定团队的完整信息（包含智能体详细信息）

        Args:
            team_id (str): 团队ID

        Returns:
            Optional[str]: 团队完整信息的JSON字符串，如果未找到则返回None
        """
        try:
            # 获取团队基础配置
            team_config = self.config_manager.get_team_config(team_id)
            if not team_config:
                logger.warning(f"团队 {team_id} 未找到")
                return None

            # 构建完整的团队信息
            team_info = {
                "team_id": team_id,
                "name": team_config.get("name", ""),
                "description": team_config.get("description", ""),
                "workflow_type": team_config.get("workflow_type", "sequential"),
                "context_sharing_strategy": team_config.get("context_sharing_strategy", "selective"),
                "collaboration_rules": team_config.get("collaboration_rules", []),
                "routing_conditions": team_config.get("routing_conditions", {}),
                "agents": []
            }

            # 获取团队中每个智能体的详细信息
            agent_ids = team_config.get("agents", [])
            for agent_id in agent_ids:
                agent_config = self.config_manager.get_agent_config(agent_id)
                if agent_config:
                    # 构建智能体详细信息
                    agent_info = {
                        "agent_id": agent_id,
                        "role_name": agent_config.get("role_name", ""),
                        "role_definition": agent_config.get("role_definition", ""),
                        "background": agent_config.get("background", ""),
                        "description": agent_config.get("description", ""),
                        "core_rules": agent_config.get("core_rules", []),
                        "context_strategy": agent_config.get("context_strategy", ""),
                        "associated_servers": agent_config.get("associated_servers", []),
                        "model": agent_config.get("model", ""),
                        "max_context_window": agent_config.get("max_context_window", 16000),
                        "avatar_url": agent_config.get("avatar_url", ""),
                        "context_management": agent_config.get("context_management", {})
                    }
                    team_info["agents"].append(agent_info)
                else:
                    logger.warning(f"智能体 {agent_id} 配置未找到，跳过")

            return json.dumps(team_info, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"获取团队 {team_id} 信息时出错: {str(e)}")
            return None

    def get_all_teams_info(self) -> str:
        """
        获取所有团队的完整信息

        Returns:
            str: 所有团队信息的JSON字符串数组
        """
        try:
            # 获取所有团队ID
            team_ids = self.config_manager.get_all_team_ids()
            all_teams = []

            for team_id in team_ids:
                team_info_json = self.get_team_info(team_id)
                if team_info_json:
                    team_info = json.loads(team_info_json)
                    all_teams.append(team_info)

            return json.dumps(all_teams, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"获取所有团队信息时出错: {str(e)}")
            return json.dumps([])

    def get_teams_summary(self) -> Dict[str, Any]:
        """
        获取团队概要统计信息

        Returns:
            Dict[str, Any]: 团队统计信息
        """
        try:
            team_ids = self.config_manager.get_all_team_ids()
            summary = {
                "total_teams": len(team_ids),
                "teams": []
            }

            for team_id in team_ids:
                team_config = self.config_manager.get_team_config(team_id)
                if team_config:
                    team_summary = {
                        "team_id": team_id,
                        "name": team_config.get("name", ""),
                        "workflow_type": team_config.get("workflow_type", "sequential"),
                        "agent_count": len(team_config.get("agents", []))
                    }
                    summary["teams"].append(team_summary)

            return summary

        except Exception as e:
            logger.error(f"获取团队概要信息时出错: {str(e)}")
            return {"total_teams": 0, "teams": []} 