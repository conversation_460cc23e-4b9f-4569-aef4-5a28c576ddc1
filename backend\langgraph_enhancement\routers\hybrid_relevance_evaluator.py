# -*- coding: utf-8 -*-
"""
🎯 混合词典相关性评估器 - 使用jieba词典 + 语义标签 (V3 - 中英混合修复版)

核心改进：
- 统一处理中英文混合内容
- 核心关键词中英文映射
- 扩展词典与更合理的阈值
"""

import re
import json
import logging
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

# 尝试导入jieba
try:
    import jieba
    import jieba.posseg as pseg
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("⚠️ jieba未安装，将使用基础分词")

logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """评估结果数据类"""
    result: str
    score: float
    request_type: str
    details: Dict

class HybridRelevanceEvaluator:
    """
    🎯 混合词典相关性评估器 (V3)
    """
    
    def __init__(self):
        self.hybrid_dict = self._load_hybrid_dictionary()
        if JIEBA_AVAILABLE:
            self._init_jieba()
        
        self.thresholds = {
            "GREETING": 0.1,
            "SIMPLE_QUERY": 0.3,
            "TASK": 0.4,
            "GENERAL": 0.2  # 基础通用类型阈值
        }
        logger.info(f"🎯 混合词典评估器(V3)初始化完成 (jieba: {'✅' if JIEBA_AVAILABLE else '❌'})")
    
    def _load_hybrid_dictionary(self) -> Dict:
        dict_path = Path(__file__).parent / "hybrid_dictionary.json"
        if dict_path.exists():
            with open(dict_path, 'r', encoding='utf-8') as f:
                hybrid_dict = json.load(f)
            logger.info(f"📚 加载混合词典: {hybrid_dict.get('metadata', {}).get('total_words', 'N/A')} 词条")
            return hybrid_dict
        else:
            logger.warning("⚠️ 混合词典不存在，使用默认词典")
            return {"metadata": {}, "semantic_categories": {}}
    
    def _init_jieba(self):
        if not JIEBA_AVAILABLE:
            return
        for category, info in self.hybrid_dict.get("semantic_categories", {}).items():
            for word in info.get("words", []):
                jieba.add_word(word)
        logger.info("🔧 jieba分词器初始化完成")
    
    def evaluate(self, user_request: str, agent_response: str) -> EvaluationResult:
        try:
            request_type = self._classify_request(user_request)
            
            if request_type == "GREETING":
                return self._evaluate_greeting(user_request, agent_response)
            
            request_keywords = self._extract_keywords_with_weight(user_request)
            response_keywords = self._extract_keywords_with_weight(agent_response)
            weighted_score = self._calculate_weighted_relevance(request_keywords, response_keywords)

            details = {
                "weighted_score": weighted_score,
                "request_keywords": list(request_keywords.keys()),
                "response_keywords": list(response_keywords.keys()),
                "common_keywords": list(set(request_keywords.keys()) & set(response_keywords.keys()))
            }
            
            # 任务动词匹配加分
            if request_type == "TASK":
                task_verbs = self.hybrid_dict.get("semantic_categories", {}).get("task_verbs", {}).get("words", [])
                task_match = any(verb in agent_response for verb in task_verbs if verb in user_request)
                if task_match:
                    weighted_score = min(weighted_score + 0.2, 1.0)
                details["task_match"] = task_match

            threshold = self.thresholds.get(request_type, 0.2)

            # 针对有明确领域主题的GENERAL请求，动态降低阈值
            if request_type == "GENERAL":
                domain_categories = ["travel_words", "location_words", "domain_nouns"]
                has_domain_keywords = any(
                    word in request_keywords 
                    for cat in domain_categories 
                    if cat in self.hybrid_dict.get("semantic_categories", {})
                    for word in self.hybrid_dict["semantic_categories"][cat].get("words", [])
                )
                if has_domain_keywords:
                    threshold = 0.15 # 动态调整阈值
                    details["dynamic_threshold"] = threshold
            
            result = "quality_approved" if weighted_score >= threshold else "quality_failed"
            
            return EvaluationResult(result, weighted_score, request_type, details)

        except Exception as e:
            logger.error(f"❌ 评估过程异常: {e}", exc_info=True)
            return EvaluationResult("quality_failed", 0.0, "ERROR", {"error": str(e)})

    def _classify_request(self, user_request: str) -> str:
        categories = self.hybrid_dict.get("semantic_categories", {})
        
        # 检查的优先级顺序
        check_order = ["GREETING", "TASK", "SIMPLE_QUERY"]
        
        for cat_name in check_order:
            cat_key = f"{cat_name.lower()}_words" if cat_name != "TASK" else "task_verbs"
            words = categories.get(cat_key, {}).get("words", [])
            if any(word in user_request for word in words):
                # 对简单询问增加长度限制
                if cat_name == "SIMPLE_QUERY" and len(user_request) > 20:
                    continue
                return cat_name
        return "GENERAL"

    def _extract_keywords_with_weight(self, text: str) -> Dict[str, float]:
        keywords = {}
        
        travel_mapping = {
            "travel": "旅游", "trip": "旅游", "journey": "旅游", "plan": "规划", 
            "itinerary": "路线", "route": "路线", "dongguan": "东莞", "xinjiang": "新疆",
            "information": "信息", "destination": "目的地", "accommodation": "住宿", 
            "hotel": "酒店", "responsible": "负责", "gathering": "收集", "comprehensive": "全面"
        }

        # 1. 使用jieba提取中文词汇
        if JIEBA_AVAILABLE:
            for word, pos in pseg.cut(text):
                if len(word) >= 2 and pos not in ['x', 'u', 'p', 'c', 'd', 'w']:
                    keywords[word] = self._get_word_weight(word, pos)
        else: # 兜底中文处理
            for word in re.findall(r'[\u4e00-\u9fa5]{2,}', text):
                 keywords[word] = 1.0

        # 2. 提取英文词汇并映射
        stop_words = {'the', 'a', 'an', 'in', 'on', 'is', 'are', 'to', 'for', 'of', 'and', 'or', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'am'}
        for word in re.findall(r'[a-zA-Z]{3,}', text.lower()):
            if word not in stop_words:
                mapped_word = travel_mapping.get(word, word)
                keywords[mapped_word] = keywords.get(mapped_word, 1.2) 

        return keywords
    
    def _get_word_weight(self, word: str, pos: str) -> float:
        for category, info in self.hybrid_dict.get("semantic_categories", {}).items():
            if word in info.get("word_info", {}):
                freq = info["word_info"][word].get("freq", 0)
                base_weight = 0.9 if freq < 1000 else (0.7 if freq < 5000 else (0.5 if freq < 10000 else 0.3))
                
                category_weights = {"task_verbs": 1.5, "travel_words": 1.4, "location_words": 1.4, "domain_nouns": 1.3}
                return base_weight * category_weights.get(category, 1.0)
        
        pos_weights = {"n": 0.8, "v": 0.9, "vn": 0.9, "a": 0.6, "nr": 0.7, "ns": 1.2, "nt": 0.7} # 进一步提高地名ns权重
        return pos_weights.get(pos, 0.5)
    
    def _calculate_weighted_relevance(self, keywords1: Dict[str, float], keywords2: Dict[str, float]) -> float:
        if not keywords1 or not keywords2: return 0.0
        
        common_words = set(keywords1.keys()) & set(keywords2.keys())
        if not common_words: return 0.0
        
        weighted_sum = sum(min(keywords1[w], keywords2[w]) for w in common_words)
        total_weight1 = sum(keywords1.values())
        total_weight2 = sum(keywords2.values())
        
        return 2 * weighted_sum / (total_weight1 + total_weight2) if (total_weight1 + total_weight2) > 0 else 0.0

    # _evaluate_greeting 保持不变，可以简化，因为它没有在主流程中被直接调用
    def _evaluate_greeting(self, user_request: str, agent_response: str) -> EvaluationResult:
        greeting_words = self.hybrid_dict.get("semantic_categories", {}).get("greeting_words", {}).get("words", [])
        has_greeting = any(word in agent_response for word in greeting_words)
        score = 1.0 if has_greeting else 0.3
        return EvaluationResult("quality_approved", score, "GREETING", {"has_greeting": has_greeting})

def get_hybrid_relevance_evaluator() -> HybridRelevanceEvaluator:
    """获取混合词典评估器实例"""
    if not hasattr(get_hybrid_relevance_evaluator, '_instance'):
        get_hybrid_relevance_evaluator._instance = HybridRelevanceEvaluator()
    return get_hybrid_relevance_evaluator._instance