<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* CSS Variables for colors */
    :root {
      --primary-color: #1E40AF;
      --secondary-color: #475569;
      --accent-color: #3B82F6;
      --background-color: #F8FAFC;
      --text-primary: #1E293B;
      --text-secondary: #64748B;
      --text-light: #94A3B8;
      --card-background: #FFFFFF;
      --card-border: #BAE6FD;
      --container-background: #E0F2FE;
      --hover-color: #7DD3FC;
    }

    /* Font Families */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Font Sizes */
    .hero-title { font-size: 72px; line-height: 1.1; }
    .main-title { font-size: 56px; line-height: 1.1; }
    .section-title { font-size: 36px; line-height: 1.4; }
    .content-title { font-size: 28px; line-height: 1.4; }
    .body-text { font-size: 22px; line-height: 1.6; }
    .small-text { font-size: 16px; line-height: 1.6; }
    .caption { font-size: 14px; line-height: 1.6; }

    /* Font Weights */
    .font-normal { font-weight: 400; }
    .font-medium { font-weight: 500; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }

    /* Text Colors */
    .text-primary-color { fill: var(--text-primary); }
    .text-secondary-color { fill: var(--text-secondary); }
    .text-light-color { fill: var(--text-light); }
    .text-accent-color { fill: var(--accent-color); }
    .text-main-color { fill: var(--primary-color); }

    /* Card Style */
    .card {
      fill: var(--card-background);
      stroke: var(--card-border);
      stroke-width: 1px;
      rx: 12px;
      ry: 12px;
      filter: url(#cardShadow); /* Apply shadow filter */
    }

    /* Decorative Elements */
    .accent-bg-shape { fill: var(--accent-color); opacity: 0.1; }
    .primary-bg-shape { fill: var(--primary-color); opacity: 0.05; }
    .divider-line { stroke: var(--card-border); stroke-width: 1px; }

    /* Icon Style (Stroke based) */
    .icon-stroke {
      stroke: var(--accent-color);
      stroke-width: 2px;
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    /* General text alignment */
    .text-align-left { text-anchor: start; }
    .text-align-center { text-anchor: middle; }
    .text-align-right { text-anchor: end; }

    /* Page specific styles */
    .page-container {
      fill: var(--background-color);
    }
  </style>

  <defs>
    <!-- Card Shadow Filter -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Subtle background gradient for container -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="var(--background-color)"/>
      <stop offset="100%" stop-color="var(--container-background)"/>
    </linearGradient>

    <!-- Primary Gradient -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>

    <!-- Accent Gradient -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Gradient for large highlight number/text -->
    <linearGradient id="highlightTextGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Icon: Chart for data/analytics -->
    <symbol id="icon-chart" viewBox="0 0 24 24">
      <path d="M16 11V3h2v8h-2zm-6 11V9h2v13h-2zm-6 0V14h2v8H4zm16 0V16h2v6h-2z" class="icon-stroke"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Page Content Container -->
  <g transform="translate(80 60)">
    <!-- Top Bar / Logo Placeholder -->
    <rect x="0" y="0" width="1760" height="80" fill="var(--card-background)" rx="12" ry="12"/>
    <text x="40" y="50" class="font-semibold content-title text-main-color text-align-left">
      <tspan class="font-primary">企业愿景</tspan>
      <tspan class="font-secondary small-text text-light-color" dx="10">Business Vision</tspan>
    </text>
    <image href="{logo_url}" x="1600" y="20" width="120" height="40" preserveAspectRatio="xMidYMid meet"/>

    <!-- Main Content Area -->
    <g transform="translate(0 120)">
      <!-- Left Column: Title and Subtitle -->
      <g transform="translate(0 0)">
        <text x="0" y="0" class="main-title font-bold text-primary-color text-align-left">
          <tspan class="font-primary">{title}</tspan>
        </text>
        <text x="0" y="60" class="content-title font-normal text-secondary-color text-align-left">
          <tspan class="font-secondary">{subtitle}</tspan>
        </text>

        <!-- Main Content Card -->
        <rect x="0" y="120" width="1000" height="660" class="card"/>

        <g transform="translate(40 160)">
          <text x="0" y="0" class="content-title font-semibold text-primary-color text-align-left">核心战略和市场定位</text>
          <text x="0" y="40" class="body-text font-normal text-secondary-color text-align-left" width="920">
            <tspan x="0" dy="0" class="font-primary">
              {content}
            </tspan>
            <tspan x="0" dy="1.6em" class="font-secondary">
              Our strategy focuses on disruptive innovation and sustainable growth.
            </tspan>
            <tspan x="0" dy="1.6em" class="font-primary">
              通过深入的市场分析，我们识别了关键增长领域和竞争优势。
            </tspan>
            <tspan x="0" dy="1.6em" class="font-secondary">
              We leverage cutting-edge technology to deliver superior value to our customers.
            </tspan>
            <tspan x="0" dy="1.6em" class="font-primary">
              我们的目标是成为行业领导者，为股东创造长期价值。
            </tspan>
          </text>

          <!-- Bullet Points Section -->
          <text x="0" y="320" class="content-title font-semibold text-primary-color text-align-left">关键要点</text>

          <g transform="translate(0 360)">
            <circle cx="10" cy="10" r="5" fill="var(--accent-color)"/>
            <text x="25" y="15" class="body-text font-normal text-primary-color text-align-left">
              <tspan class="font-primary">市场扩张：</tspan>
              <tspan class="font-secondary small-text text-secondary-color">拓展新兴市场，提升品牌影响力。</tspan>
            </text>
            <circle cx="10" cy="55" r="5" fill="var(--accent-color)"/>
            <text x="25" y="60" class="body-text font-normal text-primary-color text-align-left">
              <tspan class="font-primary">技术创新：</tspan>
              <tspan class="font-secondary small-text text-secondary-color">持续研发投入，保持技术领先地位。</tspan>
            </text>
            <circle cx="10" cy="100" r="5" fill="var(--accent-color)"/>
            <text x="25" y="105" class="body-text font-normal text-primary-color text-align-left">
              <tspan class="font-primary">财务稳健：</tspan>
              <tspan class="font-secondary small-text text-secondary-color">优化成本结构，实现可持续盈利。</tspan>
            </ttext>
            <circle cx="10" cy="145" r="5" fill="var(--accent-color)"/>
            <text x="25" y="150" class="body-text font-normal text-primary-color text-align-left">
              <tspan class="font-primary">人才发展：</tspan>
              <tspan class="font-secondary small-text text-secondary-color">吸引和培养顶尖人才，构建高效团队。</tspan>
            </text>
          </g>
        </g>
      </g>

      <!-- Right Column: Visual emphasis and data placeholder -->
      <g transform="translate(1080 0)">
        <!-- Large Emphasis Number/Text -->
        <text x="0" y="180" class="hero-title font-bold" fill="url(#highlightTextGradient)" text-anchor="middle" transform="translate(360 0)">
          <tspan class="font-accent">200</tspan>
          <tspan class="font-primary" dx="10" dy="-20" font-size="0.5em">亿</tspan>
        </text>
        <text x="0" y="240" class="content-title font-semibold text-accent-color text-align-center" transform="translate(360 0)">
          <tspan class="font-primary">目标市场规模</tspan>
          <tspan class="font-secondary small-text text-secondary-color" dx="10">Target Market Size</tspan>
        </text>

        <!-- Placeholder for Image or Chart -->
        <rect x="0" y="300" width="680" height="480" class="card"/>
        <image href="{image_url}" x="20" y="320" width="640" height="440" preserveAspectRatio="xMidYMid slice" style="border-radius: 8px;"/>

        <!-- Simple Data Visualization Placeholder (Bar Chart style) -->
        <g transform="translate(40 360)">
          <text x="0" y="-20" class="small-text font-semibold text-primary-color">年度增长预测</text>
          <line x1="0" y1="0" x2="0" y2="200" class="divider-line" stroke="var(--text-light)" stroke-dasharray="2 2"/>
          <line x1="0" y1="200" x2="600" y2="200" class="divider-line" stroke="var(--text-light)"/>

          <rect x="50" y="140" width="60" height="60" fill="var(--accent-color)" rx="4" ry="4"/>
          <text x="80" y="220" class="caption font-normal text-secondary-color text-align-center">2023</text>
          <text x="80" y="130" class="caption font-semibold text-accent-color text-align-center">30%</text>

          <rect x="150" y="100" width="60" height="100" fill="var(--accent-color)" rx="4" ry="4"/>
          <text x="180" y="220" class="caption font-normal text-secondary-color text-align-center">2024</text>
          <text x="180" y="90" class="caption font-semibold text-accent-color text-align-center">50%</text>

          <rect x="250" y="60" width="60" height="140" fill="var(--accent-color)" rx="4" ry="4"/>
          <text x="280" y="220" class="caption font-normal text-secondary-color text-align-center">2025</text>
          <text x="280" y="50" class="caption font-semibold text-accent-color text-align-center">70%</text>

          <rect x="350" y="20" width="60" height="180" fill="url(#accentGradient)" rx="4" ry="4"/>
          <text x="380" y="220" class="caption font-normal text-secondary-color text-align-center">2026</text>
          <text x="380" y="10" class="caption font-semibold text-accent-color text-align-center">90%</text>

          <use href="#icon-chart" x="500" y="10" width="32" height="32" class="icon-stroke"/>
        </g>
      </g>
    </g>

    <!-- Footer -->
    <g transform="translate(0 900)">
      <text x="0" y="0" class="small-text font-normal text-light-color text-align-left">
        <tspan class="font-secondary">Page</tspan>
        <tspan class="font-primary" dx="5">4</tspan>
        <tspan class="font-secondary">/10</tspan>
      </text>
      <text x="1760" y="0" class="small-text font-normal text-light-color text-align-right">
        <tspan class="font-primary">日期:</tspan>
        <tspan class="font-secondary" dx="5">{date}</tspan>
        <tspan class="font-primary" dx="20">作者:</tspan>
        <tspan class="font-secondary" dx="5">{author}</tspan>
      </text>
    </g>

  </g>

  <!-- Decorative geometric shapes in background, subtle -->
  <rect x="1500" y="0" width="420" height="200" class="primary-bg-shape" rx="20"/>
  <rect x="0" y="800" width="300" height="280" class="accent-bg-shape" rx="20"/>
  <circle cx="1800" cy="1000" r="100" class="accent-bg-shape"/>
  <circle cx="100" cy="100" r="50" class="primary-bg-shape"/>

</svg>