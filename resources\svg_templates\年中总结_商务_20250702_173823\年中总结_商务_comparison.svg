<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette - Based on unified blue system -->
    <style type="text/css">
      <![CDATA[
        :root {
          --primary-color: #1E40AF; /* 主色 */
          --secondary-color: #475569; /* 辅助色 */
          --accent-color: #3B82F6; /* 强调色 */
          --background-color: #F8FAFC; /* 背景色 */
          --text-primary: #1E293B; /* 文字主色 */
          --text-secondary: #64748B; /* 文字辅助色 */
          --text-light: #94A3B8; /* 浅色文字 */
          --card-background: #FFFFFF; /* 卡片背景 */
          --card-border: #BAE6FD; /* 卡片边框 */
          --container-background: #E0F2FE; /* 容器背景 */
        }

        /* Font System */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; } /* Not heavily used for this style, but defined */

        /* Font Sizes & Weights */
        .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
        .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
        .text-section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
        .text-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
        .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; }
        .text-small { font-size: 16px; font-weight: 400; line-height: 1.6; }
        .text-caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

        /* Colors as CSS classes */
        .fill-primary { fill: var(--primary-color); }
        .fill-secondary { fill: var(--secondary-color); }
        .fill-accent { fill: var(--accent-color); }
        .fill-background { fill: var(--background-color); }
        .fill-text-primary { fill: var(--text-primary); }
        .fill-text-secondary { fill: var(--text-secondary); }
        .fill-text-light { fill: var(--text-light); }
        .fill-card-background { fill: var(--card-background); }
        .stroke-card-border { stroke: var(--card-border); }
        .stroke-accent { stroke: var(--accent-color); }
        .stroke-primary { stroke: var(--primary-color); }
        .stroke-secondary { stroke: var(--secondary-color); }
        .fill-container-background { fill: var(--container-background); }

        /* General Styles */
        .shadow-sm { filter: url(#dropShadowSmall); }
        .shadow-md { filter: url(#dropShadowMedium); }
      ]]>
    </style>

    <!-- Drop Shadow Filters -->
    <filter id="dropShadowSmall" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="dropShadowMedium" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="10"/>
      <feGaussianBlur stdDeviation="15"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--accent-color)" stop-opacity="1"/>
      <stop offset="100%" stop-color="var(--primary-color)" stop-opacity="0.8"/>
    </linearGradient>
    <linearGradient id="subtleBackgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="var(--background-color)"/>
      <stop offset="100%" stop-color="var(--container-background)"/>
    </linearGradient>

    <!-- Icon: Chart Bar (simplified outline) -->
    <symbol id="icon-chart-bar" viewBox="0 0 24 24">
      <path d="M4 22V10H8V22H4ZM10 22V2H14V22H10ZM16 22V14H20V22H16Z" fill="currentColor"/>
    </symbol>
    <!-- Icon: Target (simplified outline) -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
      <circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="2" fill="none"/>
      <circle cx="12" cy="12" r="2" fill="currentColor"/>
    </symbol>
    <!-- Icon: Check Circle (simplified outline) -->
    <symbol id="icon-check-circle" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" fill="currentColor"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#subtleBackgroundGradient)"/>

  <!-- Header & Page Info -->
  <g id="header">
    <text x="80" y="80" class="font-secondary text-small fill-text-light">
      <tspan>页面 {page_number}</tspan>
      <tspan dx="5">7/10</tspan>
    </text>

    <!-- Logo Placeholder -->
    <image x="1680" y="60" width="160" height="40" href="{logo_url}" id="logo" visibility="hidden"/>
    <rect x="1680" y="60" width="160" height="40" rx="5" fill="transparent" stroke="var(--text-light)" stroke-dasharray="5 5" stroke-width="1"/>
    <text x="1760" y="85" text-anchor="middle" class="font-secondary text-caption fill-text-light">Logo Placeholder</text>
  </g>

  <!-- Main Title -->
  <g id="main-title">
    <text x="960" y="180" text-anchor="middle" class="font-primary text-main-title fill-text-primary">
      <tspan x="960" dy="0">{title} 年中总结: 关键业务对比分析</tspan>
    </text>
    <text x="960" y="240" text-anchor="middle" class="font-secondary text-content-title fill-text-secondary">
      <tspan x="960" dy="0">{subtitle} Mid-Year Summary: Key Business Comparison Analysis</tspan>
    </text>
  </g>

  <!-- Content Area: Comparison Cards (Bento Grid Style Adaptation) -->
  <g id="comparison-section">
    <!-- Left Card: H1 2023 Performance -->
    <rect x="80" y="320" width="800" height="600" rx="12" class="fill-card-background stroke-card-border shadow-md"/>
    <g transform="translate(80, 320)">
      <text x="40" y="60" class="font-primary text-section-title fill-primary">
        <tspan>上半年表现</tspan>
      </text>
      <text x="40" y="100" class="font-secondary text-small fill-text-secondary">
        <tspan>Performance in H1 2023</tspan>
      </text>

      <!-- Key Metric 1 - Large Number Emphasis -->
      <g transform="translate(40, 160)">
        <text class="font-primary text-hero-title fill-primary">
          <tspan>+25%</tspan>
        </text>
        <text y="70" class="font-secondary text-content-title fill-text-primary">
          <tspan>营收增长</tspan>
        </text>
        <text y="100" class="font-secondary text-small fill-text-secondary">
          <tspan>Revenue Growth</tspan>
        </text>
      </g>

      <!-- Metric Details - Content Placeholder -->
      <g transform="translate(40, 350)">
        <text class="font-secondary text-body fill-text-primary">
          <tspan x="0" dy="0">主要成就:</tspan>
          <tspan x="0" dy="30">· 市场份额提升至15%</tspan>
          <tspan x="0" dy="30">· 新产品线成功上线</tspan>
          <tspan x="0" dy="30">· 客户满意度达90%</tspan>
          <tspan x="0" dy="30">{content}</tspan>
        </text>
      </g>
      <!-- Decorative Icon with subtle background -->
      <use xlink:href="#icon-chart-bar" x="680" y="40" width="80" height="80" class="stroke-accent" stroke-width="2" fill="none"/>
      <circle cx="720" cy="80" r="30" class="fill-accent" fill-opacity="0.1"/>
    </g>

    <!-- Right Card: H2 2023 Outlook -->
    <rect x="1040" y="320" width="800" height="600" rx="12" class="fill-card-background stroke-card-border shadow-md"/>
    <g transform="translate(1040, 320)">
      <text x="40" y="60" class="font-primary text-section-title fill-secondary">
        <tspan>下半年展望</tspan>
      </text>
      <text x="40" y="100" class="font-secondary text-small fill-text-secondary">
        <tspan>Outlook for H2 2023</tspan>
      </text>

      <!-- Key Metric 2 - Large Number Emphasis -->
      <g transform="translate(40, 160)">
        <text class="font-primary text-hero-title fill-secondary">
          <tspan>+30%</tspan>
        </text>
        <text y="70" class="font-secondary text-content-title fill-text-primary">
          <tspan>目标增长率</tspan>
        </text>
        <text y="100" class="font-secondary text-small fill-text-secondary">
          <tspan>Target Growth Rate</tspan>
        </text>
      </g>

      <!-- Metric Details - Content Placeholder -->
      <g transform="translate(40, 350)">
        <text class="font-secondary text-body fill-text-primary">
          <tspan x="0" dy="0">重点规划:</tspan>
          <tspan x="0" dy="30">· 拓展国际市场</tspan>
          <tspan x="0" dy="30">· 优化产品迭代周期</tspan>
          <tspan x="0" dy="30">· 提升运营效率</tspan>
          <tspan x="0" dy="30">{content}</tspan>
        </text>
      </g>
      <!-- Decorative Icon with subtle background -->
      <use xlink:href="#icon-target" x="680" y="40" width="80" height="80" class="stroke-secondary" stroke-width="2" fill="none"/>
      <circle cx="720" cy="80" r="30" class="fill-secondary" fill-opacity="0.1"/>
    </g>
  </g>

  <!-- Central Difference/Insight Area - Visually Emphasized -->
  <g id="difference-insight">
    <rect x="910" y="420" width="100" height="400" rx="10" fill="url(#accentGradient)" class="shadow-sm"/>
    <text x="960" y="520" text-anchor="middle" class="font-primary text-content-title" fill="var(--card-background)" transform="rotate(90 960 520)">
      <tspan>差异和机会</tspan>
    </text>
    <text x="960" y="550" text-anchor="middle" class="font-secondary text-small" fill="var(--card-background)" transform="rotate(90 960 550)">
      <tspan>Differences 和#38; Opportunities</tspan>
    </text>

    <!-- Arrows connecting comparison cards -->
    <path d="M880 520 L910 540 L880 560 Z" fill="url(#accentGradient)"/>
    <path d="M1040 540 L1010 520 L1010 560 Z" fill="url(#accentGradient)"/>
  </g>

  <!-- Conclusion Area - Prominently Displayed -->
  <g id="conclusion-summary">
    <rect x="80" y="950" width="1760" height="80" rx="12" class="fill-container-background stroke-card-border shadow-sm"/>
    <text x="120" y="995" class="font-primary text-content-title fill-primary">
      <tspan>结论: 聚焦创新和市场拓展，实现持续增长</tspan>
    </text>
    <text x="120" y="1025" class="font-secondary text-small fill-text-secondary">
      <tspan>Conclusion: Focus on innovation and market expansion for sustained growth.</tspan>
    </tspan>
    <use xlink:href="#icon-check-circle" x="1750" y="975" width="40" height="40" class="fill-primary"/>
  </g>

  <!-- Footer -->
  <g id="footer">
    <text x="80" y="1060" class="font-secondary text-small fill-text-light">
      <tspan>{date}</tspan>
      <tspan dx="10">2023年7月15日</tspan>
    </text>
    <text x="1840" y="1060" text-anchor="end" class="font-secondary text-small fill-text-light">
      <tspan>{author}</tspan>
      <tspan dx="-10">团队名称 / 报告人</tspan>
    </text>
  </g>

  <!-- Content Placeholders - Invisible, just for reference -->
  <text x="0" y="0" visibility="hidden">
    {title} - 主标题
    {subtitle} - 副标题
    {content} - 正文内容
    {image_url} - 图片链接
    {logo_url} - Logo链接
    {date} - 日期
    {author} - 作者
    {page_number} - 页面序号
  </text>
</svg>