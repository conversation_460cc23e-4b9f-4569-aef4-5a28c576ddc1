<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变和滤镜 -->
  <defs>
    <!-- 特斯拉红色渐变 -->
    <linearGradient id="teslaRedGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E31937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E31937;stop-opacity:0.6" />
    </linearGradient>
    
    <!-- 图表区域渐变 -->
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E31937;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#E31937;stop-opacity:0" />
    </linearGradient>
    
    <!-- 发光效果滤镜 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="10" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#000000" />
  
  <!-- 标题区域 -->
  <g transform="translate(600, 80)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="60" font-weight="bold" text-anchor="middle" fill="#FFFFFF">MPV<tspan fill="#E31937">研究报告</tspan></text>
    <text x="0" y="30" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="#999999">MULTI-PURPOSE VEHICLE RESEARCH REPORT</text>
  </g>
  
  <!-- Bento Grid 布局 -->
  <!-- 核心特点 - 大尺寸突出显示 -->
  <g transform="translate(100, 150)">
    <rect width="400" height="400" rx="15" fill="#111111" />
    <circle cx="50" cy="50" r="100" fill="#E31937" opacity="0.2" filter="url(#glow)" />
    
    <text x="40" y="60" font-family="Arial, sans-serif" font-size="30" font-weight="bold" fill="#FFFFFF">核心特点</text>
    <text x="40" y="85" font-family="Arial, sans-serif" font-size="14" fill="#999999">CORE FEATURES</text>
    
    <text x="40" y="200" font-family="Arial, sans-serif" font-size="120" font-weight="900" fill="url(#teslaRedGradient)">MPV</text>
    
    <g transform="translate(40, 240)">
      <circle cx="10" cy="10" r="8" fill="#E31937" />
      <text x="30" y="15" font-family="Arial, sans-serif" font-size="18" fill="#FFFFFF">宽敞灵活的内部空间</text>
      
      <circle cx="10" cy="50" r="8" fill="#E31937" />
      <text x="30" y="55" font-family="Arial, sans-serif" font-size="18" fill="#FFFFFF">多变座椅布局</text>
      
      <circle cx="10" cy="90" r="8" fill="#E31937" />
      <text x="30" y="95" font-family="Arial, sans-serif" font-size="18" fill="#FFFFFF">兼具轿车舒适性和厢式货车功能性</text>
    </g>
  </g>
  
  <!-- 市场趋势图表 -->
  <g transform="translate(520, 150)">
    <rect width="580" height="180" rx="15" fill="#111111" />
    
    <text x="30" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">市场趋势</text>
    <text x="30" y="65" font-family="Arial, sans-serif" font-size="14" fill="#999999">MARKET TRENDS</text>
    
    <!-- 图表 -->
    <g transform="translate(30, 80)">
      <!-- 坐标轴 -->
      <line x1="0" y1="70" x2="520" y2="70" stroke="#444444" stroke-width="1" />
      <line x1="0" y1="0" x2="0" y2="70" stroke="#444444" stroke-width="1" />
      
      <!-- 趋势线和区域 -->
      <path d="M0,60 L80,55 L160,65 L240,45 L320,30 L400,15 L480,5" stroke="#E31937" stroke-width="3" fill="none" />
      <path d="M0,60 L80,55 L160,65 L240,45 L320,30 L400,15 L480,5 L480,70 L0,70 Z" fill="url(#chartGradient)" />
      
      <!-- 数据点 -->
      <circle cx="0" cy="60" r="4" fill="#E31937" />
      <circle cx="80" cy="55" r="4" fill="#E31937" />
      <circle cx="160" cy="65" r="4" fill="#E31937" />
      <circle cx="240" cy="45" r="4" fill="#E31937" />
      <circle cx="320" cy="30" r="4" fill="#E31937" />
      <circle cx="400" cy="15" r="4" fill="#E31937" />
      <circle cx="480" cy="5" r="4" fill="#E31937" />
      
      <!-- 标签 -->
      <text x="0" y="85" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#888888">2020</text>
      <text x="160" y="85" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#888888">2022</text>
      <text x="320" y="85" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#888888">2024</text>
      <text x="480" y="85" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#888888">2026</text>
    </g>
    
    <text x="290" y="160" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#FFFFFF">中国MPV市场在2023年呈现<tspan fill="#E31937">触底回升</tspan>并显著增长的态势</text>
  </g>
  
  <!-- 消费者群体 -->
  <g transform="translate(520, 350)">
    <rect width="280" height="200" rx="15" fill="#111111" />
    
    <text x="30" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">主要消费群体</text>
    <text x="30" y="65" font-family="Arial, sans-serif" font-size="14" fill="#999999">CONSUMER GROUPS</text>
    
    <!-- 图标和文字 -->
    <g transform="translate(70, 100)">
      <!-- 家庭用户图标 -->
      <path d="M25,40 C25,29 15,20 0,20 C-15,20 -25,29 -25,40" stroke="#E31937" stroke-width="3" fill="none" />
      <circle cx="0" cy="0" r="15" stroke="#E31937" stroke-width="3" fill="none" />
      <text x="0" y="70" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#FFFFFF">家庭用户</text>
    </g>
    
    <g transform="translate(210, 100)">
      <!-- 商务用户图标 -->
      <rect x="-15" y="-15" width="30" height="40" stroke="#E31937" stroke-width="3" fill="none" rx="2" />
      <rect x="-25" y="-25" width="50" height="10" stroke="#E31937" stroke-width="3" fill="none" rx="2" />
      <text x="0" y="70" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#FFFFFF">商务用户</text>
    </g>
  </g>
  
  <!-- 热门车型 -->
  <g transform="translate(820, 350)">
    <rect width="280" height="400" rx="15" fill="#111111" />
    <circle cx="230" cy="350" r="100" fill="#E31937" opacity="0.2" filter="url(#glow)" />
    
    <text x="30" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">热门车型</text>
    <text x="30" y="65" font-family="Arial, sans-serif" font-size="14" fill="#999999">POPULAR MODELS</text>
    
    <!-- 车型列表 -->
    <g transform="translate(30, 100)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#E31937">01</text>
      <text x="50" y="0" font-family="Arial, sans-serif" font-size="20" fill="#FFFFFF">腾势D9</text>
      
      <text x="0" y="50" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#E31937">02</text>
      <text x="50" y="50" font-family="Arial, sans-serif" font-size="20" fill="#FFFFFF">别克GL8</text>
      
      <text x="0" y="100" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#E31937">03</text>
      <text x="50" y="100" font-family="Arial, sans-serif" font-size="20" fill="#FFFFFF">丰田赛那</text>
      
      <text x="0" y="150" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#E31937">04</text>
      <text x="50" y="150" font-family="Arial, sans-serif" font-size="20" fill="#FFFFFF">传祺M8</text>
      
      <text x="0" y="200" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#E31937">05</text>
      <text x="50" y="200" font-family="Arial, sans-serif" font-size="20" fill="#FFFFFF">小鹏X9</text>
    </g>
  </g>
  
  <!-- 新能源趋势 -->
  <g transform="translate(100, 570)">
    <rect width="200" height="180" rx="15" fill="#111111" />
    
    <text x="30" y="40" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">新能源趋势</text>
    <text x="30" y="60" font-family="Arial, sans-serif" font-size="12" fill="#999999">NEW ENERGY TRENDS</text>
    
    <!-- 电池图标 -->
    <g transform="translate(100, 110)">
      <rect x="-40" y="-25" width="70" height="40" rx="5" stroke="#E31937" stroke-width="2" fill="none" />
      <rect x="30" y="-15" width="10" height="20" rx="2" stroke="#E31937" stroke-width="2" fill="none" />
      <rect x="-35" y="-20" width="40" height="30" fill="#E31937" opacity="0.6" rx="2" />
      <path d="M0,-40 L-10,-20 L5,-20 L-5,10" stroke="#E31937" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round" />
    </g>
    
    <text x="100" y="160" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#FFFFFF">新能源MPV在<tspan fill="#E31937">智能化</tspan>和<tspan fill="#E31937">能耗</tspan>方面展现优势</text>
  </g>
  
  <!-- MPV vs SUV 对比 -->
  <g transform="translate(320, 570)">
    <rect width="480" height="180" rx="15" fill="#111111" />
    
    <text x="30" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">MPV vs SUV</text>
    <text x="30" y="65" font-family="Arial, sans-serif" font-size="14" fill="#999999">COMPARISON</text>
    
    <!-- 对比内容 -->
    <g transform="translate(30, 90)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#E31937">MPV优势</text>
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">• 更宽敞的内部空间</text>
      <text x="0" y="60" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">• 灵活多变的座椅布局</text>
      <text x="0" y="90" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">• 更好的乘坐舒适性</text>
    </g>
    
    <g transform="translate(250, 90)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#999999">SUV优势</text>
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">• 更好的通过性能</text>
      <text x="0" y="60" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">• 更时尚的外观设计</text>
      <text x="0" y="90" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">• 更高的离地间隙</text>
    </g>
  </g>
  
  <!-- 未来发展 -->
  <g transform="translate(100, 770)">
    <rect width="340" height="180" rx="15" fill="#111111" transform="translate(0, -150)" />
    
    <text x="30" y="-110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">未来发展</text>
    <text x="30" y="-90" font-family="Arial, sans-serif" font-size="12" fill="#999999">FUTURE DEVELOPMENT</text>
    
    <text x="170" y="-50" font-family="Arial, sans-serif" font-size="60" font-weight="900" text-anchor="middle" fill="url(#teslaRedGradient)">2030</text>
    <text x="170" y="-10" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#FFFFFF">智能化与电动化<tspan fill="#E31937">深度融合</tspan></text>
  </g>
  
  <!-- 车型分类 -->
  <g transform="translate(460, 770)">
    <rect width="340" height="180" rx="15" fill="#111111" transform="translate(0, -150)" />
    
    <text x="30" y="-110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">车型分类</text>
    <text x="30" y="-90" font-family="Arial, sans-serif" font-size="12" fill="#999999">VEHICLE TYPES</text>
    
    <g transform="translate(30, -70)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">迷你型</text>
      <text x="260" y="0" font-family="Arial, sans-serif" font-size="16" text-anchor="end" fill="#E31937">&lt;4100mm</text>
      
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">小型</text>
      <text x="260" y="30" font-family="Arial, sans-serif" font-size="16" text-anchor="end" fill="#E31937">&lt;4100mm</text>
      
      <text x="0" y="60" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">中型</text>
      <text x="260" y="60" font-family="Arial, sans-serif" font-size="16" text-anchor="end" fill="#E31937">4100-4200mm</text>
      
      <text x="0" y="90" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF">大型</text>
      <text x="260" y="90" font-family="Arial, sans-serif" font-size="16" text-anchor="end" fill="#E31937">&gt;4600mm</text>
    </g>
  </g>
</svg> 