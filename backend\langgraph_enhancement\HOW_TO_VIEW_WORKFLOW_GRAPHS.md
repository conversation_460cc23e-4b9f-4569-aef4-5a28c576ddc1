# 🎨 如何查看LangGraph工作流图

## 📋 概述

LangGraph增强框架提供了多种方式来可视化和查看工作流图，帮助您理解和调试复杂的多智能体工作流。

## 🔍 查看方式

### 1. **文本版可视化**（立即可用）

运行文本版演示：
```bash
python3 text_workflow_diagram.py
```

这会显示ASCII艺术格式的工作流图，包括：
- 简单顺序工作流
- 复杂并行工作流  
- 条件分支工作流

### 2. **PNG图片可视化**（需要安装依赖）

#### 安装依赖：
```bash
# 基础依赖
pip install matplotlib networkx

# 高级图布局（可选）
pip install pygraphviz

# 系统级graphviz（Ubuntu/Debian）
sudo apt-get install graphviz graphviz-dev

# 系统级graphviz（macOS）
brew install graphviz
```

#### 生成图片：
```bash
# 运行独立演示
python3 simple_graph_demo.py

# 或运行完整的工作流可视化生成器
python3 generate_workflow_visualization.py
```

### 3. **自动生成**（集成到工作流中）

在实际使用中，工作流图会自动生成：

```python
from core.workflow_engine import StateGraph, END
from core.state_definitions import EnhancedTeamState

# 创建工作流
graph = StateGraph(EnhancedTeamState)
graph.add_node("start", start_function)
graph.add_node("process", process_function)
graph.add_edge("start", "process")
graph.add_edge("process", END)

# 🎨 生成可视化图
graph.visualize("my_workflow.png")  # 会在当前目录生成PNG文件
```

### 4. **WorkflowBuilder自动生成**

当使用DynamicWorkflowEngine构建工作流时，系统会自动生成可视化图：

```python
engine = DynamicWorkflowEngine()
workflow = await engine.build_workflow_from_team_config(team_config)

# 图片会自动保存为: workflow_<team_name>_<workflow_type>.png
```

## 📁 生成的文件位置

可视化图片会保存在以下位置：
- **手动生成**: 脚本运行目录
- **自动生成**: `langgraph_enhancement/` 目录
- **文件命名**: `workflow_<name>_<timestamp>.png`

## 🔍 查看生成的图片

### Windows:
- 双击PNG文件，使用默认图片查看器
- 或在文件资源管理器中右键 → "打开方式"

### macOS:
- 双击PNG文件，使用"预览"应用
- 或拖拽到任何图片查看器

### Linux:
- 使用`eog` (GNOME): `eog workflow.png`
- 使用`feh`: `feh workflow.png`
- 使用`display` (ImageMagick): `display workflow.png`

### IDE中查看:
- **VS Code**: 直接在编辑器中打开PNG文件
- **PyCharm**: 在项目面板中双击PNG文件
- **其他IDE**: 大多数现代IDE都支持图片预览

## 📊 图片内容说明

### 节点（圆圈）:
- **蓝色圆圈**: 普通工作流节点
- **不同颜色**: 根据节点类型区分（协调器、智能体、处理器等）
- **标签**: 显示节点名称

### 边（箭头）:
- **实线箭头**: 固定的执行路径
- **虚线箭头**: 条件边（根据条件选择路径）
- **箭头方向**: 显示执行流向

### 布局:
- **层次布局**: 清晰的从上到下的执行流程
- **节点大小**: 根据重要性调整
- **间距**: 便于阅读和理解

## 🎯 实际示例

### 简单的研究工作流:
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   开 始     │───▶│  数据收集   │───▶│  分析处理   │
└─────────────┘    └─────────────┘    └─────────────┘
                                               │
                                               ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  最终输出   │◀───│  报告生成   │◀───│  质量检查   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 并行智能体工作流:
```
               ┌─────────────┐
               │  协调器     │
               └──────┬──────┘
                      │
       ┌──────────────┼──────────────┐
       │              │              │
       ▼              ▼              ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ 研究智能体  │ │ 分析智能体  │ │ 写作智能体  │
└──────┬──────┘ └──────┬──────┘ └──────┬──────┘
       │              │              │
       └──────────────┼──────────────┘
                      │
                      ▼
               ┌─────────────┐
               │ 结果合并    │
               └─────────────┘
```

## 🚀 高级功能

### 交互式查看（计划中）:
- Web界面实时显示工作流状态
- 节点执行状态的实时更新
- 错误节点的高亮显示

### 动态图（计划中）:
- 显示执行过程的动画
- 数据流的可视化
- 性能热点的标识

## 💡 故障排除

### 如果看不到图片:
1. **检查依赖**: 确保安装了matplotlib和networkx
2. **检查权限**: 确保有写入当前目录的权限
3. **检查路径**: 确认图片文件确实生成了
4. **检查格式**: 确认图片查看器支持PNG格式

### 如果图片布局不佳:
1. **安装graphviz**: 获得更好的图布局算法
2. **调整参数**: 修改图片大小和节点间距
3. **简化图结构**: 对于复杂图，考虑分层展示

### 常见错误:
- `ModuleNotFoundError: matplotlib`: 运行 `pip install matplotlib`
- `No module named 'networkx'`: 运行 `pip install networkx`
- 图片空白: 检查节点和边是否正确添加

## 📞 获取帮助

如果遇到问题，请：
1. 检查生成的日志信息
2. 确认所有依赖都正确安装
3. 查看示例代码和文档
4. 运行测试脚本验证功能

---

通过这些方法，您可以轻松地查看和理解LangGraph工作流的结构，有助于开发、调试和优化您的多智能体工作流系统。