"""
AI服务管理器模块，负责管理和调用不同的AI服务
"""
import logging
import asyncio
import json
import re
import ast
from typing import Dict, List, Any, Optional, Callable, Union, TypedDict, Awaitable, Tuple

# 从其他模块导入
from backend.core.config import config
from backend.ai.service import AIService
from backend.ai.tools.tools import _process_tool_result
from backend.mcp.server_manager import mcp_server
from backend.ai.tools.tools import parse_xml_tool_calls,_compress_tool_result
from backend.mcp.prompts import get_mcp_prompt
from backend.ai.tools.prompts import get_system_prompt_with_code_interpreter
from backend.ai.tools.code_interpreter_adapter import execute_code, parse_execute_code_tag
from backend.mcp.tool_parser import mcp_tool_parser

# 移除直接导入，改为在需要的方法中动态导入
# from backend.crewai_integration.crew_setup import execute_crew_workflow

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ToolCallContext:
    """工具调用上下文类，封装工具调用过程中需要的参数"""
    
    def __init__(self, messages: List[Dict], model: Optional[str], 
                 tool_calls_history: List[Dict], is_cancelled: Optional[Callable], 
                 is_agent: bool, stream_callback: Optional[Callable], 
                 original_model: Optional[str], content: str, 
                 tool_calls_count: int, service: 'AIService'):
        self.messages = messages
        self.model = model
        self.tool_calls_history = tool_calls_history
        self.is_cancelled = is_cancelled
        self.is_agent = is_agent
        self.stream_callback = stream_callback
        self.original_model = original_model
        self.content = content
        self.tool_calls_count = tool_calls_count
        self.service = service

class AIManager:
    """AI服务管理器类，负责管理不同的AI服务"""
    
    MAX_PARSING_CORRECTION_ATTEMPTS = 2 # Max attempts for AI to correct its own malformed XML
    
    def __init__(self):
        """初始化AI服务管理器"""
        self.services = {}
        # 延迟导入服务类，避免循环导入
      
    async def chat_completion(self, messages: List[Dict], 
                      service_name: str = None,
                      model: str = None,
                      role_id: Optional[str] = None, # Added role_id parameter
                      selected_servers: Optional[List[Dict]] = None, 
                      stream_callback: Optional[Callable] = None,
                      handle_tools: bool = True,  # 是否处理工具调用
                      max_tool_calls: int = 5,    # 最大工具调用次数
                      is_agent: bool = False,
                      is_cancelled: Optional[Callable] = None,
                      max_tokens: Optional[int] = None,   # 最大token数量
                      thinking_budget: Optional[int] = None,
                      is_web_search_enabled: bool = False):  # 添加网络搜索开关参数

        """聊天补全接口"""
        is_code_interpreter_enabled = False
        # 以下是原有的非Agent模式处理逻辑
        service = self._get_service(service_name)

        # ---> MODIFICATION: Append instruction to last user message, excluding simple greetings <---
        if messages and messages[-1].get("role") == "user":
            content = messages[-1].get("content", "")
            simple_greetings = ["你好", "您好", "hello", "hi", "hey", "你好呀", "在吗", "hi there", "greetings"]
            if isinstance(content, list):
                # 只对text部分做greeting判断和拼接指令，image_url直接保留
                text_items = [item for item in content if item.get("type") == "text"]
                text_str = " ".join([item.get("text", "").strip() for item in text_items if item.get("text")])
                is_greeting = False
                for greeting in simple_greetings:
                    if text_str.lower() == greeting.lower():
                        is_greeting = True
                        break
                new_content = []
                for item in content:
                    if item.get("type") == "text":
                        if not is_greeting:
                            new_text = item.get("text", "").strip() + "\n请遵循系统指令，完成以上需求"
                            new_content.append({"type": "text", "text": new_text})
                        else:
                            new_content.append(item)
                    elif item.get("type") == "image_url":
                        new_content.append(item)
                    elif item.get("type") == "file":
                        new_content.append({"type": "text", "text":"这个是一个文件，文件名是："+item.get("filename", "")+",文件类型是："+item.get("filetype", "")+",内容是："+item.get("text", "")})
                    else:
                        new_content.append(item)
                messages[-1]["content"] = new_content
            else:
                user_content = str(content).strip()
                is_greeting = False
                for greeting in simple_greetings:
                    if user_content.lower() == greeting.lower():
                        is_greeting = True
                        break
                if not is_greeting:
                    messages[-1]["content"] = user_content + "\n请遵循系统指令，完成以上需求"
                else:
                    messages[-1]["content"] = user_content
        # ---> END MODIFICATION <---

        # 如果指定了模型，临时设置服务的模型
        original_model = None
        if model:
            original_model = service.model
            service.model = model
        functions = []
        
        # 记录思考预算状态
        if thinking_budget is not None:
            # logging.info(f"普通模式，思考预算: {thinking_budget}")
            pass # 添加pass语句以满足缩进要求

        # 记录网络搜索状态
        tavily_prompt = ""
        if is_web_search_enabled:
            # logging.info("已启用网络搜索功能")
            from backend.search.tavily import get_tavily_prompt
            tavily_prompt = get_tavily_prompt()
            # logging.info("已添加Tavily搜索提示词")

        try:
            # 检查是否有系统消息
            has_system_message = False
            for message in messages:
                if message.get('role') == 'system':
                    has_system_message = True
                    break
                    
            # ---> MODIFICATION: 使用 role_id 构建系统提示词 <---
            # 增加标志，标记是否已通过角色ID设置了系统提示词
            role_prompt_set = False
            
            if role_id:
                # 🔥 修复：特殊处理 LangGraph 智能体，保护其系统提示词不被覆盖
                if role_id == "langgraph_agent":
                    logging.info(f"检测到 LangGraph 智能体，保护其系统提示词不被覆盖")
                    # 🔥 调试：记录接收到的消息状态
                    for i, msg in enumerate(messages):
                        if msg.get('role') == 'system':
                            content = msg.get('content', '')
                            # logging.info(f"🔍 LangGraph系统消息 {i}: 长度={len(content)}, 前100字符: {content[:100]}...")
                    role_prompt_set = True  # 设置标志，防止后续逻辑覆盖系统提示词
                else:
                    logging.info(f"使用角色ID {role_id} 构建系统提示词")
                    try:
                        # 导入RoleManager
                        from backend.utils.role_utils import RoleManager
                        
                        # 获取角色系统提示词
                        role_manager = RoleManager()
                        system_prompt = await role_manager.construct_system_prompt_for_role(role_id)
                        
                        # 如果已有系统消息，则替换它
                        if has_system_message:
                            for i, message in enumerate(messages):
                                if message.get('role') == 'system':
                                    messages[i]['content'] = system_prompt
                                    break
                        else:
                            # 如果没有系统消息，则创建一个
                            system_message = {
                                "role": "system",
                                "content": system_prompt
                            }
                            messages = [system_message] + messages
                            has_system_message = True
                        
                        # 设置标志，表示已通过角色ID设置系统提示词
                        role_prompt_set = True
                        
                        logging.info(f"已使用角色 {role_id} 的提示词替换系统消息")
                    except Exception as e:
                        logging.error(f"构建角色系统提示词时出错: {str(e)}")
                        # 出错时继续使用默认的系统提示词逻辑
            # ---> END MODIFICATION <---
            
            if selected_servers and len(selected_servers) > 0:
                # 根据选定的服务器获取工具列表
                try:
                    from backend.mcp.server_manager import mcp_server
                    # 确保 selected_servers 是服务器名称列表
                    server_names = []
                    for server in selected_servers:
                        if isinstance(server, str):
                            server_names.append(server)
                        elif isinstance(server, dict) and 'name' in server:
                            server_names.append(server['name'])
                    
                    if server_names:
                        # logging.info(f"获取服务器工具列表: {server_names}")
                        # 获取这些服务器的工具列表
                        mcp_tools = await mcp_server.list_tools_from_servers(server_names)
                        
                        # 确保每个工具都有 serverName 属性
                        for tool in mcp_tools:
                            if 'name' in tool and '_' in tool['name']:
                                server_name, tool_name = tool['name'].split('_', 1)
                                if 'serverName' not in tool:
                                    tool['serverName'] = server_name
                        
                        # 用于MCP提示词的工具列表
                        functions = mcp_tools
                    else:
                        logging.warning("选定的服务器列表格式不正确")
                except Exception as e:
                    logging.error(f"获取MCP工具时出错: {str(e)}")
                

                
                #logger.info(f"functions: {functions}")
                # 如果没有系统消息，创建一个
                if not has_system_message:
                    # 使用标准MCP提示词
                    try:
                        # 将工具列表和服务器名称传递给get_mcp_prompt
                        # ---> MODIFICATION: Update keyword list <--- 
                        user_last_message = messages[-1].get("content", "").lower() if messages and messages[-1].get("role") == "user" else ""
                        include_html = any(keyword in user_last_message for keyword in ["html", "网页", "网站", "前端", "界面", "UI", "UX", "design", "layout", "设计", "布局"])
                        mcp_prompt = get_mcp_prompt(functions, is_agent_mode=is_agent, include_html_guidelines=include_html)
                    except ImportError:
                        mcp_prompt = ""

                    # 添加系统提示词，不再包含重复的tools_description
                    system_message = {
                        "role": "system",
                        "content":f"{mcp_prompt}\n\n" +
                                  f"{tavily_prompt}\n\n" +
                                  "When the user's question requires the use of tools, please use the tools above. If tools are not needed, answer directly."
                    }

                    messages = [system_message] + messages
                # 如果已有系统消息且不是通过角色ID设置的，更新第一个系统消息
                # 🔥 修复：LangGraph智能体不覆盖系统提示词
                elif not role_prompt_set and role_id != "langgraph_agent":
                    for i, message in enumerate(messages):
                        if message.get('role') == 'system':
                            original_content = message.get('content', '')

                            # 使用标准MCP提示词
                            try:
                                # 将工具列表和服务器名称传递给get_mcp_prompt
                                # ---> MODIFICATION: Update keyword list <--- 
                                user_last_message = messages[-1].get("content", "").lower() if messages and messages[-1].get("role") == "user" else ""
                                include_html = any(keyword in user_last_message for keyword in ["html", "网页", "网站", "前端", "界面", "UI", "UX", "design", "layout", "设计", "布局"])
                                mcp_prompt = get_mcp_prompt(functions, is_agent_mode=is_agent, include_html_guidelines=include_html)
                            except ImportError:
                                mcp_prompt = ""

                            # 检查是否已经包含工具提示词
                            if "tools:" not in original_content.lower() and "工具:" not in original_content:
                                # 更新系统提示词
                                messages[i]['content'] = original_content + f"\n\nYou can use the following tools provided by MCP servers:\n\n{mcp_prompt}"
                                                            # 检查是否需要添加Tavily搜索提示词
                                if is_web_search_enabled and "WEB SEARCH CAPABILITY" not in original_content:
                                    messages[i]['content'] = messages[i]['content'] + f"\n\n{tavily_prompt}"
                                
                                # 添加代码解释器提示词
                                # 🔥 修复：LangGraph智能体不添加代码解释器提示词
                                if role_id != "langgraph_agent":
                                    original_system_prompt = messages[i]['content']
                                    messages[i]['content'] = get_system_prompt_with_code_interpreter(original_system_prompt,is_code_interpreter_enabled)
                                    logging.info("已将代码解释器提示词添加到系统消息")
                                else:
                                    logging.info("LangGraph智能体跳过代码解释器提示词添加")
                                break
            else:
                # 如果没有工具但启用了网络搜索，仍然需要添加Tavily提示词
                if is_web_search_enabled:
                    # 导入Tavily提示词
                    try:
                        # 检查是否有系统消息
                        # 🔥 修复：LangGraph智能体不覆盖系统提示词
                        if has_system_message and not role_prompt_set and role_id != "langgraph_agent":
                            for i, message in enumerate(messages):
                                if message.get('role') == 'system':
                                    original_content = message.get('content', '')
                                    # 检查是否已经包含网络搜索提示词
                                    if "WEB SEARCH CAPABILITY" not in original_content:
                                        messages[i]['content'] = original_content + f"\n\n{tavily_prompt}"
                                    # 添加代码解释器提示词
                                    # 🔥 修复：LangGraph智能体不添加代码解释器提示词
                                    if role_id != "langgraph_agent":
                                        original_system_prompt = messages[i]['content']
                                        messages[i]['content'] = get_system_prompt_with_code_interpreter(original_system_prompt,is_code_interpreter_enabled)
                                        logging.info("已将代码解释器提示词添加到系统消息")
                                    else:
                                        logging.info("LangGraph智能体跳过代码解释器提示词添加")
                                    break

                        # 如果没有系统消息，创建一个
                        if not has_system_message:
                            # 添加代码解释器提示词到系统消息
                            # 🔥 修复：LangGraph智能体不添加代码解释器提示词
                            if role_id != "langgraph_agent":
                                system_content = f"You are a helpful assistant.\n\n{tavily_prompt}"
                                system_content = get_system_prompt_with_code_interpreter(system_content,is_code_interpreter_enabled)
                                system_message = {
                                    "role": "system",
                                    "content": system_content
                                }
                                messages = [system_message] + messages
                                logging.info("已创建带有代码解释器的新系统消息")
                            else:
                                # LangGraph智能体使用简单的系统消息
                                system_message = {
                                    "role": "system",
                                    "content": f"You are a helpful assistant.\n\n{tavily_prompt}"
                                }
                                messages = [system_message] + messages
                                logging.info("LangGraph智能体创建简单系统消息（跳过代码解释器）")
                    except ImportError:
                        logging.error("导入Tavily搜索提示词失败")
                else:
                    # logging.info("未启用网络搜索功能")
                    if not has_system_message:
                        # 创建包含代码解释器的系统消息
                        # 🔥 修复：LangGraph智能体不添加代码解释器提示词
                        if role_id != "langgraph_agent":
                            system_content = get_system_prompt_with_code_interpreter("You are a helpful assistant.",is_code_interpreter_enabled)
                            system_message = {
                                "role": "system",
                                "content": system_content
                            }
                            messages = [system_message] + messages
                            logging.info("已创建默认系统消息，包含代码解释器提示词")
                        else:
                            # LangGraph智能体使用简单的系统消息
                            system_message = {
                                "role": "system",
                                "content": "You are a helpful assistant."
                            }
                            messages = [system_message] + messages
                            logging.info("LangGraph智能体创建简单默认系统消息（跳过代码解释器）")
                    # 🔥 修复：LangGraph智能体不覆盖系统提示词
                    elif not role_prompt_set and role_id != "langgraph_agent":
                        for i, message in enumerate(messages):
                            if message.get('role') == 'system':
                                original_content = message.get('content', '')
                                # 🔥 修复：再次检查role_id，确保不为LangGraph智能体添加代码解释器
                                if role_id != "langgraph_agent":
                                    messages[i]['content'] = get_system_prompt_with_code_interpreter(original_content,is_code_interpreter_enabled)
                                    logging.info("已将代码解释器提示词添加到系统消息")
                                else:
                                    logging.info("LangGraph智能体跳过代码解释器提示词添加")
                                break
            # logging.info(f"messages: {messages}")
            
            # 🔥 调试日志：打印最终发送给模型的完整系统消息
            for msg in messages:
                if msg.get('role') == 'system':
                    # logger.warning(f"🚨 [SYSTEM PROMPT DEBUG] 🚨\n--- START ---\n{msg.get('content')}\n--- END ---")
                    break

            # 处理工具调用逻辑
            if handle_tools:
                
                tool_calls_count = 0
                tool_calls_history = []
                
                # ---> 在循环外初始化最终的 response 字典 <--- 
                final_task_response = {}
                
                # ---> 修改：根据 is_agent 调整最大工具调用次数 <--- 
                effective_max_tool_calls = max_tool_calls # 默认使用传入的值
                if is_agent:
                    effective_max_tool_calls = 50 # Agent模式下设置一个非常高的上限
                    logger.debug(f"Agent模式启用，将最大工具调用次数上限提高到 {effective_max_tool_calls}")
                # ---> 结束修改 <--- 
                
                # ---> 修改：使用 effective_max_tool_calls <--- 
                parsing_correction_attempts = 0 # Initialize correction attempts counter
                empty_response_count = 0 # 🔥 熔断器：追踪连续空响应的次数
                
                while tool_calls_count < effective_max_tool_calls:
                    # 检查是否有取消信号
                    if is_cancelled and is_cancelled():
                        # logging.info("检测到取消信号，停止工具调用循环")

                        # 创建一个包含当前状态的响应，表示操作被取消
                        final_response = {
                            "content": "操作已被用户取消",
                            "cancelled": True,
                            "tool_calls_history": tool_calls_history
                        }

                        # 恢复原始模型设置
                        if original_model is not None:
                            service.model = original_model

                        return final_response

                    # 调用模型获取响应，传入max_tokens参数，使用重试机制
                    #logger.info(f"messages: {messages}")
                    # logger.info(f"messages: {messages}")
                    # logger.info(f"max_tokens: {max_tokens}")
                    response = await self._call_service_with_simple_retry(
                        service=service,
                        messages=messages,
                        functions=functions,
                        stream_callback=stream_callback,  
                        max_tokens=max_tokens,  
                        is_cancelled=is_cancelled,
                        thinking_budget=thinking_budget,
                        retry_context="主要AI聊天调用",
                        max_retries=3
                    )
                    
                    # 🔥 熔断机制：检查模型是否返回空内容
                    content = response.get("content", "").strip()
                    if not content:
                        empty_response_count += 1
                        logger.warning(f"模型返回空内容，连续空响应次数: {empty_response_count}")
                        if empty_response_count >= 3:
                            logger.error("模型连续3次返回空内容，中断操作。")
                            response["error"] = "模型连续返回空响应，执行中断。"
                            # 确保将错误信息传播出去
                            final_task_response["content"] = ""
                            final_task_response["error"] = response["error"]
                            break # 强制跳出循环
                        
                        # 准备重试，但不需要将空响应加入历史
                        messages.append({
                            "role": "user",
                            "content": "你刚才返回了一个空响应，请重新生成回复。"
                        })
                        continue # 进入下一次循环重试
                    else:
                        # 如果有内容，重置空响应计数器
                        empty_response_count = 0

                    if is_cancelled and is_cancelled():
                        logging.info("AI异步请求在处理数据时被用户取消")
                        return {"error": "请求已取消", "content": "", "cancelled": True}
                                        
                    # logging.info(f"messages: {messages}")
                    # logging.info(f"response: {response}")
                    if "error" in response:
                         # ---> 在返回前恢复模型 <---
                         if original_model is not None:
                            service.model = original_model
                         return response
                    
                    content = response.get("content", "")
                    model_response ={
                        "role": "assistant",
                        "content": content
                    }
                    # ---> 将 AI 响应添加到 messages 列表 <---
                    messages.append(model_response)
                    # logging.info(f"response: {response}")
                    # 检查是否有 OpenAI 函数调用格式的工具调用
                    if "function_call" in response:
                        parsing_correction_attempts = 0 # Reset on valid non-XML tool path
                        tool_calls_count = await self._function_call(messages, model, response, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service)
                        continue
                    
                    # 使用增强的工具调用解析方法
                    tool_calls = self._parse_tool_calls_enhanced(content)
                    
                    # logging.info(f"content: {content}")
                    # logger.info(f"parsed tool_calls: {tool_calls}")
                    
                    if tool_calls:
                        parsing_correction_attempts = 0 # Reset: successfully parsed valid tool calls
                        
                        # 如果有多个工具调用，记录警告
                        if len(tool_calls) > 1:
                            logging.warning(f"检测到{len(tool_calls)}个工具调用，建议AI模型遵循一次一个工具的原则")
                            if stream_callback:
                                try:
                                    warning_msg = f"\n⚠️ 检测到{len(tool_calls)}个工具调用，将按顺序执行\n\n"
                                    await stream_callback(warning_msg)
                                except Exception as cb_err:
                                    logging.warning(f"发送多工具调用警告时出错: {cb_err}")
                        
                        # 处理多个工具调用
                        for i, tool_call in enumerate(tool_calls):
                            # 检查是否有取消信号
                            if is_cancelled and is_cancelled():
                                logging.info("检测到取消信号，停止多工具调用处理")
                                break
                            
                            # 如果是多工具调用，显示当前处理的工具序号
                            if len(tool_calls) > 1 and stream_callback:
                                try:
                                    progress_msg = f"🔧 执行工具 {i+1}/{len(tool_calls)}: {tool_call.get('name', tool_call.get('type', 'unknown'))}\n\n"
                                    await stream_callback(progress_msg)
                                except Exception as cb_err:
                                    logging.warning(f"发送工具执行进度时出错: {cb_err}")
                            
                            if tool_call["type"] == "tool":
                                tool_calls_count = await self._tool(messages, tool_call, model, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service)
                            elif tool_call["type"] == "resource":
                                tool_calls_count = await self._resource(messages, tool_call, model, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service)
                            elif tool_call["type"] == "tavily_search":
                                tool_calls_count = await self._tavily_search(messages, tool_call, model, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service)
                            elif tool_call["type"] == "code_execution":
                                # 直接处理代码执行
                                code = tool_call["code"]
                                tool_calls_count = await self.code_interpreter(messages, code, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service)
                        
                        # 如果是多工具调用，显示完成总结
                        if len(tool_calls) > 1 and stream_callback:
                            try:
                                completion_msg = f"\n✅ 已完成 {len(tool_calls)} 个工具调用的执行\n\n"
                                await stream_callback(completion_msg)
                            except Exception as cb_err:
                                logging.warning(f"发送工具执行完成总结时出错: {cb_err}")
                        
                        # 如果处理了工具调用，继续循环
                        continue
                    else:
                        # No valid XML tool call was parsed by parse_xml_tool_calls.
                        
                        # First, check if this looks like a deliberate final text response from the AI.
                        if self._is_likely_final_text_response(content):
                            logger.info(f"AI output appears to be a final text response. Treating as final. Content: '{content[:200]}...'")
                            # Treat as final text, break the loop
                            final_task_response["content"] = content
                            parsing_correction_attempts = 0 # Reset attempts as turn is ending
                            continue # Exit tool calling loop.

                        # If not a final text response, check if it's a potentially malformed XML tool attempt.
                        is_potential_malformed = self._is_potential_malformed_xml(content) 
                        logger.info(f"parse_xml_tool_calls returned None. AI output not deemed final text. Heuristic malformed XML check: {is_potential_malformed}. Content: '{content[:200]}...'")

                        # Only attempt correction if it looks like a malformed XML AND correction attempts are available.
                        if is_potential_malformed and parsing_correction_attempts < self.MAX_PARSING_CORRECTION_ATTEMPTS:
                            logger.info(f"AI output is potentially a malformed XML tool call. Attempting correction ({parsing_correction_attempts + 1}/{self.MAX_PARSING_CORRECTION_ATTEMPTS}).")
                            parsing_correction_attempts += 1

                            # 使用增强的修正提示，包含新的格式要求
                            correction_prompt_content = self._build_xml_correction_prompt_enhanced(content)
                            
                            # Add our correction prompt to messages.
                            # The AI's previous response is messages[-1].
                            messages.append({"role": "user", "content": correction_prompt_content})
                            
                            # Call LLM again for correction
                            # Ensure `functions` (the list of available MCP tools/schemas) is passed correctly
                            # Use a distinct service call if necessary, or ensure chat_completion handles this internal call correctly.
                            # Assuming chat_completion is suitable for internal calls with functions.
                            corrected_llm_response = await service.chat_completion(
                                messages=messages, # Messages includes the correction prompt
                                functions=functions, 
                                stream_callback=None, # No streaming for this internal correction attempt
                                max_tokens=max_tokens, 
                                is_cancelled=is_cancelled, # Pass cancellation signal
                                thinking_budget=thinking_budget 
                            )

                            # After getting the corrected response, remove the correction prompt from messages.
                            messages.pop() 

                            if is_cancelled and is_cancelled():
                                logger.info("AI XML correction call was cancelled by the user.")
                                # Ensure consistent cancellation handling:
                                # Construct a response indicating cancellation and return it.
                                final_response_cancelled = {
                                    "content": "操作在XML修正期间已被用户取消",
                                    "cancelled": True,
                                    "tool_calls_history": tool_calls_history,
                                    "full_message_history": messages
                                }
                                if original_model is not None:
                                    service.model = original_model
                                return final_response_cancelled # Directly return, exiting chat_completion

                            if "error" in corrected_llm_response:
                                logger.warning(f"Error occurred during XML correction LLM call: {corrected_llm_response['error']}. Treating original malformed XML as final content.")
                                # If correction LLM call failed, treat the original malformed content as final.
                                final_task_response["content"] = content # Original malformed content
                                # No need to increment tool_calls_count here as it wasn't a successful tool call.
                                break # Exit tool calling loop

                            new_ai_content = corrected_llm_response.get("content", "")
                            
                            # Update the AI's previous message (which was malformed) with the new, hopefully corrected, content.
                            if messages and messages[-1]["role"] == "assistant":
                                # Modify the last message in place, assuming it was the AI's malformed one.
                                messages[-1]['content'] = new_ai_content 
                            else: 
                                # Should not happen if AI responded before entering this block, but as a safeguard
                                messages.append({"role": "assistant", "content": new_ai_content})
                                
                            content = new_ai_content # Update current `content` variable for next iteration's parsing.
                            response["content"] = new_ai_content # Also update the response dict.

                            logger.info("AI provided a correction. Retrying XML parsing with the new content.")
                            # Continue the loop to re-evaluate the *new* content from the AI.
                            # This might now be a valid XML tool call or a final text response.
                            continue 
                        else:
                            # This branch is hit if:
                            # 1. parse_xml_tool_calls returned None.
                            # 2. It was *not* deemed a likely final text response by _is_likely_final_text_response.
                            # 3. It was *not* deemed a potentially malformed XML by _is_potential_malformed_xml, OR max correction attempts were reached.

                            # Treat current `content` as the final assistant response for this turn.
                            if is_potential_malformed: # If it was malformed but attempts exhausted
                                 logger.warning(f"Max parsing correction attempts ({self.MAX_PARSING_CORRECTION_ATTEMPTS}) reached for potentially malformed XML. Treating current content as final: '{content[:200]}...'")
                            else: # It was just normal text that wasn't deemed a final sign-off
                                logger.info(f"parse_xml_tool_calls returned None, AI output not final text or malformed XML. Treating as final content for this turn: '{content[:200]}...'")

                            final_task_response["content"] = content
                            parsing_correction_attempts = 0 # Reset attempts as turn is ending
                            break # Exit tool calling loop.
                
                # ---> 循环结束后，将工具历史添加到 final_task_response <--- 
                if tool_calls_history:
                    final_task_response["tool_calls_history"] = tool_calls_history
                
                # ---> 处理达到最大调用次数的情况 <--- 
                # ---> 修改：使用 effective_max_tool_calls <--- 
                # if tool_calls_count >= effective_max_tool_calls:
                #     warning_message = f"达到最大工具调用次数 ({effective_max_tool_calls})，停止进一步调用"
                #     logger.warning(warning_message)
                    
                #     # 添加警告消息到 messages
                #     messages.append({
                #         "role": "system",
                #         "content": warning_message
                #     })
                    
                #     # ---> 修改：移除调用 service.chat_completion 时的 handle_tools 参数 <--- 
                #     # 重新获取模型最终响应 (不处理工具)
                #     final_llm_response = await service.chat_completion(
                #         messages=messages,
                #         functions=functions,
                #         stream_callback=None,
                #         # handle_tools=False, # <-- 移除此参数
                #         max_tokens=max_tokens  
                #     )
                #     # ---> 结束修改 <--- 
                    
                #     # 合并响应
                #     final_task_response["content"] = final_llm_response.get("content", "")
                #     final_task_response["max_tool_calls_reached"] = True
                #     # 将最终的LLM响应也添加到messages历史中
                #     if final_llm_response.get("content"):
                #         messages.append({"role":"assistant", "content": final_llm_response["content"]})
                
                # ---> 将完整的 messages 历史添加到最终响应 <--- 
                final_task_response["full_message_history"] = messages
                
                # 循环结束后，如果需要，发送最终的 AI 响应 (包含content和工具历史)
                # ---> 添加 is_agent 条件 <---
                if is_agent:
                    # 检查最终的 response 是否包含内容或工具调用历史
                    final_content = final_task_response.get("content", "")
                    if final_content or tool_calls_history:
                        final_response_message = {
                            "type": "ai_response", # 区分于流式块
                            "content": final_content,
                            "tool_calls_history": tool_calls_history,
                            "full_message_history": messages # 也包含最终历史
                        }
                        if stream_callback:
                            try:
                                # ---> Send stream_end signal BEFORE the final response <--- 
                                logger.info("Attempting to send stream_end signal...") # LOG BEFORE
                                stream_end_signal = {"type": "stream_end"}
                                await stream_callback(stream_end_signal)
                                logger.debug(f"Successfully sent stream_end 信号") # LOG AFTER SUCCESS
                                
                                logger.info("Attempting to send agent_final_stream_message...") # LOG BEFORE
                                agent_final_stream_message = {
                                    "type": "ai_response",
                                    **final_response_message 
                                }
                                await stream_callback(agent_final_stream_message)
                                logger.debug(f"Successfully sent最终 Agent 模式 ai_response 消息") # LOG AFTER SUCCESS
                            except Exception as cb_err:
                                logger.error(f"CRITICAL ERROR during final stream send: {cb_err}", exc_info=True) # Use ERROR level
                # ---> 结束 is_agent 条件 <---
                else:
                    # 非 Agent 模式下，最终结果已通过流式块发送，此处无需额外操作
                    # 但仍然需要返回包含完整历史的字典
                    final_response_message = {
                            "content": final_task_response.get("content", ""),
                            "tool_calls_history": tool_calls_history,
                            "full_message_history": messages
                        }

            else: # 不处理工具 (可能是普通模式或Agent模式但未调用工具)
                # Call the service directly
                response = await service.chat_completion(
                    messages=messages,
                    functions=functions, # Still pass functions? Maybe not needed if handle_tools is False.
                    stream_callback=stream_callback,
                    max_tokens=max_tokens,
                    is_cancelled=is_cancelled,
                    thinking_budget=thinking_budget
                )

                # ... (Error handling) ...

                # Construct the dictionary to be returned
                final_content = response.get("content")
                assistant_message = {"role": "assistant", "content": final_content}
                messages.append(assistant_message) # Add final assistant reply to history

                final_response_message = {
                    "content": final_content,
                    "tool_calls_history": [], # No tool calls in this branch
                    "full_message_history": messages # Contains initial + final assistant message
                }

            # --- Centralized sending logic for Agent mode ---
            if is_agent:
                # Only send the final aggregated message if in agent mode
                if stream_callback:
                    try:
                        # Add the 'type' only when sending via stream for agent
                        # ---> Send stream_end signal BEFORE the final response <--- 
                        stream_end_signal = {"type": "stream_end"}
                        # await stream_callback(stream_end_signal) # 移除 await
                        await stream_callback(stream_end_signal)
                        logger.debug(f"发送 stream_end 信号")
                        # ---> End sending stream_end signal <---
                        
                        agent_final_stream_message = {
                            "type": "ai_response",
                            **final_response_message # Spread the content, tool_history, full_history
                        }
                        # await stream_callback(agent_final_stream_message) # 移除 await
                        await stream_callback(agent_final_stream_message)
                        logger.debug(f"发送最终 Agent 模式 ai_response 消息: {json.dumps(agent_final_stream_message, default=str, ensure_ascii=False)}")
                    except Exception as cb_err:
                        logger.warning(f"发送最终 Agent 模式 ai_response 或 stream_end 回调时出错: {cb_err}")
            
            # --- Always return the full response dictionary ---
            return final_response_message

        except asyncio.CancelledError:
            # ---> Fix: Add pass or actual handling if needed <--- 
            logger.info("AI Manager chat completion 被取消.")
            raise # Re-raise to allow upper layers to handle
        except Exception as e:
            # ---> Fix: Add pass or actual handling <--- 
            logger.exception(f"AI Manager chat completion 出现错误: {e}")
            # Construct or return an error message as needed
            error_content = f"处理请求时发生错误: {e}"
            # Ensure messages is defined in this scope if needed, or handle differently
            # If messages list exists from the try block:
            # messages.append({"role": "assistant", "content": error_content})
            return {
                 "content": error_content,
                 "tool_calls_history": [],
                 # "full_message_history": messages, # Be cautious if messages might not exist here
                 "error": str(e)
            }
        finally:
            # ---> Fix: Add pass or actual handling <--- 
            # Restore original model if it was changed
            if original_model is not None and 'service' in locals(): # Check if service exists
                service.model = original_model
            # pass # Add pass if no other code is needed here
    
    async def function_call(self, messages: List[Dict], 
                   functions: List[Dict],
                   service_name: Optional[str] = None,
                   model: Optional[str] = None):
        """
        执行函数调用
        
        Args:
            messages: 消息列表
            functions: 函数列表
            service_name: 服务名称
            model: 模型名称
            
        Returns:
            函数调用响应
        """
        # 获取服务
        service = self._get_service(service_name)
        
        # 执行聊天补全(带函数调用)
        response = await service.chat_completion(
            messages=messages,
            functions=functions,
            handle_tools=False # Function call shouldn't trigger tool loops here
        )
        
        return response
    async def code_interpreter(self, messages, code: str, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service) -> int:
        """执行代码解释器"""
        # 创建工具调用上下文
        ctx = ToolCallContext(
            messages=messages,
            model=None,  # 代码解释器不需要模型信息
            tool_calls_history=tool_calls_history,
            is_cancelled=is_cancelled,
            is_agent=is_agent,
            stream_callback=stream_callback,
            original_model=original_model,
            content=content,
            tool_calls_count=tool_calls_count,
            service=service
        )
        
        # 创建工具信息字典
        tool_info = {
            "name": "code_interpreter",
            "call_record": {
                "type": "code_execution",
                "code": code
            },
            "result_record": lambda result: {
                "type": "code_execution_result",
                "result": result
            },
            "error_record": lambda error: {
                "type": "code_execution_error",
                "error": error
            },
            "agent_result_message": lambda result, idx: {
                "type": "code_execution_result",
                "name": "code_interpreter",
                "parameters": {"code": code},
                "result": result,
                "status": "completed",
                "isSuccess": True,
                "tool_call_index": idx
            },
            "agent_error_message": lambda error, idx: {
                "type": "code_execution_result",
                "name": "code_interpreter",
                "parameters": {"code": code},
                "result": error,
                "status": "failed",
                "isSuccess": False,
                "tool_call_index": idx
            },
            "normal_result_message": lambda result: self._format_code_result(code, result),
            "normal_error_message": lambda error: f"\n\n**Code Execution Error:**\n```\n{str(error.get('error', ''))}\n```\n\n"
        }
        
        # 定义执行函数
        async def execute_func():
            result = await execute_code(code)
            # logging.info(f"result: {result}")
            
            # 处理结果对象，确保有正确的格式
            if isinstance(result, dict) and 'content' in result:
                # 记录结果长度
                result_content = result['content']
                # logging.info(f"代码执行完成，结果长度: {len(str(result_content))}")
                # 直接使用字典结果
                return {'content': result_content}
            else:
                # 尝试将任何内容转化为字符串
                result_content = str(result)
                # logging.info(f"代码执行完成，转换后的结果长度: {len(result_content)}")
                return {'content': result_content}
        
        # 使用通用工具执行处理方法
        return await self._handle_tool_execution(ctx, "代码执行", tool_info, execute_func)
    
    def _format_code_result(self, code: str, result: Dict) -> str:
        """格式化代码执行结果输出"""
        content_to_display = result.get('content', '')
        # 检查代码解释器返回的内容是否已经包含了代码块格式
        if content_to_display and isinstance(content_to_display, str):
            if content_to_display.strip().startswith('stdout:') or content_to_display.strip().startswith('stderr:'):
                # 已经格式化好的结果，直接发送
                return f"**执行命令**\n{code}\n\n**Code Execution Result:**\n{content_to_display}\n\n"
            else:
                # 未格式化的结果，添加代码块
                return f"**执行命令**\n{code}\n\n**Code Execution Result:**\n```\n{content_to_display}\n```\n\n"
        return f"**执行命令**\n{code}\n\n**Code Execution Result:**\n```\n{str(content_to_display)}\n```\n\n"

    def _is_error_result(self, result) -> Tuple[bool, Optional[str]]:
        """判断返回结果是否包含错误信息
        
        Args:
            result: 工具调用返回结果
            
        Returns:
            (是否为错误, 错误信息)
        """
        # 检查结果是否为字符串且包含错误标识
        if isinstance(result, str) and result.startswith("Error:"):
            return True, result
        if isinstance(result, str) and result.startswith("Invalid "):
            return True, result
        # 检查结果是否为字典且包含错误字段
        if isinstance(result, dict) and "error" in result:
            error_msg = result["error"]
            if isinstance(error_msg, str):
                return True, error_msg
        
        return False, None

    async def _tool(self, messages, tool_call, model, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service) -> int:
        """处理XML格式的工具调用（带错误重试功能）"""
        return await self._tool_with_retry(messages, tool_call, model, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service)

    async def _tool_with_retry(self, messages, tool_call, model, tool_calls_history, is_cancelled, is_agent, stream_callback, original_model, content, tool_calls_count, service, max_retries=3) -> int:
        """处理XML格式的工具调用，支持错误重试机制
        
        当工具调用失败时，会：
        1. 获取工具的正确格式信息
        2. 构造包含错误信息的重试提示
        3. 让模型重新生成正确的工具调用
        4. 最多重试3次
        """
        tool_name = tool_call["name"]
        tool_args = tool_call["arguments"]
        server_name = tool_call["server_name"]
        
        # 创建工具调用上下文
        ctx = ToolCallContext(
            messages=messages,
            model=model,
            tool_calls_history=tool_calls_history,
            is_cancelled=is_cancelled,
            is_agent=is_agent,
            stream_callback=stream_callback,
            original_model=original_model,
            content=content,
            tool_calls_count=tool_calls_count,
            service=service
        )
        
        # 创建工具信息字典
        tool_info = {
            "name": tool_name,
            "call_record": {
                "type": "xml_tool_call",
                "name": tool_name,
                "server": server_name,
                "arguments": tool_args
            },
            "result_record": lambda result: {
                "type": "xml_tool_result",
                "name": tool_name,
                "server": server_name,
                "result": result
            },
            "error_record": lambda error: {
                "type": "xml_tool_error",
                "name": tool_name,
                "server": server_name,
                "error": error
            },
            "agent_result_message": lambda result, idx: {
                "type": "function_result",
                "name": tool_name,
                "parameters": json.loads(tool_args) if isinstance(tool_args, str) else tool_args,
                "result": result,
                "status": "completed",
                "isSuccess": True,
                "tool_call_index": idx
            },
            "agent_error_message": lambda error, idx: {
                "type": "function_result",
                "name": tool_name,
                "parameters": json.loads(tool_args) if isinstance(tool_args, str) else tool_args,
                "result": error,
                "status": "failed",
                "isSuccess": False,
                "tool_call_index": idx
            },
            "normal_result_message": lambda result: f"\n\n**Tool Call:** {tool_name}\n```json\n{tool_args}\n```\n\n**Tool Result:**\n```json\n{json.dumps(result, ensure_ascii=False, indent=2)}\n```\n\n",
            "normal_error_message": lambda error: f"\n\n**Tool Call Error:** {tool_name}\n```json\n{json.dumps(error, ensure_ascii=False, indent=2)}\n```\n\n"
        }
        retry_messages = messages.copy()
        # 重试逻辑
        for retry_count in range(max_retries + 1):
            try:
        # 定义执行函数
                async def execute_func():
                    # 🧠 检查是否为记忆工具调用
                    if server_name == "memory":
                        return await self._execute_memory_tool(tool_name, tool_args, ctx)
                    else:
                        return await mcp_server.call_tool_from_server(server_name, tool_name, tool_args)
        
                # 直接执行工具调用获取结果
                result = await execute_func()
                
                # 检查结果是否包含错误信息
                is_error, error_msg = self._is_error_result(result)
                
                if is_error:
                    # 如果结果包含错误信息，则进行重试逻辑
                    logging.warning(f"XML工具调用返回错误 (第{retry_count + 1}次尝试): {error_msg}")
                    
                    # 如果已经是最后一次重试，或者是取消操作，直接返回错误
                    if retry_count >= max_retries or (is_cancelled and is_cancelled()):
                        logging.error(f"XML工具调用最终失败，已重试{retry_count}次: {error_msg}")
                        
                        # 记录最终失败
                        tool_calls_history.append(tool_info["error_record"](f"工具调用失败，已重试{retry_count}次: {error_msg}"))
                        
                        # 发送最终失败消息
                        if stream_callback:
                            try:
                                if is_agent:
                                    error_chunk = tool_info["agent_error_message"]({"error": f"工具调用失败，已重试{retry_count}次: {error_msg}"}, tool_calls_count)
                                    await stream_callback(error_chunk)
                                else:
                                    normal_error = tool_info["normal_error_message"]({"error": f"工具调用失败，已重试{retry_count}次: {error_msg}"})
                                    await stream_callback(normal_error)
                            except Exception as cb_err:
                                logging.warning(f"发送最终错误回调时出错: {cb_err}")
                        
                        # 增加工具调用计数
                        return tool_calls_count + 1
                    if stream_callback:
                        await stream_callback(f"\n\n工具调用返回错误，正在进行第{retry_count+1}次重试\n")
                    # 获取工具的正确格式信息
                    tool_schema = await self._get_tool_schema(server_name, tool_name)
                    if not tool_schema:
                        logging.warning(f"无法获取工具 {server_name}.{tool_name} 的格式信息，跳过重试")
                        continue
                    
                    # 构造重试提示
                    retry_prompt = self._build_retry_prompt(tool_name, server_name, tool_args, error_msg, tool_schema, retry_count)
                    #logger.info(f"retry_prompt: {retry_prompt}")
                    # 创建重试消息（不保存到历史记录）
                    retry_messages.append({
                        "role": "user",
                        "content": retry_prompt
                    })
                    
                    # 调用AI服务重新生成工具调用
                    retry_response = await service.chat_completion(
                        messages=retry_messages,
                        max_tokens=2000,
                        stream_callback=None,
                        is_cancelled=is_cancelled
                    )
                    content = retry_response.get("content", "")
                    retry_messages.append({
                        "role": "assistant",
                        "content": content
                    })
                    #logger.info(f"retry_response: {content}")
                    # 解析新的工具调用
                    retry_content = content
                    new_tool_call = parse_xml_tool_calls(retry_content)
                    
                    if not new_tool_call or new_tool_call.get("type") != "tool":
                        logging.warning(f"重试时未能解析出有效的工具调用，跳过第{retry_count + 1}次重试")
                        continue
                    
                    # 更新工具调用参数
                    tool_call["arguments"] = new_tool_call["arguments"]
                    tool_args = new_tool_call["arguments"]
                    
                    # 更新工具信息
                    tool_info["call_record"]["arguments"] = tool_args
                    
                    logging.info(f"已生成新的工具调用参数，准备第{retry_count + 2}次尝试")
                else:
                    # 如果结果没有错误，使用通用工具执行处理方法处理结果
                    # 创建一个新的执行函数，直接返回已获取的结果
                    async def return_result():
                        return result
                    
                    return await self._handle_tool_execution(ctx, "XML工具调用", tool_info, return_result)
                    
            except Exception as e:
                # 异常处理逻辑（与原来相同）
                error_msg = str(e)
                logging.warning(f"XML工具调用异常 (第{retry_count + 1}次尝试): {error_msg}")
                
                # 如果已经是最后一次重试，或者是取消操作，直接抛出异常
                if retry_count >= max_retries or (is_cancelled and is_cancelled()):
                    logging.error(f"XML工具调用最终失败，已重试{retry_count}次: {error_msg}")
                    # 记录最终失败
                    tool_calls_history.append(tool_info["error_record"](f"工具调用失败，已重试{retry_count}次: {error_msg}"))
                    
                    # 发送最终失败消息
                    if stream_callback:
                        try:
                            if is_agent:
                                error_chunk = tool_info["agent_error_message"]({"error": f"工具调用失败，已重试{retry_count}次: {error_msg}"}, tool_calls_count)
                                await stream_callback(error_chunk)
                            else:
                                normal_error = tool_info["normal_error_message"]({"error": f"工具调用失败，已重试{retry_count}次: {error_msg}"})
                                await stream_callback(normal_error)
                        except Exception as cb_err:
                            logging.warning(f"向客户端发送错误回调时出错: {cb_err}")
                    
                    # 增加工具调用计数
                    return tool_calls_count + 1
                if stream_callback:
                    await stream_callback(f"\n\n工具调用异常，正在进行第{retry_count+1}次重试\n")
                # 获取工具的正确格式信息和重试逻辑（与原来相同）
                try:
                    tool_schema = await self._get_tool_schema(server_name, tool_name)
                    if not tool_schema:
                        logging.warning(f"无法获取工具 {server_name}.{tool_name} 的格式信息，跳过重试")
                        continue
                    
                    # 构造重试提示
                    retry_prompt = self._build_retry_prompt(tool_name, server_name, tool_args, error_msg, tool_schema, retry_count)
                    logger.info(f"retry_prompt: {retry_prompt}")
                    # 创建重试消息（不保存到历史记录）
                    
                    retry_messages.append({
                        "role": "user",
                        "content": retry_prompt
                    })
                    
                    # 调用AI服务重新生成工具调用
                    retry_response = await service.chat_completion(
                        messages=retry_messages,
                        max_tokens=2000,
                        stream_callback=None,
                        is_cancelled=is_cancelled
                    )
                    content = retry_response.get("content", "")
                    retry_messages.append({
                        "role": "assistant",
                        "content": content
                    })
                    logger.info(f"retry_response: {content}")
                    new_tool_call = parse_xml_tool_calls(content)
                    
                    if not new_tool_call or new_tool_call.get("type") != "tool":
                        logging.warning(f"重试时未能解析出有效的工具调用，跳过第{retry_count + 1}次重试")
                        continue
                    
                    # 更新工具调用参数
                    tool_call["arguments"] = new_tool_call["arguments"]
                    tool_args = new_tool_call["arguments"]
                    
                    # 更新工具信息
                    tool_info["call_record"]["arguments"] = tool_args
                    
                    logging.info(f"已生成新的工具调用参数，准备第{retry_count + 2}次尝试")
                    
                except Exception as retry_error:
                    logging.error(f"构造重试时出错: {str(retry_error)}")
                    continue
        
        # 这里不应该执行到，因为上面的循环会在最后一次重试时返回
        return tool_calls_count + 1

    async def _get_tool_schema(self, server_name: str, tool_name: str) -> Optional[Dict]:
        """获取工具的参数schema信息
        
        Args:
            server_name: 服务器名称
            tool_name: 工具名称
            
        Returns:
            工具的参数schema字典，如果获取失败则返回None
        """
        try:
            # 获取指定服务器的所有工具
            tools = await mcp_server.list_tools_from_servers([server_name])
            
            # 查找目标工具
            full_tool_name = f"{server_name}_{tool_name}"
            for tool in tools:
                if tool.get("name") == full_tool_name:
                    # 优先使用parameters字段，然后是inputSchema
                    if "parameters" in tool:
                        return tool["parameters"]
                    elif "inputSchema" in tool:
                        return tool["inputSchema"]
                    break
            
            logging.warning(f"未找到工具 {server_name}.{tool_name} 的schema信息")
            return None
            
        except Exception as e:
            logging.error(f"获取工具schema时出错: {str(e)}")
            return None

    def _build_retry_prompt(self, tool_name: str, server_name: str, original_args: Union[str, Dict], error_msg: str, tool_schema: Dict, retry_count: int) -> str:
        """构造重试提示信息
        
        Args:
            tool_name: 工具名称
            server_name: 服务器名称  
            original_args: 原始参数
            error_msg: 错误信息
            tool_schema: 工具的参数schema
            retry_count: 当前重试次数
            
        Returns:
            重试提示字符串
        """
        # 格式化原始参数
        if isinstance(original_args, str):
            args_str = original_args
        else:
            args_str = json.dumps(original_args, ensure_ascii=False, indent=2)
        
        # 格式化工具schema
        schema_str = json.dumps(tool_schema, ensure_ascii=False, indent=2)
        
        # 生成参数示例
        example_args = self._generate_example_args(tool_schema)
        example_str = json.dumps(example_args, ensure_ascii=False, indent=2)
        
        prompt = f"""工具调用出现错误，请根据错误信息修正参数格式。

**工具信息:**
- 服务器: {server_name}
- 工具名: {tool_name}

**错误信息:**
{error_msg}

**你之前使用的参数:**
{args_str}

**正确的参数格式规范:**
{schema_str}

**参数示例:**
{example_str}

**要求:**
1. 分析错误信息，确定参数格式问题
2. 根据schema规范修正参数
3. 确保所有必填字段都有提供，如果用户提供的数据不正确你应该转换数据类型或者格式
4. 确保参数类型正确
5. 严格根据参数示例进行参数生成，不要添加任何其他内容
6. 确保xml格式的完整性和正确性
7. 使用以下XML格式重新生成工具调用:

<use_mcp_tool>
<server_name>{server_name}</server_name>
<tool_name>{tool_name}</tool_name>
<arguments>
{{修正后的JSON参数}}
</arguments>
</use_mcp_tool>

请只返回修正后的XML工具调用，不要包含其他说明文字。"""
        logger.info(f"retry_prompt: {prompt}")
        return prompt

    def _extract_example_from_description(self, description: str) -> Optional[Any]:
        """从描述文本中提取示例数据
        
        Args:
            description: 描述文本
            
        Returns:
            提取的示例数据，如果没有找到则返回None
        """
        if not description:
            return None
        
        # 尝试匹配包含示例的模式
        patterns = [
            # 匹配 "such as, [...]" 或 "such as, {...}" 模式
            r'such as,?\s*(\[.*?\]|\{.*?\})',
            # 匹配 "example: [...]" 或 "example: {...}" 模式
            r'example:?\s*(\[.*?\]|\{.*?\})',
            # 匹配 "e.g. [...]" 或 "e.g. {...}" 模式
            r'e\.g\.?\s*(\[.*?\]|\{.*?\})',
            # 匹配 "like [...]" 或 "like {...}" 模式
            r'like\s*(\[.*?\]|\{.*?\})',
            # 匹配独立的 JSON 数组或对象
            r'(\[(?:\s*\{.*?\}\s*,?)+\s*\])',
            r'(\{(?:"[^"]+"\s*:|\'[^\']+\'\s*:).*?\})'
        ]
        
        # 尝试所有模式
        example_str = None
        for pattern in patterns:
            match = re.search(pattern, description, re.DOTALL | re.IGNORECASE)
            if match:
                example_str = match.group(1)
                break
        
        if example_str:
            try:
                # 标准化示例字符串
                # 1. 将单引号替换为双引号
                normalized_str = example_str.replace("'", '"')
                # 2. 将没有引号的键名添加双引号
                normalized_str = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', normalized_str)
                
                # 尝试解析为JSON
                try:
                    return json.loads(normalized_str)
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试使用Python的ast模块解析
                    try:
                        return ast.literal_eval(example_str)
                    except (SyntaxError, ValueError):
                        # 如果仍然失败，返回None
                        logging.debug(f"无法解析描述中的示例: {example_str}")
                        return None
            except Exception as e:
                logging.debug(f"从描述提取示例时出错: {str(e)}")
                return None
        
        return None

    def _generate_example_args(self, tool_schema: Dict, processed_refs: Optional[List[str]] = None, depth: int = 0, include_optional: bool = True, prioritize_required: bool = True) -> Dict:
        """根据工具schema生成示例参数
        
        Args:
            tool_schema: 工具的参数schema
            processed_refs: 已处理的引用路径，用于避免循环引用
            depth: 当前递归深度，用于限制递归层级
            include_optional: 是否包含可选参数（默认True）
            prioritize_required: 是否优先处理必填参数（默认True）
            
        Returns:
            示例参数字典
        """
        # 最大递归深度限制，避免无限递归
        MAX_DEPTH = 3
        if depth > MAX_DEPTH:
            return {}
        
        example_args = {}
        
        # 初始化已处理引用列表，避免循环引用
        if processed_refs is None:
            processed_refs = []
        
        # 检查schema中是否有examples或example字段
        if "examples" in tool_schema and isinstance(tool_schema["examples"], list) and tool_schema["examples"]:
            return tool_schema["examples"][0]
        elif "example" in tool_schema:
            return tool_schema["example"]
        
        if "properties" not in tool_schema:
            return example_args
        
        properties = tool_schema["properties"]
        required_fields = tool_schema.get("required", [])
        
        # 根据优先级排序字段：必填字段优先
        field_items = list(properties.items())
        if prioritize_required:
            field_items.sort(key=lambda x: (x[0] not in required_fields, x[0]))
        
        for field_name, field_info in field_items:
            # 首先尝试从字段信息中提取示例
            if "examples" in field_info and isinstance(field_info["examples"], list) and field_info["examples"]:
                example_args[field_name] = field_info["examples"][0]
                continue
            elif "example" in field_info:
                example_args[field_name] = field_info["example"]
                continue
            
            # 尝试从描述中提取示例
            description = field_info.get("description", "")
            example_from_desc = self._extract_example_from_description(description)
            
            if example_from_desc is not None:
                example_args[field_name] = example_from_desc
                continue
            
            # 处理 $ref 引用
            if "$ref" in field_info:
                ref_path = field_info["$ref"]
                # 避免循环引用
                if ref_path not in processed_refs:
                    processed_refs.append(ref_path)
                    # 解析引用路径
                    if ref_path.startswith("#/"):
                        parts = ref_path[2:].split("/")
                        ref_schema = tool_schema
                        for part in parts:
                            if part in ref_schema:
                                ref_schema = ref_schema[part]
                            else:
                                ref_schema = None
                                break
                        
                        if ref_schema:
                            # 递归处理引用的schema，增加深度
                            example_args[field_name] = self._generate_example_args(ref_schema, processed_refs.copy(), depth + 1, include_optional, prioritize_required)
                            continue
            
            field_type = field_info.get("type", "string")
            
            # 处理各种数据类型
            if field_type == "string":
                # 根据字段名称推断合适的示例值
                if any(kw in field_name.lower() for kw in ["path", "file", "directory"]):
                    example_args[field_name] = "/path/to/file.txt"
                elif any(kw in field_name.lower() for kw in ["url", "uri", "link", "website"]):
                    example_args[field_name] = "https://example.com"
                elif any(kw in field_name.lower() for kw in ["query", "search", "keyword"]):
                    example_args[field_name] = "搜索关键词"
                elif any(kw in field_name.lower() for kw in ["email", "mail"]):
                    example_args[field_name] = "<EMAIL>"
                elif any(kw in field_name.lower() for kw in ["phone", "mobile", "tel"]):
                    example_args[field_name] = "13800138000"
                elif any(kw in field_name.lower() for kw in ["name", "title"]):
                    example_args[field_name] = "示例名称"
                elif any(kw in field_name.lower() for kw in ["desc", "description", "content", "text"]):
                    example_args[field_name] = "这是一段示例描述文本"
                elif any(kw in field_name.lower() for kw in ["id", "code", "key"]):
                    example_args[field_name] = "sample_id_123"
                elif any(kw in field_name.lower() for kw in ["date", "time"]):
                    example_args[field_name] = "2023-01-01"
                elif "color" in field_name.lower():
                    example_args[field_name] = "#FF5733"
                else:
                    example_args[field_name] = f"示例{field_name}"
                
                # 检查是否有enum限制
                if "enum" in field_info and field_info["enum"]:
                    example_args[field_name] = field_info["enum"][0]
            
            elif field_type == "integer":
                # 根据字段名称推断合适的整数值
                if any(kw in field_name.lower() for kw in ["age", "year"]):
                    example_args[field_name] = 30
                elif any(kw in field_name.lower() for kw in ["count", "number", "quantity"]):
                    example_args[field_name] = 5
                elif "id" in field_name.lower():
                    example_args[field_name] = 12345
                elif any(kw in field_name.lower() for kw in ["index", "position"]):
                    example_args[field_name] = 0
                else:
                    example_args[field_name] = 10
                
                # 检查是否有最小/最大值限制
                if "minimum" in field_info:
                    example_args[field_name] = max(example_args[field_name], field_info["minimum"])
                if "maximum" in field_info:
                    example_args[field_name] = min(example_args[field_name], field_info["maximum"])
            
            elif field_type == "number":
                # 根据字段名称推断合适的数值
                if any(kw in field_name.lower() for kw in ["price", "cost", "amount"]):
                    example_args[field_name] = 99.99
                elif any(kw in field_name.lower() for kw in ["rate", "ratio", "percent"]):
                    example_args[field_name] = 0.75
                elif any(kw in field_name.lower() for kw in ["lat", "latitude"]):
                    example_args[field_name] = 39.9042
                elif any(kw in field_name.lower() for kw in ["lon", "lng", "longitude"]):
                    example_args[field_name] = 116.4074
                elif "weight" in field_name.lower():
                    example_args[field_name] = 65.5
                elif "height" in field_name.lower():
                    example_args[field_name] = 175.5
                elif "width" in field_name.lower():
                    example_args[field_name] = 800.0
                else:
                    example_args[field_name] = 10.5
                
                # 检查是否有最小/最大值限制
                if "minimum" in field_info:
                    example_args[field_name] = max(example_args[field_name], field_info["minimum"])
                if "maximum" in field_info:
                    example_args[field_name] = min(example_args[field_name], field_info["maximum"])
            
            elif field_type == "boolean":
                # 根据字段名称推断合适的布尔值
                if any(kw in field_name.lower() for kw in ["is_", "has_", "enable", "active", "visible", "show"]):
                    example_args[field_name] = True
                elif any(kw in field_name.lower() for kw in ["disable", "hidden", "hide"]):
                    example_args[field_name] = False
                else:
                    example_args[field_name] = True
            
            elif field_type == "array":
                # 改进对数组类型的处理
                items_schema = field_info.get("items", {})
                
                # 检查数组项是否有引用
                if "$ref" in items_schema:
                    ref_path = items_schema["$ref"]
                    # 避免循环引用
                    if ref_path not in processed_refs and depth < MAX_DEPTH:
                        processed_refs.append(ref_path)
                        # 解析引用路径
                        if ref_path.startswith("#/"):
                            parts = ref_path[2:].split("/")
                            ref_schema = tool_schema
                            for part in parts:
                                if part in ref_schema:
                                    ref_schema = ref_schema[part]
                                else:
                                    ref_schema = None
                                    break
                            
                            if ref_schema:
                                # 生成两个示例项，增加深度
                                example_args[field_name] = [
                                    self._generate_example_args(ref_schema, processed_refs.copy(), depth + 1, include_optional, prioritize_required),
                                    self._generate_example_args(ref_schema, processed_refs.copy(), depth + 1, include_optional, prioritize_required)
                                ]
                                continue
                
                # 处理有type属性的items
                if "type" in items_schema:
                    item_type = items_schema["type"]
                    if item_type == "object":
                        # 为对象数组生成示例项
                        item_schema = {"properties": items_schema.get("properties", {}), "required": items_schema.get("required", [])}
                        num_examples = min(3, max(1, field_info.get("minItems", 2)))
                        example_args[field_name] = [
                            self._generate_example_args(item_schema, processed_refs.copy(), depth + 1, include_optional, prioritize_required)
                            for _ in range(num_examples)
                        ]
                    elif item_type == "string":
                        if "enum" in items_schema and items_schema["enum"]:
                            # 如果有枚举值，使用枚举值作为示例
                            example_args[field_name] = items_schema["enum"][:2]
                        else:
                            # 根据字段名称推断合适的字符串数组
                            if any(kw in field_name.lower() for kw in ["tag", "label", "category"]):
                                example_args[field_name] = ["标签1", "标签2"]
                            elif "name" in field_name.lower():
                                example_args[field_name] = ["名称1", "名称2"]
                            elif "file" in field_name.lower():
                                example_args[field_name] = ["/path/to/file1.txt", "/path/to/file2.txt"]
                            else:
                                example_args[field_name] = ["示例1", "示例2"]
                    elif item_type == "integer":
                        if "enum" in items_schema and items_schema["enum"]:
                            example_args[field_name] = items_schema["enum"][:2]
                        else:
                            example_args[field_name] = [10, 20]
                    elif item_type == "number":
                        example_args[field_name] = [10.5, 20.5]
                    elif item_type == "boolean":
                        example_args[field_name] = [True, False]
                    else:
                        example_args[field_name] = ["item1", "item2"]
                else:
                    # 默认数组示例
                    example_args[field_name] = ["item1", "item2"]
                
                # 检查是否有最小/最大项数限制
                min_items = field_info.get("minItems")
                max_items = field_info.get("maxItems")
                if min_items and len(example_args[field_name]) < min_items:
                    # 扩展数组至最小项数
                    while len(example_args[field_name]) < min_items:
                        if example_args[field_name]:
                            example_args[field_name].append(example_args[field_name][0])
                        else:
                            example_args[field_name].append("item")
                if max_items and len(example_args[field_name]) > max_items:
                    # 裁剪数组至最大项数
                    example_args[field_name] = example_args[field_name][:max_items]
            
            elif field_type == "object":
                # 处理嵌套对象
                nested_schema = {"properties": field_info.get("properties", {}), "required": field_info.get("required", [])}
                example_args[field_name] = self._generate_example_args(nested_schema, processed_refs.copy(), depth + 1, include_optional, prioritize_required)
                if not example_args[field_name]:  # 如果生成的嵌套对象为空
                    example_args[field_name] = {"key": "value"}
            
            # 处理null类型
            elif field_type == "null":
                example_args[field_name] = None
            
            # 处理多类型字段
            elif isinstance(field_type, list):
                # 选择第一个非null类型
                for t in field_type:
                    if t != "null":
                        field_info_copy = field_info.copy()
                        field_info_copy["type"] = t
                        temp_args = {}
                        temp_args[field_name] = self._generate_example_args({"properties": {field_name: field_info_copy}}, processed_refs.copy(), depth, include_optional, prioritize_required)[field_name]
                        example_args[field_name] = temp_args[field_name]
                        break
                else:
                    # 如果所有类型都是null
                    example_args[field_name] = None
            
            # 优化的可选参数处理逻辑
            is_required = field_name in required_fields
            
            # 如果不包含可选参数且当前字段非必填，则移除
            if not include_optional and not is_required:
                example_args.pop(field_name, None)
                continue
            
            # 如果有默认值，使用默认值
            if "default" in field_info:
                example_args[field_name] = field_info["default"]
            
            # 为可选参数添加注释标识（如果字段已存在）
            if field_name in example_args and not is_required and include_optional:
                # 对于可选字段，我们确保示例值更具描述性
                current_value = example_args[field_name]
                
                # 如果是字符串类型，为可选字段添加提示性前缀
                if isinstance(current_value, str) and not current_value.startswith("(可选)"):
                    # 只有当值看起来是我们生成的示例时才添加前缀
                    if any(keyword in current_value for keyword in ["示例", "example", "/path/", "https://", "搜索", "sample_"]):
                        example_args[field_name] = f"(可选){current_value}"
        
        return example_args

    def _get_service(self, service_name: Optional[str] = None) -> AIService:
        """
        获取AI服务实例
        
        Args:
            service_name: 服务名称，如果未指定则使用默认服务
            
        Returns:
            AI服务实例
        """
        # 延迟导入服务类，避免循环导入
        from backend.ai.openai_service import OpenAIService
        
        # 如果没有指定服务，使用默认服务
        if not service_name:
            service_name = config.get_default_service()
        
        # 懒加载服务
        if service_name not in self.services:
            if service_name == "openai":
                self.services["openai"] = OpenAIService()
            else:
                # 默认使用OpenAI服务
                self.services[service_name] = OpenAIService()
        
        return self.services[service_name]
    
    async def get_models(self, is_update: bool = False):
        """
        获取所有支持的模型
        
        Args:
            is_update: 是否强制更新模型列表
            
        Returns:
            模型列表
        """
        models=[]

        if is_update==False:
            models=config.get("api.services.openai.models", [])
            # logging.info(f"models: {models}")
            if models:
                return {"openai":models}
        result = {}
        services = config.get("api.services", {})
        
        # 遍历所有启用的服务
        for name, service_config in services.items():
            if service_config.get("enabled", False):
                try:
                    # 获取服务实例
                    service = self._get_service(name)
                    
                    # 获取模型列表
                    models = await service.get_models()
                    
                    # 添加到结果
                    result[name] = models
                    config.set(f"api.services.{name}.models", models)
                except Exception as e:
                    logger.error(f"获取 {name} 服务的模型列表失败: {str(e)}")
                    result[name] = []
        # logging.info(f"result: {result}")
        return result

    async def _resource(self, messages: List[Dict], tool_call: Dict, model: str, tool_calls_history: List[Dict], is_cancelled: Optional[Callable], is_agent: bool, stream_callback: Optional[Callable], original_model: Optional[str], content: str, tool_calls_count: int, service: AIService) -> int:
        """处理资源访问工具调用"""
        server_name = tool_call["server_name"]
        uri = tool_call["uri"]
        
        # 创建工具调用上下文
        ctx = ToolCallContext(
            messages=messages,
            model=model,
            tool_calls_history=tool_calls_history,
            is_cancelled=is_cancelled,
            is_agent=is_agent,
            stream_callback=stream_callback,
            original_model=original_model,
            content=content,
            tool_calls_count=tool_calls_count,
            service=service
        )
        
        # 创建资源访问名称
        resource_name = f"resource_access_{server_name}"
        
        # 创建工具信息字典
        tool_info = {
            "name": resource_name,
            "call_record": {
                "type": "resource_access",
                "server": server_name,
                "uri": uri
            },
            "result_record": lambda result: {
                "type": "resource_result",
                "server": server_name,
                "uri": uri,
                "result": result
            },
            "error_record": lambda error: {
                "type": "resource_error",
                "server": server_name,
                "uri": uri,
                "error": error
            },
            "agent_result_message": lambda result, idx: {
                "type": "resource_result",
                "name": resource_name,
                "server": server_name,
                "parameters": {"uri": uri},
                "result": result,
                "status": "completed",
                "isSuccess": True,
                "tool_call_index": idx
            },
            "agent_error_message": lambda error, idx: {
                "type": "resource_result",
                "name": resource_name,
                "server": server_name,
                "parameters": {"uri": uri},
                "result": error,
                "status": "failed",
                "isSuccess": False,
                "tool_call_index": idx
            },
            "normal_result_message": lambda result: f"\n\n**Resource Access:** {uri}\n```json\n{json.dumps(result, ensure_ascii=False, indent=2)}\n```\n\n",
            "normal_error_message": lambda error: f"\n\n**Resource Access Error:**\n```json\n{json.dumps(error, ensure_ascii=False, indent=2)}\n```\n\n"
        }
        
        # 定义执行函数
        async def execute_func():
            return await mcp_server.access_resource_from_server(server_name, uri)
        
        # 使用通用工具执行处理方法
        return await self._handle_tool_execution(ctx, "资源访问", tool_info, execute_func)

    async def _tavily_search(self, messages: List[Dict], tool_call: Dict, model: str, tool_calls_history: List[Dict], is_cancelled: Optional[Callable], is_agent: bool, stream_callback: Optional[Callable], original_model: Optional[str], content: str, tool_calls_count: int, service: AIService) -> int:
        """处理Tavily搜索工具调用"""
        # 创建工具调用上下文
        ctx = ToolCallContext(
            messages=messages,
            model=model,
            tool_calls_history=tool_calls_history,
            is_cancelled=is_cancelled,
            is_agent=is_agent,
            stream_callback=stream_callback,
            original_model=original_model,
            content=content,
            tool_calls_count=tool_calls_count,
            service=service
        )
        
        # 创建工具信息字典
        tool_info = {
            "name": "tavily-search",
            "call_record": {
                "type": "tavily_search",
                "content": tool_call["content"]
            },
            "result_record": lambda result: {
                "type": "tavily_search_result",
                "summary": f"找到 {len(result.get('raw_data', {}).get('results', []))} 个结果" if not isinstance(result, dict) or "error" not in result else result 
            },
            "error_record": lambda error: {
                "type": "tavily_search_error",
                "error": error
            },
            "agent_result_message": lambda result, idx: {
                "type": "tavily_search_result",
                "name": "tavily-search",
                "parameters": tool_call.get("content", {}),
                "result": {"content": result.get('raw_data', {}).get('results', [])} if not isinstance(result, dict) or "error" not in result else result,
                "status": "completed",
                "isSuccess": True,
                "tool_call_index": idx
            },
            "agent_error_message": lambda error, idx: {
                "type": "tavily_search_result",
                "name": "tavily-search",
                "parameters": tool_call.get("content", {}),
                "result": {"error": error},
                "status": "failed",
                "isSuccess": False,
                "tool_call_index": idx
            },
            "normal_result_message": lambda result: f"\n\n**Tavily Search Results:**\n```json\n{json.dumps(result.get('raw_data', {}).get('results', []), ensure_ascii=False, indent=2)}\n```\n\n",
            "normal_error_message": lambda error: f"\n\n**Tavily Search Error:**\n```json\n{json.dumps({'error': error}, ensure_ascii=False, indent=2)}\n```\n\n"
        }
        
        # 定义执行函数
        async def execute_func():
            from backend.search.tavily import handle_tavily_search_call
            search_result = await handle_tavily_search_call(tool_call["original_xml"])
            
            if search_result.get("error", False):
                error_message = f"搜索失败: {search_result.get('message', '未知错误')}"
                raise Exception(error_message)
                
            return search_result
        
        # 使用通用工具执行处理方法
        return await self._handle_tool_execution(ctx, "Tavily搜索", tool_info, execute_func)

    async def _function_call(self, messages: List[Dict], model, response: Dict, tool_calls_history: List[Dict], is_cancelled: Optional[Callable], is_agent: bool, stream_callback: Optional[Callable], original_model: Optional[str], content: str, tool_calls_count: int, service: AIService) -> int:
        """处理OpenAI函数调用格式的工具调用"""
        function_call = response["function_call"]
        tool_name = function_call.get("name")
        tool_args = function_call.get("arguments")
        
        # 创建工具调用上下文
        ctx = ToolCallContext(
            messages=messages,
            model=model,
            tool_calls_history=tool_calls_history,
            is_cancelled=is_cancelled,
            is_agent=is_agent,
            stream_callback=stream_callback,
            original_model=original_model,
            content=content,
            tool_calls_count=tool_calls_count,
            service=service
        )
        
        # 创建工具信息字典
        tool_info = {
            "name": tool_name,
            "call_record": {
                "type": "function_call",
                "name": tool_name,
                "arguments": tool_args
            },
            "result_record": lambda result: {
                "type": "function_result",
                "name": tool_name,
                "result": result
            },
            "error_record": lambda error: {
                "type": "function_error",
                "name": tool_name,
                "error": error
            },
            "agent_result_message": lambda result, idx: {
                "type": "function_result",
                "name": tool_name,
                "parameters": json.loads(tool_args) if isinstance(tool_args, str) else tool_args,
                "result": result,
                "status": "completed",
                "isSuccess": True,
                "tool_call_index": idx
            },
            "agent_error_message": lambda error, idx: {
                "type": "function_result",
                "name": tool_name,
                "parameters": json.loads(tool_args) if isinstance(tool_args, str) else tool_args,
                "result": error,
                "status": "failed",
                "isSuccess": False,
                "tool_call_index": idx
            },
            "normal_result_message": lambda result: f"\n\n**Tool Call:** {tool_name}\n```json\n{tool_args}\n```\n\n**Tool Result:**\n```json\n{json.dumps(result, ensure_ascii=False, indent=2)}\n```\n\n",
            "normal_error_message": lambda error: f"\n\n**Tool Call Error:** {tool_name}\n```json\n{json.dumps(error, ensure_ascii=False, indent=2)}\n```\n\n"
        }
        
        # 定义执行函数
        async def execute_func():
            parsed_args = json.loads(tool_args)
            return await mcp_server.call_tool(tool_name, parsed_args)
        
        # 使用通用工具执行处理方法
        return await self._handle_tool_execution(ctx, "函数调用", tool_info, execute_func)

    async def _handle_tool_execution(self, ctx: ToolCallContext, tool_type: str, tool_info: dict, execute_func: callable) -> int:
        """
        处理工具调用
        
        Args:
            ctx: 工具调用上下文
            tool_type: 工具类型
            tool_info: 工具信息字典
            execute_func: 执行函数
            
        Returns:
            int: 更新后的tool_calls_count值
        """
        # 记录工具调用
        tool_calls_history = ctx.tool_calls_history
        tool_calls_count = ctx.tool_calls_count
        tool_name = tool_info["name"]  # 从tool_info中获取工具名称
        model = ctx.model
        is_cancelled = ctx.is_cancelled
        is_agent = ctx.is_agent
        stream_callback = ctx.stream_callback
        original_model = ctx.original_model
        content = ctx.content
        service = ctx.service
        
        # 记录工具调用
        tool_calls_history.append(tool_info["call_record"])
        
        # 执行工具调用前再次检查是否有取消信号
        if is_cancelled and is_cancelled():
            logging.info(f"检测到取消信号，停止{tool_type}执行")

            final_response = {
                "content": content,
                "cancelled": True,
                "tool_calls_history": tool_calls_history
            }

            # 恢复原始模型设置
            if original_model is not None:
                service.model = original_model

            # Return current count as operation was cancelled before execution
            return tool_calls_count

        # 执行工具调用
        try:
            result = await execute_func()
            # logging.info(f"{tool_type}执行结果: {result}")

            # 处理工具调用结果，确保可以被 JSON 序列化
            processed_result = _process_tool_result(result)
            if model and "gemini" not in model and "gpt" not in model and "claude" not in model:
                # 压缩处理工具调用结果，确保不超过token限制
                compressed_result = _compress_tool_result(processed_result)

                if compressed_result != processed_result:
                    logging.info(f"{tool_type}结果被压缩以适应token限制")
                    processed_result = compressed_result

            # 记录工具结果
            tool_calls_history.append(tool_info["result_record"](processed_result))

            # 将工具调用结果添加到消息列表
            if is_agent:
                # Agent模式
                agent_result = tool_info["agent_result_message"](processed_result, tool_calls_count)
                tool_result_message = {
                    "role": "function",
                    "name": tool_name,
                    "content": json.dumps(processed_result)
                }
                ctx.messages.append(tool_result_message)
                
                # 发送结果给客户端
                if stream_callback:
                    try:
                        await stream_callback(agent_result)
                    except Exception as cb_err:
                        logging.warning(f"向客户端发送{tool_type}结果回调时出错: {cb_err}")
            else:
                # 普通模式
                normal_result = tool_info["normal_result_message"](processed_result)
                # logger.info(f"normal_result: {normal_result}")
                tool_result_message = {
                    "role": "function",
                    "name": tool_name,
                    "content": json.dumps(processed_result)
                }
                ctx.messages.append(tool_result_message)
                
                # 发送结果给客户端
                if stream_callback:
                    try:
                        await stream_callback(normal_result)
                    except Exception as cb_err:
                        logging.warning(f"向客户端发送{tool_type}结果回调时出错: {cb_err}")

            # 增加工具调用计数
            tool_calls_count += 1
            return tool_calls_count
        except Exception as e:
            logging.error(f"{tool_type}执行失败: {str(e)}")
            error_result = {"error": str(e)}
            tool_calls_history.append(tool_info["error_record"](str(e)))
            error_message = {
                "role": "function",
                "name": tool_name,
                "content": json.dumps(error_result)
            }
            ctx.messages.append(error_message)

            if stream_callback:
                try:
                    if is_agent:
                        error_chunk = tool_info["agent_error_message"](error_result, tool_calls_count)
                        await stream_callback(error_chunk)
                    else:
                        normal_error = tool_info["normal_error_message"](error_result)
                        await stream_callback(normal_error)
                except Exception as cb_err:
                    logging.warning(f"向客户端发送{tool_type}错误回调时出错: {cb_err}")

            tool_calls_count += 1
            return tool_calls_count
    
    async def _call_service_with_simple_retry(self, service: AIService, messages: List[Dict], 
                                             functions: Optional[List[Dict]] = None,
                                             stream_callback: Optional[Callable] = None,
                                             max_tokens: Optional[int] = None,
                                             is_cancelled: Optional[Callable] = None,
                                             thinking_budget: Optional[int] = None,
                                             retry_context: str = "内部调用",
                                             max_retries: int = 2) -> Dict:
        """
        简化的AI服务调用重试机制，用于内部调用（如XML修正、工具重试等）
        
        Args:
            service: AI服务实例
            messages: 消息列表
            functions: 函数列表
            stream_callback: 流式回调
            max_tokens: 最大token数
            is_cancelled: 取消检查函数
            thinking_budget: 思考预算
            retry_context: 重试上下文描述
            max_retries: 最大重试次数（默认2次）
            
        Returns:
            AI服务响应
        """
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                response = await service.chat_completion(
                    messages=messages,
                    functions=functions,
                    stream_callback=stream_callback,
                    max_tokens=max_tokens,
                    is_cancelled=is_cancelled,
                    thinking_budget=thinking_budget
                )
                
                if is_cancelled and is_cancelled():
                    logging.info(f"{retry_context}调用被用户取消")
                    return {"error": "请求已取消", "content": "", "cancelled": True}
                
                # 检查响应是否包含错误
                if "error" in response:
                    if retry_count < max_retries:
                        retry_delay = 3  # 内部调用使用固定1秒延迟
                        logging.warning(f"{retry_context}调用失败 (第{retry_count}次重试): {response.get('error', '未知错误')}，{retry_delay}秒后重试...")
                        if '429' in response.get('error', ''):
                            retry_delay = 5
                        else:
                            retry_count += 1
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        # 达到最大重试次数，返回错误
                        logging.error(f"{retry_context}调用最终失败，已重试{max_retries}次: {response.get('error', '未知错误')}")
                        return response
                else:
                    # 成功获取响应
                    if retry_count > 0:
                        logging.info(f"{retry_context}调用在第{retry_count + 1}次尝试后成功")
                    return response
                    
            except Exception as e:
                if retry_count < max_retries:
                    retry_delay = 3
                    logging.warning(f"{retry_context}调用异常 (第{retry_count}次重试): {str(e)}，{retry_delay}秒后重试...")
                    if '429' in str(e):
                        retry_delay = 5
                        await asyncio.sleep(retry_delay)
                    else:
                        retry_count += 1
                        await asyncio.sleep(retry_delay)
                    continue
                else:
                    # 达到最大重试次数，返回错误
                    logging.error(f"{retry_context}调用最终异常，已重试{max_retries}次: {str(e)}")
                    return {"error": f"{retry_context}调用失败，已重试{max_retries}次: {str(e)}", "error_type": "service_error", "code": 503}
        
        # 理论上不应该到达这里
        return {"error": f"{retry_context}调用失败", "error_type": "service_error", "code": 503}

    def _is_potential_malformed_xml(self, content: str) -> bool:
        """
        Heuristically checks if the content looks like a malformed XML tool call
        that parse_xml_tool_calls might have missed.
        """
        if not content:
            return False
        # Check for presence of XML-like tags
        has_angle_brackets = "<" in content and ">" in content
        has_closing_tag_syntax = "</" in content
        
        # Check for keywords common in our expected tool call XML structures
        # These keywords are indicative of an attempt to call one of the supported tools.
        has_tool_keywords = any(kw in content.lower() for kw in [
            "use_mcp_tool", "server_name", "tool_name", "arguments", 
            "access_mcp_resource", "uri", 
            "tavily_search",
            "file_content", "write_file" # Keywords from user's problematic example
        ])
        
        # If it has XML tags and tool-related keywords, it's a candidate.
        # parse_xml_tool_calls already confirmed it's not a *valid* top-level tool call.
        return (has_angle_brackets or has_closing_tag_syntax) and has_tool_keywords

    def _build_xml_correction_prompt(self, malformed_xml: str) -> str:
        """
        Builds a prompt to ask the AI to correct its previously malformed XML.
        """
        # Consider fetching MCP_TOOL_GUIDELINES if available and relevant context exists
        # For now, a generic prompt:
        return f"""Your previous response included an XML-like structure that could not be parsed as a valid tool call.
The malformed XML output was:
---
{malformed_xml}
---
Please review the XML syntax and ensure it strictly matches the required format for tool calls.
The expected formats are:
1. For MCP tools:
<use_mcp_tool>
  <server_name>SERVER_NAME</server_name>
  <tool_name>TOOL_NAME</tool_name>
  <arguments>{{JSON_formatted_arguments_object_as_string}}</arguments>
</use_mcp_tool>

2. For Tavily search:
<tavily_search>QUERY_STRING</tavily_search>

3. For MCP resource access:
<access_mcp_resource>
  <server_name>SERVER_NAME</server_name>
  <uri>RESOURCE_URI</uri>
</access_mcp_resource>

Ensure all tags are correctly opened and closed, and arguments are properly formatted.
If you intended to call a tool, please provide the corrected XML tool call.
If a tool call was not intended or cannot be corrected to a valid format, please provide a standard text response clarifying your previous output or intent.
"""

    def _build_xml_correction_prompt_enhanced(self, malformed_xml: str) -> str:
        """
        构建增强的XML修正提示，参考新的格式要求
        """
        return f"""您上次的响应包含了无法解析为有效工具调用的XML结构。

有问题的XML输出：
---
{malformed_xml}
---

**关键要求（必须严格遵守）：**
1. ⚠️ **每次只生成一个XML工具调用**
2. ⚠️ **确保所有XML标签都正确闭合**
3. ⚠️ **arguments内容必须是有效的JSON格式**

**正确的XML格式示例：**

1. **MCP工具调用：**
```xml
<use_mcp_tool>
<server_name>服务器名称</server_name>
<tool_name>工具名称</tool_name>
<arguments>
{{"参数名": "参数值"}}
</arguments>
</use_mcp_tool>
```

2. **资源访问：**
```xml
<access_mcp_resource>
<server_name>服务器名称</server_name>
<uri>资源URI</uri>
</access_mcp_resource>
```

3. **代码执行：**
```xml
<execute_code>
<language>python</language>
<L_code>
print("Hello World")
</L_code>
</execute_code>
```

**请执行以下操作：**
- 如果您打算调用工具，请提供修正后的**单个**XML工具调用
- 如果不需要调用工具，请提供普通文本响应
- 确保XML格式完整且正确

请立即提供修正版本："""

    def _is_likely_final_text_response(self, content: str) -> bool:
        """
        Heuristically checks if the content is likely a final text response
        rather than a malformed tool call or an intermediate thought.
        """
        if not content:
            return True # Empty content could be considered as nothing more to say.

        content_lower = content.lower()

        # Keywords indicating a final response or closing statement
        final_keywords_cn = [
            "感谢您的提问", "希望这对您有帮助", "如果您还有其他问题", "请随时提出",
            "本次对话结束", "再见", "祝您一切顺利", "服务到此结束"
        ]
        final_keywords_en = [
            "thank you for", "hope this helps", "if you have any other questions",
            "feel free to ask", "goodbye", "all the best", "anything else i can help with"
        ]

        # Check for Chinese keywords without lowercasing, as AI might not always lowercase them.
        if any(kw in content for kw in final_keywords_cn):
            return True
        if any(kw in content_lower for kw in final_keywords_en):
            return True
        
        # Short, polite sign-offs, but only if they don't also look like a malformed tool call attempt
        if len(content_lower.split()) < 10: # Arbitrary short length
            if any(sign_off in content_lower for sign_off in ["ok.", "okay.", "got it.", "sure.", "alright."]):
                # If it's short AND doesn't look like a failed/malformed tool call attempt,
                # then it might be a simple final acknowledgment.
                if not self._is_potential_malformed_xml(content): 
                    return True
        
        # Add more heuristics here if needed, e.g., based on punctuation, lack of questions from AI.
        # For instance, if the response is a question back to the user, it's not final.
        if content.strip().endswith("?") or content.strip().endswith("？"):
            return False # It's a question, so likely expecting a response.

        # If content is very long and doesn't have tool keywords, it might be a final detailed answer.
        # This is harder to quantify without more context.
        
        return False

    def _parse_tool_calls_enhanced(self, content: str) -> List[Dict]:
        """
        增强的工具调用解析方法，支持多工具调用和格式修复
        
        Args:
            content: AI响应内容
            
        Returns:
            解析出的工具调用列表
        """
        try:
            # 首先尝试使用新的解析器
            tool_calls = mcp_tool_parser.parse_tool_calls(content)
            
            if tool_calls:
                # 将新解析器的格式转换为原有格式
                converted_calls = []
                for tool_call in tool_calls:
                    if tool_call.get('tool_type') == 'use_mcp_tool':
                        converted_calls.append({
                            'type': 'tool',
                            'server_name': tool_call.get('server_name'),
                            'name': tool_call.get('name'),
                            'arguments': tool_call.get('arguments', {})
                        })
                    elif tool_call.get('tool_type') == 'access_mcp_resource':
                        converted_calls.append({
                            'type': 'resource',
                            'server_name': tool_call.get('server_name'),
                            'uri': tool_call.get('uri')
                        })
                    elif tool_call.get('tool_type') == 'execute_code':
                        # 对于代码执行，直接标记为特殊类型
                        converted_calls.append({
                            'type': 'code_execution',
                            'language': tool_call.get('language'),
                            'code': tool_call.get('code')
                        })
                
                if converted_calls:
                    logging.info(f"新解析器成功解析出 {len(converted_calls)} 个工具调用")
                    return converted_calls
            
            # 如果新解析器没有结果，回退到原有解析器
            original_tool_call = parse_xml_tool_calls(content)
            if original_tool_call:
                logging.info("回退到原有解析器成功")
                return [original_tool_call]
            
            return []
            
        except Exception as e:
            logging.error(f"增强工具解析失败: {e}")
            # 发生异常时回退到原有解析器
            original_tool_call = parse_xml_tool_calls(content)
            return [original_tool_call] if original_tool_call else []

    # 🧠 ============ 记忆工具执行器 ============

    async def _execute_memory_tool(self, tool_name: str, tool_args: dict, ctx) -> dict:
        """
        🧠 执行记忆工具调用
        
        Args:
            tool_name: 记忆工具名称 
            tool_args: 工具参数
            ctx: 工具调用上下文
            
        Returns:
            dict: 工具执行结果
        """
        try:
            # 导入记忆工具管理器
            from backend.langgraph_enhancement.tools.memory_tools import get_memory_tools_manager
            memory_tools_manager = get_memory_tools_manager()
            
            # 验证工具名称和参数
            is_valid, error_msg = memory_tools_manager.validate_arguments(tool_name, tool_args)
            if not is_valid:
                return {
                    "error": f"记忆工具参数验证失败: {error_msg}",
                    "isError": True
                }
            
            # 🔍 获取当前智能体实例 - 使用多种策略
            agent_instance = await self._get_current_agent_instance(ctx)
            if not agent_instance:
                return {
                    "error": "无法获取当前智能体实例，记忆工具需要智能体上下文",
                    "isError": True
                }
            
            # 🐛 调试日志：显示当前智能体信息
            logger.warning(f"🐛 [MEMORY DEBUG] 当前智能体: {agent_instance.agent_id}, 请求的agent_id: {tool_args.get('agent_id', 'self')}")
            
            # 📝 执行对应的统一记忆工具
            if tool_name == "get_memory":
                key = tool_args.get("key")
                team_id = tool_args.get("team_id")
                agent_id = tool_args.get("agent_id", "self")  # 保持向后兼容
                default = tool_args.get("default")
                
                # 🔥 新的智能记忆路由逻辑 - 优先使用team_id
                from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                memory_manager = get_memory_manager()
                
                if team_id:
                    # 团队记忆：使用team_id直接访问团队记忆空间
                    result = await memory_manager.get_team_memory(team_id, key, default)
                    target_desc = f"团队记忆 ({team_id})"
                    actual_target = team_id
                    scope = "team"
                    
                elif agent_id and agent_id != "self" and agent_id != agent_instance.agent_id:
                    # 跨智能体记忆：访问其他智能体的记忆空间
                    result = await memory_manager.get_agent_memory(agent_id, key, default)
                    target_desc = f"智能体记忆 ({agent_id})"
                    actual_target = agent_id
                    scope = "shared"
                    
                else:
                    # 个人记忆：获取自己的记忆（默认情况）
                    result = await agent_instance.get_my_memory(key, default)
                    target_desc = "个人记忆"
                    actual_target = agent_instance.agent_id
                    scope = "personal"
                
                logger.info(f"🧠 [MEMORY] 获取记忆结果: {target_desc}.{key} = {result}")
                
                return {
                    "success": True,
                    "memory_scope": scope,
                    "requester_agent": agent_instance.agent_id,
                    "target_agent": actual_target,
                    "key": key,
                    "value": result,
                    "message": f"成功获取{target_desc}的记忆: {key}" if result is not None else f"成功获取团队记忆 ({team_id}) 的记忆: {key}"
                }
            
            elif tool_name == "store_memory":
                key = tool_args.get("key")
                value = tool_args.get("value")
                team_id = tool_args.get("team_id")
                agent_id = tool_args.get("agent_id")
                mode = tool_args.get("mode", "append")
                separator = tool_args.get("separator", "\n")
                
                # 🔥 新的智能记忆路由逻辑 - 优先使用team_id
                from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                memory_manager = get_memory_manager()
                
                if team_id:
                    # 团队记忆：使用team_id直接访问团队记忆空间
                    if mode == "append":
                        # 获取现有内容并追加
                        existing_value = await memory_manager.get_team_memory(team_id, key, "")
                        if existing_value:
                            new_value = str(existing_value) + separator + str(value)
                        else:
                            new_value = str(value)
                        success = await memory_manager.store_team_memory(team_id, key, new_value)
                    else:  # overwrite
                        success = await memory_manager.store_team_memory(team_id, key, value)
                    target_desc = f"团队记忆 ({team_id})"
                    actual_target = team_id
                    
                elif agent_id and agent_id != "self" and agent_id != agent_instance.agent_id:
                    # 跨智能体记忆：存储到其他智能体的记忆空间（如果允许）
                    if mode == "append":
                        existing_value = await memory_manager.get_agent_memory(agent_id, key, "")
                        if existing_value:
                            new_value = str(existing_value) + separator + str(value)
                        else:
                            new_value = str(value)
                        success = await memory_manager.store_agent_memory(agent_id, key, new_value)
                    else:  # overwrite
                        success = await memory_manager.store_agent_memory(agent_id, key, value)
                    target_desc = f"智能体记忆 ({agent_id})"
                    actual_target = agent_id
                    
                else:
                    # 个人记忆：存储到自己的记忆（默认情况）
                    if mode == "append":
                        existing_value = await agent_instance.get_my_memory(key, "")
                        if existing_value:
                            new_value = str(existing_value) + separator + str(value)
                        else:
                            new_value = str(value)
                        success = await agent_instance.store_my_memory(key, new_value)
                    else:  # overwrite
                        success = await agent_instance.store_my_memory(key, value)
                    target_desc = "个人记忆"
                    actual_target = agent_instance.agent_id
                
                logger.info(f"🧠 [MEMORY] 存储记忆结果: {target_desc}.{key} = {value} (模式: {mode})")
                
                return {
                    "success": success,
                    "memory_scope": "team" if team_id else ("shared" if agent_id and agent_id != agent_instance.agent_id else "personal"),
                    "agent": agent_instance.agent_id,
                    "target": actual_target,
                    "key": key,
                    "value": value,
                    "mode": mode,
                    "message": f"成功存储到{target_desc}: {key}" if success else f"存储失败: {key}"
                }
            
            elif tool_name == "list_memories":
                agent_id = tool_args.get("agent_id", "self")
                
                # 判断是列出自己还是其他智能体的记忆
                if agent_id == "self" or not agent_id or agent_id == agent_instance.agent_id:
                    # 列出自己的记忆（包括传入自己的实际ID的情况）
                    memories = await agent_instance.list_my_memories()
                    target_desc = "自己"
                else:
                    # 列出其他智能体的记忆
                    from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                    memory_manager = get_memory_manager()
                    agent_memories = memory_manager.agent_memory.get(agent_id, {})
                    memories = list(agent_memories.keys())
                    target_desc = f"智能体 {agent_id}"
                
                return {
                    "success": True,
                    "agent_id": agent_id,
                    "memories": memories,
                    "count": len(memories),
                    "message": f"{target_desc}有 {len(memories)} 项记忆"
                }
            
            elif tool_name == "search_memories":
                pattern = tool_args.get("pattern")
                agent_id = tool_args.get("agent_id", "self")
                
                # 判断是搜索自己还是其他智能体的记忆
                if agent_id == "self" or not agent_id or agent_id == agent_instance.agent_id:
                    # 搜索自己的记忆（包括传入自己的实际ID的情况）
                    results = await agent_instance.search_my_memories(pattern)
                    target_desc = "自己"
                else:
                    # 搜索其他智能体的记忆
                    from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                    memory_manager = get_memory_manager()
                    agent_memories = memory_manager.agent_memory.get(agent_id, {})
                    
                    matched = {}
                    for key, memory_item in agent_memories.items():
                        # 在键名和值中搜索
                        if (pattern.lower() in key.lower() or 
                            pattern.lower() in str(memory_item.get("value", "")).lower()):
                            matched[key] = memory_item["value"]
                    results = matched
                    target_desc = f"智能体 {agent_id}"
                
                return {
                    "success": True,
                    "agent_id": agent_id,
                    "pattern": pattern,
                    "results": results,
                    "count": len(results),
                    "message": f"在{target_desc}中搜索 '{pattern}' 找到 {len(results)} 项记忆"
                }
            
            elif tool_name == "clear_memory":
                key = tool_args.get("key")
                # 清除只能操作自己的记忆
                success = await agent_instance.clear_my_memory(key)
                clear_type = "指定记忆" if key else "所有记忆"
                return {
                    "success": success,
                    "key": key,
                    "message": f"清除{clear_type}{'成功' if success else '失败'}"
                }
            
            elif tool_name == "get_memory_stats":
                agent_id = tool_args.get("agent_id", "self")
                
                # 判断是获取自己还是其他智能体的记忆统计
                if agent_id == "self" or not agent_id or agent_id == agent_instance.agent_id:
                    # 获取自己的记忆统计（包括传入自己的实际ID的情况）
                    stats = await agent_instance.get_my_memory_stats()
                    target_desc = "自己"
                else:
                    # 获取其他智能体的记忆统计
                    from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                    memory_manager = get_memory_manager()
                    agent_memories = memory_manager.agent_memory.get(agent_id, {})
                    
                    stats = {
                        "agent_id": agent_id,
                        "total_memories": len(agent_memories),
                        "memory_keys": list(agent_memories.keys()),
                        "total_access_count": sum(item.get("accessed_count", 0) for item in agent_memories.values())
                    }
                    
                    # 找出最常访问的记忆
                    if agent_memories:
                        access_counts = [(k, v.get("accessed_count", 0)) for k, v in agent_memories.items()]
                        access_counts.sort(key=lambda x: x[1], reverse=True)
                        stats["most_accessed"] = access_counts[0] if access_counts else None
                    
                    target_desc = f"智能体 {agent_id}"
                
                return {
                    "success": True,
                    "agent_id": agent_id,
                    "stats": stats,
                    "message": f"成功获取{target_desc}的记忆统计"
                }
            
            elif tool_name == "list_all_agents":
                from backend.langgraph_enhancement.agents.context_optimized_agent import get_agent_pool
                agent_pool = get_agent_pool()
                
                all_agents = agent_pool.get_all_agents()
                agent_ids = list(all_agents.keys())
                
                # 收集每个智能体的基本信息
                agents_info = []
                for agent_id, agent in all_agents.items():
                    info = {
                        "agent_id": agent_id,
                        "role_name": getattr(agent, 'role_name', agent_id),
                        "memory_enabled": getattr(agent, 'memory_enabled', False)
                    }
                    agents_info.append(info)
                
                return {
                    "success": True,
                    "agents": agents_info,
                    "agent_ids": agent_ids,
                    "count": len(agent_ids),
                    "message": f"找到 {len(agent_ids)} 个活跃智能体"
                }
            
            elif tool_name == "share_memory":
                target_agent_id = tool_args.get("target_agent_id")
                memory_key = tool_args.get("memory_key")
                new_key = tool_args.get("new_key")
                
                # 获取当前智能体的记忆
                source_memory = await agent_instance.get_my_memory(memory_key)
                if source_memory is None:
                    return {
                        "success": False,
                        "error": f"当前智能体没有记忆项: {memory_key}",
                        "message": f"无法分享不存在的记忆: {memory_key}"
                    }
                
                # 使用记忆管理器分享记忆
                from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                memory_manager = get_memory_manager()
                
                # 如果没有指定新键名，使用带前缀的原键名
                if not new_key:
                    new_key = f"shared_from_{agent_instance.agent_id}_{memory_key}"
                
                success = await memory_manager.share_memory_between_agents(
                    agent_instance.agent_id, target_agent_id, memory_key, new_key
                )
                
                if success:
                    return {
                        "success": True,
                        "source_agent": agent_instance.agent_id,
                        "target_agent": target_agent_id,
                        "memory_key": memory_key,
                        "new_key": new_key,
                        "message": f"成功将记忆 '{memory_key}' 分享给智能体 {target_agent_id} (新键名: {new_key})"
                    }
                else:
                    return {
                        "success": False,
                        "error": "记忆分享失败",
                        "message": f"无法将记忆分享给智能体 {target_agent_id}"
                    }
            

            
            elif tool_name == "create_shared_memory":
                key = tool_args.get("key")
                value = tool_args.get("value")
                shared_with = tool_args.get("shared_with", [])
                description = tool_args.get("description", "")
                
                # 首先在当前智能体中存储记忆
                success = await agent_instance.store_my_memory(key, value)
                if not success:
                    return {
                        "success": False,
                        "error": "创建共享记忆失败：无法在当前智能体中存储记忆",
                        "message": f"共享记忆创建失败: {key}"
                    }
                
                # 使用记忆管理器将记忆分享给指定的智能体
                from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
                memory_manager = get_memory_manager()
                
                shared_results = []
                failed_shares = []
                
                for target_agent_id in shared_with:
                    # 为每个目标智能体创建共享记忆，使用带前缀的键名
                    shared_key = f"shared_{key}_from_{agent_instance.agent_id}"
                    share_success = await memory_manager.share_memory_between_agents(
                        agent_instance.agent_id, target_agent_id, key, shared_key
                    )
                    
                    if share_success:
                        shared_results.append({
                            "agent_id": target_agent_id,
                            "shared_key": shared_key,
                            "success": True
                        })
                    else:
                        failed_shares.append(target_agent_id)
                
                return {
                    "success": True,
                    "key": key,
                    "value": value,
                    "description": description,
                    "creator_agent": agent_instance.agent_id,
                    "shared_with": shared_with,
                    "successful_shares": shared_results,
                    "failed_shares": failed_shares,
                    "share_success_count": len(shared_results),
                    "share_fail_count": len(failed_shares),
                    "message": f"共享记忆创建成功: {key} (成功分享给 {len(shared_results)}/{len(shared_with)} 个智能体)"
                }
            
            else:
                return {
                    "error": f"未知的记忆工具: {tool_name}",
                    "isError": True
                }
        
        except ImportError:
            return {
                "error": "LangGraph增强系统不可用，无法执行记忆工具",
                "isError": True
            }
        except Exception as e:
            logger.error(f"❌ 记忆工具执行失败: {e}")
            return {
                "error": f"记忆工具执行失败: {str(e)}",
                "isError": True
            }
    
    async def _get_current_agent_instance(self, ctx):
        """
        🔍 获取当前智能体实例 - 增强版本
        
        增强的智能体实例获取机制：
        1. 优先从上下文中获取智能体信息
        2. 从线程本地存储中获取 (通过智能体处理时设置)
        3. 从全局智能体池中查找活跃智能体
        4. 通过最近活跃时间推断智能体
        
        Args:
            ctx: 工具调用上下文
            
        Returns:
            ContextOptimizedAgent或None: 智能体实例
        """
        try:
            from backend.langgraph_enhancement.agents.context_optimized_agent import get_agent_pool
            agent_pool = get_agent_pool()
            all_agents = agent_pool.get_all_agents()
            
            # 🔥 策略0 (新增): 尝试从工具调用上下文中获取智能体信息
            if ctx and hasattr(ctx, 'agent_id') and ctx.agent_id:
                agent = agent_pool.get_agent(ctx.agent_id)
                if agent:
                    logger.info(f"🎯 从工具调用上下文获取智能体: {ctx.agent_id}")
                    return agent
                    
            # 🔥 策略1 (优先): 尝试通过线程本地存储获取
            import threading
            current_thread = threading.current_thread()
            
            # 改进：支持多种线程本地存储属性名
            for attr_name in ['current_agent_id', 'agent_id', 'active_agent']:
                if hasattr(current_thread, attr_name):
                    agent_id = getattr(current_thread, attr_name)
                    logger.info(f"🧵 从线程本地存储获取 {attr_name}: {agent_id}")
                    
                    agent = agent_pool.get_agent(agent_id)
                    if agent:
                        logger.info(f"✅ 通过线程本地存储找到智能体: {agent_id}")
                        # 🔥 增强：更新线程本地存储，确保一致性
                        current_thread.current_agent_id = agent_id
                        return agent
                    else:
                        logger.warning(f"⚠️ 线程本地存储中的智能体ID {agent_id} 在池中不存在")
            
            logger.info("ℹ️ 线程本地存储中没有找到有效的智能体ID")
            
            # 🔥 策略2: 如果只有一个活跃智能体，直接返回并设置线程本地存储
            if len(all_agents) == 1:
                agent_id, agent = next(iter(all_agents.items()))
                logger.info(f"🎯 智能体池中只有一个智能体，返回: {agent_id}")
                # 设置线程本地存储，便于后续使用
                current_thread.current_agent_id = agent_id
                return agent
            
            # 🔥 策略3: 如果有多个智能体，尝试从最近活跃的获取
            if all_agents:
                # 选择最近处理时间最晚的智能体
                latest_agent = None
                latest_time = None
                
                for agent_id, agent in all_agents.items():
                    # 改进：支持多种时间戳属性
                    for time_attr in ['last_processing_time', 'last_activity_time', 'created_time']:
                        if hasattr(agent, time_attr):
                            agent_time = getattr(agent, time_attr)
                            if agent_time and (latest_time is None or agent_time > latest_time):
                                latest_time = agent_time
                                latest_agent = agent
                            break
                
                if latest_agent:
                    logger.info(f"🎯 通过最近活跃时间找到智能体: {latest_agent.agent_id}")
                    # 设置线程本地存储
                    current_thread.current_agent_id = latest_agent.agent_id
                    return latest_agent
                
                # 如果没有处理时间信息，返回第一个并警告
                first_agent = next(iter(all_agents.values()))
                logger.warning(f"⚠️ 无法确定活跃智能体，使用第一个: {first_agent.agent_id}")
                logger.warning(f"🔍 可用智能体列表: {list(all_agents.keys())}")
                # 设置线程本地存储
                current_thread.current_agent_id = first_agent.agent_id
                return first_agent
            
            logger.error("❌ 无法找到任何活跃的智能体实例")
            return None
            
        except ImportError:
            logger.error("❌ LangGraph增强系统不可用，无法执行记忆工具")
            return None
        except Exception as e:
            logger.error(f"❌ 获取智能体实例失败: {e}")
            import traceback
            logger.error(f"❌ 完整错误信息: {traceback.format_exc()}")
            return None

# 创建全局AIManager实例
ai_manager = AIManager() 