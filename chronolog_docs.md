# ChronoLog: 带精确定时功能的高级日志记录库

ChronoLog 是一个强大的 Python 日志记录库，它扩展了标准库的日志功能，特别增加了精确定时能力。在性能敏感的应用程序或需要详细时间分析的调试场景中，ChronoLog 能够帮助开发者轻松地记录代码块或操作的执行时间，从而提供更丰富、更有洞察力的日志信息。

## 1. 简介

在开发复杂的系统时，理解各个组件的执行时间至关重要。ChronoLog 旨在简化这一过程，它提供了一套直观的 API，允许您：

*   **轻松测量和记录执行时间**：无论是单个函数调用还是复杂的工作流，ChronoLog 都能自动计算并记录其耗时。
*   **细粒度时间追踪**：通过命名计时器，您可以并行追踪多个操作的耗时，而不会相互干扰。
*   **无缝集成标准日志**：ChronoLog 的输出与 Python 标准 `logging` 模块兼容，您可以利用现有的日志配置（如日志级别、处理器、格式化器等）。
*   **提高可观测性**：通过将时间信息直接嵌入日志，您可以更好地分析应用程序的性能瓶颈，优化关键路径。

ChronoLog 适用于从脚本自动化到大型Web服务，任何需要精确时间记录的场景。

## 2. 快速上手

本节将引导您完成 ChronoLog 的安装和基本使用。

### 2.1 安装

ChronoLog 可以通过 pip 轻松安装：

```bash
pip install chronolog
```

### 2.2 基本用法

ChronoLog 提供了两种主要的时间记录方式：使用 `timed_log` 上下文管理器来记录代码块的执行时间，以及使用 `start_timer` 和 `end_timer` 来手动控制计时。

#### 2.2.1 使用 `timed_log` 上下文管理器

`timed_log` 是测量和记录单个代码块执行时间的推荐方式。它作为一个上下文管理器，在进入和退出块时自动开始和停止计时，并将结果记录到日志中。

**示例：**

```python
import time
from chronolog import timed_log

# 默认情况下，日志会输出到控制台
# 您也可以配置标准logging模块来控制输出

print("\n--- 示例1: 基本 timed_log ---")
with timed_log("Performing a quick operation"): # 默认 INFO 级别
    time.sleep(0.05) # 模拟耗时操作

print("\n--- 示例2: 指定日志级别和logger名称 ---")
import logging

# 配置一个特定的logger，例如，输出到文件
file_logger = logging.getLogger('my_app_logger')
file_logger.setLevel(logging.INFO)
# 如果没有处理器，日志可能不会显示。这里添加一个StreamHandler用于演示。
if not file_logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    file_logger.addHandler(handler)

with timed_log("Executing a longer task", level='DEBUG', logger_name='my_app_logger'):
    time.sleep(0.2)

```

#### 2.2.2 使用 `start_timer` 和 `end_timer`

当您需要更精细地控制计时生命周期，或者需要测量非连续的代码段，又或者需要并行测量多个操作时，`start_timer` 和 `end_timer` 组合非常有用。每个计时器都可以通过一个唯一的 `timer_id` 进行标识。

**示例：**

```python
import time
from chronolog import start_timer, end_timer

print("\n--- 示例3: 使用 start_timer 和 end_timer ---")

# 开始一个默认计时器
start_timer()
time.sleep(0.1)
end_timer(message="Default operation completed")

# 开始一个具名计时器
start_timer('db_query')
time.sleep(0.08) # 模拟数据库查询
end_timer('db_query', message="Database query finished")

# 另一个具名计时器，可能与第一个并行
start_timer('api_call')
time.sleep(0.15) # 模拟API调用
end_timer('api_call', message="API call responded", level='WARNING')

# 测量一段代码，并在中间进行其他操作
start_timer('complex_process')
print("  -> Step 1 of complex process...")
time.sleep(0.05)

# 期间可能做一些不计入 'complex_process' 的事情
start_timer('intermediate_task')
time.sleep(0.02)
end_timer('intermediate_task', message="Intermediate task done")

print("  -> Step 2 of complex process...")
time.sleep(0.07)
end_timer('complex_process', message="Complex process complete")
```

## 3. API 参考

### `chronolog.timed_log(message: str, level: int = logging.INFO, logger_name: str = 'chronolog_logger', log_start: bool = False, log_finish: bool = True, duration_format: str = '{:.2f}ms')`

一个上下文管理器，用于测量并记录一个代码块的执行时间。

*   **参数**:
    *   `message` (`str`): 将要记录的日志消息。在日志输出中，执行时间会自动附加到此消息之后。
    *   `level` (`int`, 可选): 日志级别，默认为 `logging.INFO`。可以是 `logging.DEBUG`, `logging.INFO`, `logging.WARNING`, `logging.ERROR`, `logging.CRITICAL`。
    *   `logger_name` (`str`, 可选): 使用的日志记录器名称，默认为 `'chronolog_logger'`。如果此日志记录器尚未配置，它将使用 Python 的默认配置（通常输出到控制台）。
    *   `log_start` (`bool`, 可选): 如果为 `True`，则在代码块开始时立即记录一条消息（不包含时间）。默认为 `False`。
    *   `log_finish` (`bool`, 可选): 如果为 `True`，则在代码块结束时记录一条包含执行时间的消息。默认为 `True`。
    *   `duration_format` (`str`, 可选): 一个格式字符串，用于格式化持续时间。默认值为 `'{:.2f}ms'`，表示毫秒并保留两位小数。时间总是以秒为单位计算，但会根据此格式化字符串显示。

*   **用法示例**:

    ```python
    from chronolog import timed_log
    import time
    import logging

    # 为 chronolog_logger 配置 StreamHandler 以便在控制台看到输出
    logger = logging.getLogger('chronolog_logger')
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    with timed_log("Complex calculation", level=logging.INFO, log_start=True):
        time.sleep(0.123)
    # 预期输出:
    # INFO:chronolog_logger:Complex calculation (开始)
    # INFO:chronolog_logger:Complex calculation (took 123.xx ms)

    with timed_log("Short operation", duration_format='{:.4f}s'):
        time.sleep(0.0015)
    # 预期输出:
    # INFO:chronolog_logger:Short operation (took 0.0015s)
    ```

### `chronolog.start_timer(timer_id: str = 'default') -> float`

启动一个新的计时器或重置一个现有计时器。如果多次调用同一个 `timer_id`，之前的计时将被重置。

*   **参数**:
    *   `timer_id` (`str`, 可选): 计时器的唯一标识符。默认为 `'default'`。

*   **返回**:
    *   `float`: 计时器开始时的当前时间戳（自纪元以来的秒数）。

*   **用法示例**:

    ```python
    from chronolog import start_timer, end_timer
    import time

    start_time_default = start_timer() # 启动默认计时器
    # ... 执行一些操作 ...
    print(f"Default timer started at: {start_time_default}")

    start_timer('data_load') # 启动名为 'data_load' 的计时器
    # ... 加载数据 ...

    # 再次启动 'data_load' 将会重置它
    print("Resetting 'data_load' timer...")
    start_timer('data_load')
    ```

### `chronolog.end_timer(timer_id: str = 'default', message: str = None, level: int = logging.INFO, logger_name: str = 'chronolog_logger', duration_format: str = '{:.2f}ms') -> float`

停止指定 `timer_id` 的计时器，计算持续时间，并可选地将其记录为日志消息。

*   **参数**:
    *   `timer_id` (`str`, 可选): 要停止的计时器的唯一标识符。默认为 `'default'`。如果指定的 `timer_id` 不存在或未启动，则会记录一个警告并返回 `None`。
    *   `message` (`str`, 可选): 将要记录的日志消息。执行时间会自动附加到此消息之后。如果为 `None`，则不会记录日志，但仍会返回持续时间。
    *   `level` (`int`, 可选): 日志级别，默认为 `logging.INFO`。
    *   `logger_name` (`str`, 可选): 使用的日志记录器名称，默认为 `'chronolog_logger'`。
    *   `duration_format` (`str`, 可选): 一个格式字符串，用于格式化持续时间。默认值为 `'{:.2f}ms'`。

*   **返回**:
    *   `float`: 计时器从开始到停止的持续时间（以秒为单位）。如果计时器未找到，则返回 `None`。

*   **用法示例**:

    ```python
    from chronolog import start_timer, end_timer
    import time
    import logging

    # 确保日志器配置
    logger = logging.getLogger('chronolog_logger')
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    start_timer('processing')
    time.sleep(0.35)
    duration = end_timer('processing', message="Data processing complete", level=logging.INFO)
    if duration is not None:
        print(f"Raw duration for 'processing': {duration:.4f} seconds")

    # 尝试结束一个不存在的计时器
    end_timer('non_existent_timer', message="This won't be logged, but a warning might appear.")
    ```

## 4. 故障排除和注意事项

*   **日志配置**: ChronoLog 依赖于 Python 标准库的 `logging` 模块。为了正确查看 ChronoLog 的输出，请确保为相应的 `logger_name` 配置了处理器（Handler）和级别。
*   **计时器未找到**: 如果 `end_timer` 被调用，但对应的 `start_timer` 未曾调用或已被前一个 `end_timer` 清除，它将不会记录时间，并可能打印一个警告信息。
*   **线程安全**: ChronoLog 内部使用线程局部存储来管理计时器，这意味着每个线程都有自己独立的计时器集合，从而确保在多线程环境下计时的准确性而不会相互干扰。

希望这份文档能帮助您高效地使用 ChronoLog 库！
