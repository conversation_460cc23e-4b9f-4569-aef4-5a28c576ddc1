# LangGraph 实战教程 - 第三部分：多代理协作系统

## 1. 多代理系统概述

多代理系统是LangGraph的核心优势之一，它允许多个专门化的AI代理协同工作，解决复杂任务。

### 1.1 核心组件

- **研究代理**：负责信息收集
- **分析代理**：负责数据分析
- **写作代理**：负责内容生成
- **协调器**：管理代理间的协作

### 1.2 状态定义

```python
from typing import TypedDict, List, Dict, Optional
from datetime import datetime

class MultiAgentState(TypedDict):
    """多代理系统状态"""
    task: str
    current_agent: str
    completed_agents: List[str]
    research_data: Dict
    analysis_results: Dict
    draft_content: str
    session_id: str
```

## 2. 基础代理类

```python
from abc import ABC, abstractmethod
from langchain_openai import ChatOpenAI

class BaseAgent(ABC):
    """基础代理抽象类"""
    
    def __init__(self, name: str, model: str = "gpt-4"):
        self.name = name
        self.llm = ChatOpenAI(model=model, temperature=0.1)
    
    @abstractmethod
    def process(self, state: MultiAgentState) -> MultiAgentState:
        """处理任务的核心方法"""
        pass
```

## 3. 具体代理实现

### 3.1 研究代理

```python
class ResearchAgent(BaseAgent):
    """研究代理"""
    
    def process(self, state: MultiAgentState) -> MultiAgentState:
        task = state["task"]
        
        # 模拟研究过程
        research_prompt = f"""
        作为研究专家，请深入研究以下主题：{task}
        
        提供：
        1. 关键概念和定义
        2. 重要事实和数据
        3. 相关理论和观点
        4. 最新发展趋势
        """
        
        response = self.llm.invoke([{"role": "user", "content": research_prompt}])
        
        state["research_data"] = {
            "content": response.content,
            "completed_by": self.name,
            "timestamp": datetime.now().isoformat()
        }
        
        state["completed_agents"].append(self.name)
        state["current_agent"] = self.name
        
        return state
```

### 3.2 分析代理

```python
class AnalysisAgent(BaseAgent):
    """分析代理"""
    
    def process(self, state: MultiAgentState) -> MultiAgentState:
        task = state["task"]
        research_data = state.get("research_data", {})
        
        analysis_prompt = f"""
        基于以下研究资料，进行深入分析：
        
        任务：{task}
        研究资料：{research_data.get('content', '')}
        
        请提供：
        1. 数据分析和洞察
        2. 模式识别和趋势
        3. 关键发现和结论
        4. 实践建议
        """
        
        response = self.llm.invoke([{"role": "user", "content": analysis_prompt}])
        
        state["analysis_results"] = {
            "content": response.content,
            "completed_by": self.name,
            "timestamp": datetime.now().isoformat()
        }
        
        state["completed_agents"].append(self.name)
        state["current_agent"] = self.name
        
        return state
```

### 3.3 写作代理

```python
class WritingAgent(BaseAgent):
    """写作代理"""
    
    def process(self, state: MultiAgentState) -> MultiAgentState:
        task = state["task"]
        research_data = state.get("research_data", {})
        analysis_results = state.get("analysis_results", {})
        
        writing_prompt = f"""
        基于研究和分析结果，创作一篇关于"{task}"的文章。
        
        研究内容：{research_data.get('content', '')[:500]}...
        分析结果：{analysis_results.get('content', '')[:500]}...
        
        要求：
        1. 结构清晰，逻辑合理
        2. 内容准确，有价值
        3. 语言流畅，易于理解
        4. 包含实例和应用
        """
        
        response = self.llm.invoke([{"role": "user", "content": writing_prompt}])
        
        state["draft_content"] = response.content
        state["completed_agents"].append(self.name)
        state["current_agent"] = self.name
        
        return state
```

## 4. 代理协调器

```python
from langgraph.graph import StateGraph, END

class AgentCoordinator:
    """代理协调器"""
    
    def __init__(self):
        self.research_agent = ResearchAgent("ResearchAgent")
        self.analysis_agent = AnalysisAgent("AnalysisAgent")
        self.writing_agent = WritingAgent("WritingAgent")
        
        self.workflow = self._build_workflow()
        self.app = self.workflow.compile()
    
    def _build_workflow(self) -> StateGraph:
        """构建工作流"""
        workflow = StateGraph(MultiAgentState)
        
        # 添加节点
        workflow.add_node("research", self.research_agent.process)
        workflow.add_node("analysis", self.analysis_agent.process)
        workflow.add_node("writing", self.writing_agent.process)
        workflow.add_node("coordinator", self._coordinate)
        
        # 设置入口
        workflow.set_entry_point("coordinator")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "coordinator",
            self._route_next_agent,
            {
                "research": "research",
                "analysis": "analysis",
                "writing": "writing",
                "end": END
            }
        )
        
        # 各代理完成后回到协调器
        workflow.add_edge("research", "coordinator")
        workflow.add_edge("analysis", "coordinator")
        workflow.add_edge("writing", "coordinator")
        
        return workflow
    
    def _coordinate(self, state: MultiAgentState) -> MultiAgentState:
        """协调代理执行"""
        if "completed_agents" not in state:
            state["completed_agents"] = []
        
        return state
    
    def _route_next_agent(self, state: MultiAgentState) -> str:
        """决定下一个执行的代理"""
        completed = set(state.get("completed_agents", []))
        
        if "ResearchAgent" not in completed:
            return "research"
        elif "AnalysisAgent" not in completed:
            return "analysis"
        elif "WritingAgent" not in completed:
            return "writing"
        else:
            return "end"
```

## 5. 使用示例

```python
# 创建协调器
coordinator = AgentCoordinator()

# 执行多代理任务
initial_state = {
    "task": "人工智能在教育领域的应用与发展",
    "session_id": "multi_agent_demo_001"
}

# 运行工作流
result = coordinator.app.invoke(initial_state)

# 查看结果
print("=== 研究结果 ===")
print(result["research_data"]["content"][:300] + "...")

print("\n=== 分析结果 ===")
print(result["analysis_results"]["content"][:300] + "...")

print("\n=== 最终文章 ===")
print(result["draft_content"][:500] + "...")
```

## 6. 高级特性

### 6.1 代理间通信

```python
class CommunicationMixin:
    """代理通信混入类"""
    
    def send_message(self, state: MultiAgentState, to_agent: str, message: str):
        """发送消息给其他代理"""
        if "agent_messages" not in state:
            state["agent_messages"] = []
        
        state["agent_messages"].append({
            "from": self.name,
            "to": to_agent,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
    
    def get_messages(self, state: MultiAgentState) -> List[Dict]:
        """获取发给本代理的消息"""
        messages = state.get("agent_messages", [])
        return [msg for msg in messages if msg["to"] == self.name]
```

### 6.2 错误处理和重试

```python
class RobustAgent(BaseAgent):
    """具有错误处理能力的代理"""
    
    def process(self, state: MultiAgentState) -> MultiAgentState:
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                return self._process_internal(state)
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    state["error"] = f"{self.name} 处理失败: {str(e)}"
                    break
                
        return state
    
    def _process_internal(self, state: MultiAgentState) -> MultiAgentState:
        """实际的处理逻辑"""
        # 子类实现
        pass
```

## 总结

多代理协作系统是LangGraph的强大功能，通过：

1. **专业化分工**：每个代理专注特定任务
2. **协调机制**：统一管理执行流程
3. **状态共享**：代理间无缝数据传递
4. **容错处理**：提高系统稳定性

下一部分将介绍复杂工作流自动化，包括条件分支、循环处理等高级特性。 