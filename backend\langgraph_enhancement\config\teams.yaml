# 🔥 LangGraph 多智能体团队协作配置 (v2.0 - 通用策略版)
# 支持 sequential、parallel、conditional 工作流类型
# 
# ===== 🆕 重大更新: 通用记忆策略架构 =====
#
# 🎆 v2.0 新特性:
#    - 通用记忆策略: 所有团队使用相同的记忆管理模板
#    - 简化团队规则: 团队特定规则使用简短指令，不再是详细步骤
#    - memory_strategy 字段: 指定团队使用的通用策略类型
#    - {{TEAM_ID}} 变量: 通用策略中用于团队ID替换的占位符
#
# 🔄 工作流类型说明:
#    1. sequential_workflow: 顺序工作流 - 智能体按顺序执行，共享上下文
#    2. parallel_workflow: 并行工作流 - 智能体同时执行，协调分工
#    3. conditional_workflow: 条件工作流 - 根据条件路由到专家
#
# 🛠️ 团队配置方式:
#    - memory_strategy: "策略名称" (必须) - 指定使用的通用记忆策略
#    - collaboration_rules: [简短指令] - 团队特定的工作规则，保持简洁
#    - workflow_type: "工作流类型" - 与以前相同，但现在与 memory_strategy 对应
#
# ===== 🎭 上下文共享策略 =====
#
# 📦 context_sharing_strategy 配置说明：
#    - "selective"：选择性共享（推荐）- 只共享相关上下文，保持效率
#    - "full"：完全共享 - 所有智能体共享全部对话历史
#    - "minimal"：最小共享 - 只共享当前任务相关信息
#
# ===== 🔧 并行工作流输出配置 =====
#
# 📊 output_mode（输出模式）：
#    - "complete"：完整输出模式（推荐）- 智能体后台并行执行，完成后展示
#    - "stream"：流式输出模式 - 实时展示智能体执行过程
#
# 🎨 display_mode（展示模式，仅在output_mode="complete"时生效）：
#    - "sequential"：顺序展示 - 按agent_display_order顺序等待并展示
#    - "immediate"：立即展示 - 智能体完成后立即展示，按完成时间顺序
#    - "grouped"：分组展示 - 按agent_groups配置分批展示结果
#
# ===== 🎯 条件路由配置 =====
#
# 🔍 routing_conditions 条件语法：
#    - keyword_match(['关键词1', '关键词2'])：关键词匹配
#    - message_count >= 5：消息数量条件
#    - 逻辑运算：AND, OR, NOT
#    - 条件按定义顺序从上到下评估，匹配第一个满足的条件
#
# 🎯 条件路由结果：
#    - agent: "agent_name" - 路由到单个智能体
#    - agents: ["agent1", "agent2"] + flow: "sequential|parallel" - 路由到多智能体工作流
#

# 🎯 基础团队协作规则 - 所有团队共享
base_collaboration_rules:
  - "Self-introduction (ONLY ONCE): First agent introduces role and tool specialization. Subsequent agents skip introductions."
  - "Tool boundaries: Focus exclusively on your tool. Transfer tasks requiring different tools to appropriate team members."
  - "Team priority: All work serves team goals. Ensure smooth data flow between tools."
  - "Quality assurance: Each agent owns their output quality. Seek support when needed."
  - "MEMORY AWARENESS: Prioritize using team memory (`get_memory(key, team_id='team_name')`) for context before starting."
  - "CONTEXT RELIANCE: If memory is insufficient, rely on conversation context. Do not depend solely on memory."
  - "🔥 DETAILED WORK DOCUMENTATION (CRITICAL): When using 'team_work_details' key, store COMPREHENSIVE work details including: methodology used, data sources consulted, analysis steps taken, challenges encountered, solutions applied, key insights discovered, decision rationale, and actionable recommendations. NEVER store simple summaries - provide complete work processes that enable team members to build upon your contribution."

# 🧠 通用工作流记忆策略 - 适用于所有团队的通用模板
# 团队使用时将 {{TEAM_ID}} 替换为实际的团队ID
universal_memory_strategies:
  # 🔄 顺序工作流通用记忆策略
  sequential_workflow:
    description: "Sequential workflow memory management strategy - agents execute in sequence, subsequent agents build on previous outputs"
    core_patterns:
      team_memory:
        - "🔥 Team Memory (REQUIRED): Use team_id='{{TEAM_ID}}' to access team virtual memory space"
        - "  • Store user requirements: store_memory('user_requirements', 'original request', team_id='{{TEAM_ID}}')"
        - "  • Record work details: store_memory('team_work_details', '🔥 DETAILED WORK CONTENT: agent_name: [Include methodology, data sources, analysis steps, challenges, solutions, insights, rationale, recommendations - NOT summaries]', team_id='{{TEAM_ID}}', mode='append')"
        - "  • Read team context: get_memory('team_work_details', team_id='{{TEAM_ID}}') - understand previous work"
      personal_memory:
        - "🟡 Personal Memory (RECOMMENDED): Use without additional parameters for personal work tracking"
        - "  • Personal progress: store_memory('my_progress', 'PROGRESS: current stage work', mode='append')"
        - "  • Key findings: store_memory('my_insights', 'INSIGHTS: important discoveries', mode='append')"
        - "  • Work output: store_memory('my_output', 'OUTPUT: results to pass to next agent', mode='append')"
      cross_agent_access:
        - "🟢 Cross-agent Access (OPTIONAL): Read other agents' memory when needed"
        - "  • Read previous work: get_memory('some_key', agent_id='previous_agent_id')"
        - "  • Share data: create_shared_memory('shared_data', data, ['next_agent_list'])"
    xml_examples:
      - "📝 Team Memory Storage Example:"
      - "    <use_mcp_tool>"
      - "    <server_name>memory</server_name>"
      - "    <tool_name>store_memory</tool_name>"
      - "    <arguments>{'key': 'user_requirements', 'value': 'user original request', 'team_id': '{{TEAM_ID}}'}</arguments>"
      - "    </use_mcp_tool>"
      - "📝 Team Memory Detailed Work Example (REQUIRED FORMAT):"
      - "    <use_mcp_tool>"
      - "    <server_name>memory</server_name>"
      - "    <tool_name>store_memory</tool_name>"
      - "    <arguments>{'key': 'team_work_details', 'value': 'web_researcher: METHODOLOGY: Used tavily search + knowledge base. DATA SOURCES: 15 academic papers, 8 industry reports. ANALYSIS: Cross-validated findings across sources. CHALLENGES: Limited recent data. SOLUTIONS: Triangulated older sources with expert opinions. KEY INSIGHTS: Market shows 23% growth with seasonal variations. RATIONALE: Prioritized peer-reviewed sources for credibility. RECOMMENDATIONS: Focus on Q2-Q3 opportunities with risk mitigation.', 'team_id': '{{TEAM_ID}}', 'mode': 'append'}</arguments>"
      - "    </use_mcp_tool>"

  # ⚡ 并行工作流通用记忆策略
  parallel_workflow:
    description: "Parallel workflow memory management strategy - agents execute simultaneously, requiring coordination to avoid duplicate work"
    core_patterns:
      team_coordination:
        - "🔥 Team Coordination (REQUIRED): Use team_id='{{TEAM_ID}}' for work allocation coordination"
        - "  • Work assignments: store_memory('work_assignments', 'Agent_X responsible for domain_Y', team_id='{{TEAM_ID}}', mode='append')"
        - "  • Work records: store_memory('team_work_details', '🔥 COMPREHENSIVE CONTENT: agent_name: [Include domain expertise applied, analysis methodology, data quality assessment, limitations identified, validation methods, professional insights, integration opportunities - COMPLETE work documentation]', team_id='{{TEAM_ID}}', mode='append')"
        - "  • Check assignments: get_memory('work_assignments', team_id='{{TEAM_ID}}') - check assigned work before starting"
      personal_work:
        - "🟡 Personal Work (RECOMMENDED): Track individual professional analysis progress"
        - "  • Work progress: store_memory('my_progress', 'PROGRESS: specific analysis task progress', mode='append')"
        - "  • Key findings: store_memory('my_findings', 'FINDINGS: professional domain insights', mode='append')"
        - "  • Final results: store_memory('my_result', 'RESULT: complete analysis from my perspective', mode='append')"
      cross_collaboration:
        - "🟢 Cross-collaboration (OPTIONAL): Read other agents' memory when specific collaboration adds value"
        - "  • Read parallel work: get_memory('some_key', agent_id='parallel_agent_id')"
        - "  • Share findings: create_shared_memory('parallel_insights', data, ['parallel_agent_list'])"
    xml_examples:
      - "📝 Work Assignment Example:"
      - "    <use_mcp_tool>"
      - "    <server_name>memory</server_name>"
      - "    <tool_name>store_memory</tool_name>"
      - "    <arguments>{'key': 'work_assignments', 'value': 'data_calculator focuses on statistical analysis', 'team_id': '{{TEAM_ID}}', 'mode': 'append'}</arguments>"
      - "    </use_mcp_tool>"

  # 🎯 条件工作流通用记忆策略
  conditional_workflow:
    description: "Conditional workflow memory management strategy - route to experts based on conditions, need to record routing decisions and expert results"
    core_patterns:
      team_context:
        - "🔥 Team Context (REQUIRED): Use team_id='{{TEAM_ID}}' to manage routing decisions and expert results"
        - "  • Routing context: get_memory('routing_decision', team_id='{{TEAM_ID}}') - understand selection rationale"
        - "  • User requirements: store_memory('user_requirements', 'original request details', team_id='{{TEAM_ID}}')"
        - "  • Expert results: store_memory('expert_outputs', '🔥 COMPLETE EXPERT ANALYSIS: [Include specialized methodology, domain knowledge applied, analysis depth, quality measures, technical details, professional recommendations, implementation guidance - FULL expert work documentation]', team_id='{{TEAM_ID}}', mode='append')"
        - "  • Decision rationale: store_memory('routing_rationale', 'why this approach is optimal', team_id='{{TEAM_ID}}')"
      specialist_work:
        - "🟡 Specialist Work (RECOMMENDED): Track individual expert analysis process"
        - "  • Work method: store_memory('my_method', 'METHOD: applied domain expertise approach', mode='append')"
        - "  • Expert insights: store_memory('my_analysis', 'ANALYSIS: specialized professional assessment', mode='append')"
        - "  • Quality validation: store_memory('my_confidence', 'CONFIDENCE: high confidence based on expert analysis', mode='append')"
      knowledge_transfer:
        - "🟢 Knowledge Transfer (OPTIONAL): Share professional knowledge when beneficial for routing decisions"
        - "  • Read team routing: get_memory('routing_decision', team_id='{{TEAM_ID}}')"
        - "  • Share expertise: create_shared_memory('expert_knowledge', methods, ['routing_coordinator'])"
    xml_examples:
      - "📝 Routing Decision Record Example:"
      - "    <use_mcp_tool>"
      - "    <server_name>memory</server_name>"
      - "    <tool_name>store_memory</tool_name>"
      - "    <arguments>{'key': 'routing_decision', 'value': 'selected geo_analyzer because request involves geographic analysis', 'team_id': '{{TEAM_ID}}'}</arguments>"
      - "    </use_mcp_tool>"

  # 📋 计划路由工作流通用记忆策略
  planning_router_workflow:
    description: "Planning router workflow memory management strategy - generates task list then routes each task to appropriate experts in sequence"
    core_patterns:
      planning_coordination:
        - "🔥 Team Coordination (REQUIRED): Use team_id='{{TEAM_ID}}' for team memory coordination"
        - "  • 🧐 Review Context FIRST: get_memory('team_work_details', team_id='{{TEAM_ID}}') - Before starting any new task, ALWAYS review the complete history of team progress to understand the current state and build upon previous work."
        - "  • ✍️ Record Detailed Work: store_memory('team_work_details', '🔥 COMPREHENSIVE TASK COMPLETION: agent_name: [Include task understanding, approach taken, resources used, obstacles overcome, detailed findings, integration points, follow-up requirements - COMPLETE task documentation]', team_id='{{TEAM_ID}}', mode='append')"
      task_execution:
        - "🟡 Task Execution (RECOMMENDED): Track individual task processing and expert work"
        - "  • Expert work: store_memory('expert_outputs', 'EXPERT_RESULT: detailed task completion by assigned expert', mode='append')"
      planning_integration:
        - "🟢 Planning Integration (OPTIONAL): Coordinate cross-task dependencies and final integration"
        - "  • Read team context: get_memory('team_work_details', team_id='{{TEAM_ID}}') - understand team progress"
        - "  • Share task results: create_shared_memory('task_integration_data', results, ['task_list_generator'])"
    xml_examples:
      - "📝 Team Work Details Example:"
      - "    <use_mcp_tool>"
      - "    <server_name>memory</server_name>"
      - "    <tool_name>store_memory</tool_name>"
      - "    <arguments>{'key': 'team_work_details', 'value': 'web_researcher: COMPREHENSIVE RESEARCH: Applied systematic search strategy across 12 sources. METHODOLOGY: Cross-validation of data points. CHALLENGES: Conflicting information resolved through source authority ranking. FINDINGS: Market expansion viable with 67% confidence. INTEGRATION: Data prepared for calculator analysis. NEXT STEPS: Recommend statistical validation of growth projections.', 'team_id': '{{TEAM_ID}}', 'mode': 'append'}</arguments>"
      - "    </use_mcp_tool>"

  # 🔧 通用记忆工具 - 所有工作流都可使用
  common_tools:
    troubleshooting:
      - "🔧 Memory Troubleshooting Tools (when unsure about key names):"
      - "  • list_memories() - List all personal memory keys"
      - "  • list_memories(agent_id='other_agent_id') - List teammate's memory keys"
      - "  • search_memories('keyword') - Find memories containing keyword in personal memory"
      - "  • list_all_agents() - View all available agent IDs"
      - "  • get_memory_stats() - Check memory usage statistics"
    api_benefits:
      - "🎆 New Design Advantages:"
      - "  ✅ team_id intuitive team memory access - no longer use agent_id for team memory operations"
      - "  ✅ Unified mode parameter for append/overwrite - no need for separate append_memory tool"
      - "  ✅ Smart routing logic - team_id priority, agent_id secondary, default personal memory"
      - "  ✅ Universal strategy templates - all teams use same memory management patterns"
      - "  ✅ Backward compatible - old agent_id approach still works"

teams:
  # ===== 🔥 顺序工作流团队 (保留2个) =====
  
  # 🌍 综合研究团队 - 顺序工作流
  comprehensive_research_team:
    name: "综合研究团队"
    description: "专业信息研究团队，通过专家顺序协作提供全面的研究分析解决方案"
    
    # 三个专业智能体按顺序协作：搜索 → 计算 → 文档
    agents:
      - "web_researcher"
      - "data_calculator"
      - "file_manager"
    
    workflow_type: "sequential"
    
    # 上下文共享策略 - 选择性共享以保持效率
    context_sharing_strategy: "selective"
    
    # 应用通用策略：sequential_workflow
    memory_strategy: "sequential_workflow"
    
    # 团队特定协作规则 - 围绕团队目标的详细协作
    collaboration_rules:
      - "🎯 TEAM GOAL FIRST: The primary objective is to deliver a comprehensive research analysis. All actions must align with this goal."
      - "🧐 CONTEXT IS KING (READ FIRST): Before starting your task, you MUST retrieve the full history of work from team memory using get_memory('team_work_details', team_id='comprehensive_research_team'). This is critical for understanding context and building upon previous work."
      - "✍️ DETAILED MEMORY (WRITE LAST): Upon completion, you MUST record your FULL, DETAILED work process into team memory using store_memory('team_work_details', '...', team_id='comprehensive_research_team', mode='append'). Do not save summaries; save your methodology, findings, and rationale."
      - "⚡ SEQUENTIAL EXECUTION: The workflow is strictly sequential: web_researcher → data_calculator → file_manager. Each agent builds on the detailed memory of the previous one."
      - "🔍 PROACTIVE RESEARCH: Autonomously use your tools to find information. Never ask the user for information you can find yourself."

  # 🌍 专业旅游规划团队 - 顺序工作流（5个智能体保证质量）
  comprehensive_travel_team:
    name: "专业旅游规划团队"
    description: "高质量旅游规划团队，通过五个专业智能体顺序协作提供端到端旅游解决方案"
    
    # 五个专业智能体按顺序协作：搜索 → 地理 → 图表 → 流程 → 文档
    agents:
      - "web_researcher"
      - "geo_analyzer"
      - "chart_creator"
      - "flow_designer"
      - "file_manager"
    
    workflow_type: "sequential"
    
    # 上下文共享策略 - 选择性共享以保持效率
    context_sharing_strategy: "selective"
    
    # 应用通用策略：sequential_workflow
    memory_strategy: "sequential_workflow"
    
    # 团队特定协作规则 - 专业旅游规划团队协作
    collaboration_rules:
      - "🎯 TEAM GOAL FIRST: The primary objective is to produce a high-quality, end-to-end travel plan. All actions must serve this goal."
      - "🧐 CONTEXT IS KING (READ FIRST): Before you begin, you MUST retrieve the complete planning history from team memory using get_memory('team_work_details', team_id='comprehensive_travel_team'). Understanding the work of previous agents is not optional."
      - "✍️ DETAILED MEMORY (WRITE LAST): After completing your part, you MUST save a COMPREHENSIVE record of your work to team memory via store_memory('team_work_details', '...', team_id='comprehensive_travel_team', mode='append'). Include data sources, analysis, and decision rationale, not just results."
      - "⚡ SEQUENTIAL EXECUTION: The workflow is strictly sequential: web_researcher → geo_analyzer → chart_creator → flow_designer → file_manager. Ensure a smooth handoff by providing detailed memory."
      - "🌍 PROACTIVE PLANNING: Autonomously research all aspects of the travel plan. Do not rely on the user to provide details you can find with your tools."

  # ===== 🔥 并行工作流团队 (保持2个智能体) =====
  
  # 📊 分析可视化团队 - 并行工作流  
  analytics_visualization_team:
    name: "分析可视化团队"
    description: "数据分析可视化团队，两个专家同时从不同角度进行数据处理和可视化"
    
    # 两个智能体并行工作：计算 + 图表
    agents:
      - "data_calculator"
      - "chart_creator"
    
    # 🔄 工作流类型：parallel（并行执行）
    # 数据计算和图表创建可以同时进行，提高效率
    workflow_type: "parallel"
    
    # 📊 输出模式配置 - 立即展示模式
    output_mode: "complete"     # 完整输出模式：后台并行执行，完成后展示完整结果
    display_mode: "immediate"   # 立即展示模式：智能体完成后立即展示，按完成时间顺序
    
    # 应用通用策略：parallel_workflow
    memory_strategy: "parallel_workflow"
    
    collaboration_rules:
      - "🎯 TEAM GOAL FIRST: The team's mission is to deliver a combined data analysis and visualization report. Both agents work towards this unified output."
      - "🧐 COORDINATE VIA MEMORY (READ FIRST): To avoid duplicate work and ensure synergy, you MUST check the team's memory at the start using get_memory('team_work_details', team_id='analytics_visualization_team'). Understand what your counterpart is doing or has done."
      - "✍️ DETAILED MEMORY (WRITE LAST): As you make progress, you MUST record your DETAILED analysis or visualization process into team memory using store_memory('team_work_details', '...', team_id='analytics_visualization_team', mode='append'). This allows for dynamic collaboration and a richer final integration."
      - "🔄 PARALLEL WORK, UNIFIED GOAL: data_calculator and chart_creator work in parallel. Constant communication through detailed team memory is key to success."

  # ===== 🔥 条件路由团队 (排除evaluator_agent) =====
  
  # 🔀 智能路由团队 - 条件工作流
  intelligent_routing_team:
    name: "智能路由团队"
    description: "基于内容类型、复杂度和用户需求的智能任务路由系统"
    
    # 包含所有7个原子化专家智能体（排除evaluator_agent，新增greeting_handler）
    agents:
      - "web_researcher"
      - "data_calculator"
      - "file_manager"
      - "chart_creator"
      - "flow_designer"
      - "geo_analyzer"
      - "greeting_handler"
    
    # 🔄 工作流类型：conditional（条件路由）
    # 根据用户输入的关键词和复杂度智能选择最合适的专家处理任务
    workflow_type: "conditional"
    
    # 🔗 上下文共享策略：selective（选择性共享）
    # 根据路由条件只共享相关上下文，避免信息过载
    context_sharing_strategy: "selective"
    
    # 🔥 条件路由配置 - 按定义顺序从上到下评估
    # 系统会从第一个条件开始检查，匹配到第一个满足的条件就停止评估
    routing_conditions:
      
      # ===== 条件1：问候与简单交互 =====
      # 🤝 匹配问候和简单交互关键词，路由到问候处理专家
      greeting_interaction:
        condition: "keyword_match(['你好', '您好', '嗨', '哈喽', '早上好', '下午好', '晚上好', '问候', '打招呼', '介绍', '自我介绍', '欢迎', '帮助', '功能', '能力', '可以做什么', '怎么用', '使用方法', '系统介绍', '开始', '开始使用', '第一次', '新用户', '谢谢', '感谢', '再见', '拜拜', '结束'])"
        agent: "greeting_handler"
        description: "用户问候、系统介绍和简单交互处理"
      
      # ===== 条件2：地理旅游规划 =====
      # 🏖️ 匹配旅游相关关键词，路由到地理分析专家
      travel_planning:
        condition: "keyword_match(['旅游', '旅行', '出行', '度假', '休假', '假期', '景点', '名胜', '风景区', '酒店', '民宿', '客栈', '机票', '航班', '火车票', '高铁', '行程', '计划', '攻略', '指南', '目的地', '城市', '国家', '地区', '签证', '护照', '住宿', '预订', '交通', '路线', '线路', '自驾', '包车', '导游', '旅行社', '团队游', '自由行', '背包客', '亲子游', '蜜月游', '商务旅行', '距离', '地理', '位置', '坐标'])"
        agent: "geo_analyzer"
        description: "旅游和地理相关的规划和信息请求"
      
      # ===== 条件3：数据计算分析 =====
      # 🔢 匹配数据计算关键词，路由到数据计算专家
      data_calculation:
        condition: "keyword_match(['计算', '数学', '统计学', '数理统计', '平均数', '中位数', '标准差', '方差', '相关性', '回归', '概率', '百分比', '增长率', 'ROI', 'NPV', '复合增长率', '数值', '数字', '算法', '数学公式', '预测', '建模', '数值计算'])"
        agent: "data_calculator"
        description: "数值计算和统计分析任务"
      
      # ===== 条件4：图表可视化 =====
      # 📈 匹配图表可视化关键词，路由到图表创建专家
      chart_visualization:
        condition: "keyword_match(['可视化', '柱状图', '折线图', '饼图', '散点图', '直方图', '条形图', '趋势图', '对比图', '统计图', '数据图', '数据可视化', '图形展示', '图像展示', '可视化展示', '图表制作', '图形制作', '数据展示'])"
        agent: "chart_creator"
        description: "数据可视化和图表创建任务"
      
      # ===== 条件5：流程设计 =====
      # 🔄 匹配流程图关键词，路由到流程设计专家
      process_design:
        condition: "keyword_match(['流程图', '流程', '工作流', '步骤', '流程设计', '过程', '程序', '架构图', '结构图', '关系图', '思维导图', '决策树', '组织架构', '系统架构', '业务流程', '操作流程', 'mermaid', '图示', '示意图'])"
        agent: "flow_designer"
        description: "流程设计和图表创建任务"
      
      # ===== 条件6：文件管理 =====
      # 📁 匹配文件操作关键词，路由到文件管理专家
      file_management:
        condition: "keyword_match(['文件', '文档', '保存', '读取', '写入', '创建', '删除', '管理', '整理', '组织', '目录', '文件夹', '路径', '文件系统', '存储', '备份', 'JSON', 'CSV', 'TXT', '格式', '文件操作'])"
        agent: "file_manager"
        description: "文件操作和文档管理任务"
      
      # ===== 条件7：网络搜索研究 =====
      # 🔍 匹配搜索研究关键词，路由到网络搜索专家
      web_research:
        condition: "keyword_match(['搜索', '查找', '信息搜索', '网络调研', '在线调查', '信息收集', '资料收集', '数据搜集', '市场调研', '网络搜索', '在线搜索', '最新信息', '当前', '现状', '趋势', '新闻', '资讯', '了解', '查询', '信息查找'])"
        agent: "web_researcher"
        description: "网络搜索和信息研究任务"
      
      # ===== 条件8：复杂多步骤任务 =====
      # 🔄 消息数量≥6的复杂任务或包含复杂关键词，启动多专家顺序协作流程
      complex_multi_step:
        condition: "message_count >= 6 OR keyword_match(['综合分析', '全面分析', '深度分析', '系统分析', '多维度', '多角度', '端到端', '完整方案', '整体解决方案', '项目规划', '战略规划', '可行性分析', '风险评估', '投资分析', '商业计划书', '完整报告', '综合报告'])"
        agents: ["web_researcher", "data_calculator", "file_manager"]
        flow: "sequential"
        description: "复杂任务：先研究，再分析数据，最后创建综合文档"
      
      # ===== 条件9：中等复杂度任务 =====
      # ⚖️ 消息数量4-6且包含分析关键词，启动双专家并行协作
      medium_complexity:
        condition: "message_count >= 4 AND message_count < 6 AND keyword_match(['深度分析', '详细分析', '专业研究', '深入研究', '全面调查', '全面', '详细', '系统性', '完整性', '综合性', '总结性', '梳理性', '整理性', '比较性', '对比性', '评估性', '评价性'])"
        agents: ["data_calculator", "chart_creator"]
        flow: "parallel"
        description: "中等复杂度的分析任务：数据分析和可视化并行"
    
    # 应用通用策略：conditional_workflow
    memory_strategy: "conditional_workflow"
    
    # 智能路由团队专业协作规则 - 精准路由与专家协作
    collaboration_rules:
      - "🎯 TEAM GOAL FIRST: The goal is to provide the most accurate and efficient solution by routing the user's request to the single best expert."
      - "🧐 EXPERT CONTEXT (READ FIRST): Once routed, the selected expert MUST first understand the full user request and the routing rationale by consulting the team memory, especially 'user_requirements' and 'routing_decision'."
      - "✍️ DETAILED EXPERT MEMORY (WRITE LAST): The expert's responsibility is to perform a deep, comprehensive analysis within their domain and record the FULL work process and findings into 'expert_outputs' in the team memory. This is for quality assurance and team learning."
      - "⚡ PRECISION ROUTING: The system routes based on specific keywords and conditions. The selected agent must focus exclusively on fulfilling the request that triggered their activation."

  # ===== 🧠 记忆工具完整测试团队 =====
  
  # 🧠 记忆工具完整测试团队 - 顺序工作流
  memory_testing_team:
    name: "记忆工具完整测试团队"
    description: "全面测试所有记忆工具功能的专业团队，包括新增的create_shared_memory工具"
    
    # 两个专门的测试智能体按顺序协作
    agents:
      - "memory_store_agent"      # 第一个：负责存储各种测试数据和新工具测试
      - "memory_read_agent"       # 第二个：负责读取验证和跨智能体记忆访问测试
    
    # 🔄 工作流类型：sequential（顺序执行）
    # 必须按顺序执行：先存储数据，再测试跨智能体读取
    workflow_type: "sequential"
    
    # 🔗 上下文共享策略：selective（选择性共享）
    # 共享记忆操作结果，保持测试过程清晰
    context_sharing_strategy: "selective"
    
    # 应用通用策略：sequential_workflow
    memory_strategy: "sequential_workflow"
    
    # 🧠 记忆工具全面测试协作规则 - 系统化测试与验证
    collaboration_rules:
      - "🎯 TEAM GOAL FIRST: The single purpose of this team is to systematically test and validate every feature of the memory system."
      - "🧐 VALIDATE BY READING (READ FIRST): The memory_read_agent's primary job is to start by reading the memory states created by the memory_store_agent. It MUST use get_memory, list_memories, etc., to verify the integrity and correctness of the stored data. The test is only valid if the read data matches the expected data."
      - "✍️ DETAILED TEST LOGS (WRITE LAST): Both agents MUST log their actions and results in extreme detail into 'team_work_details'. This includes what was written, what was read, what was expected, and what the actual result was. No summaries."
      - "🔬 SYSTEMATIC FLOW: The workflow is memory_store_agent (WRITE) → memory_read_agent (READ & VALIDATE). This sequence is critical and must not be altered."
      - "✅ COMPLETE VALIDATION: Tests must confirm that all memory tools, including cross-agent access and shared memory, function exactly as documented. Any deviation is a failure."

  # ===== 🔥 计划路由团队 (新增) =====
  
  # 📋 计划执行团队 - 计划路由工作流
  planning_execution_team:
    name: "计划执行团队"
    description: "智能任务分解与计划执行团队，将复杂请求分解为任务清单并循环路由给合适的专家处理"
    
    # 包含所有6个专家智能体，能够处理各种类型的任务
    agents:
      - "web_researcher"
      - "data_calculator"
      - "file_manager"
      - "chart_creator"
      - "flow_designer"
      - "geo_analyzer"
    
    # 🔄 工作流类型：planning_router（计划路由）
    # 先生成任务清单，然后循环路由每个任务给合适的专家处理
    workflow_type: "planning_router"
    
    # 🔗 上下文共享策略：selective（选择性共享）
    # 根据任务需要有选择地共享上下文，提高处理效率
    context_sharing_strategy: "selective"
    
    # 应用通用策略：planning_router_workflow
    memory_strategy: "planning_router_workflow"
    
    # 🔥 任务分解与路由条件 - 复用intelligent_routing_team的路由条件
    # 这样可以确保每个任务都能被正确路由到合适的专家
    routing_conditions:
      
      # ===== 条件1：地理旅游规划 =====
      travel_planning:
        condition: "keyword_match(['旅游', '旅行', '出行', '度假', '休假', '假期', '景点', '名胜', '风景区', '酒店', '民宿', '客栈', '机票', '航班', '火车票', '高铁', '行程', '计划', '攻略', '指南', '目的地', '城市', '国家', '地区', '签证', '护照', '住宿', '预订', '交通', '路线', '线路', '自驾', '包车', '导游', '旅行社', '团队游', '自由行', '背包客', '亲子游', '蜜月游', '商务旅行', '距离', '地理', '位置', '坐标'])"
        agent: "geo_analyzer"
        description: "旅游和地理相关的规划和信息请求"
      
      # ===== 条件2：数据计算分析 =====
      data_calculation:
        condition: "keyword_match(['计算', '数学', '统计学', '数理统计', '平均数', '中位数', '标准差', '方差', '相关性', '回归', '概率', '百分比', '增长率', 'ROI', 'NPV', '复合增长率', '数值', '数字', '算法', '数学公式', '预测', '建模', '数值计算'])"
        agent: "data_calculator"
        description: "数值计算和统计分析任务"
      
      # ===== 条件3：图表可视化 =====
      chart_visualization:
        condition: "keyword_match(['可视化', '柱状图', '折线图', '饼图', '散点图', '直方图', '条形图', '趋势图', '对比图', '统计图', '数据图', '数据可视化', '图形展示', '图像展示', '可视化展示', '图表制作', '图形制作', '数据展示'])"
        agent: "chart_creator"
        description: "数据可视化和图表创建任务"
      
      # ===== 条件4：流程设计 =====
      process_design:
        condition: "keyword_match(['流程图', '流程', '工作流', '步骤', '流程设计', '过程', '程序', '架构图', '结构图', '关系图', '思维导图', '决策树', '组织架构', '系统架构', '业务流程', '操作流程', 'mermaid', '图示', '示意图'])"
        agent: "flow_designer"
        description: "流程设计和图表创建任务"
      
      # ===== 条件5：文件管理 =====
      file_management:
        condition: "keyword_match(['文件', '文档', '保存', '读取', '写入', '创建', '删除', '管理', '整理', '组织', '目录', '文件夹', '路径', '文件系统', '存储', '备份', 'JSON', 'CSV', 'TXT', '格式', '文件操作'])"
        agent: "file_manager"
        description: "文件操作和文档管理任务"
      
      # ===== 条件6：网络搜索研究 =====
      web_research:
        condition: "keyword_match(['搜索', '查找', '信息搜索', '网络调研', '在线调查', '信息收集', '资料收集', '数据搜集', '市场调研', '网络搜索', '在线搜索', '最新信息', '当前', '现状', '趋势', '新闻', '资讯', '了解', '查询', '信息查找'])"
        agent: "web_researcher"
        description: "网络搜索和信息研究任务"
    
    # 计划执行团队专业协作规则 - 任务分解与循环执行
    collaboration_rules:
      - "🎯 TEAM GOAL FIRST: The goal is to execute a multi-step plan by breaking it into tasks and routing each to the best expert in a cycle."
      - "🧐 CONTEXT IS CRITICAL (READ FIRST): Before starting any task, an expert MUST retrieve the complete history from team memory using get_memory('team_work_details', team_id='planning_execution_team'). This is not optional; it is required for context-aware execution."
      - "✍️ DETAILED MEMORY (WRITE LAST): After completing a task, the expert MUST record their FULL, DETAILED work process into team memory using store_memory. This provides the context for the next expert in the chain."
      - "🔄 CYCLICAL EXECUTION: The team operates in a loop: Plan -> Route Task -> Read Memory -> Execute Task -> Write Memory. This cycle repeats until the plan is complete."
      - "🤝 SEAMLESS HANDOFF: The success of the entire plan depends on the quality and detail of the memory passed between agents. Each agent is responsible for ensuring the next one has everything they need."