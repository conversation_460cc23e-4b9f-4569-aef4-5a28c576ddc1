<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <!-- Color Palette as CSS Variables -->
        <style type="text/css">
            :root {
                --primary-color: #1E40AF;
                --secondary-color: #475569;
                --accent-color: #3B82F6;
                --background-color: #F8FAFC;
                --text-primary: #1E293B;
                --text-secondary: #64748B;
                --text-light: #94A3B8;
                --success-color: #10B981;
                --warning-color: #F59E0B;
                --error-color: #EF4444;
                --info-color: #3B82F6;
                --card-background: #FFFFFF;
                --card-border: #BAE6FD;
                --container-background: #E0F2FE;
                --hover-color: #7DD3FC;
                --active-color: #1E40AF;
                --disabled-color: #64748B;
            }

            /* Font System */
            /* Prioritize Microsoft YaHei for Chinese, Segoe UI for English */
            .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
            .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
            .font-accent { font-family: 'Times New Roman', serif; }

            /* Font Sizes */
            .text-hero-title { font-size: 72px; }
            .text-main-title { font-size: 56px; }
            .text-section-title { font-size: 36px; }
            .text-content-title { font-size: 28px; }
            .text-body { font-size: 22px; }
            .text-small { font-size: 16px; }
            .text-caption { font-size: 14px; }

            /* Font Weights */
            .font-thin { font-weight: 100; }
            .font-light { font-weight: 300; }
            .font-normal { font-weight: 400; }
            .font-medium { font-weight: 500; }
            .font-semibold { font-weight: 600; }
            .font-bold { font-weight: 700; }
            .font-black { font-weight: 900; }

            /* Text Colors */
            .color-text-primary { fill: var(--text-primary); }
            .color-text-secondary { fill: var(--text-secondary); }
            .color-text-light { fill: var(--text-light); }

            /* Element Styling */
            .shadow-subtle { filter: url(#drop-shadow); }
        </style>

        <!-- Gradients -->
        <!-- Background gradient from background_color to container_background -->
        <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stop-color="var(--background-color)" />
            <stop offset="100%" stop-color="var(--container-background)" />
        </linearGradient>

        <!-- Accent gradient for decorative elements (from accent_color to primary_color) -->
        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="var(--accent-color)" />
            <stop offset="100%" stop-color="var(--primary-color)" />
        </linearGradient>

        <!-- Accent color with transparency gradient for tech-like effect -->
        <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="var(--accent-color)" stop-opacity="0.8" />
            <stop offset="100%" stop-color="var(--accent-color)" stop-opacity="0.2" />
        </linearGradient>

        <!-- Drop Shadow Filter -->
        <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="4"/>
            <feOffset dx="0" dy="4"/>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.1"/>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>

    <!-- Background Layer -->
    <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

    <!-- Decorative Geometric Shapes (Bento Grid inspired, with blue palette and subtle transparency) -->
    <!-- Large, subtle rounded rectangle in the bottom right, using container background color -->
    <rect x="1000" y="600" width="800" height="400" rx="24" ry="24" fill="var(--container-background)" opacity="0.7"/>

    <!-- Dynamic accent shapes top left, using primary color with low opacity for depth -->
    <path d="M0 0 H600 V300 L300 0 Z" fill="var(--primary-color)" opacity="0.1"/>
    <path d="M0 0 L600 0 L0 300 Z" fill="var(--primary-color)" opacity="0.05"/>

    <!-- Floating accent circles/dots for a subtle, modern touch -->
    <circle cx="1600" cy="150" r="40" fill="var(--accent-color)" opacity="0.15"/>
    <circle cx="1700" cy="80" r="20" fill="var(--accent-color)" opacity="0.1"/>
    <circle cx="1500" cy="250" r="30" fill="var(--accent-color)" opacity="0.08"/>
    <rect x="1800" y="900" width="60" height="60" rx="12" ry="12" fill="var(--accent-color)" opacity="0.1"/>

    <!-- Main Content Area - Grouped for easy positioning -->
    <!-- Content is positioned within page margins (horizontal: 80px, vertical: 60px) -->
    <!-- The translate values provide an additional offset for visual balance -->
    <g transform="translate(120, 160)">
        <!-- Logo - Top Left of Content Area -->
        <!-- Placeholder for logo image. Adjust x, y, width, height as needed. Max height 80px. -->
        <image x="0" y="-80" width="200" height="80" href="{logo_url}" preserveAspectRatio="xMidYMid meet" class="shadow-subtle"/>

        <!-- Main Title -->
        <!-- Uses hero_title font size and bold weight for strong visual impact -->
        <text x="0" y="0" class="font-primary text-hero-title font-bold color-text-primary">
            <!-- {title} placeholder for the main title, e.g., "商业计划书" -->
            <tspan x="0" dy="0">{title}</tspan>
        </text>

        <!-- Subtitle -->
        <!-- Uses main_title font size and semibold weight for clear hierarchy -->
        <text x="0" y="100" class="font-primary text-main-title font-semibold color-text-secondary">
            <!-- {subtitle} placeholder for the subtitle, e.g., "未来十年战略规划" -->
            <tspan x="0" dy="0">{subtitle}</tspan>
        </text>

        <!-- Additional Information (Date, Author) - Aligned below subtitle, smaller font -->
        <text x="0" y="220" class="font-primary text-body color-text-secondary">
            <!-- {date} placeholder for the date, e.g., "2023年10月26日" -->
            <tspan x="0" dy="0">日期: {date}</tspan>
            <!-- {author} placeholder for the author, e.g., "创新科技团队" -->
            <tspan x="0" dy="40">作者: {author}</tspan>
        </text>
    </g>

    <!-- Page Number - Bottom Right Corner -->
    <text x="1840" y="1020" text-anchor="end" class="font-primary text-small color-text-light">
        1/10
    </text>

    <!-- Decorative Elements: Outline Graphics / Data Visualization Hint -->
    <!-- Simple bar chart outline, suggesting data-driven content -->
    <g transform="translate(1200, 400)" stroke="var(--primary-color)" stroke-width="3" fill="none" opacity="0.6">
        <rect x="0" y="100" width="40" height="150" rx="8" ry="8" />
        <rect x="60" y="50" width="40" height="200" rx="8" ry="8" />
        <rect x="120" y="150" width="40" height="100" rx="8" ry="8" />
        <rect x="180" y="80" width="40" height="170" rx="8" ry="8" />
        <line x1="0" y1="250" x2="220" y2="250" stroke-width="2"/>
    </g>

    <!-- Simple line chart outline, for visual interest and context -->
    <g transform="translate(1300, 150)" stroke="var(--accent-color)" stroke-width="3" fill="none" opacity="0.6">
        <polyline points="0,100 50,50 100,80 150,20 200,60" />
        <circle cx="0" cy="100" r="5" fill="var(--accent-color)" />
        <circle cx="50" cy="50" r="5" fill="var(--accent-color)" />
        <circle cx="100" cy="80" r="5" fill="var(--accent-color)" />
        <circle cx="150" cy="20" r="5" fill="var(--accent-color)" />
        <circle cx="200" cy="60" r="5" fill="var(--accent-color)" />
        <line x1="0" y1="120" x2="200" y2="120" stroke-width="1" stroke="var(--text-light)" stroke-dasharray="5,5"/>
    </g>

    <!-- Abstract large number (e.g., "01" for page 1) as a subtle background element -->
    <text x="1780" y="800" text-anchor="end" class="font-primary font-black" fill="var(--primary-color)" opacity="0.05" style="font-size: 300px;">
        01
    </text>

</svg>