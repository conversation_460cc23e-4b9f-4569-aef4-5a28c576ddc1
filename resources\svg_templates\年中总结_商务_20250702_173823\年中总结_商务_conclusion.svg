<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <style>
    /* CSS Variables for easier editing and theme consistency */
    :root {
      /* Overriding original blue scheme with Bento Grid + Tesla Red */
      --background-color: #000000; /* Pure Black */
      --accent-color: #E31937; /* Tesla Red */
      --text-primary: #F8FAFC; /* White for main text */
      --text-secondary: #94A3B8; /* Light grey for secondary text */
      --card-background: #1A1A1A; /* Very dark grey for card backgrounds */
      --card-border: #333333; /* Dark grey for card borders */
      --shadow-color: rgba(0, 0, 0, 0.5);

      /* Fonts */
      --primary-font: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      --secondary-font: 'Source <PERSON> Sans CN', 'Noto Sans CJK SC', sans-serif;
      --accent-font: 'Times New Roman', serif; /* Not heavily used in this style */

      /* Font Sizes from design norms */
      --hero-title-size: 72px;
      --main-title-size: 56px;
      --section-title-size: 36px;
      --content-title-size: 28px;
      --body-text-size: 22px;
      --small-text-size: 16px;
      --caption-size: 14px;

      /* Font Weights from design norms */
      --font-weight-light: 300;
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      --font-weight-bold: 700;
      --font-weight-black: 900;

      /* Layout Spacing from design norms */
      --margin-horizontal: 80px;
      --margin-vertical: 60px;
      --module-gap: 32px;
      --element-gap: 16px;
      --text-gap: 12px;
      --card-border-radius: 16px; /* Slightly larger for Bento style */
    }

    /* General styles for all text elements */
    text {
      font-family: var(--primary-font);
      fill: var(--text-primary);
    }

    /* Specific text styles based on hierarchy */
    .main-title {
      font-size: var(--main-title-size);
      font-weight: var(--font-weight-bold);
      fill: var(--text-primary);
    }
    .subtitle {
      font-size: var(--content-title-size);
      font-weight: var(--font-weight-light);
      fill: var(--text-secondary);
      font-family: var(--secondary-font); /* English uses secondary font */
    }
    .section-title {
      font-size: var(--section-title-size);
      font-weight: var(--font-weight-bold);
      fill: var(--accent-color); /* Tesla Red for section titles */
    }
    .content-title {
      font-size: var(--content-title-size);
      font-weight: var(--font-weight-semibold);
      fill: var(--text-primary);
    }
    .body-text {
      font-size: var(--body-text-size);
      font-weight: var(--font-weight-normal);
      fill: var(--text-primary);
    }
    .small-text {
      font-size: var(--small-text-size);
      font-weight: var(--font-weight-normal);
      fill: var(--text-secondary);
    }
    .caption-text {
      font-size: var(--caption-size);
      font-weight: var(--font-weight-normal);
      fill: var(--text-secondary);
    }
    .accent-text {
      fill: var(--accent-color);
      font-weight: var(--font-weight-bold);
    }
    .large-number {
      font-size: 180px; /* Super large for emphasis */
      font-weight: var(--font-weight-bold);
      fill: var(--accent-color);
      opacity: 0.08; /* Very subtle, as a background element */
    }

    /* Card styles for Bento Grid effect */
    .bento-card {
      fill: var(--card-background);
      stroke: var(--card-border);
      stroke-width: 1px;
      rx: var(--card-border-radius);
      ry: var(--card-border-radius);
      filter: url(#shadow); /* Apply shadow filter for depth */
    }
    
    /* Icon styling - simple outline */
    .icon {
      stroke: var(--accent-color);
      stroke-width: 2px;
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
  </style>

  <defs>
    <!-- Tesla Red to Transparent Gradient for decorative elements -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--accent-color)" stop-opacity="1"/>
      <stop offset="100%" stop-color="var(--accent-color)" stop-opacity="0"/>
    </linearGradient>

    <!-- Shadow Filter for card elements -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="15" flood-color="var(--shadow-color)"/>
    </filter>

    <!-- Reusable Icons (simplified for SVG template) -->
    <!-- Checkmark Icon -->
    <g id="icon-checkmark" class="icon">
      <polyline points="5 12 10 17 20 7" />
    </g>
    <!-- Arrow Right Icon (for call to action) -->
    <g id="icon-arrow-right" class="icon">
      <polyline points="12 5 19 12 12 19"></polyline>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </g>
    <!-- Mail Icon -->
    <g id="icon-mail" class="icon">
      <rect x="2" y="4" width="20" height="16" rx="2" ry="2"></rect>
      <path d="M22 7L12 14L2 7"></path>
    </g>
    <!-- Phone Icon -->
    <g id="icon-phone" class="icon">
      <path d="M22 16.92v3.08a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3.08A2 2 0 0 1 9 4.11a10.85 10.85 0 0 0 .66 2.81 2 2 0 0 1-0.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-0.45 10.85 10.85 0 0 0 2.81.66A2 2 0 0 1 22 16.92z"></path>
    </g>
    <!-- Globe Icon (for website) -->
    <g id="icon-globe" class="icon">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="2" y1="12" x2="22" y2="12"></line>
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
    </g>
  </defs>

  <!-- Full Background - Pure Black -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-color)"/>

  <!-- Page Header -->
  <g id="header" transform="translate(960, 90)">
    <text class="main-title" text-anchor="middle" y="0">{title}</text>
    <text class="subtitle" text-anchor="middle" y="50">{subtitle}</text>
    <text class="small-text" x="780" y="-40" text-anchor="end">10/10</text>
  </g>

  <!-- Main Content Area - Bento Grid Layout -->
  <g id="content-area">
    <!-- Card 1: Main Conclusions -->
    <rect class="bento-card" x="var(--margin-horizontal)" y="200" width="800" height="400"/>
    <text class="section-title" x="calc(var(--margin-horizontal) + 32)" y="250">核心结论</text>
    <text class="small-text" x="calc(var(--margin-horizontal) + 32)" y="285" fill="var(--text-secondary)">Core Conclusions</text>

    <g id="conclusion-points" transform="translate(calc(var(--margin-horizontal) + 32), 330)">
      <g>
        <use href="#icon-checkmark" x="0" y="-12" width="24" height="24"/>
        <text class="body-text" x="40" y="0">
          <tspan x="40" dy="0">销售额同比增长 {content} %，市场份额稳步提升。</tspan>
          <tspan x="40" dy="35" class="small-text" fill="var(--text-secondary)">Sales increased by {content}% year-on-year, market share steadily growing.</tspan>
        </text>
      </g>
      <g transform="translate(0, 80)">
        <use href="#icon-checkmark" x="0" y="-12" width="24" height="24"/>
        <text class="body-text" x="40" y="0">
          <tspan x="40" dy="0">产品创新 {content} 项，用户满意度达 {content} 分。</tspan>
          <tspan x="40" dy="35" class="small-text" fill="var(--text-secondary)">{content} product innovations, user satisfaction reached {content} points.</tspan>
        </text>
      </g>
      <g transform="translate(0, 160)">
        <use href="#icon-checkmark" x="0" y="-12" width="24" height="24"/>
        <text class="body-text" x="40" y="0">
          <tspan x="40" dy="0">团队协作效率显著提升，项目按期完成率 {content} %。</tspan>
          <tspan x="40" dy="35" class="small-text" fill="var(--text-secondary)">Team collaboration efficiency improved, {content}% project completion rate.</tspan>
        </text>
      </g>
    </g>

    <!-- Card 2: Action Points - Larger card for emphasis -->
    <rect class="bento-card" x="calc(var(--margin-horizontal) + 800 + var(--module-gap))" y="200" width="880" height="600"/>
    <text class="section-title" x="calc(var(--margin-horizontal) + 800 + var(--module-gap) + 32)" y="250">行动要点</text>
    <text class="small-text" x="calc(var(--margin-horizontal) + 800 + var(--module-gap) + 32)" y="285" fill="var(--text-secondary)">Actionable Insights 和#38; Next Steps</text>

    <g id="action-points" transform="translate(calc(var(--margin-horizontal) + 800 + var(--module-gap) + 32), 330)">
      <g>
        <text class="large-number" x="0" y="80">01</text>
        <text class="content-title" x="120" y="0">市场拓展</text>
        <text class="body-text" x="120" y="35">
          <tspan x="120" dy="0">深化区域市场渗透，开拓 {content} 个新渠道。</tspan>
          <tspan x="120" dy="30" class="small-text" fill="var(--text-secondary)">Deepen regional market penetration, open {content} new channels.</tspan>
        </text>
      </g>
      <g transform="translate(0, 150)">
        <text class="large-number" x="0" y="80">02</text>
        <text class="content-title" x="120" y="0">技术创新</text>
        <text class="body-text" x="120" y="35">
          <tspan x="120" dy="0">投入 {content} 万元研发预算，推出 {content} 款颠覆性产品。</tspan>
          <tspan x="120" dy="30" class="small-text" fill="var(--text-secondary)">Invest {content} million in R和#38;D, launch {content} disruptive products.</tspan>
        </text>
      </g>
      <g transform="translate(0, 300)">
        <text class="large-number" x="0" y="80">03</text>
        <text class="content-title" x="120" y="0">人才培养</text>
        <text class="body-text" x="120" y="35">
          <tspan x="120" dy="0">启动 {content} 项人才发展计划，提升团队核心竞争力。</tspan>
          <tspan x="120" dy="30" class="small-text" fill="var(--text-secondary)">Initiate {content} talent development programs, enhance core competitiveness.</tspan>
        </text>
      </g>
    </g>

    <!-- Card 3: Contact Info & Call to Action -->
    <rect class="bento-card" x="var(--margin-horizontal)" y="calc(200 + 400 + var(--module-gap))" width="800" height="380"/>
    <text class="section-title" x="calc(var(--margin-horizontal) + 32)" y="calc(200 + 400 + var(--module-gap) + 50)">联系我们</text>
    <text class="small-text" x="calc(var(--margin-horizontal) + 32)" y="calc(200 + 400 + var(--module-gap) + 85)" fill="var(--text-secondary)">Get in Touch</text>

    <g id="contact-info" transform="translate(calc(var(--margin-horizontal) + 32), calc(200 + 400 + var(--module-gap) + 130))">
      <g>
        <use href="#icon-mail" x="0" y="-12" width="24" height="24"/>
        <text class="body-text" x="40" y="0">邮箱: {author}和#64;example.com</text>
        <text class="small-text" x="40" y="25" fill="var(--text-secondary)">Email: {author}和#64;example.com</text>
      </g>
      <g transform="translate(0, 60)">
        <use href="#icon-phone" x="0" y="-12" width="24" height="24"/>
        <text class="body-text" x="40" y="0">电话: +86 123 4567 8900</text>
        <text class="small-text" x="40" y="25" fill="var(--text-secondary)">Phone: +86 123 4567 8900</text>
      </g>
      <g transform="translate(0, 120)">
        <use href="#icon-globe" x="0" y="-12" width="24" height="24"/>
        <text class="body-text" x="40" y="0">网址: www.yourcompany.com</text>
        <text class="small-text" x="40" y="25" fill="var(--text-secondary)">Website: www.yourcompany.com</tspan>
      </g>
    </g>

    <!-- Thank You and Call to Action (Big, prominent, centered in remaining space) -->
    <g id="call-to-action" transform="translate(960, 850)">
      <text class="main-title accent-text" text-anchor="middle" y="0">
        <tspan x="0" dy="0">感谢您的聆听</tspan>
        <tspan x="0" dy="60" class="subtitle" fill="var(--text-primary)">Thank You For Your Attention</tspan>
      </text>
      <text class="content-title accent-text" text-anchor="middle" y="150">
        <tspan x="0" dy="0">期待您的反馈和建议！</tspan>
        <tspan x="0" dy="40" class="small-text" fill="var(--text-secondary)">We Look Forward To Your Feedback And Suggestions!</tspan>
      </text>
      <!-- Decorative arrow/line with gradient -->
      <line x1="-150" y1="230" x2="150" y2="230" stroke="url(#accentGradient)" stroke-width="4" stroke-linecap="round"/>
      <use href="#icon-arrow-right" x="140" y="218" width="24" height="24"/>
    </g>

    <!-- Decorative Elements - Tesla Red Gradients & Geometric Shapes -->
    <g id="decorative-elements">
      <!-- Top-right large abstract shape (subtle) -->
      <path d="M1840 0 C1900 100 1800 250 1700 200 C1600 150 1550 50 1600 0 Z" fill="url(#accentGradient)" opacity="0.1"/>
      <circle cx="1700" cy="150" r="80" fill="var(--accent-color)" opacity="0.05"/>

      <!-- Bottom-left abstract shape (subtle) -->
      <path d="M80 1080 C0 980 150 900 250 950 C350 1000 400 1080 300 1080 Z" fill="url(#accentGradient)" opacity="0.1" transform="scale(-1, 1) translate(-380, 0)"/>
      <rect x="100" y="980" width="150" height="15" fill="var(--accent-color)" opacity="0.2" rx="5" ry="5"/>

      <!-- Subtle line patterns within cards for separation -->
      <line x1="calc(var(--margin-horizontal) + 32)" y1="310" x2="calc(var(--margin-horizontal) + 800 - 32)" y2="310" stroke="var(--card-border)" stroke-width="1"/>
      <line x1="calc(var(--margin-horizontal) + 800 + var(--module-gap) + 32)" y1="310" x2="calc(var(--margin-horizontal) + 800 + var(--module-gap) + 880 - 32)" y2="310" stroke="var(--card-border)" stroke-width="1"/>
      <line x1="calc(var(--margin-horizontal) + 32)" y1="755" x2="calc(var(--margin-horizontal) + 800 - 32)" y2="755" stroke="var(--card-border)" stroke-width="1"/>

      <!-- Abstract data lines for visual interest in the action points card (mimicking charts) -->
      <polyline points="1000 700 1100 680 1200 720 1300 690 1400 710 1500 670 1600 690 1700 650 1800 680"
                stroke="var(--accent-color)" stroke-width="2" fill="none" opacity="0.3"/>
      <polyline points="1000 750 1100 730 1200 770 1300 740 1400 760 1500 720 1600 740 1700 700 1800 730"
                stroke="var(--text-secondary)" stroke-width="1" fill="none" opacity="0.2"/>
    </g>

    <!-- Logo Placeholder (Top Left, as per guidelines) -->
    <g id="logo-placeholder">
      <rect x="var(--margin-horizontal)" y="40" width="120" height="40" fill="var(--card-background)" rx="8" ry="8" filter="url(#shadow)"/>
      <text x="calc(var(--margin-horizontal) + 60)" y="68" text-anchor="middle" class="small-text" fill="var(--text-primary)">{logo_url}</text>
    </g>

    <!-- Date and Author (Subtle, bottom right) -->
    <g id="footer-info">
      <text x="1840" y="1040" text-anchor="end" class="caption-text">
        <tspan x="1840" dy="0">报告人: {author}</tspan>
        <tspan x="1840" dy="20">日期: {date}</tspan>
      </text>
    </g>

  </g>
</svg>