#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 智能体记忆访问实战示例

展示智能体如何在实际场景中访问和使用记忆
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)


class SmartCustomerServiceAgent:
    """智能客服示例 - 展示记忆访问的实际应用"""
    
    def __init__(self, agent):
        self.agent = agent
    
    async def handle_customer_interaction(self, customer_id: str, message: str) -> str:
        """
        处理客户交互 - 展示智能体如何主动访问记忆
        """
        
        print(f"\n🔍 客服智能体开始处理客户 {customer_id} 的请求...")
        
        # 1. 🧠 主动获取客户记忆
        print("📖 正在加载客户历史记忆...")
        
        customer_profile = await self.agent.get_my_memory(f"customer_{customer_id}", {
            "name": "新客户",
            "preferred_language": "中文", 
            "service_level": "标准",
            "previous_issues": [],
            "satisfaction_score": 5,
            "last_contact": None
        })
        
        print(f"✅ 客户档案: {customer_profile['name']} (满意度: {customer_profile['satisfaction_score']}/5)")
        
        # 2. 🔍 搜索相关历史问题
        print("🔍 搜索相关历史问题...")
        
        # 从消息中提取关键词搜索
        keywords = message.lower().split()
        relevant_cases = {}
        
        for keyword in keywords[:3]:  # 取前3个关键词
            if len(keyword) > 2:
                cases = await self.agent.search_my_memories(keyword)
                relevant_cases.update(cases)
        
        if relevant_cases:
            print(f"📋 找到 {len(relevant_cases)} 个相关历史案例")
        
        # 3. 🧠 获取服务知识库
        service_knowledge = await self.agent.get_my_memory("service_knowledge", {})
        
        # 4. 🚀 构建个性化响应
        print("🚀 构建个性化响应...")
        
        personalized_context = f"""
        客户信息：
        - 姓名：{customer_profile['name']}
        - 偏好语言：{customer_profile['preferred_language']}
        - 服务等级：{customer_profile['service_level']}
        - 历史满意度：{customer_profile['satisfaction_score']}/5
        
        历史问题：{len(customer_profile['previous_issues'])} 个
        相关案例：{len(relevant_cases)} 个
        
        请根据客户历史和偏好，为以下问题提供个性化回复：
        {message}
        """
        
        # 5. 🎯 使用记忆增强处理
        state = {
            "messages": [
                {"role": "user", "content": personalized_context}
            ]
        }
        
        # 调用记忆增强处理（会自动加载更多记忆）
        result = await self.agent.process_with_memory_enhancement(state)
        response = result.get("content", "抱歉，无法处理您的请求。")
        
        # 6. 💾 更新客户记忆
        print("💾 更新客户记忆...")
        
        # 记录本次交互
        interaction_record = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "response": response,
            "keywords": keywords[:3]
        }
        
        customer_profile["previous_issues"].append(interaction_record)
        customer_profile["last_contact"] = datetime.now().isoformat()
        
        # 只保留最近20次交互
        if len(customer_profile["previous_issues"]) > 20:
            customer_profile["previous_issues"] = customer_profile["previous_issues"][-20:]
        
        # 保存更新的客户档案
        await self.agent.store_my_memory(f"customer_{customer_id}", customer_profile)
        
        # 7. 📊 更新服务统计
        service_stats = await self.agent.get_my_memory("service_stats", {
            "total_customers": 0,
            "total_interactions": 0,
            "common_issues": {}
        })
        
        service_stats["total_interactions"] += 1
        
        # 统计常见问题
        for keyword in keywords[:3]:
            if len(keyword) > 2:
                service_stats["common_issues"][keyword] = service_stats["common_issues"].get(keyword, 0) + 1
        
        await self.agent.store_my_memory("service_stats", service_stats)
        
        print(f"✅ 处理完成，已更新客户记忆和服务统计")
        
        return response


class LearningResearchAgent:
    """学习型研究智能体 - 展示知识积累和记忆应用"""
    
    def __init__(self, agent):
        self.agent = agent
    
    async def conduct_research(self, topic: str) -> Dict[str, Any]:
        """
        进行研究 - 展示智能体如何积累和应用知识记忆
        """
        
        print(f"\n🔬 研究智能体开始研究主题: {topic}")
        
        # 1. 🧠 检查是否有相关研究历史
        print("📚 检查现有知识库...")
        
        # 搜索相关研究
        related_research = await self.agent.search_my_memories(topic)
        
        if related_research:
            print(f"✅ 发现 {len(related_research)} 项相关研究")
            
            # 获取最新的研究成果
            latest_research = None
            for key, value in related_research.items():
                if isinstance(value, dict) and "timestamp" in value:
                    if latest_research is None or value["timestamp"] > latest_research["timestamp"]:
                        latest_research = value
        else:
            print("ℹ️ 未发现相关历史研究")
            latest_research = None
        
        # 2. 🎯 获取专业知识库
        knowledge_base = await self.agent.get_my_memory("knowledge_base", {})
        domain_knowledge = knowledge_base.get(self._extract_domain(topic), {})
        
        # 3. 📊 获取研究统计
        research_stats = await self.agent.get_my_memory("research_stats", {
            "total_topics": 0,
            "successful_research": 0,
            "research_domains": {}
        })
        
        print(f"📊 历史研究统计: {research_stats['total_topics']} 个主题")
        
        # 4. 🚀 构建研究策略
        research_strategy = self._build_research_strategy(
            topic, latest_research, domain_knowledge, research_stats
        )
        
        print(f"🎯 研究策略: {research_strategy['approach']}")
        
        # 5. 🔍 执行研究
        state = {
            "messages": [
                {"role": "user", "content": research_strategy["prompt"]}
            ]
        }
        
        result = await self.agent.process_with_memory_enhancement(state)
        
        # 6. 💾 保存研究成果
        print("💾 保存研究成果到记忆...")
        
        research_findings = {
            "topic": topic,
            "findings": result.get("content", ""),
            "timestamp": datetime.now().isoformat(),
            "research_strategy": research_strategy["approach"],
            "domain": self._extract_domain(topic),
            "related_topics": self._extract_related_topics(result.get("content", "")),
            "confidence_score": self._calculate_confidence(result)
        }
        
        # 保存到专门的研究记忆
        await self.agent.store_my_memory(f"research_{topic.replace(' ', '_')}", research_findings)
        
        # 7. 🧠 更新知识库
        domain = research_findings["domain"]
        if domain not in knowledge_base:
            knowledge_base[domain] = {}
        
        knowledge_base[domain][topic] = {
            "summary": research_findings["findings"][:500],  # 保存摘要
            "timestamp": research_findings["timestamp"],
            "confidence": research_findings["confidence_score"]
        }
        
        await self.agent.store_my_memory("knowledge_base", knowledge_base)
        
        # 8. 📈 更新研究统计
        research_stats["total_topics"] += 1
        research_stats["successful_research"] += 1 if research_findings["confidence_score"] > 0.7 else 0
        research_stats["research_domains"][domain] = research_stats["research_domains"].get(domain, 0) + 1
        
        await self.agent.store_my_memory("research_stats", research_stats)
        
        print(f"✅ 研究完成，知识库已更新")
        
        return research_findings
    
    def _extract_domain(self, topic: str) -> str:
        """提取研究领域"""
        # 简单的领域分类逻辑
        tech_keywords = ["AI", "机器学习", "深度学习", "算法", "编程"]
        business_keywords = ["市场", "营销", "商业", "管理", "策略"]
        
        topic_lower = topic.lower()
        
        if any(keyword.lower() in topic_lower for keyword in tech_keywords):
            return "技术"
        elif any(keyword.lower() in topic_lower for keyword in business_keywords):
            return "商业"
        else:
            return "通用"
    
    def _build_research_strategy(self, topic, latest_research, domain_knowledge, stats):
        """构建研究策略"""
        
        if latest_research:
            approach = "增量研究"
            prompt = f"基于已有研究成果，深度分析 {topic} 的最新发展和未覆盖领域"
        elif domain_knowledge:
            approach = "领域扩展"
            prompt = f"结合领域知识，全面研究 {topic}"
        else:
            approach = "全新研究" 
            prompt = f"从零开始全面研究 {topic}"
        
        return {
            "approach": approach,
            "prompt": prompt
        }
    
    def _extract_related_topics(self, content: str) -> list:
        """提取相关主题"""
        # 简化实现，实际可以使用NLP技术
        return []
    
    def _calculate_confidence(self, result: Dict) -> float:
        """计算研究置信度"""
        # 简化实现，基于结果长度和质量
        content = result.get("content", "")
        if len(content) > 1000:
            return 0.9
        elif len(content) > 500:
            return 0.7
        else:
            return 0.5


async def demonstrate_memory_access():
    """演示智能体记忆访问"""
    
    print("🧠 智能体记忆访问演示")
    print("=" * 60)
    
    try:
        # 这里需要导入实际的智能体类
        from backend.langgraph_enhancement.agents.context_optimized_agent import ContextOptimizedAgent
        
        # 创建客服智能体
        customer_service_config = {
            "role_name": "智能客服",
            "enable_memory_access": True,
            "model": "gemini-2.5-flash-preview-05-20"
        }
        
        cs_agent = ContextOptimizedAgent(
            agent_id="customer_service",
            config=customer_service_config,
            system_prompt="你是一个智能客服，能够记住客户信息并提供个性化服务。"
        )
        
        # 创建客服助手
        cs_assistant = SmartCustomerServiceAgent(cs_agent)
        
        # 演示客服记忆访问
        print("\n🎯 演示场景1: 智能客服记忆访问")
        
        # 第一次交互
        response1 = await cs_assistant.handle_customer_interaction(
            "CUST001", 
            "你好，我想了解一下你们的退货政策"
        )
        
        # 第二次交互（同一客户）
        response2 = await cs_assistant.handle_customer_interaction(
            "CUST001",
            "我上次问的退货问题解决了，现在想问一下换货流程"
        )
        
        # 创建研究智能体
        research_config = {
            "role_name": "研究专家",
            "enable_memory_access": True,
            "model": "gemini-2.5-flash-preview-05-20"
        }
        
        research_agent = ContextOptimizedAgent(
            agent_id="research_expert",
            config=research_config,
            system_prompt="你是一个研究专家，能够积累知识并进行深入研究。"
        )
        
        # 创建研究助手
        research_assistant = LearningResearchAgent(research_agent)
        
        # 演示研究记忆访问
        print("\n🎯 演示场景2: 研究智能体知识积累")
        
        # 第一次研究
        research1 = await research_assistant.conduct_research("人工智能在客服中的应用")
        
        # 第二次相关研究
        research2 = await research_assistant.conduct_research("AI客服系统的技术架构")
        
        print("\n🎉 记忆访问演示完成！")
        
        # 显示记忆统计
        cs_stats = await cs_agent.get_my_memory_stats()
        research_stats = await research_agent.get_my_memory_stats()
        
        print(f"\n📊 客服智能体记忆统计: {cs_stats}")
        print(f"📊 研究智能体记忆统计: {research_stats}")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(demonstrate_memory_access())