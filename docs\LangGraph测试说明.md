# LangGraph 测试说明

## 概述

本项目提供了基于JIMU项目配置的LangGraph综合测试套件，使用 **Gemini 2.5 Flash Preview (05-20)** 模型进行测试。

## 文件结构

```
docs/
├── langgraph-tutorial-test.py     # 完整测试套件
├── run_langgraph_test.py          # 快速测试脚本
├── LangGraph测试说明.md           # 本说明文档
├── langgraph-tutorial-index.md    # 教程总索引
├── langgraph-tutorial-part1.md    # 快速入门教程
├── langgraph-tutorial-part2.md    # 智能问答机器人
├── langgraph-tutorial-part3.md    # 多代理协作系统
└── langgraph-tutorial-part4.md    # 最佳实践指南
```

## 快速开始

### 1. 安装依赖

```bash
pip install langgraph requests typing_extensions
```

### 2. 快速测试

运行简化版测试，验证基本功能：

```bash
python docs/run_langgraph_test.py
```

**输出示例：**
```
🚀 开始LangGraph快速测试
----------------------------------------
✅ requests 已安装
✅ LangGraph 已安装
📡 使用模型: gemini-2.5-flash-preview-05-20
🔍 搜索服务: tavily

=== 快速功能验证 ===
✅ LLM测试: 你好！我是 Gemini，一个由 Google 开发的大型语言模型...
✅ 搜索测试: 成功获取1条结果
✅ 工作流测试: LangGraph 是一个用于构建有状态的、多参与者应用程序的库...

🎉 快速测试完成！
```

### 3. 完整测试

运行全套测试，包含所有功能验证：

```bash
python docs/langgraph-tutorial-test.py
```

## 测试内容

### 基础组件测试
- ✅ **LLM连接测试**: 验证与Gemini模型的连接
- ✅ **搜索连接测试**: 验证与JIMU搜索服务的连接

### Gemini模型功能测试
- ✅ **多轮对话**: 测试上下文记忆能力
- ✅ **结构化输出**: 测试JSON等格式化输出
- ✅ **中文处理**: 验证中文理解和生成质量

### 工作流测试
- ✅ **简单工作流**: 基础节点和边的功能
- ✅ **搜索增强工作流**: 集成搜索功能的智能问答
- ✅ **多代理工作流**: 研究员→写作者→审查员协作
- ✅ **高级工作流模式**: 条件分支、智能路由

### 性能测试
- ✅ **LLM缓存性能**: 验证缓存机制的有效性
- ✅ **并发搜索性能**: 测试多线程搜索能力
- ✅ **响应速度测试**: 评估模型响应时间

## 配置说明

### LLM配置
```python
JIMU_LLM_BASE_URL = "http://jimu.ffa.chat/v1"
JIMU_API_KEY = "sk-Z0MdU0NAXCmiwYF_iz-gu4aqoEg8XSYGUL3IR32geJ7ZlaflLmzJVENtrEk"
CURRENT_MODEL = "gemini-2.5-flash-preview-05-20"
```

### 搜索服务配置
```python
JIMU_SEARCH_BASE_URL = "http://************:9999/api/v1"
SYSTEM_TOKEN = "jimu_system_2024"
SUPPORTED_PROVIDERS = ["tavily", "exa", "jina", "firecrawl"]
```

## 测试结果

测试完成后会生成详细报告：

- **控制台输出**: 实时测试状态和结果
- **JSON报告**: `langgraph_test_report.json` 包含详细数据

**示例报告结构：**
```json
{
  "summary": {
    "total": 15,
    "success": 14,
    "failure": 1,
    "success_rate": 93.3
  },
  "details": [
    {
      "test_name": "LLM连接测试",
      "result": "响应: 1+1等于2...",
      "success": true,
      "timestamp": "2025-01-23T10:30:45"
    }
  ]
}
```

## 故障排除

### 常见问题

1. **LangGraph导入失败**
   ```bash
   pip install langgraph
   ```

2. **LLM调用失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 确认服务地址可访问

3. **搜索服务连接失败**
   - 检查搜索服务是否运行在 `http://************:9999`
   - 验证系统token是否有效

4. **工作流执行错误**
   - 检查状态定义是否正确
   - 确认节点函数返回值格式

### 调试模式

在测试文件中启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展测试

### 添加自定义测试

1. 在 `TestRunner` 类中添加新的测试方法：

```python
def test_custom_feature(self):
    """自定义功能测试"""
    try:
        # 你的测试逻辑
        result = your_test_function()
        self.log_test("自定义测试", f"结果: {result}")
    except Exception as e:
        self.log_test("自定义测试", f"失败: {str(e)}", False)
```

2. 在 `run_all_tests` 方法中调用：

```python
def run_all_tests(self):
    # ... 现有测试 ...
    self.test_custom_feature()
```

### 测试不同模型

修改 `JimuLLMManager` 中的 `current_model`：

```python
self.current_model = "gpt-4o-mini"  # 或其他支持的模型
```

## 联系支持

如遇到问题，请检查：
1. 网络连接状态
2. 服务配置是否正确
3. 依赖包版本兼容性

---

**最后更新**: 2025-01-23  
**测试环境**: Python 3.8+, LangGraph 0.2+  
**支持模型**: Gemini 2.5 Flash Preview (05-20) 