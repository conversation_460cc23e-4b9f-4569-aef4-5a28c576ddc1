.card-title-with-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  /* 确保标题容器稳定，不受变换影响 */
  position: relative;
  transform: none !important;
  transition: none !important;
}

.toggle-button {
  padding: 0 6px;
  font-size: 0.75rem;
  height: 20px;
  line-height: 20px;
  /* 确保按钮有足够的点击区域和稳定的位置 */
  min-width: 40px;
  position: relative;
  z-index: 10;
  /* 防止按钮受到父元素变换的影响 */
  transform: none !important;
  transition: none !important;
}

.tool-call-card, 
.tool-result-card, 
.tool-error-card, 
.code-card, 
.default-card {
  border-width: 1px;
  border-radius: 4px;
  /* 防止动画和过渡效果导致跳动 */
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* 确保Ant Design Card组件不会有动画效果 */
.tool-call-card .ant-card,
.tool-result-card .ant-card,
.tool-error-card .ant-card,
.code-card .ant-card,
.default-card .ant-card {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* 确保Card的头部和按钮区域稳定 */
.tool-call-card .ant-card-head,
.tool-result-card .ant-card-head,
.tool-error-card .ant-card-head,
.code-card .ant-card-head,
.default-card .ant-card-head {
  animation: none !important;
  transition: none !important;
  transform: none !important;
  position: relative !important;
}

/* 确保Card的内容区域变化不影响头部 */
.tool-call-card .ant-card-body,
.tool-result-card .ant-card-body,
.tool-error-card .ant-card-body,
.code-card .ant-card-body,
.default-card .ant-card-body {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* 禁用hover效果以避免跳动 */
.tool-call-card:hover,
.tool-result-card:hover,
.tool-error-card:hover,
.code-card:hover,
.default-card:hover {
  transform: none !important;
  box-shadow: none !important;
  background: inherit !important;
}

.tool-call-card {
  border-color: #1890ff;
}

.tool-result-card {
  border-color: #52c41a;
}

.tool-error-card {
  border-color: #ff4d4f;
}

.code-card {
  border-color: #722ed1;
}

.error-card {
  border-color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.05);
}

.default-card {
  border-color: #d9d9d9;
} 