<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- CSS Styles -->
    <style type="text/css">
      <![CDATA[
        /* Color Palette */
        .bg-color { fill: #000000; } /* Pure Black Background as per enhanced aesthetics */
        .primary-color { fill: #1E40AF; } /* Dark Blue */
        .secondary-color { fill: #475569; } /* Slate Blue */
        .accent-color { fill: #3B82F6; } /* Bright Blue */
        .text-primary { fill: #F8FAFC; } /* Light text for black background */
        .text-secondary { fill: #94A3B8; } /* Lighter text for black background */
        .highlight-red { fill: #E31937; } /* Tesla Red Highlight */
        .card-bg { fill: #1A1A1A; } /* Slightly lighter black for card backgrounds */
        .card-border { stroke: #333333; } /* Dark grey border for cards */

        /* Font Families */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        /* Font Sizes and Weights */
        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* For large numbers/titles */
        .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
        .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
        .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
        .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }
        .bold { font-weight: 700; }
        .semibold { font-weight: 600; }

        /* Icon Style */
        .icon-stroke { stroke: #3B82F6; stroke-width: 2; fill: none; } /* Blue for outline icons */
        .icon-fill { fill: #3B82F6; } /* Blue for filled icons */

        /* Card Styles */
        .card-style {
          fill: #1A1A1A; /* Dark card background */
          stroke: #333333; /* Dark border */
          stroke-width: 1px;
          rx: 12px; /* border-radius */
          ry: 12px;
        }

        /* Shadows */
        .shadow-filter {
          filter: url(#drop-shadow);
        }
      ]]>
    </style>

    <!-- Gradients -->
    <!-- Primary Gradient (Blue, for general elements) -->
    <linearGradient id="primary-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <!-- Accent Gradient (Blue, for specific elements) -->
    <linearGradient id="accent-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Highlight Red Gradient (Transparency for tech feel) -->
    <linearGradient id="highlight-red-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.1" />
      <stop offset="50%" stop-color="#E31937" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#E31937" stop-opacity="0.1" />
    </linearGradient>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-5%" y="-5%" width="110%" height="110%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="5" result="blur" />
      <feOffset dx="0" dy="5" result="offsetBlur" />
      <feFlood flood-color="#000000" flood-opacity="0.3" result="offsetColor" />
      <feComposite in="offsetColor" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

  </defs>

  <!-- Main Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Page Content Group (with margins applied) -->
  <g transform="translate(80 60)">
    <!-- Top Left - Logo Placeholder -->
    <image x="0" y="0" width="160" height="40" href="{logo_url}" />

    <!-- Header Section -->
    <g transform="translate(0 40)">
      <text x="880" y="60" text-anchor="middle" class="main-title font-primary text-primary">
        <tspan>{title}</tspan>
      </text>
      <text x="880" y="120" text-anchor="middle" class="section-title font-secondary text-secondary">
        <tspan>{subtitle}</tspan>
      </text>
    </g>

    <!-- Decorative Red highlight line below header -->
    <rect x="0" y="180" width="1760" height="5" fill="url(#highlight-red-gradient)" />

    <!-- Main Content Grid (Bento Grid Inspired Layout) -->
    <!-- Content area: 1760px width, ~700px height below header -->

    <!-- Left Column: Main Content Block -->
    <g transform="translate(0 200)">
      <rect x="0" y="0" width="1050" height="680" class="card-style shadow-filter" />

      <text x="40" y="60" class="content-title font-primary text-primary">核心业务概述</text>

      <text x="40" y="110" class="body-text font-secondary text-secondary">
        <tspan x="40" dy="0">{content}</tspan>
        <tspan x="40" dy="30">我们专注于提供创新的解决方案，以满足市场不断变化的需求。</tspan>
        <tspan x="40" dy="30">本计划将详细阐述我们的市场定位、竞争优势和未来增长策略。</tspan>
      </text>

      <!-- Bullet Points Section -->
      <g transform="translate(40 280)">
        <text x="0" y="0" class="content-title font-primary text-primary">关键要点</text>

        <!-- List Item 1 -->
        <circle cx="10" cy="40" r="5" class="highlight-red" />
        <text x="30" y="45" class="body-text font-secondary text-primary">
          <tspan class="bold">市场分析和机遇识别：</tspan> 深入洞察行业趋势，发现潜在增长点。
        </text>

        <!-- List Item 2 -->
        <circle cx="10" cy="90" r="5" class="highlight-red" />
        <text x="30" y="95" class="body-text font-secondary text-primary">
          <tspan class="bold">财务预测和投资回报：</tspan> 详细的财务模型，展示强劲的盈利能力。
        </text>

        <!-- List Item 3 -->
        <circle cx="10" cy="140" r="5" class="highlight-red" />
        <text x="30" y="145" class="body-text font-secondary text-primary">
          <tspan class="bold">风险评估和缓解策略：</tspan> 全面分析风险，并制定应对计划。
        </text>

        <!-- List Item 4 -->
        <circle cx="10" cy="190" r="5" class="highlight-red" />
        <text x="30" y="195" class="body-text font-secondary text-primary">
          <tspan class="bold">团队实力和执行能力：</tspan> 经验丰富的团队，确保战略有效实施。
        </text>
      </g>
    </g>

    <!-- Right Column: Visuals and Hero Number -->
    <g transform="translate(1100 200)">
      <!-- Hero Number / Key Metric Card -->
      <rect x="0" y="0" width="660" height="300" class="card-style shadow-filter" />
      <text x="330" y="120" text-anchor="middle" class="hero-title font-accent highlight-red">
        <tspan>75%</tspan>
      </text>
      <text x="330" y="180" text-anchor="middle" class="content-title font-primary text-primary">
        <tspan>市场份额增长潜力</tspan>
      </text>
      <text x="330" y="215" text-anchor="middle" class="small-text font-secondary text-secondary">
        <tspan>Potential Market Share Growth</tspan>
      </text>

      <!-- Placeholder for Chart/Image -->
      <rect x="0" y="330" width="660" height="350" class="card-style shadow-filter" />
      <text x="330" y="370" text-anchor="middle" class="content-title font-primary text-primary">数据可视化</text>
      <image x="40" y="400" width="580" height="240" href="{image_url}" preserveAspectRatio="xMidYMid slice" />

      <!-- Simple Bar Chart Placeholder (stylized outline) -->
      <g transform="translate(80 440)">
        <text x="0" y="-10" class="small-text font-secondary text-secondary">年度增长</text>
        <!-- Bar 1 -->
        <rect x="0" y="0" width="50" height="100" fill="url(#primary-gradient)" rx="5" ry="5"/>
        <text x="25" y="120" text-anchor="middle" class="caption-text font-secondary text-light">2022</text>

        <!-- Bar 2 -->
        <rect x="100" y="20" width="50" height="80" fill="url(#accent-gradient)" rx="5" ry="5"/>
        <text x="125" y="120" text-anchor="middle" class="caption-text font-secondary text-light">2023</text>

        <!-- Bar 3 -->
        <rect x="200" y="40" width="50" height="60" fill="url(#primary-gradient)" rx="5" ry="5"/>
        <text x="225" y="120" text-anchor="middle" class="caption-text font-secondary text-light">2024</text>

        <!-- Bar 4 -->
        <rect x="300" y="0" width="50" height="100" fill="url(#accent-gradient)" rx="5" ry="5"/>
        <text x="325" y="120" text-anchor="middle" class="caption-text font-secondary text-light">2025</text>

        <!-- Bar 5 -->
        <rect x="400" y="20" width="50" height="80" fill="url(#primary-gradient)" rx="5" ry="5"/>
        <text x="425" y="120" text-anchor="middle" class="caption-text font-secondary text-light">2026</text>
      </g>
    </g>

    <!-- Footer Section (Page Number, Date, Author) -->
    <g transform="translate(0 920)">
      <text x="0" y="0" class="small-text font-secondary text-secondary">页面 4/10</text>
      <text x="1760" y="0" text-anchor="end" class="small-text font-secondary text-secondary">
        <tspan>{date}</tspan>
        <tspan dx="10">由</tspan>
        <tspan dx="5">{author}</tspan>
      </text>
    </g>

  </g>
</svg>