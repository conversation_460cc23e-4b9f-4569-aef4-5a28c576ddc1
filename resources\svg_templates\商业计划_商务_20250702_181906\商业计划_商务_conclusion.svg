<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette - Defined as CSS variables for easy management -->
    <style type="text/css">
      <![CDATA[
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6; /* Matches accent-color */
      }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Text Colors */
      .text-primary { fill: var(--text-primary); }
      .text-secondary { fill: var(--text-secondary); }
      .text-light { fill: var(--text-light); }
      .text-accent { fill: var(--accent-color); }
      .text-white { fill: #FFFFFF; }

      /* Card Styles */
      .card {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        rx: 12px; /* border-radius */
      }

      /* Shadows */
      .shadow { filter: url(#drop-shadow); }

      /* Icon Styles */
      .icon {
        stroke: var(--info-color); /* Matches accent_color from info_color in palette */
        stroke-width: 2;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      /* Buttons / CTA */
      .cta-button {
        fill: var(--accent-color);
        rx: 8px;
        cursor: pointer;
      }
      .cta-button-text {
        fill: #FFFFFF;
        font-family: var(--primary-font);
        font-size: 24px;
        font-weight: 600;
        text-anchor: middle;
      }

      /* Hover Effects (Basic SVG support for :hover) */
      .cta-button:hover {
        fill: var(--primary-color); /* Darker blue on hover */
      }
      .cta-button-text:hover {
        fill: #F8FAFC; /* Lighter text on hover */
      }

      ]]>
    </style>

    <!-- Linear Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="4"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feBlend in="SourceGraphic" in2="BackgroundImage"/>
    </filter>

    <!-- Icon: Target (Goals) -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" class="icon"/>
      <circle cx="12" cy="12" r="6" class="icon"/>
      <circle cx="12" cy="12" r="2" class="icon"/>
    </symbol>

    <!-- Icon: Handshake (Partnership) -->
    <symbol id="icon-handshake" viewBox="0 0 24 24">
      <path d="M11 18L7 22H4V19L8 15L11 18Z" class="icon"/>
      <path d="M16 11L20 7V4H17L13 8L16 11Z" class="icon"/>
      <path d="M12.5 12.5L16 9L20 13L16.5 16.5L13 20L9 16L12.5 12.5Z" class="icon"/>
    </symbol>

    <!-- Icon: Phone (Contact) -->
    <symbol id="icon-phone" viewBox="0 0 24 24">
      <path d="M22 16.92V20C22 20.5523 21.5523 21 21 21H19C13.4772 21 9 16.5228 9 11V9C9 8.44772 9.44772 8 10 8H13C13.5523 8 14 8.44772 14 9V12C14 12.5523 13.5523 13 13 13H11" class="icon"/>
      <path d="M16.5 3C19.5376 3 22 5.46243 22 8.5C22 11.5376 19.5376 14 16.5 14C13.4624 14 11 11.5376 11 8.5C11 5.46243 13.4624 3 16.5 3Z" class="icon"/>
    </symbol>

    <!-- Icon: Mail (Email) -->
    <symbol id="icon-mail" viewBox="0 0 24 24">
      <path d="M4 4H20C21.1046 4 22 4.89543 22 6V18C22 19.1046 21.1046 20 20 20H4C2.89543 20 2 19.1046 2 18V6C2 4.89543 2.89543 4 4 4Z" class="icon"/>
      <path d="M22 6L12 13L2 6" class="icon"/>
    </symbol>

    <!-- Icon: Location (Address) -->
    <symbol id="icon-location" viewBox="0 0 24 24">
      <path d="M12 2C8.13401 2 5 5.13401 5 9C5 14.25 12 22 12 22C12 22 19 14.25 19 9C19 5.13401 15.866 2 12 2Z" class="icon"/>
      <circle cx="12" cy="9" r="3" class="icon"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-color)"/>

  <!-- Page Header (Top-Left Logo and Page Number) -->
  <g id="page-header">
    <rect x="80" y="60" width="100" height="40" fill="var(--primary-color)" rx="8"/>
    <text x="130" y="87" text-anchor="middle" class="small-text font-primary text-white">LOGO</text>
    <text x="1760" y="87" text-anchor="end" class="small-text font-primary text-secondary">页面 10/10</text>
  </g>

  <!-- Main Content Area - Bento Grid Inspired Layout -->
  <g id="main-content" transform="translate(80 140)">
    <!-- Main Conclusion Card (Large, Left) -->
    <rect x="0" y="0" width="800" height="400" class="card shadow"/>
    <text x="40" y="80" class="section-title font-primary text-primary">
      核心结论 <tspan class="text-accent" font-size="0.8em">| Key Takeaways</tspan>
    </text>
    <text x="40" y="160" class="body-text font-secondary text-primary">
      <tspan x="40" dy="1.6em">1. {title} 市场潜力巨大，创新技术引领行业发展。</tspan>
      <tspan x="40" dy="1.6em">   <tspan class="small-text text-secondary">Significant market potential, innovation drives industry growth.</tspan></tspan>
      <tspan x="40" dy="1.6em">2. {content} 财务模型稳健，预计未来三年实现爆发式增长。</tspan>
      <tspan x="40" dy="1.6em">   <tspan class="small-text text-secondary">Robust financial model, exponential growth projected for next three years.</tspan></tspan>
      <tspan x="40" dy="1.6em">3. {content} 团队经验丰富，拥有核心竞争力。</tspan>
      <tspan x="40" dy="1.6em">   <tspan class="small-text text-secondary">Experienced team with strong core competencies.</tspan></tspan>
    </text>
    <!-- Icon with gradient stroke for emphasis -->
    <use xlink:href="#icon-target" x="700" y="30" width="64" height="64" class="icon" stroke-width="3" stroke="url(#primaryGradient)"/>


    <!-- Action Points Card (Right, Top-Middle) -->
    <rect x="840" y="0" width="840" height="300" class="card shadow"/>
    <text x="880" y="80" class="section-title font-primary text-primary">
      行动要点 <tspan class="text-accent" font-size="0.8em">| Action Plan</tspan>
    </text>
    <text x="880" y="160" class="body-text font-secondary text-primary">
      <tspan x="880" dy="1.6em">● {content} 启动A轮融资，目标金额2000万元人民币。</tspan>
      <tspan x="880" dy="1.6em">   <tspan class="small-text text-secondary">Initiate Series A funding round, target 20M RMB.</tspan></tspan>
      <tspan x="880" dy="1.6em">● {content} 拓展市场份额，深化与战略伙伴的合作。</tspan>
      <tspan x="880" dy="1.6em">   <tspan class="small-text text-secondary">Expand market share, deepen strategic partnerships.</tspan></tspan>
      <tspan x="880" dy="1.6em">● {content} 持续研发投入，保持技术领先优势。</tspan>
      <tspan x="880" dy="1.6em">   <tspan class="small-text text-secondary">Continue R和#38;D investment, maintain technological leadership.</tspan></tspan>
    </text>
    <!-- Icon with gradient stroke for emphasis -->
    <use xlink:href="#icon-handshake" x="1600" y="30" width="64" height="64" class="icon" stroke-width="3" stroke="url(#accentGradient)"/>


    <!-- Contact Information Card (Bottom-Left) -->
    <rect x="0" y="440" width="800" height="300" class="card shadow"/>
    <text x="40" y="500" class="section-title font-primary text-primary">
      联系我们 <tspan class="text-accent" font-size="0.8em">| Contact Us</tspan>
    </text>
    <g transform="translate(40 560)">
      <use xlink:href="#icon-phone" x="0" y="0" width="32" height="32" class="icon"/>
      <text x="50" y="24" class="body-text font-primary text-primary">{phone_number}</text>
      <use xlink:href="#icon-mail" x="0" y="50" width="32" height="32" class="icon"/>
      <text x="50" y="74" class="body-text font-primary text-primary">{email_address}</text>
      <use xlink:href="#icon-location" x="0" y="100" width="32" height="32" class="icon"/>
      <text x="50" y="124" class="body-text font-primary text-primary">{company_address}</text>
      <text x="50" y="148" class="body-text font-primary text-secondary">
        <tspan>{website_url}</tspan>
      </text>
    </g>

    <!-- Call to Action / Thank You Card (Bottom-Right) -->
    <rect x="840" y="340" width="840" height="400" class="card shadow"/>
    <text x="1260" y="430" text-anchor="middle" class="hero-title font-primary text-primary">
      感谢您的时间！
    </text>
    <text x="1260" y="490" text-anchor="middle" class="section-title font-primary text-accent">
      Thank You for Your Time!
    </text>

    <!-- Call to Action Button -->
    <g id="cta-button-group">
      <rect x="1040" y="550" width="440" height="80" class="cta-button"/>
      <text x="1260" y="605" class="cta-button-text font-primary">立即预约洽谈</text>
      <text x="1260" y="635" class="small-text font-primary text-white" text-anchor="middle">Schedule a Meeting Now</text>
    </g>

    <text x="1260" y="700" text-anchor="middle" class="body-text font-secondary text-secondary">
      期待与您携手共创未来。
      <tspan x="1260" dy="1.6em" class="small-text text-light">Looking forward to building the future together.</tspan>
    </text>

  </g>

  <!-- Decorative Elements -->
  <g id="decorative-elements">
    <!-- Subtle wave/line at bottom -->
    <path d="M0 980 Q480 1020 960 980 T1920 980" stroke="var(--card-border)" stroke-width="2" fill="none"/>
    <path d="M0 1000 Q480 1040 960 1000 T1920 1000" stroke="url(#primaryGradient)" stroke-width="3" fill="none" opacity="0.5"/>

    <!-- Corner accent shapes -->
    <rect x="1800" y="0" width="120" height="120" fill="url(#accentGradient)" opacity="0.1" rx="20"/>
    <rect x="0" y="960" width="120" height="120" fill="url(#primaryGradient)" opacity="0.1" rx="20"/>
  </g>

</svg>