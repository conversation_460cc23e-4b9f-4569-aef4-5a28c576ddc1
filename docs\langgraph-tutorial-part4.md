# LangGraph 教程 - 第四部分：最佳实践

## 1. 高级工作流模式

### 1.1 条件分支

```python
from langgraph.graph import StateGraph, END

def route_decision(state):
    """根据条件路由到不同节点"""
    if state.get("urgent"):
        return "urgent_path"
    elif state.get("complex"):
        return "complex_path"
    else:
        return "simple_path"

workflow.add_conditional_edges(
    "decision_node",
    route_decision,
    {
        "urgent_path": "urgent_processor",
        "complex_path": "complex_processor", 
        "simple_path": "simple_processor"
    }
)
```

### 1.2 循环处理

```python
def should_continue(state):
    """判断是否继续循环"""
    return "continue" if state["iteration"] < 3 else "end"

workflow.add_conditional_edges(
    "process_node",
    should_continue,
    {
        "continue": "process_node",  # 循环回自己
        "end": END
    }
)
```

### 1.3 人机交互

```python
from langgraph.checkpoint.memory import MemorySaver

# 编译时设置中断点
app = workflow.compile(
    checkpointer=MemorySaver(),
    interrupt_before=["human_review"]
)

# 使用时提供线程ID
config = {"configurable": {"thread_id": "user_123"}}
result = app.invoke(initial_state, config)

# 人工反馈后继续
updated_state = {"human_feedback": "审核通过"}
final_result = app.invoke(updated_state, config)
```

## 2. 性能优化

### 2.1 缓存机制

```python
import hashlib

class SimpleCache:
    def __init__(self):
        self.cache = {}
    
    def get_key(self, state):
        return hashlib.md5(str(state).encode()).hexdigest()
    
    def get(self, key):
        return self.cache.get(key)
    
    def set(self, key, value):
        self.cache[key] = value

cache = SimpleCache()

def cached_node(state):
    key = cache.get_key(state)
    result = cache.get(key)
    
    if result is None:
        result = expensive_operation(state)
        cache.set(key, result)
    
    return result
```

### 2.2 错误处理

```python
def robust_node(state):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            return process_data(state)
        except Exception as e:
            if attempt == max_retries - 1:
                state["error"] = str(e)
                return state
            time.sleep(2 ** attempt)  # 指数退避
    return state
```

## 3. 监控和调试

### 3.1 执行日志

```python
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def logged_node(state):
    logger.info(f"开始执行节点，输入状态: {state}")
    result = process_function(state)
    logger.info(f"节点执行完成，输出状态: {result}")
    return result
```

### 3.2 状态可视化

```python
def print_state(state, title="当前状态"):
    print(f"\n=== {title} ===")
    for key, value in state.items():
        if isinstance(value, str) and len(value) > 100:
            print(f"{key}: {value[:100]}...")
        else:
            print(f"{key}: {value}")
    print("=" * 20)
```

## 4. 部署和集成

### 4.1 Web API

```python
from fastapi import FastAPI

app = FastAPI()

@app.post("/process")
async def process_request(data: dict):
    try:
        result = langgraph_app.invoke(data)
        return {"success": True, "result": result}
    except Exception as e:
        return {"success": False, "error": str(e)}
```

### 4.2 配置管理

```python
import os

class Config:
    LLM_MODEL = os.getenv("LLM_MODEL", "gpt-3.5-turbo")
    API_KEY = os.getenv("OPENAI_API_KEY")
    MAX_ITERATIONS = int(os.getenv("MAX_ITERATIONS", "5"))
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "true").lower() == "true"

config = Config()
```

## 5. 测试

### 5.1 单元测试

```python
def test_node_function():
    test_state = {"input": "test"}
    result = my_node(test_state)
    assert "output" in result
    assert result["output"] is not None
```

### 5.2 集成测试

```python
def test_complete_workflow():
    initial_state = {"task": "测试任务"}
    result = app.invoke(initial_state)
    assert result["status"] == "completed"
```

## 6. 最佳实践

1. **设计原则**
   - 每个节点职责单一
   - 状态结构简洁明了
   - 合理处理异常情况

2. **性能优化**
   - 缓存昂贵的计算
   - 并行处理独立任务
   - 设置合理的超时

3. **维护性**
   - 模块化设计
   - 配置外置化
   - 完善的文档

4. **监控调试**
   - 添加执行日志
   - 状态可视化
   - 性能指标监控

## 总结

通过本系列教程，你已经学会了：

- LangGraph基础概念和使用
- 构建智能问答机器人
- 实现多代理协作系统
- 应用最佳实践和高级技巧

现在你可以开始构建自己的LangGraph应用了！ 