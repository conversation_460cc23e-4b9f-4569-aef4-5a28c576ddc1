import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, Tooltip, Modal, Tabs, Card, Typography, Empty, Spin, Select, message } from 'antd';
import {
  SendOutlined, LoadingOutlined, PaperClipOutlined, FileImageOutlined,
  ToolOutlined, GlobalOutlined, DownOutlined, XFilled, RobotFilled, CloseOutlined
} from '@ant-design/icons';
import './MessageInput.css';
import { getTools } from '../../mcp';
import { MessageService } from '../../utils/MessageService';
import RoleSelector from '../RoleSelector'; // 导入RoleSelector组件
const { TextArea } = Input;
const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const MessageInput = ({
  onSendMessage,
  loading,
  selectedService = 'openai',
  models = [],
  selectedModel = '',
  onModelChange,
  functionTools = [],
  onSelectFunctionTool,
  mcpServers = {},
  mcpServersStatus = {},
  selectedServers = [],
  onSelectServers,
  onStopGeneration,
  showAttachmentButtons = true,
  isAgentMode = false,
  isWebSearchEnabled = false,
  onUpdateConversationModel = null,
  role_id = null,  // 添加role_id参数
  team_id = null   // 添加team_id参数
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const textAreaRef = useRef(null);
  const [mcpModalVisible, setMcpModalVisible] = useState(false);
  const [loadingTools, setLoadingTools] = useState(false);
  const [tools, setTools] = useState([]);
  const [localWebSearchEnabled, setLocalWebSearchEnabled] = useState(isWebSearchEnabled);
  // 使用传入的role_id作为默认值
  const [selectedRoleId, setSelectedRoleId] = useState(role_id);
  const [imagePreview, setImagePreview] = useState(null); // base64图片
  const [imageFile, setImageFile] = useState(null); // 原始文件
  const [attachmentInfo, setAttachmentInfo] = useState(null); // { filename, filetype, text }
  const [uploadingAttachment, setUploadingAttachment] = useState(false);

  // 当props中的role_id改变时，更新本地状态
  useEffect(() => {
    setSelectedRoleId(role_id);
  }, [role_id]);

  useEffect(() => {
    setLocalWebSearchEnabled(isWebSearchEnabled);
  }, [isWebSearchEnabled]);

  const handleChange = (e) => {
    const value = e.target.value;
    setInputMessage(value);
  };

  const fetchTools = async () => {
    setLoadingTools(true);
    try {
      const toolsList = await getTools();
      setTools(toolsList || []);
    } catch (e) {
      console.error('获取MCP工具列表失败:', e);
      setTools([]);
    } finally {
      setLoadingTools(false);
    }
  };

  const toggleMcpModal = () => {
    if (!mcpModalVisible) {
      fetchTools();
    }
    setMcpModalVisible(!mcpModalVisible);
  };

  // 修改发送消息逻辑，传递角色ID
  const handleSend = () => {
    const trimmedMessage = inputMessage.trim();
    if ((trimmedMessage || imagePreview || attachmentInfo) && !loading) {
      let contentArr = [];
      if (trimmedMessage) {
        contentArr.push({ type: 'text', text: trimmedMessage });
      }
      if (imagePreview) {
        contentArr.push({ type: 'image_url', image_url: { url: imagePreview } });
      }
      if (attachmentInfo) {
        contentArr.push({
          type: 'file',
          filename: attachmentInfo.filename,
          filetype: attachmentInfo.filetype,
          text: attachmentInfo.text, // 只传递到后端，不展示
        });
      }
      // 构建消息选项，实现团队和角色互斥
      const messageOptions = {
        is_web_search_enabled: localWebSearchEnabled,
        supportMultimodal: true
      };

      // 团队和角色互斥逻辑
      if (team_id) {
        messageOptions.team_id = team_id;
        console.log('[MessageInput] 使用团队模式发送消息, team_id:', team_id);
      } else {
        messageOptions.role_id = selectedRoleId;
        console.log('[MessageInput] 使用角色模式发送消息, role_id:', selectedRoleId);
      }

      console.log('[MessageInput] 发送消息的完整选项:', messageOptions);
      onSendMessage(contentArr, selectedModel, messageOptions);
      setInputMessage('');
      setImagePreview(null);
      setImageFile(null);
      setAttachmentInfo(null);
      setTimeout(() => {
        textAreaRef.current?.focus();
        const messagesContainer = document.querySelector('.messages-container');
        if (messagesContainer) {
          messagesContainer.scrollTo({
            top: messagesContainer.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 0);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="message-input-container">
      <div className={`message-input-wrapper ${isAgentMode ? 'agent-mode' : ''}`}>
        <TextArea
          ref={textAreaRef}
          value={inputMessage}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={isAgentMode ? "向积木智能体提问..." : "随便问点什么吧..."}
          autoSize={{ minRows: 1, maxRows: 6 }}
          disabled={loading}
          className="message-textarea"
        />
      </div>

      <div className="input-message-actions">
        <div className="input-left-actions">
          <Tooltip title="Add Attachment">
            <Button
              type="text"
              icon={<PaperClipOutlined />}
              onClick={() => {
                document.getElementById('attachment-file-input')?.click();
              }}
            />
            <input
              id="attachment-file-input"
              type="file"
              accept=".pdf,.docx,.pptx,.xls,.xlsx"
              style={{ display: 'none' }}
              multiple={false}
              onChange={async e => {
                const file = e.target.files[0];
                if (file) {
                  if (file.size > 5 * 1024 * 1024) {
                    Modal.error({
                      title: '文件过大',
                      content: '请选择不超过5M的文件。',
                    });
                    e.target.value = '';
                    return;
                  }
                  setUploadingAttachment(true);
                  const formData = new FormData();
                  formData.append('file', file);
                  try {
                    const res = await fetch('/api/upload-and-parse-file', {
                      method: 'POST',
                      body: formData,
                    });
                    const data = await res.json();
                    if (data.success) {
                      setAttachmentInfo({
                        filename: data.filename,
                        filetype: data.filetype,
                        text: data.text,
                      });
                    } else {
                      //增加弹窗提示框
                      MessageService.error(data.error || '文件上传失败，请重试');
                    }
                  } catch (err) {
                    //增加消息提示框  
                    MessageService.error(err.message || '未知错误');
                  }
                  setUploadingAttachment(false);
                }
                e.target.value = '';
              }}
            />
          </Tooltip>
          <Tooltip title="Use Image">
            <Button
              type="text"
              icon={<FileImageOutlined />}
              onClick={() => {
                document.getElementById('image-file-input')?.click();
              }}
            />
            <input
              id="image-file-input"
              type="file"
              accept=".xbm,.tif,.tiff,.jfif,.pjp,.apng,.jpeg,.heif,.ico,.webp,.svgz,.jpg,.heic,.gif,.svg,.png,.bmp,.pjpeg,.avif"
              style={{ display: 'none' }}
              multiple={false}
              onChange={e => {
                const file = e.target.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (ev) => {
                    setImagePreview(ev.target.result);
                    setImageFile(file);
                  };
                  reader.readAsDataURL(file);
                }
                e.target.value = '';
              }}
            />
          </Tooltip>
        </div>

        <div className="input-right-actions">
          {isAgentMode && (
            <div className="agent-mode-indicator">
              <RobotFilled style={{ color: '#52c41a', marginRight: 4 }} />
              <span>智能体模式</span>
            </div>
          )}

          {/* 在非Agent模式下且没有选择角色时才显示全网搜索按钮 */}
          {!isAgentMode && !selectedRoleId && (
            <Button
              className="input-mode-selector"
              type={localWebSearchEnabled ? "primary" : "default"}
              onClick={() => {
                const newState = !localWebSearchEnabled;
                setLocalWebSearchEnabled(newState);

                if (onUpdateConversationModel) {
                  onUpdateConversationModel(selectedModel, { is_web_search_enabled: newState });
                }
              }}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                visibility: 'visible',
                zIndex: 10
              }}
            >
              <GlobalOutlined style={{ marginRight: 4 }} />
              全网搜索
            </Button>
          )}

          {loading ? (
            <Tooltip title="停止生成">
              <Button
                type="primary"
                danger
                icon={<div style={{ width: 12, height: 12, backgroundColor: 'white', borderRadius: 2 }} />}
                onClick={onStopGeneration}
                className="input-stop-button"
              />
            </Tooltip>
          ) : (
            <Tooltip title="发送 (Enter)">
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSend}
                disabled={!inputMessage.trim()}
                className="input-send-button"
              />
            </Tooltip>
          )}
        </div>
      </div>

      <Modal
        title="可用MCP工具"
        open={mcpModalVisible}
        onCancel={toggleMcpModal}
        footer={null}
        width={800}
      >
        {loadingTools ? (
          <div className="loading-container" style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <Text style={{ display: 'block', marginTop: '16px' }}>正在加载MCP工具...</Text>
          </div>
        ) : (
          <Tabs defaultActiveKey="all">
            <TabPane tab="所有工具" key="all">
              <div className="tools-container" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
                {tools.length > 0 ? (
                  tools.map((tool, index) => (
                    <Card key={index} title={tool.name} className="tool-card" style={{ marginBottom: '16px' }}>
                      <p>{tool.description}</p>
                      <div className="tool-details">
                        <Paragraph>
                          <Text strong>参数：</Text>
                          <ul>
                            {tool.parameters?.properties &&
                              Object.entries(tool.parameters.properties).map(([name, prop]) => (
                                <li key={name}>
                                  <Text code>{name}</Text>: {prop.description || '无描述'}
                                  {tool.parameters?.required?.includes(name) && <Text type="danger"> (必填)</Text>}
                                </li>
                              ))
                            }
                          </ul>
                        </Paragraph>
                      </div>
                    </Card>
                  ))
                ) : (
                  <Empty description="暂无可用的MCP工具" />
                )}
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      <div style={{ marginTop: 8 }}>
        {imagePreview && (
          <div style={{ position: 'relative', display: 'inline-block', maxWidth: 200 }}>
            <img
              src={imagePreview}
              alt="预览"
              style={{ maxWidth: 200, maxHeight: 120, borderRadius: 8, border: '1px solid #eee' }}
            />
            <Button
              size="small"
              type="text"
              style={{
                position: 'absolute',
                top: 2,
                right: 2,
                background: 'rgba(255,255,255,0.9)',
                color: '#ff4d4f',
                border: '1px solid #ff4d4f',
                borderRadius: '50%',
                width: 24,
                height: 24,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                transition: 'background 0.2s, color 0.2s, border 0.2s',
                cursor: 'pointer',
                zIndex: 2
              }}
              onMouseOver={e => {
                e.currentTarget.style.background = '#ff4d4f';
                e.currentTarget.style.color = '#fff';
                e.currentTarget.style.border = '1px solid #ff7875';
              }}
              onMouseOut={e => {
                e.currentTarget.style.background = 'rgba(255,255,255,0.9)';
                e.currentTarget.style.color = '#ff4d4f';
                e.currentTarget.style.border = '1px solid #ff4d4f';
              }}
              icon={<CloseOutlined />}
              onClick={() => {
                setImagePreview(null);
                setImageFile(null);
              }}
            />
          </div>
        )}
      </div>

      {attachmentInfo && (
        <div style={{ marginTop: 8, display: 'flex', alignItems: 'center' }}>
          <PaperClipOutlined style={{ fontSize: 18, marginRight: 8 }} />
          <span>{attachmentInfo.filename}</span>
          <Button
            size="small"
            type="text"
            icon={<CloseOutlined />}
            onClick={() => setAttachmentInfo(null)}
            style={{ marginLeft: 8 }}
          />
          {uploadingAttachment && <Spin size="small" style={{ marginLeft: 8 }} />}
        </div>
      )}
    </div>
  );
};

export default MessageInput; 