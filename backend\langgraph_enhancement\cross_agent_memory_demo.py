#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 跨智能体记忆访问演示

展示智能体自动保存记忆和其他智能体访问记忆的功能
"""

import asyncio
import json
from datetime import datetime


async def demonstrate_cross_agent_memory():
    """演示跨智能体记忆访问功能"""
    
    print("🔄 跨智能体记忆访问演示")
    print("=" * 60)
    
    try:
        from backend.langgraph_enhancement.agents.context_optimized_agent import ContextOptimizedAgent
        from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
        
        # 获取内存管理器
        memory_manager = get_memory_manager()
        
        # 创建三个智能体
        researcher_config = {"role_name": "研究员", "enable_memory_access": True}
        writer_config = {"role_name": "写作者", "enable_memory_access": True}
        reviewer_config = {"role_name": "审核员", "enable_memory_access": True}
        
        researcher = ContextOptimizedAgent("researcher", researcher_config, "你是研究员")
        writer = ContextOptimizedAgent("writer", writer_config, "你是写作者")
        reviewer = ContextOptimizedAgent("reviewer", reviewer_config, "你是审核员")
        
        print("✅ 创建了三个智能体: researcher, writer, reviewer")
        
        # 🔬 第一步：研究员处理任务并自动保存记忆
        print("\n🔬 步骤1: 研究员执行任务 - 自动保存记忆")
        
        research_state = {
            "messages": [
                {"role": "user", "content": "请研究人工智能在教育领域的应用"}
            ]
        }
        
        # 研究员处理任务（会自动保存记忆）
        print("⚡ 研究员开始处理...")
        research_result = await researcher.process_with_memory_enhancement(research_state)
        
        # 手动保存一些研究成果
        await researcher.store_my_memory("research_findings", {
            "topic": "AI在教育领域的应用",
            "key_points": [
                "个性化学习",
                "智能评估", 
                "虚拟助教",
                "自适应学习路径"
            ],
            "confidence_level": 0.85,
            "timestamp": datetime.now().isoformat()
        })
        
        await researcher.store_my_memory("research_status", "completed")
        
        print("✅ 研究员完成任务，记忆已自动保存")
        
        # 检查研究员的记忆
        researcher_memories = await researcher.list_my_memories()
        print(f"📊 研究员记忆数量: {len(researcher_memories)}")
        print(f"📋 研究员记忆键: {researcher_memories}")
        
        # 🔍 第二步：其他智能体查看研究员的记忆
        print("\n🔍 步骤2: 其他智能体查看研究员的记忆")
        
        # 方法1：直接通过内存管理器访问
        print("📖 方法1: 通过内存管理器直接访问")
        
        research_findings = await memory_manager.get_agent_memory(
            "researcher", "research_findings", None
        )
        
        research_status = await memory_manager.get_agent_memory(
            "researcher", "research_status", "unknown"
        )
        
        print(f"✅ 获取研究成果: {research_findings['topic'] if research_findings else '无'}")
        print(f"✅ 获取研究状态: {research_status}")
        
        # 方法2：获取研究员的完整记忆上下文
        print("\n📖 方法2: 获取完整记忆上下文")
        
        researcher_full_context = await memory_manager.get_full_context_for_agent("researcher")
        researcher_agent_memory = researcher_full_context["agent_memory"]
        
        print(f"✅ 研究员完整记忆包含 {len(researcher_agent_memory)} 项:")
        for key in researcher_agent_memory.keys():
            print(f"   - {key}")
        
        # 🔄 第三步：记忆共享机制演示
        print("\n🔄 步骤3: 记忆共享机制演示")
        
        # 3.1 点对点记忆共享
        print("📤 3.1 点对点记忆共享: 研究员 → 写作者")
        
        share_success = await memory_manager.share_memory_between_agents(
            source_agent_id="researcher",
            target_agent_id="writer",
            memory_key="research_findings",
            new_key="source_research"
        )
        
        print(f"✅ 记忆共享结果: {share_success}")
        
        # 验证写作者收到了共享记忆
        writer_received = await writer.get_my_memory("source_research")
        print(f"✅ 写作者接收到研究成果: {writer_received['topic'] if writer_received else '无'}")
        
        # 3.2 团队广播记忆
        print("\n📢 3.2 团队广播记忆")
        
        broadcast_count = await memory_manager.broadcast_memory_to_team(
            sender_agent_id="researcher",
            memory_key="project_milestone",
            value={
                "milestone": "研究阶段完成",
                "next_phase": "内容写作",
                "deadline": "2024-12-31",
                "priority": "high"
            },
            team_agent_ids=["researcher", "writer", "reviewer"]
        )
        
        print(f"✅ 团队广播成功数量: {broadcast_count}/2 (不包括发送者自己)")
        
        # 验证团队成员收到广播
        writer_broadcast = await writer.get_my_memory("team_broadcast_project_milestone")
        reviewer_broadcast = await reviewer.get_my_memory("team_broadcast_project_milestone")
        
        print(f"✅ 写作者收到广播: {writer_broadcast['value']['milestone'] if writer_broadcast else '无'}")
        print(f"✅ 审核员收到广播: {reviewer_broadcast['value']['milestone'] if reviewer_broadcast else '无'}")
        
        # 3.3 创建共享记忆
        print("\n🔗 3.3 创建共享记忆")
        
        shared_success = await memory_manager.create_shared_memory(
            memory_key="team_guidelines",
            value={
                "communication_style": "专业友好",
                "output_format": "markdown",
                "quality_standard": "高标准",
                "collaboration_rules": ["及时沟通", "互相尊重", "追求卓越"]
            },
            agent_ids=["researcher", "writer", "reviewer"]
        )
        
        print(f"✅ 共享记忆创建结果: {shared_success}")
        
        # 验证所有智能体都收到共享记忆
        for agent_name, agent in [("研究员", researcher), ("写作者", writer), ("审核员", reviewer)]:
            shared_guidelines = await agent.get_my_memory("shared_team_guidelines")
            print(f"✅ {agent_name}收到共享指南: {bool(shared_guidelines)}")
        
        # 🔍 第四步：智能体间协作工作流
        print("\n🔍 步骤4: 基于记忆的智能体协作工作流")
        
        # 4.1 写作者基于研究员的记忆进行写作
        print("✍️ 写作者基于研究记忆进行写作")
        
        # 写作者可以访问研究员的记忆来增强自己的处理
        writer_state = {
            "messages": [
                {"role": "user", "content": "基于研究成果，撰写一篇关于AI教育应用的文章"}
            ]
        }
        
        # 写作者在处理前先获取研究员的记忆
        research_context = await memory_manager.get_agent_memory("researcher", "research_findings")
        if research_context:
            # 将研究上下文添加到写作者的记忆中
            await writer.store_my_memory("current_project_context", research_context)
        
        writer_result = await writer.process_with_memory_enhancement(writer_state)
        
        # 4.2 审核员检查所有相关记忆
        print("\n🔎 审核员检查项目记忆")
        
        # 审核员可以查看研究员和写作者的记忆
        research_memories = await memory_manager.get_full_context_for_agent("researcher")
        writer_memories = await memory_manager.get_full_context_for_agent("writer")
        
        print(f"✅ 审核员查看:")
        print(f"   - 研究员记忆: {len(research_memories['agent_memory'])} 项")
        print(f"   - 写作者记忆: {len(writer_memories['agent_memory'])} 项")
        
        # 📊 第五步：记忆统计和分析
        print("\n📊 步骤5: 跨智能体记忆统计")
        
        # 获取所有智能体的记忆统计
        agents_stats = {}
        for agent_name, agent in [("researcher", researcher), ("writer", writer), ("reviewer", reviewer)]:
            stats = await agent.get_my_memory_stats()
            agents_stats[agent_name] = stats
            print(f"📈 {agent_name}记忆统计:")
            print(f"   - 记忆总数: {stats['total_memories']}")
            print(f"   - 访问次数: {stats.get('total_access_count', 0)}")
            if stats.get('most_accessed'):
                most_key, most_count = stats['most_accessed']
                print(f"   - 最常访问: {most_key} ({most_count}次)")
        
        # 🎯 总结演示结果
        print("\n🎯 演示总结")
        print("✅ 智能体自动保存记忆: 每次处理完成后自动保存交互历史、上下文、统计信息")
        print("✅ 跨智能体记忆访问: 通过memory_manager可以访问任何智能体的记忆")
        print("✅ 记忆共享机制: 支持点对点共享、团队广播、创建共享记忆")
        print("✅ 协作工作流: 智能体可以基于其他智能体的记忆进行协作")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def quick_memory_access_demo():
    """快速记忆访问演示"""
    
    print("\n🚀 快速跨智能体记忆访问演示")
    print("-" * 40)
    
    try:
        from backend.langgraph_enhancement.agents.context_optimized_agent import ContextOptimizedAgent
        from backend.langgraph_enhancement.context.memory_manager import get_memory_manager
        
        memory_manager = get_memory_manager()
        
        # 创建两个智能体
        agent_a = ContextOptimizedAgent("agent_a", {"enable_memory_access": True}, "智能体A")
        agent_b = ContextOptimizedAgent("agent_b", {"enable_memory_access": True}, "智能体B")
        
        print("1. 💾 智能体A保存记忆")
        await agent_a.store_my_memory("important_data", "这是重要信息")
        
        print("2. 🔍 智能体B访问智能体A的记忆")
        data = await memory_manager.get_agent_memory("agent_a", "important_data")
        print(f"   智能体B获取到: {data}")
        
        print("3. 🔄 记忆共享")
        success = await memory_manager.share_memory_between_agents(
            "agent_a", "agent_b", "important_data", "received_data"
        )
        print(f"   共享成功: {success}")
        
        print("4. ✅ 智能体B验证接收")
        received = await agent_b.get_my_memory("received_data")
        print(f"   智能体B收到: {received}")
        
        print("✅ 快速演示完成！")
        
    except Exception as e:
        print(f"❌ 快速演示失败: {e}")


if __name__ == "__main__":
    print("🧠 跨智能体记忆系统演示")
    print("=" * 80)
    
    # 运行完整演示
    success = asyncio.run(demonstrate_cross_agent_memory())
    
    # 运行快速演示
    asyncio.run(quick_memory_access_demo())
    
    if success:
        print("\n🎯 结论：跨智能体记忆系统完全可用！")
        print("💡 智能体会自动保存记忆，其他智能体可以通过特定参数访问")
    else:
        print("\n❌ 演示中发现问题，请检查配置")