<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 统一蓝色系配色方案 -->
    <style type="text/css">
      <![CDATA[
        :root {
          --primary-color: #1E40AF;
          --secondary-color: #475569;
          --accent-color: #3B82F6;
          --background-color: #F8FAFC;
          --text-primary: #1E293B;
          --text-secondary: #64748B;
          --text-light: #94A3B8;
          --card-background: #FFFFFF;
          --card-border: #BAE6FD;
          --container-background: #E0F2FE;
          --hover-color: #7DD3FC;
          --active-color: #1E40AF;
          --disabled-color: #64748B;
          --success-color: #10B981;
          --warning-color: #F59E0B;
          --error-color: #EF4444;
          --info-color: #3B82F6;
        }

        /* 字体系统 */
        .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
        .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
        .font-accent { font-family: 'Times New Roman', serif; }

        /* 字体大小 */
        .hero-title { font-size: 72px; }
        .main-title { font-size: 56px; }
        .section-title { font-size: 36px; }
        .content-title { font-size: 28px; }
        .body-text { font-size: 22px; }
        .small-text { font-size: 16px; }
        .caption { font-size: 14px; }

        /* 字体粗细 */
        .font-normal { font-weight: 400; }
        .font-medium { font-weight: 500; }
        .font-semibold { font-weight: 600; }
        .font-bold { font-weight: 700; }

        /* 填充颜色 */
        .fill-primary { fill: var(--primary-color); }
        .fill-secondary { fill: var(--secondary-color); }
        .fill-accent { fill: var(--accent-color); }
        .fill-background { fill: var(--background-color); }
        .fill-text-primary { fill: var(--text-primary); }
        .fill-text-secondary { fill: var(--text-secondary); }
        .fill-card-background { fill: var(--card-background); }
        .fill-container-background { fill: var(--container-background); }

        /* 描边颜色 */
        .stroke-primary { stroke: var(--primary-color); }
        .stroke-accent { stroke: var(--accent-color); }
        .stroke-card-border { stroke: var(--card-border); }

        /* 文本颜色 */
        .text-primary-color { fill: var(--text-primary); }
        .text-secondary-color { fill: var(--text-secondary); }
        .text-accent-color { fill: var(--accent-color); }

        /* 卡片样式 */
        .card-style {
          fill: var(--card-background);
          stroke: var(--card-border);
          stroke-width: 1px;
          rx: 12px; /* border-radius */
          filter: url(#drop-shadow);
        }

        /* 引用页特定样式 */
        .quote-symbol-large {
          fill: var(--accent-color);
          opacity: 0.15; /* 情感表达，微妙的透明度 */
        }
        .quote-text {
          line-height: 1.6; /* relaxed line height */
        }
        .source-text {
          letter-spacing: 0.05em; /* wider letter spacing */
        }
      ]]>
    </style>

    <!-- 渐变定义 (直接在defs中定义，避免CSS中出现&符号) -->
    <linearGradient id="gradientAccentTransparent" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0" />
    </linearGradient>
    <linearGradient id="gradientAccentTransparentReverse" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.6" />
    </linearGradient>

    <!-- 滤镜定义 (阴影效果) -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="subtle-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.08 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 商务图标系统 (简单的勾线图形化图标) -->
    <!-- 商业洞察图标 -->
    <symbol id="icon-insight" viewBox="0 0 24 24">
      <path d="M12 2C8.13401 2 5 5.13401 5 9C5 11.3857 5.9229 13.5658 7.46447 15.1074L8.17157 15.8145C8.87868 16.5216 9.87827 16.9999 11 17H13C14.1217 16.9999 15.1213 16.5216 15.8284 15.8145L16.5355 15.1074C18.0771 13.5658 19 11.3857 19 9C19 5.13401 15.866 2 12 2ZM12 4C14.7614 4 17 6.23858 17 9C17 10.7936 16.2917 12.4419 15.0711 13.6625L14.364 14.3696C13.657 15.0767 12.6574 15.4999 11.5355 15.4999C10.4136 15.4999 9.41401 15.0767 8.70711 14.3696L7.99999 13.6625C6.77943 12.4419 6.07111 10.7936 6.07111 9C6.07111 6.23858 8.30968 4 11.0711 4H12Z" fill="var(--accent-color)"/>
      <path d="M9 22H15C15 20.8954 14.1046 20 13 20H11C9.89543 20 9 20.8954 9 22Z" fill="var(--accent-color)"/>
    </symbol>
    <!-- 增长趋势图标 -->
    <symbol id="icon-growth" viewBox="0 0 24 24">
      <path d="M3 18L9 12L13 16L21 8" stroke="var(--primary-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M17 8H21V12" stroke="var(--primary-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
  </defs>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-color)"/>

  <!-- 装饰性元素：情感化视觉表达与科技感渐变 -->
  <!-- 右上角的大型抽象几何形状 -->
  <circle cx="1600" cy="200" r="400" fill="url(#gradientAccentTransparent)" opacity="0.4"/>
  <!-- 左下角的矩形渐变装饰 -->
  <rect x="100" y="800" width="800" height="300" fill="url(#gradientAccentTransparentReverse)" opacity="0.3" rx="50"/>

  <!-- 主内容区域 (基于布局原则的边距和网格系统) -->
  <g transform="translate(80, 60)">
    <!-- 页面顶部信息 (页眉) -->
    <text x="0" y="36" class="font-primary font-semibold section-title fill-text-secondary" text-anchor="start">
      商业计划书
    </text>
    <text x="1760" y="36" class="font-primary font-semibold section-title fill-text-secondary" text-anchor="end">
      9/10
    </text>

    <!-- 核心引用区块 -->
    <g transform="translate(880, 480)" text-anchor="middle">
      <!-- 引用卡片背景 -->
      <rect x="-600" y="-300" width="1200" height="600" class="card-style" />

      <!-- 大型装饰性引号符号 -->
      <text x="-500" y="-150" class="font-accent quote-symbol-large" font-size="200">“</text>
      <text x="500" y="250" class="font-accent quote-symbol-large" font-size="200">”</text>

      <!-- 引用内容 -->
      <text x="0" y="0" class="font-primary font-bold body-text text-primary-color quote-text" dominant-baseline="middle">
        <tspan x="0" dy="-1.5em">“未来属于那些能够洞察趋势</tspan>
        <tspan x="0" dy="1.6em">和勇于创新的先行者。</tspan>
        <tspan x="0" dy="1.6em">我们的愿景是塑造行业的新标准。”</tspan>
      </text>
      <!-- 占位符示例: -->
      <!--
      <text x="0" y="0" class="font-primary font-bold body-text text-primary-color quote-text" dominant-baseline="middle">
        <tspan x="0" dy="-1.5em">{content}</tspan>
      </text>
      -->

      <!-- 来源信息 -->
      <text x="0" y="150" class="font-secondary font-normal small-text text-secondary-color source-text">
        —— {author}, {date}
      </text>

      <!-- 装饰性图标点缀 -->
      <use href="#icon-insight" x="-580" y="-280" width="60" height="60" />
      <use href="#icon-growth" x="520" y="-280" width="60" height="60" />
    </g>

    <!-- 页脚区域 -->
    <g transform="translate(0, 960)">
      <text x="0" y="0" class="font-primary font-normal small-text fill-text-secondary">
        © 2023 {logo_url} | All Rights Reserved.
      </text>
      <!-- Logo占位符，通常是图片，这里用文本模拟 -->
      <text x="1760" y="0" class="font-primary font-bold small-text fill-primary" text-anchor="end">
        {logo_url}
      </text>
    </g>

  </g>
</svg>