# LangGraph 增强系统 - 上下文管理全局配置文件

# 默认上下文管理配置
# 可以在 agents.yaml 中为单个智能体覆盖这些设置
context_management:
  # 默认压缩触发比例
  # 当 (上下文大小 / max_context_window) > 此值时，触发压缩
  # 允许在 agent.yaml 中覆盖
  default_compression_trigger_ratio: 0.8

  # 上下文健康检查阈值 (单位: 字符)
  # 在 context_manager.analyze_context_health 中使用
  health_check_threshold: 50000

  # 工作流优化器节点的触发阈值 (单位: 字符)
  # 在 workflow_engine._context_optimizer_node 中使用
  optimizer_node_threshold: 20000

  # 压缩引擎中各级别的压缩阈值 (单位: 字符)
  # 在 compression_engine.adaptive_compression 中使用
  compression_levels:
    light: 4000
    medium: 8000
    heavy: 16000 