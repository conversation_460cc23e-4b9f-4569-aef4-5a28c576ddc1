<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MPV研究报告可视化</title>
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Framer Motion CDN -->
    <script src="https://cdn.jsdelivr.net/npm/framer-motion@10.16.4/dist/framer-motion.min.js"></script>
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --tesla-red: #E31937;
            --tesla-red-80: rgba(227, 25, 55, 0.8);
            --tesla-red-60: rgba(227, 25, 55, 0.6);
            --tesla-red-40: rgba(227, 25, 55, 0.4);
            --tesla-red-20: rgba(227, 25, 55, 0.2);
        }
        
        body {
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, auto);
            gap: 1rem;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .bento-item {
            background-color: #111;
            border-radius: 1rem;
            padding: 1.5rem;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .bento-item:hover {
            transform: translateY(-5px);
        }
        
        .bento-item.feature {
            grid-column: span 2;
            grid-row: span 2;
        }
        
        .bento-item.wide {
            grid-column: span 2;
        }
        
        .bento-item.tall {
            grid-row: span 2;
        }
        
        .highlight-text {
            color: var(--tesla-red);
            font-weight: 700;
        }
        
        .mega-number {
            font-size: 8rem;
            font-weight: 900;
            line-height: 1;
            margin: 0;
            background: linear-gradient(180deg, var(--tesla-red) 0%, var(--tesla-red-60) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .en-caption {
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
        }
        
        .glow-effect {
            position: absolute;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, var(--tesla-red-40) 0%, transparent 70%);
            border-radius: 50%;
            filter: blur(20px);
            z-index: 0;
        }
        
        .content {
            position: relative;
            z-index: 1;
        }
        
        .line-chart {
            stroke: var(--tesla-red);
            stroke-width: 2;
            fill: none;
        }
        
        .line-chart-area {
            fill: url(#redGradient);
            opacity: 0.3;
        }
        
        .data-point {
            fill: var(--tesla-red);
        }
        
        .axis {
            stroke: #444;
            stroke-width: 1;
        }
        
        .car-icon {
            width: 100%;
            height: auto;
            max-height: 120px;
            fill: #fff;
            stroke: var(--tesla-red);
            stroke-width: 0.5;
        }
        
        .scroll-prompt {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 0.9rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .scroll-prompt:hover {
            opacity: 1;
        }
        
        .scroll-arrow {
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        /* 适应移动设备 */
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
            
            .bento-item.feature,
            .bento-item.wide,
            .bento-item.tall {
                grid-column: span 1;
                grid-row: span 1;
            }
            
            .mega-number {
                font-size: 4rem;
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen">
        <header class="py-8 px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-2">MPV<span class="highlight-text">研究报告</span></h1>
            <p class="text-xl opacity-70 en-caption">MULTI-PURPOSE VEHICLE RESEARCH REPORT</p>
        </header>
        
        <div class="bento-grid">
            <!-- 主要特点 - 大尺寸突出显示 -->
            <div class="bento-item feature" id="feature-item">
                <div class="glow-effect" style="top: -50px; left: -50px;"></div>
                <div class="content">
                    <h2 class="text-2xl font-bold mb-4">核心特点</h2>
                    <p class="en-caption mb-6">CORE FEATURES</p>
                    <div class="mega-number">MPV</div>
                    <ul class="mt-4 space-y-2">
                        <li><i class="fas fa-check highlight-text mr-2"></i> 宽敞灵活的内部空间</li>
                        <li><i class="fas fa-check highlight-text mr-2"></i> 多变座椅布局</li>
                        <li><i class="fas fa-check highlight-text mr-2"></i> 兼具轿车舒适性和厢式货车功能性</li>
                    </ul>
                </div>
            </div>
            
            <!-- 市场趋势图表 -->
            <div class="bento-item wide" id="market-trend">
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">市场趋势</h2>
                    <p class="en-caption mb-4">MARKET TRENDS</p>
                    <svg width="100%" height="120" viewBox="0 0 300 120">
                        <defs>
                            <linearGradient id="redGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:var(--tesla-red);stop-opacity:0.5" />
                                <stop offset="100%" style="stop-color:var(--tesla-red);stop-opacity:0" />
                            </linearGradient>
                        </defs>
                        <!-- 坐标轴 -->
                        <line x1="30" y1="100" x2="280" y2="100" class="axis" />
                        <line x1="30" y1="20" x2="30" y2="100" class="axis" />
                        
                        <!-- 趋势线和区域 -->
                        <path d="M30,80 L70,75 L110,85 L150,65 L190,50 L230,30 L270,20" class="line-chart" />
                        <path d="M30,80 L70,75 L110,85 L150,65 L190,50 L230,30 L270,20 L270,100 L30,100 Z" class="line-chart-area" />
                        
                        <!-- 数据点 -->
                        <circle cx="30" cy="80" r="3" class="data-point" />
                        <circle cx="70" cy="75" r="3" class="data-point" />
                        <circle cx="110" cy="85" r="3" class="data-point" />
                        <circle cx="150" cy="65" r="3" class="data-point" />
                        <circle cx="190" cy="50" r="3" class="data-point" />
                        <circle cx="230" cy="30" r="3" class="data-point" />
                        <circle cx="270" cy="20" r="3" class="data-point" />
                        
                        <!-- 标签 -->
                        <text x="30" y="115" fill="#888" font-size="8">2020</text>
                        <text x="110" y="115" fill="#888" font-size="8">2022</text>
                        <text x="190" y="115" fill="#888" font-size="8">2024</text>
                        <text x="270" y="115" fill="#888" font-size="8">2026</text>
                    </svg>
                    <p class="mt-2 text-sm">中国MPV市场在2023年呈现<span class="highlight-text">触底回升</span>并显著增长的态势</p>
                </div>
            </div>
            
            <!-- 消费者群体 -->
            <div class="bento-item" id="consumer-groups">
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">主要消费群体</h2>
                    <p class="en-caption mb-4">CONSUMER GROUPS</p>
                    <div class="flex justify-between items-center">
                        <div class="text-center">
                            <i class="fas fa-users text-3xl highlight-text mb-2"></i>
                            <p class="text-sm">家庭用户</p>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-briefcase text-3xl highlight-text mb-2"></i>
                            <p class="text-sm">商务用户</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 热门车型 -->
            <div class="bento-item tall" id="popular-models">
                <div class="glow-effect" style="bottom: -50px; right: -50px;"></div>
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">热门车型</h2>
                    <p class="en-caption mb-4">POPULAR MODELS</p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <span class="highlight-text mr-2 text-lg">01</span>
                            <span>腾势D9</span>
                        </li>
                        <li class="flex items-center">
                            <span class="highlight-text mr-2 text-lg">02</span>
                            <span>别克GL8</span>
                        </li>
                        <li class="flex items-center">
                            <span class="highlight-text mr-2 text-lg">03</span>
                            <span>丰田赛那</span>
                        </li>
                        <li class="flex items-center">
                            <span class="highlight-text mr-2 text-lg">04</span>
                            <span>传祺M8</span>
                        </li>
                        <li class="flex items-center">
                            <span class="highlight-text mr-2 text-lg">05</span>
                            <span>小鹏X9</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 新能源趋势 -->
            <div class="bento-item" id="new-energy">
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">新能源趋势</h2>
                    <p class="en-caption mb-4">NEW ENERGY TRENDS</p>
                    <div class="flex items-center justify-center">
                        <svg width="120" height="120" viewBox="0 0 120 120">
                            <!-- 电池图标 -->
                            <rect x="25" y="40" width="70" height="40" rx="5" stroke="var(--tesla-red)" stroke-width="2" fill="none" />
                            <rect x="95" y="50" width="10" height="20" rx="2" stroke="var(--tesla-red)" stroke-width="2" fill="none" />
                            
                            <!-- 电量指示 -->
                            <rect x="30" y="45" width="40" height="30" fill="var(--tesla-red-60)" rx="2" />
                            
                            <!-- 闪电符号 -->
                            <path d="M60,35 L50,55 L65,55 L55,85" stroke="var(--tesla-red)" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                    <p class="text-sm text-center mt-2">新能源MPV在<span class="highlight-text">智能化</span>和<span class="highlight-text">能耗</span>方面展现优势</p>
                </div>
            </div>
            
            <!-- MPV vs SUV -->
            <div class="bento-item wide" id="comparison">
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">MPV vs SUV</h2>
                    <p class="en-caption mb-4">COMPARISON</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h3 class="text-sm font-bold highlight-text">MPV优势</h3>
                            <ul class="text-xs space-y-1 mt-2">
                                <li>• 更宽敞的内部空间</li>
                                <li>• 灵活多变的座椅布局</li>
                                <li>• 更好的乘坐舒适性</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-sm font-bold opacity-70">SUV优势</h3>
                            <ul class="text-xs space-y-1 mt-2">
                                <li>• 更好的通过性能</li>
                                <li>• 更时尚的外观设计</li>
                                <li>• 更高的离地间隙</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 未来发展 -->
            <div class="bento-item" id="future-dev">
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">未来发展</h2>
                    <p class="en-caption mb-4">FUTURE DEVELOPMENT</p>
                    <div class="text-center">
                        <div class="mega-number text-4xl">2030</div>
                        <p class="text-sm mt-2">智能化与电动化<span class="highlight-text">深度融合</span></p>
                    </div>
                </div>
            </div>
            
            <!-- 车型分类 -->
            <div class="bento-item" id="vehicle-types">
                <div class="content">
                    <h2 class="text-xl font-bold mb-2">车型分类</h2>
                    <p class="en-caption mb-4">VEHICLE TYPES</p>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>迷你型</span>
                            <span class="highlight-text">&lt;4100mm</span>
                        </div>
                        <div class="flex justify-between">
                            <span>小型</span>
                            <span class="highlight-text">&lt;4100mm</span>
                        </div>
                        <div class="flex justify-between">
                            <span>中型</span>
                            <span class="highlight-text">4100-4200mm</span>
                        </div>
                        <div class="flex justify-between">
                            <span>大型</span>
                            <span class="highlight-text">&gt;4600mm</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="scroll-prompt">
            <p class="mb-2">向下滚动探索更多</p>
            <i class="fas fa-chevron-down scroll-arrow"></i>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 滚动动效
            const observerOptions = {
                threshold: 0.1
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            const bentoItems = document.querySelectorAll('.bento-item');
            bentoItems.forEach(item => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(30px)';
                item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(item);
            });
            
            // 隐藏滚动提示
            window.addEventListener('scroll', function() {
                const scrollPrompt = document.querySelector('.scroll-prompt');
                if (window.scrollY > 100) {
                    scrollPrompt.style.opacity = '0';
                } else {
                    scrollPrompt.style.opacity = '0.7';
                }
            });
        });
    </script>
</body>
</html> 