<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <desc>
        商业计划书 - 章节分隔页 SVG模板
        此模板根据用户要求，优先采纳了“增强美观度”中提及的Bento Grid风格、纯黑色背景和特斯拉红色作为高亮色，
        而非初期指定的蓝色系配色。此选择旨在达到更强的视觉冲击力和现代感，以符合章节分隔页的“强烈的视觉分隔效果”要求。
        页面序号: 3/10
        设计风格: 商务、科技感、Bento Grid
        核心色调: 纯黑、特斯拉红
    </desc>

    <!-- 定义颜色变量、渐变和滤镜 -->
    <defs>
        <style type="text/css">
            /* 基础配色方案（基于增强美观度要求） */
            .bg-color { fill: #0A0A0A; } /* 纯黑色背景 */
            .primary-highlight { fill: #E31937; } /* 特斯拉红 - 主高亮色 */
            .primary-text { fill: #F8FAFC; } /* 白色文本 - 用于标题 */
            .secondary-text { fill: #94A3B8; } /* 浅灰色文本 - 用于副标题和辅助信息 */
            .line-color { stroke: #475569; } /* 从原蓝色系中提取的深灰色作为线条点缀，增加层次 */
            
            /* 渐变定义：高亮色自身的透明度渐变，制造科技感 */
            <linearGradient id="highlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#E31937" stop-opacity="1"/>
                <stop offset="100%" stop-color="#E31937" stop-opacity="0.3"/>
            </linearGradient>

            <!-- 文本光晕渐变：特斯拉红到略带金色的过渡，增强科技感和重要性 -->
            <linearGradient id="textGlowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stop-color="#E31937"/>
                <stop offset="100%" stop-color="#FFD700"/> 
            </linearGradient>

            <!-- 文本发光滤镜 -->
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur"/>
                <feMerge>
                    <feMergeNode in="blur"/>
                    <feMergeNode in="SourceGraphic"/>
                </feMerge>
            </filter>
        </style>
    </defs>

    <!-- 背景层 -->
    <rect width="1920" height="1080" class="bg-color"/>

    <!-- 装饰性几何图形和线条 (Bento Grid 风格) -->
    <!-- 这些元素使用特斯拉红及其透明度变化，形成简洁的勾线图形化效果 -->
    <g id="decorative-grid-elements">
        <!-- 左上角模块：抽象矩形和线条 -->
        <rect x="80" y="60" width="400" height="250" rx="20" ry="20" fill="none" stroke="#E31937" stroke-width="2" opacity="0.1"/>
        <line x1="120" y1="100" x2="440" y2="100" stroke="#E31937" stroke-width="1" opacity="0.15"/>
        <line x1="120" y1="140" x2="440" y2="140" stroke="#E31937" stroke-width="1" opacity="0.1"/>

        <!-- 右上角模块：圆形渐变 -->
        <rect x="1440" y="60" width="400" height="180" rx="20" ry="20" fill="none" stroke="#E31937" stroke-width="2" opacity="0.1"/>
        <circle cx="1640" cy="150" r="60" fill="url(#highlightGradient)" opacity="0.15"/>

        <!-- 左下角模块：抽象曲线 -->
        <rect x="80" y="800" width="300" height="200" rx="20" ry="20" fill="none" stroke="#E31937" stroke-width="2" opacity="0.08"/>
        <path d="M120 850 Q 200 820, 280 850 T 360 880" stroke="#E31937" stroke-width="1" fill="none" opacity="0.1"/>

        <!-- 底部中央抽象线条 -->
        <line x1="700" y1="950" x2="1220" y2="950" stroke="#E31937" stroke-width="1" opacity="0.05"/>

        <!-- 抽象数据点缀 (勾线图形化，模仿数据流或连接) -->
        <g transform="translate(860, 720)">
            <path d="M0 0 L100 0 L100 50 L0 50 Z" fill="none" stroke="#E31937" stroke-width="2" opacity="0.3"/>
            <circle cx="50" cy="25" r="10" fill="none" stroke="#E31937" stroke-width="2" opacity="0.4"/>
            <path d="M20 10 L80 40" stroke="#E31937" stroke-width="1" opacity="0.2"/>
        </g>
        <g transform="translate(1000, 700)">
            <rect x="0" y="0" width="60" height="60" rx="10" ry="10" fill="none" stroke="#E31937" stroke-width="2" opacity="0.2"/>
            <path d="M10 50 C 30 20, 70 20, 90 50" fill="none" stroke="#E31937" stroke-width="1" opacity="0.15"/>
        </g>
    </g>

    <!-- 中央核心内容区域：章节标题和副标题 -->
    <g id="main-content">
        <!-- 主标题：超大字体，中文大字体粗体，使用光晕渐变和滤镜突出 -->
        <text x="960" y="480" text-anchor="middle" class="primary-text" font-family="'Microsoft YaHei', 'Segoe UI', sans-serif" font-size="72" font-weight="bold" fill="url(#textGlowGradient)" filter="url(#glow)">
            {title}
        </text>

        <!-- 副标题/描述：英文小字作为点缀，浅灰色 -->
        <text x="960" y="560" text-anchor="middle" class="secondary-text" font-family="'Microsoft YaHei', 'Segoe UI', sans-serif" font-size="28" font-weight="normal" opacity="0.8">
            {subtitle}
        </text>

        <!-- 装饰性光束/分割线：使用强调色渐变，增强过渡感 -->
        <line x1="600" y1="650" x2="1320" y2="650" stroke="url(#highlightGradient)" stroke-width="3" stroke-linecap="round"/>
        <circle cx="600" cy="650" r="8" fill="#E31937"/>
        <circle cx="1320" cy="650" r="8" fill="#E31937"/>
    </g>

    <!-- 页面页脚元素：页码、Logo占位符、日期 -->
    <g id="footer-elements">
        <!-- 页码显示，强调当前页 -->
        <text x="1790" y="960" text-anchor="end" class="secondary-text" font-family="'Microsoft YaHei', 'Segoe UI', sans-serif" font-size="22" font-weight="normal" opacity="0.6">
            PAGE <tspan fill="#E31937">3</tspan>/10
        </text>

        <!-- 左下角 Logo 占位符 -->
        <text x="80" y="1020" class="secondary-text" font-family="'Microsoft YaHei', 'Segoe UI', sans-serif" font-size="16" font-weight="light" opacity="0.5">
            {logo_url} - Business Plan
        </text>

        <!-- 右下角日期占位符 -->
        <text x="1840" y="1020" text-anchor="end" class="secondary-text" font-family="'Microsoft YaHei', 'Segoe UI', sans-serif" font-size="16" font-weight="light" opacity="0.5">
            {date}
        </text>
    </g>

</svg>