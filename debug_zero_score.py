#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试为什么混合词典评估器总是返回0
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from langgraph_enhancement.routers.hybrid_relevance_evaluator import HybridRelevanceEvaluator

def debug_zero_score():
    """调试评估器返回0的问题"""
    
    print("🔍 调试评估器返回0的问题...")
    
    # 模拟实际的消息内容（从日志中看到的）
    user_request = "东莞到新疆旅游，规划 路线"
    
    # 模拟itinerary_planner的实际回答（从日志看长度3367）
    agent_response = """：用户输入为"东莞到新疆旅游，规划路线"，模型仅返回"您好，我是行程规划专家，负责在团队中制定详细、均衡的旅行计划。很高兴为您服务！"，没有规划任何路线，与用户需求完全不相关，未能满足用户需求。
您好，我是行程规划专家，负责制定详细的旅行计划。针对您的"东莞到新疆旅游，规划路线"需求，我为您制定以下详细的旅行规划：

## 📍 基本信息
- **出发地**: 东莞
- **目的地**: 新疆
- **旅行类型**: 深度旅游
- **建议时长**: 8-12天

## 🚗 交通安排

### 往返交通
1. **去程**: 东莞→深圳宝安机场→乌鲁木齐地窝堡机场
   - 航班时长: 约4.5小时
   - 建议提前2小时到达机场
   
2. **返程**: 乌鲁木齐→深圳→东莞
   - 同样路线返回

### 新疆内部交通
- **租车自驾** (推荐): 灵活自由，适合新疆地形
- **包车**: 有专业司机，安全省心
- **公共交通**: 经济但时间较长

## 🗺️ 详细行程规划

### Day 1: 到达乌鲁木齐
- 抵达后休息，适应环境
- 游览红山公园，俯瞰城市全景
- 品尝新疆特色美食

### Day 2-3: 天山天池
- 前往天山天池风景区
- 游览天池湖泊，欣赏雪山倒影
- 体验哈萨克族民俗文化

### Day 4-5: 吐鲁番
- 参观火焰山
- 探索葡萄沟
- 游览坎儿井地下水利工程

### Day 6-8: 喀纳斯
- 前往喀纳斯国家地质公园
- 游览喀纳斯湖
- 探访禾木古村落
- 观赏阿勒泰地区自然风光

### Day 9-10: 伊犁河谷
- 游览那拉提草原
- 体验哈萨克族游牧生活
- 参观伊犁河风光

### Day 11-12: 返回乌鲁木齐
- 游览国际大巴扎
- 购买特产纪念品
- 返程准备

## 🏨 住宿建议
- **乌鲁木齐**: 市中心4星级酒店
- **天池**: 景区内度假村
- **吐鲁番**: 特色民宿
- **喀纳斯**: 湖边木屋
- **伊犁**: 草原蒙古包体验

## 🍽️ 美食推荐
- 大盘鸡
- 手抓饭
- 烤羊肉串
- 马奶子
- 哈密瓜

## ⚠️ 注意事项
1. **证件**: 身份证必备，部分地区需要边防证
2. **衣物**: 昼夜温差大，需准备厚衣服
3. **防晒**: 紫外线强，务必做好防晒
4. **时差**: 新疆与北京时间差2小时
5. **通讯**: 部分地区信号较弱，提前告知家人

## 💰 预算参考
- **交通**: 往返机票约2000-3000元/人
- **住宿**: 平均300-800元/晚
- **餐饮**: 100-200元/天/人  
- **景点门票**: 合计约500-800元/人
- **总预算**: 约6000-10000元/人

这个行程规划涵盖了新疆的主要景点，既有自然风光又有人文体验，希望能为您的新疆之旅提供完整的指导！"""
    
    # 初始化评估器
    evaluator = HybridRelevanceEvaluator()
    
    print(f"\n📝 用户请求: {user_request}")
    print(f"📝 智能体回应长度: {len(agent_response)}")
    print(f"📝 智能体回应前200字符: {agent_response[:200]}...")
    
    # 执行评估
    result = evaluator.evaluate(user_request, agent_response)
    
    print(f"\n📊 评估结果:")
    print(f"请求类型: {result.request_type}")
    print(f"评估结果: {result.result}")
    print(f"相关性得分: {result.score:.3f}")
    print(f"阈值: {evaluator.thresholds.get(result.request_type, 'N/A')}")
    print(f"详细信息: {result.details}")
    
    # 详细分析关键词提取
    print(f"\n🔍 关键词分析:")
    request_keywords = evaluator._extract_keywords_with_weight(user_request)
    response_keywords = evaluator._extract_keywords_with_weight(agent_response)
    
    print(f"用户请求关键词: {request_keywords}")
    print(f"智能体回应关键词（前10个）: {dict(list(response_keywords.items())[:10])}")
    
    # 计算相似度
    similarity = evaluator._calculate_weighted_relevance(request_keywords, response_keywords)
    print(f"计算的相似度: {similarity:.3f}")
    
    # 检查共同关键词
    common_words = set(request_keywords.keys()) & set(response_keywords.keys())
    print(f"直接共同关键词: {common_words}")

if __name__ == "__main__":
    debug_zero_score()