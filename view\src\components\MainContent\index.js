import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Layout, Empty, Typography, Card, Avatar, Select, Badge, <PERSON><PERSON>, Button, Popconfirm, Input, Tooltip, Modal, Switch, message, Dropdown, Menu, Flex, Spin } from 'antd';
import {
  UserOutlined, RobotOutlined, EditOutlined, HistoryOutlined, ClearOutlined, BulbOutlined,
  CodeOutlined, AppstoreOutlined, SyncOutlined, MailOutlined,
  ProfileOutlined, ExperimentOutlined, CommentOutlined,
  PaperClipOutlined, FileImageOutlined, GlobalOutlined, SendOutlined, LoadingOutlined,
  LaptopOutlined, FunctionOutlined, PlaySquareOutlined,
  DatabaseOutlined, BugOutlined, HighlightOutlined, ScheduleOutlined, SmileOutlined, ReadOutlined, CarryOutOutlined, QuestionCircleOutlined,
  CloudServerOutlined, SettingOutlined, RobotFilled, CopyOutlined, RedoOutlined, DownOutlined,
  CloseOutlined, PlusOutlined, SearchOutlined,
  FilePdfOutlined, <PERSON>WordOutlined, FileExcelOutlined, FilePptOutlined, <PERSON>TextOutlined,
  FileZipOutlined, FileOutlined, DownloadOutlined, EyeOutlined
} from '@ant-design/icons';
import MessageInput from '../MessageInput';
import { formatTimestamp } from '../../utils';
import EnhancedMarkdown from '../EnhancedMarkdown';
import Settings from '../Settings';
import ModelSelector from '../ModelSelector';
import MCPServerSelector from '../MCPServerSelector';
import ToolCallSidePanel from '../ToolCallSidePanel';
import AgentTaskProgress from '../AgentTaskProgress'; 
import ParamSettingsModal from '../ParamSettingsModal';
import MCPMarketplace from '../MCPMarketplace';
import RoleSelector from '../RoleSelector'; // 导入角色选择器组件
import TeamSelector from '../TeamSelector'; // 导入团队选择器组件
import ImagePreview from '../EnhancedMarkdown/ImagePreview'; // 导入ImagePreview组件
import SearchBrowser from '../SearchBrowser'; // 导入积木搜索组件
import './MainContent.css';
import { useNavigate } from 'react-router-dom';
import { MessageService } from '../../utils/MessageService';
// 导入模型图标
import gemini from '../../assets/images/models/gemini.png';
import gemma from '../../assets/images/models/gemma.png';
import gpt_4 from '../../assets/images/models/gpt_4.png';
import deepseek from '../../assets/images/models/deepseek.png';
import huggingface from '../../assets/images/models/huggingface.png';
import anthropic from '../../assets/images/models/anthropic.png';
import openaiIcon from '../../assets/images/models/openai.png';
import llama from '../../assets/images/models/llama.png';
import qwen from '../../assets/images/models/qwen.png';
import grok from '../../assets/images/models/grok.png';
// import { parseToolResultsFromConversation } from '../../utils/JsonParser';
import logo0 from '../../assets/images/logo-0.png';

// Moved getServiceBadgeColor to the top-level module scope
const getServiceBadgeColor = (service) => {
  switch (service) {
    case 'openai':
      return 'green';
    default:
      return 'default';
  }
};

const { Content } = Layout;
const { Text, Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 持久化存储键名
const ROLE_STORAGE_KEY = 'jimu_selected_role_id';

// const MAX_CHARS = 2000; // Example character limit

// 图标映射表
const modelIcons = {
  openai: openaiIcon,
  llama: llama,
  gemini: gemini,
  gemma: gemma,
  gpt_4: gpt_4,
  deepseek: deepseek,
  huggingface: huggingface,
  anthropic: anthropic,
  qwen: qwen,
  grok: grok
};

// 辅助函数: 检查字符串是否以指定前缀开始(忽略大小写)
const startsWithIgnoreCase = (str, search) => {
  if (!str) return false;
  return str.toLowerCase().startsWith(search.toLowerCase());
}

// 文件类型和图标映射
const getFileTypeInfo = (filename) => {
  if (!filename) return { icon: FileOutlined, type: 'file', className: '' };
  
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  
  const fileTypeMap = {
    // 文档类型
    'pdf': { icon: FilePdfOutlined, type: 'pdf', className: 'pdf' },
    'doc': { icon: FileWordOutlined, type: 'doc', className: 'doc' },
    'docx': { icon: FileWordOutlined, type: 'docx', className: 'docx' },
    'xls': { icon: FileExcelOutlined, type: 'xls', className: 'xls' },
    'xlsx': { icon: FileExcelOutlined, type: 'xlsx', className: 'xlsx' },
    'ppt': { icon: FilePptOutlined, type: 'ppt', className: 'ppt' },
    'pptx': { icon: FilePptOutlined, type: 'pptx', className: 'pptx' },
    'txt': { icon: FileTextOutlined, type: 'txt', className: 'txt' },
    
    // 压缩文件
    'zip': { icon: FileZipOutlined, type: 'zip', className: 'zip' },
    'rar': { icon: FileZipOutlined, type: 'rar', className: 'rar' },
    '7z': { icon: FileZipOutlined, type: '7z', className: 'zip' },
    
    // 图片文件
    'jpg': { icon: FileImageOutlined, type: 'image', className: 'image' },
    'jpeg': { icon: FileImageOutlined, type: 'image', className: 'image' },
    'png': { icon: FileImageOutlined, type: 'image', className: 'image' },
    'gif': { icon: FileImageOutlined, type: 'image', className: 'image' },
    'svg': { icon: FileImageOutlined, type: 'image', className: 'image' },
    'webp': { icon: FileImageOutlined, type: 'image', className: 'image' },
    
    // 代码文件
    'js': { icon: CodeOutlined, type: 'code', className: 'code' },
    'ts': { icon: CodeOutlined, type: 'code', className: 'code' },
    'jsx': { icon: CodeOutlined, type: 'code', className: 'code' },
    'tsx': { icon: CodeOutlined, type: 'code', className: 'code' },
    'html': { icon: CodeOutlined, type: 'code', className: 'code' },
    'css': { icon: CodeOutlined, type: 'code', className: 'code' },
    'py': { icon: CodeOutlined, type: 'code', className: 'code' },
    'java': { icon: CodeOutlined, type: 'code', className: 'code' },
    'cpp': { icon: CodeOutlined, type: 'code', className: 'code' },
    'c': { icon: CodeOutlined, type: 'code', className: 'code' },
  };
  
  return fileTypeMap[ext] || { icon: FileOutlined, type: ext || 'file', className: '' };
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '';
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
};

// 增强的文件显示组件
const EnhancedFileDisplay = ({ filename, size, onDownload, onPreview }) => {
  const fileInfo = getFileTypeInfo(filename);
  const IconComponent = fileInfo.icon;
  
  return (
    <div className="file-attachment">
      <IconComponent className={`file-icon ${fileInfo.className}`} />
      <div className="file-info">
        <div className="file-name">{filename}</div>
        <div className="file-meta">
          <span className={`file-type ${fileInfo.className}`}>{fileInfo.type}</span>
          {size && <span className="file-size">{formatFileSize(size)}</span>}
        </div>
      </div>
    </div>
  );
};

/**
 * 根据模型名称获取对应的图标URL
 * @param {string} model 模型名称
 * @returns {string} 图标URL
 */
const getModelIconUrl = (model) => {
  // 基于模型名称提取可能的图标名称
  let iconName = '';

  // 处理模型名称映射到图标名称
  if (startsWithIgnoreCase(model, 'gemini')) {
    iconName = 'gemini';
  } else if (startsWithIgnoreCase(model, 'gpt')) {
    iconName = 'gpt_4';
  } else if (startsWithIgnoreCase(model, 'llama')) {
    iconName = 'llama';
  } else if (startsWithIgnoreCase(model, 'claude')) {
    iconName = 'anthropic';
  } else if (startsWithIgnoreCase(model, 'deepseek')) {
    iconName = 'deepseek';
  } else if (startsWithIgnoreCase(model, 'gemma')) {
    iconName = 'gemma';
  } else if (startsWithIgnoreCase(model, 'learnlm')) {
    iconName = 'huggingface';
  } else if (startsWithIgnoreCase(model, 'qwen')) {
    iconName = 'qwen';
  } else if (startsWithIgnoreCase(model, 'grok')) {
    iconName = 'grok';
  } else {
    // 默认使用一个通用图标
    iconName = 'openai';
  }

  return modelIcons[iconName] || openaiIcon;
};

// 添加辅助函数，解析可能包含多个连续JSON对象的字符串
const parseJsonMessages = (content) => {
  if (!content || typeof content !== 'string') return [];

  try {
    // 尝试作为单个JSON对象解析
    try {
      const jsonObj = JSON.parse(content);
      return [jsonObj];
    } catch (e) {
      // 不是单个有效的JSON，继续处理多个JSON的情况
    }

    // 处理多个连续的JSON对象（没有分隔符的情况）
    const jsonObjects = [];
    let remaining = content.trim();
    let startPos = 0;

    // 处理可能包含 BOM 的内容
    if (remaining.startsWith('\uFEFF')) {
      remaining = remaining.substring(1);
    }

    // 循环寻找每个JSON对象
    while (remaining.length > 0) {
      try {
        // 尝试找到第一个 '}'
        let endPos = remaining.indexOf('}', startPos);
        if (endPos === -1) break;

        // 寻找可能的JSON结束位置
        while (endPos !== -1) {
          try {
            // 尝试解析到当前位置的子字符串
            const possibleJson = remaining.substring(0, endPos + 1);
            const jsonObj = JSON.parse(possibleJson);

            // 如果解析成功，将对象添加到结果中
            jsonObjects.push(jsonObj);

            // 移除已解析的部分，继续处理剩余内容
            remaining = remaining.substring(endPos + 1).trim();
            startPos = 0;
            break;
          } catch (e) {
            // 当前位置不是有效的JSON结束，继续寻找下一个 '}'
            endPos = remaining.indexOf('}', endPos + 1);
            if (endPos === -1) {
              // 没有更多的 '}' 了，退出内部循环
              remaining = ''; // 清空剩余内容，结束处理
              break;
            }
          }
        }

        // 如果没有更多可能的JSON对象，退出循环
        if (endPos === -1) break;

      } catch (e) {
        console.error('解析多个JSON对象时出错:', e);
        break;
      }
    }

    return jsonObjects;
  } catch (error) {
    console.error('解析JSON字符串时出错:', error);
    return [];
  }
};

// 扩展后的提示词库，包含中英文提示和更多样化的图标
const allSuggestions = [
  // 中文提示词
  { text: '为个人项目写一个待办事项列表', icon: <ProfileOutlined /> },
  { text: '生成一封工作机会的回复邮件', icon: <MailOutlined /> },
  { text: '用一段话总结这篇文章', icon: <CommentOutlined /> },
  { text: 'AI在技术层面上是如何工作的？', icon: <ExperimentOutlined /> },
  { text: '给我讲一个关于太空旅行的笑话', icon: <SmileOutlined /> },
  { text: '解释量子计算的基本概念', icon: <ExperimentOutlined /> },
  { text: '推荐一部适合全家观看的电影', icon: <PlaySquareOutlined /> },
  { text: '写一首关于春天的短诗', icon: <HighlightOutlined /> },
  { text: '如何用 Python 实现一个简单的 Web 服务器？', icon: <CodeOutlined /> },
  { text: '解释 React Hooks 的工作原理。 ', icon: <FunctionOutlined /> },
  { text: 'SQL 和 NoSQL 数据库有什么区别？', icon: <DatabaseOutlined /> },
  { text: '调试 Node.js 应用的最佳实践是什么？', icon: <BugOutlined /> },
  { text: '为我的博客写一篇关于人工智能伦理的文章开头。', icon: <EditOutlined /> },
  { text: '给我三个关于科幻小说的创意点子。', icon: <BulbOutlined /> },
  { text: '润色这段营销文案，让它更吸引人。', icon: <HighlightOutlined /> },
  { text: '制定一个为期一周的健康饮食计划。', icon: <ScheduleOutlined /> },
  { text: '推荐几个适合周末放松的活动。', icon: <SmileOutlined /> },
  { text: '如何有效地学习一门新语言？', icon: <ReadOutlined /> },
  { text: '为即将到来的旅行打包行李清单。', icon: <CarryOutOutlined /> },
  { text: '给我讲一个关于历史的有趣事实。', icon: <HistoryOutlined /> },
  { text: '解释"墨菲定律"是什么意思。', icon: <QuestionCircleOutlined /> }
];

// 函数：从列表中随机选择N个不重复的元素 (Helper function)
const getRandomSuggestions = (count) => {
  if (!Array.isArray(allSuggestions) || allSuggestions.length === 0) {
    return [];
  }
  const shuffled = [...allSuggestions].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, shuffled.length));
};

// ChatMessageItem Component
const ChatMessageItem = React.memo(({
  message,
  index,
  currentConversationMessages, // Renamed from allMessages for clarity
  isAgentMode,
  selectedService,
  loading, // global loading for e.g. regenerate button enable/disable
  onCopyMessage,
  onRegenerateMessage,
  functionCallExpanded,
  setFunctionCallExpanded,
  functionResultExpanded,
  setFunctionResultExpanded
  // Helper functions (getModelIconUrl, parseJsonMessages, formatTimestamp)
  // getServiceBadgeColor is now accessed from the module scope.
}) => {
  // Add this check at the very beginning
  if (message.role === 'system') {
    return null; // Do not render system messages
  }

  const isUser = message.role === 'user';
  const isLoading = message.isLoading === true; // Individual message loading state

  const isAgentAI = !isUser && isAgentMode;
  const agentModelName = '积木智能体';
  const agentAvatar = logo0; // Assuming logo0 is accessible from module scope

  // Detect message type for AgentTaskProgress
  if (!isUser && (message.type === 'tool_result' || message.type === 'status_update')) {
    let relatedMessages = [message];

    if (message.type === 'tool_result' && message.taskName) {
      const taskName = message.taskName;
      const conversationId = message.conversation_id;

      for (let i = index - 1; i >= 0; i--) {
        const prevMsg = currentConversationMessages[i];
        const content = prevMsg.content || '';
        const hasTaskStartMark =
          content.includes('开始执行任务') ||
          content.includes('执行任务') ||
          content.includes('开始工作') ||
          content.includes('Starting task');

        if (prevMsg.type === 'status_update' &&
          content &&
          hasTaskStartMark &&
          content.includes(taskName) &&
          (!conversationId || prevMsg.conversation_id === conversationId)) {
          relatedMessages.unshift(prevMsg);
          break;
        }
      }

      for (let i = index + 1; i < currentConversationMessages.length; i++) {
        const nextMsg = currentConversationMessages[i];
        if (nextMsg.type === 'tool_result' &&
          nextMsg.taskName === taskName &&
          (!conversationId || nextMsg.conversation_id === conversationId)) {
          relatedMessages.push(nextMsg);
        }
        const nextContent = nextMsg.content || '';
        const nextHasTaskStartMark =
          nextContent.includes('开始执行任务') ||
          nextContent.includes('执行任务') ||
          nextContent.includes('开始工作') ||
          nextContent.includes('Starting task');

        if (nextMsg.type === 'status_update' &&
          nextContent &&
          nextHasTaskStartMark &&
          (!conversationId || nextMsg.conversation_id === conversationId)) {
          break;
        }
      }
    }

    return (
      <div
        key={index} // key is managed by the parent map function
        className={`message-container ai-message`}
      >
        <div className="message-avatar">
          {isAgentAI ? (
            <Avatar
              src={agentAvatar}
              className="ai-avatar with-model-icon"
              alt={agentModelName}
            />
          ) : message.model ? (
            <Avatar
              src={getModelIconUrl(message.model)}
              className="ai-avatar with-model-icon"
              alt={message.model || 'AI'}
            />
          ) : (
            <Avatar
              icon={<RobotOutlined />}
              className="ai-avatar"
            />
          )}
        </div>
        <div className="message-main">
          <div className="message-header">
            <div className="ai-header-top-line">
              <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
              <Badge
                color={getServiceBadgeColor(message.service || selectedService)}
                text={message.service || selectedService}
                className="service-badge"
              />
            </div>
            <Typography.Text type="secondary" className="message-time">
              {message.timestamp ? formatTimestamp(message.timestamp) : ''}
            </Typography.Text>
          </div>
          <div className="message-content">
            <div className="message-body">
              <div className="agent-task-container">
                <AgentTaskProgress
                  messages={relatedMessages}
                  loading={isLoading}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isUser && message.content && typeof message.content === 'string') {
    try {
      const jsonMessages = parseJsonMessages(message.content);
      if (jsonMessages && jsonMessages.length > 0) {
        const toolMessages = jsonMessages.filter(msg =>
          msg.type === 'tool_result' || msg.type === 'status_update'
        );

        if (toolMessages.length > 0) {
          return (
            <div
              key={index}
              className={`message-container ai-message`}
            >
              <div className="message-avatar">
                {isAgentAI ? (
                  <Avatar
                    src={agentAvatar}
                    className="ai-avatar with-model-icon"
                    alt={agentModelName}
                  />
                ) : message.model ? (
                  <Avatar
                    src={getModelIconUrl(message.model)}
                    className="ai-avatar with-model-icon"
                    alt={message.model || 'AI'}
                  />
                ) : (
                  <Avatar
                    icon={<RobotOutlined />}
                    className="ai-avatar"
                  />
                )}
              </div>
              <div className="message-main">
                <div className="message-header">
                  <div className="ai-header-top-line">
                    <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
                    <Badge
                      color={getServiceBadgeColor(message.service || selectedService)}
                      text={message.service || selectedService}
                      className="service-badge"
                    />
                  </div>
                  <Typography.Text type="secondary" className="message-time">
                    {message.timestamp ? formatTimestamp(message.timestamp) : ''}
                  </Typography.Text>
                </div>
                <div className="message-content">
                  <div className="message-body">
                    <div className="agent-task-container">
                      <AgentTaskProgress
                        messages={toolMessages}
                        loading={isLoading}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        }
      }
    } catch (error) {
      console.error('解析JSON消息失败 (ChatMessageItem):', error);
    }
  }

  if (!isUser && isLoading) {
    const modelIcon = isAgentAI ? agentAvatar : (message.model ? getModelIconUrl(message.model) : null);
    return (
      <div
        key={index}
        className={`message-container ai-message`}
      >
        <div className="message-avatar">
          {modelIcon ? (
            <Avatar
              src={modelIcon}
              className="ai-avatar with-model-icon"
              alt={isAgentAI ? agentModelName : (message.model || 'AI')}
            />
          ) : (
            <Avatar
              icon={<RobotOutlined />}
              className="ai-avatar"
            />
          )}
        </div>
        <div className="message-main">
          <div className="message-header">
            <div className="ai-header-top-line">
              <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
              <Badge
                color={getServiceBadgeColor(message.service || selectedService)}
                text={message.service || selectedService}
                className="service-badge"
              />
            </div>
            <Typography.Text type="secondary" className="message-time">
              {message.timestamp ? formatTimestamp(message.timestamp) : ''}
            </Typography.Text>
          </div>
          <div className="message-content">
            <div className="message-body">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const modelIcon = isAgentAI ? agentAvatar : (!isUser && message.model ? getModelIconUrl(message.model) : null);

  return (
    <div
      key={index}
      className={`message-container ${isUser ? 'user-message' : 'ai-message'}`}
    >
      <div className="message-avatar">
        {isUser ? (
          <Avatar
            icon={<UserOutlined />}
            className="user-avatar"
          />
        ) : modelIcon ? (
          <Avatar
            src={modelIcon}
            className="ai-avatar with-model-icon"
            alt={isAgentAI ? agentModelName : (message.model || 'AI')}
          />
        ) : (
          <Avatar
            icon={<RobotOutlined />}
            className="ai-avatar"
          />
        )}
      </div>
      <div className="message-main">
        <div className="message-header">
          {isUser ? (
            <>
              <Typography.Text strong>用户</Typography.Text>
              <Typography.Text type="secondary" className="message-time">
                {message.timestamp ? formatTimestamp(message.timestamp) : ''}
              </Typography.Text>
            </>
          ) : (
            <>
              <div className="ai-header-top-line">
                <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
                <Badge
                  color={getServiceBadgeColor(message.service || selectedService)}
                  text={message.service || selectedService}
                  className="service-badge"
                />
              </div>
              <Typography.Text type="secondary" className="message-time">
                {message.timestamp ? formatTimestamp(message.timestamp) : ''}
              </Typography.Text>
            </>
          )}
        </div>
        <div className="message-content">
          <div className="message-body">
            <div className="markdown-wrapper" style={{ fontSize: '0.75rem', lineHeight: '1.3' }}>
              {Array.isArray(message.content) ? (
                message.content.map((item, idx) => {
                  if (item.type === 'text') {
                    return <EnhancedMarkdown key={idx}>{item.text}</EnhancedMarkdown>;
                  } else if (item.type === 'image_url' && item.image_url && item.image_url.url) {
                    return (
                      <div key={idx} style={{ margin: '8px 0' }}>
                        <ImagePreview
                          src={item.image_url.url}
                          alt="图片"
                          showDownloadButton={false}
                          style={{
                            width: '100%',
                            maxWidth: '100%',
                            maxHeight: 200,
                            borderRadius: 8,
                            border: '1px solid #eee',
                            display: 'block',
                            objectFit: 'contain',
                          }}
                        />
                      </div>
                    );
                  } else if (item.type === 'file' && item.filename) {
                    return (
                      <EnhancedFileDisplay 
                        key={idx}
                        filename={item.filename}
                        size={item.size}
                        onDownload={() => {
                          // 可以添加下载逻辑
                          console.log('下载文件:', item.filename);
                        }}
                        onPreview={() => {
                          // 可以添加预览逻辑
                          console.log('预览文件:', item.filename);
                        }}
                      />
                    );
                  } else {
                    return null;
                  }
                })
              ) : (
                <EnhancedMarkdown>{message.content}</EnhancedMarkdown>
              )}
            </div>
            {message.function_call && (
              <Card
                title={
                  <div className="card-title-with-toggle">
                    <span>函数调用</span>
                    <Button
                      type="text"
                      size="small"
                      className="toggle-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        const messageId = `${message.id || message.timestamp || 'unknown'}-function-call`;
                        setFunctionCallExpanded(prev => ({
                          ...prev,
                          [messageId]: !prev[messageId]
                        }));
                      }}
                    >
                      {functionCallExpanded[`${message.id || message.timestamp || 'unknown'}-function-call`] ? '收起' : '展开'}
                    </Button>
                  </div>
                }
                size="small"
                className="function-call-card"
              >
                {functionCallExpanded[`${message.id || message.timestamp || 'unknown'}-function-call`] && (
                  <div>
                    <Typography.Text strong>函数名:</Typography.Text> {message.function_call.name}
                    <br />
                    <Typography.Text strong>参数:</Typography.Text>
                    <pre>{JSON.stringify(JSON.parse(message.function_call.arguments), null, 2)}</pre>
                  </div>
                )}
              </Card>
            )}
            {message.function_result && (
              <Card
                title={
                  <div className="card-title-with-toggle">
                    <span>函数结果</span>
                    <Button
                      type="text"
                      size="small"
                      className="toggle-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        const messageId = `${message.id || message.timestamp || 'unknown'}-function-result`;
                        setFunctionResultExpanded(prev => ({
                          ...prev,
                          [messageId]: !prev[messageId]
                        }));
                      }}
                    >
                      {functionResultExpanded[`${message.id || message.timestamp || 'unknown'}-function-result`] ? '收起' : '展开'}
                    </Button>
                  </div>
                }
                size="small"
                className="function-result-card"
              >
                {functionResultExpanded[`${message.id || message.timestamp || 'unknown'}-function-result`] && (
                  <div>
                    <pre>{JSON.stringify(message.function_result, null, 2)}</pre>
                  </div>
                )}
              </Card>
            )}
          </div>
          <div className="message-actions">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => onCopyMessage(message.content)}
            >
              复制文字
            </Button>
            {!isUser && (
              <Button
                type="text"
                size="small"
                icon={<RedoOutlined />}
                onClick={() => onRegenerateMessage(index)}
                disabled={loading} // Global loading state for "Stop Generation" might disable this
              >
                重新发送
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

const MainContent = ({
  currentConversation,
  onSendMessage,
  onStopGeneration,
  onSelectConversation,
  onDeleteConversation,
  onRenameConversation,
  onClearHistory,
  onNewConversation,
  loading,
  error,
  selectedService = 'openai',
  onServiceChange,
  functionTools = [],
  onSelectFunctionTool,
  mcpServers = {},
  mcpServersStatus = {},
  selectedServers = [],
  onSelectServers,
  view = 'home',
  modelsCache = {},
  settings = {},
  onToggleConversationDrawer,
  onUpdateSettings,
  onRefreshTools,
  onDeleteAllConversations,
  onRefreshModels,
  onUpdateMcpConfig,
  onUpdateConversationModel,
  isAgentMode = false,
  onToggleAgentMode = () => { },
  onRegenerate = null,
  toolResults = [],
  onToolResult = () => { }
}) => {
  // 使用会话级loading状态，如果没有则使用全局loading状态作为后备
  const currentLoading = currentConversation?.loading ?? loading;
  
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null); // 添加消息容器的引用
  const [selectedModel, setSelectedModel] = useState(currentConversation?.model || '');
  const [homeMessage, setHomeMessage] = useState('');
  // 使用 useState 管理提示词建议，并用辅助函数初始化 (Initialize state)
  const [suggestions, setSuggestions] = useState(() => getRandomSuggestions(4));
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // 添加状态控制模型选择器和 MCP 服务选择器的显示
  const [modelSelectorVisible, setModelSelectorVisible] = useState(false);
  const [mcpServerSelectorVisible, setMcpServerSelectorVisible] = useState(false);

  // 添加WebSearch模式状态
  const [isWebSearchEnabled, setIsWebSearchEnabled] = useState(currentConversation?.is_web_search_enabled || false);

  // 添加单独的首页WebSearch模式状态
  const [homeWebSearchEnabled, setHomeWebSearchEnabled] = useState(false);
  const [homeImagePreview, setHomeImagePreview] = useState(null); // base64图片
  const [homeImageFile, setHomeImageFile] = useState(null); // 原始文件
  const [homeAttachmentInfo, setHomeAttachmentInfo] = useState(null); // { filename, filetype, text }
  const [homeUploadingAttachment, setHomeUploadingAttachment] = useState(false);

  // 添加工具调用面板的状态
  const [isToolPanelOpen, setIsToolPanelOpen] = useState(false);
  const [toolCallsData, setToolCallsData] = useState([]);
  const [currentToolCallIndex, setCurrentToolCallIndex] = useState(0);

  // 添加参数设置模态框状态
  const [paramSettingsVisible, setParamSettingsVisible] = useState(false);
  const [marketplaceVisible, setMarketplaceVisible] = useState(false); // 添加插件市场可见性状态
  
  // 添加积木搜索模态框状态
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  
  // 添加函数调用和函数结果展开状态管理
  const [functionCallExpanded, setFunctionCallExpanded] = useState({});
  const [functionResultExpanded, setFunctionResultExpanded] = useState({});

  // 从localStorage获取保存的角色ID
  const getRoleIdFromStorage = () => {
    try {
      return localStorage.getItem(ROLE_STORAGE_KEY);
    } catch (error) {
      console.error('从localStorage读取角色ID失败:', error);
      return null;
    }
  };

  // 添加角色选择状态，使用localStorage中存储的值初始化
  const [selectedRoleId, setSelectedRoleId] = useState(() => getRoleIdFromStorage());

  // 从localStorage获取保存的团队ID
  const getTeamIdFromStorage = () => {
    try {
      return localStorage.getItem('jimu_selected_team_id');
    } catch (error) {
      console.error('从localStorage读取团队ID失败:', error);
      return null;
    }
  };

  // 添加团队选择状态，使用localStorage中存储的值初始化
  const [selectedTeamId, setSelectedTeamId] = useState(() => getTeamIdFromStorage());

  // 引入导航钩子
  const navigate = useNavigate();

  // 添加滚动状态
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const scrollTimerRef = useRef(null);
  const lastScrollTimeRef = useRef(0);

  // 添加一个标记来追踪是否是自动滚动
  const isAutoScrollingRef = useRef(false);

  // 新增：标签栏相关状态
  const [openTabs, setOpenTabs] = useState([]);
  const [activeTabId, setActiveTabId] = useState(null);
  // 新增：标签折叠相关状态
  const [collapsedTabs, setCollapsedTabs] = useState([]);
  const [showTabsDropdown, setShowTabsDropdown] = useState(false);
  const tabsContainerRef = useRef(null);

  // 新增：处理用户明确滚动意图的函数 (例如：鼠标滚轮、触摸)
  const handleUserScrollIntent = useCallback(() => {
    setIsUserScrolling(true);
  }, [setIsUserScrolling]);

  // 修改 scrollToBottom 函数
  const scrollToBottom = () => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // 如果用户正在滚动，不执行自动滚动
    if (isUserScrolling) {
      console.log('[自动滚动] 用户正在滚动，跳过自动滚动');
      return;
    }

    // 设置自动滚动标记
    isAutoScrollingRef.current = true;

    // 执行滚动
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    // 滚动完成后重置标记
    setTimeout(() => {
      isAutoScrollingRef.current = false;
    }, 500); // 给滚动动画一些时间完成
  };

  // 修改 handleScroll 函数
  const handleScroll = useCallback(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // 添加scrolling类名
    container.classList.add('scrolling');

    // 输出滚动状态日志
    // console.log('[滚动状态]', {
    //   isUserScrolling: isUserScrolling, // 读取当前的 isUserScrolling state
    //   isNearBottom: container.scrollHeight - container.scrollTop - container.clientHeight < 1000,
    //   scrollPosition: container.scrollTop,
    //   scrollHeight: container.scrollHeight,
    //   clientHeight: container.clientHeight,
    //   distanceToBottom: container.scrollHeight - container.scrollTop - container.clientHeight
    // });
    // 计算是否接近底部（距离底部1000px以内）
    const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 1000;
    setShowScrollToBottom(!isNearBottom);

    // 清除之前的定时器
    if (scrollTimerRef.current) {
      clearTimeout(scrollTimerRef.current);
    }

    // 设置新的定时器，滚动停止1000毫秒后移除scrolling类并重置滚动状态
    scrollTimerRef.current = setTimeout(() => {
      if (container) {
        container.classList.remove('scrolling');
        // 只有在不是自动滚动时才重置用户滚动状态
        // 这一条件有助于在自动滚动（如点击"滚动到底部"按钮）结束后正确重置状态
        if (!isAutoScrollingRef.current) {
          setIsUserScrolling(false);
        }
          // 输出滚动停止状态日志
          console.log('[滚动停止]', {
          isUserScrolling: false, // 此时预期的 isUserScrolling 状态
            isNearBottom: container.scrollHeight - container.scrollTop - container.clientHeight < 1500,
            scrollPosition: container.scrollTop,
            scrollHeight: container.scrollHeight,
            clientHeight: container.clientHeight,
            distanceToBottom: container.scrollHeight - container.scrollTop - container.clientHeight
          });
      }
    }, 1000);
  }, [isUserScrolling, setShowScrollToBottom, setIsUserScrolling]); // isUserScrolling 作为依赖项，因为它在日志中被读取

  // 修改 handleScrollToBottom 函数
  const handleScrollToBottom = () => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // 设置自动滚动标记
    isAutoScrollingRef.current = true;
    // 重置用户滚动状态
    setIsUserScrolling(false);

    // 强制滚动到底部
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });


    // 清除之前的定时器
    if (scrollTimerRef.current) {
      clearTimeout(scrollTimerRef.current);
    }

    // 设置新的定时器，滚动停止1000毫秒后移除scrolling类并重置滚动状态
    scrollTimerRef.current = setTimeout(() => {
      console.log('点击回到底部', container,isUserScrolling, scrollTimerRef.current);
      
      if (container) {
        container.classList.remove('scrolling');
        // 重置自动滚动标记
        isAutoScrollingRef.current = false;
        // 确保用户滚动状态为 false
        setIsUserScrolling(false);
        
        // 输出日志
        console.log('[点击回到底部]', {
          isUserScrolling: false,
          isAutoScrolling: false,
          scrollPosition: container.scrollTop,
          scrollHeight: container.scrollHeight,
          clientHeight: container.clientHeight,
          distanceToBottom: container.scrollHeight - container.scrollTop - container.clientHeight
        });
      }
    }, 1000);
  };

  // 在组件挂载和卸载时添加/移除滚动事件监听
    useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      // 新增：监听鼠标滚轮和触摸事件以识别用户滚动意图
      container.addEventListener('wheel', handleUserScrollIntent, { passive: true });
      container.addEventListener('touchstart', handleUserScrollIntent, { passive: true });
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
        // 新增：移除监听
        container.removeEventListener('wheel', handleUserScrollIntent);
        container.removeEventListener('touchstart', handleUserScrollIntent);
      }
      // 清除定时器
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }
    };
  }, [view, currentConversation, handleScroll, handleUserScrollIntent]); // 当视图或会话变化时重新设置监听

  // 添加格式化工具调用数据的函数，将会话中的工具调用消息格式化为结构化数据
  const formatToolCallsData = useCallback((conversation) => {
    if (!conversation || !conversation.messages) {
      // console.log('[formatToolCallsData] 会话不存在或没有消息');
      return [];
    }

    // console.log(`[formatToolCallsData] 从会话 ${conversation.id} 中提取工具调用`);

    const processedMessages = [];
    conversation.messages.forEach((message, index) => {
      if (message.type === 'tool_result' || message.type === 'status_update') {
        processedMessages.push({ ...message, messageIndex: index });
      } else if (message.content && typeof message.content === 'string') {
        try {
          const jsonMessages = parseJsonMessages(message.content);
          if (jsonMessages && jsonMessages.length > 0) {
            const toolMessages = jsonMessages.filter(msg =>
              msg.type === 'tool_result' || msg.type === 'status_update'
            );
            if (toolMessages.length > 0) {
              // console.log(`[formatToolCallsData] 从消息#${index}内容中解析出 ${toolMessages.length} 个工具调用JSON`);
              toolMessages.forEach(msg => {
                processedMessages.push({
                  ...msg,
                  messageIndex: index, // Original message index
                  timestamp: msg.timestamp || message.timestamp, // Prefer parsed timestamp, fallback to original message
                  fromJsonContent: true
                });
              });
            }
          }
        } catch (error) {
          // console.error('解析消息内容中的JSON失败:', error);
        }
      }
    });

    processedMessages.sort((a, b) => {
      const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
      const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
      
      const validTimeA = !isNaN(timeA) ? timeA : (a.timestamp ? Infinity : 0); 
      const validTimeB = !isNaN(timeB) ? timeB : (b.timestamp ? Infinity : 0);

      if (validTimeA !== validTimeB) {
        return validTimeA - validTimeB;
      }
      return a.messageIndex - b.messageIndex;
    });

    const finalTaskGroups = {};

    processedMessages.forEach(message => {
      if (message.type === 'tool_result') {
        const taskName = message.taskName || '未命名任务';
        const taskKey = taskName + (message.conversation_id || ''); 

        if (!finalTaskGroups[taskKey]) {
          finalTaskGroups[taskKey] = {
            taskName: taskName,
            toolCalls: [],
            statusUpdate: null,
            startTime: message.timestamp || null,
            firstToolCallIndex: message.messageIndex, 
            conversationId: message.conversation_id 
          };
        }
        
        finalTaskGroups[taskKey].toolCalls.push({
          id: `tc-${message.timestamp || message.messageIndex}-${Math.random().toString(36).substr(2, 5)}`,
          type: 'tool_result',
          name: message.name || '未知工具',
          taskName: taskName,
          result: message.result,
          status: message.status || (message.isSuccess ? 'completed' : 'failed'),
          isSuccess: message.isSuccess === true,
          timestamp: message.timestamp || new Date().toISOString(),
          parameters: message.parameters || null,
          messageIndex: message.messageIndex
        });

        if (message.timestamp && (!finalTaskGroups[taskKey].startTime || new Date(message.timestamp) < new Date(finalTaskGroups[taskKey].startTime))) {
          finalTaskGroups[taskKey].startTime = message.timestamp;
        }
        if (message.messageIndex < finalTaskGroups[taskKey].firstToolCallIndex) {
          finalTaskGroups[taskKey].firstToolCallIndex = message.messageIndex;
        }
      }
    });

    const statusUpdateCandidates = processedMessages.filter(m => m.type === 'status_update');

    Object.values(finalTaskGroups).forEach(group => {
      let bestStatusUpdate = null;
      let bestStatusUpdateIndex = -1;

      for (const statusMsg of statusUpdateCandidates) {
        const content = statusMsg.content || '';
        const hasTaskStartMark =
          content.includes('开始执行任务') ||
          content.includes('执行任务') ||
          content.includes('开始工作') ||
          content.includes('Starting task');

        if (hasTaskStartMark && content.includes(group.taskName) &&
            (!group.conversationId || statusMsg.conversation_id === group.conversationId)) {
          
          if (statusMsg.messageIndex < group.firstToolCallIndex && statusMsg.messageIndex > bestStatusUpdateIndex) {
            bestStatusUpdate = statusMsg;
            bestStatusUpdateIndex = statusMsg.messageIndex;
          }
        }
      }
      if (bestStatusUpdate) {
        group.statusUpdate = bestStatusUpdate;
        if (bestStatusUpdate.timestamp && (!group.startTime || new Date(bestStatusUpdate.timestamp) < new Date(group.startTime))) {
          group.startTime = bestStatusUpdate.timestamp;
        }
      }
    });

    const formattedData = [];
    Object.values(finalTaskGroups).forEach(group => {
      group.toolCalls.sort((a, b) => {
          const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
          const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
          const validTimeA = !isNaN(timeA) ? timeA : (a.timestamp ? Infinity : 0);
          const validTimeB = !isNaN(timeB) ? timeB : (b.timestamp ? Infinity : 0);
          if (validTimeA !== validTimeB) return validTimeA - validTimeB;
          return a.messageIndex - b.messageIndex;
      });

      if (group.statusUpdate) {
        formattedData.push({
          id: `st-${group.statusUpdate.timestamp || group.statusUpdate.messageIndex}-${Math.random().toString(36).substr(2, 5)}`,
          type: 'status_update',
          content: group.statusUpdate.content,
          taskName: group.taskName,
          timestamp: group.statusUpdate.timestamp || new Date().toISOString(),
          messageIndex: group.statusUpdate.messageIndex
        });
      }
      formattedData.push(...group.toolCalls);
    });

    formattedData.sort((a, b) => {
      const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
      const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;

      const validTimeA = !isNaN(timeA) ? timeA : Infinity; 
      const validTimeB = !isNaN(timeB) ? timeB : Infinity;
      
      if (validTimeA !== validTimeB) {
        return validTimeA - validTimeB;
      }
      return a.messageIndex - b.messageIndex;
    });
    
    console.log(`[formatToolCallsData] 从消息#${conversation.messages.length}中提取到 ${formattedData.length} 个工具结果`);
    return formattedData;
  }, []);

  // 处理流式工具调用，避免过早显示未完成的结果
  const completeStreamingToolCalls = useCallback((toolCalls, isLoading) => {
    // 如果没有正在生成内容，则直接返回原始工具调用数据
    if (!isLoading) return toolCalls;

    // 复制工具调用数据，避免修改原始数据
    const completedCalls = [...toolCalls];

    // 找到最后一个工具调用，如果是未完成状态，则标记为正在执行中
    if (completedCalls.length > 0) {
      const lastCall = completedCalls[completedCalls.length - 1];
      if (lastCall.status !== 'completed' && lastCall.status !== 'failed') {
        lastCall.status = 'running';
        lastCall.isRunning = true;
      }
    }

    return completedCalls;
  }, []);

  // 格式化时间戳为友好显示格式
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return '';

      return date.toLocaleString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    } catch (error) {
      console.error('格式化时间戳出错:', error);
      return '';
    }
  };



  // 复制消息内容到剪贴板
  const handleCopyMessage = useCallback((content) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        message.success('内容已复制到剪贴板');
      })
      .catch(err => {
        message.error('复制失败: ' + err);
      });
  }, []);

  // 重新生成消息
  const handleRegenerateMessage = useCallback((messageIndex) => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    if (onRegenerate) {
      onRegenerate(messageIndex);
    }
  }, [onRegenerate]);

  // 在消息发送后立即滚动
  const handleSendMessage = (message, model, options = {}) => {
    // 确保options是一个对象, 并传递所有选项给onSendMessage
    const messageOptions = {
      is_web_search_enabled: options.is_web_search_enabled,
      systemPrompt: options.systemPrompt,
      maxTokens: options.maxTokens,
      is_team_test: options.is_team_test || false, // 将团队测试标志向上传递
    };

    // 实现团队和角色互斥逻辑
    if (selectedTeamId || options.team_id) {
      // 如果选择了团队，则传递团队ID，不传递角色ID
      messageOptions.team_id = options.team_id || selectedTeamId;
    } else {
      // 如果未选择团队，则传递角色ID
      messageOptions.role_id = options.role_id || selectedRoleId;
    }
    
    // 调用父组件传入的onSendMessage，传递完整的options
    onSendMessage(message, model, messageOptions);
    
    setTimeout(scrollToBottoms, 100); // 添加小延迟确保消息已添加
  };
  // 滚动到底部函数
  const scrollToBottoms = () => {
    const container = messagesContainerRef.current;
    if (!container) return;
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 处理附件文件选择
  const handleHomeAttachmentChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 限制文件大小为5MB
        Modal.error({
          title: '文件过大',
          content: '请选择不超过5M的文件。',
        });
        e.target.value = '';
        return;
      }
      setHomeUploadingAttachment(true);
      const formData = new FormData();
      formData.append('file', file);
      try {
        const res = await fetch('/api/upload-and-parse-file', {
          method: 'POST',
          body: formData,
        });
        const data = await res.json();
        if (data.success) {
          setHomeAttachmentInfo({
            filename: data.filename,
            filetype: data.filetype,
            text: data.text,
          });
        } else {
          message.error(data.error || '文件上传失败，请重试');
        }
      } catch (err) {
        message.error(err.message || '未知错误');
      }
      setHomeUploadingAttachment(false);
    }
    e.target.value = ''; // 清空文件输入，以便下次选择相同文件也能触发onChange
  };

  // 处理图片文件选择
  const handleHomeImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        setHomeImagePreview(ev.target.result);
        setHomeImageFile(file);
      };
      reader.readAsDataURL(file);
    }
    e.target.value = ''; // 清空文件输入
  };

  // 移除图片
  const handleRemoveHomeImage = () => {
    setHomeImagePreview(null);
    setHomeImageFile(null);
  };

  // 移除附件
  const handleRemoveHomeAttachment = () => {
    setHomeAttachmentInfo(null);
  };

  // 当会话变更时，更新选择的模型
  useEffect(() => {
    if (currentConversation?.model) {
      setSelectedModel(currentConversation.model);
    }
  }, [currentConversation]);

  // 当服务变更时，设置默认模型
  useEffect(() => {
    // 如果当前会话有模型，优先使用会话模型
    if (currentConversation?.model) {
      setSelectedModel(currentConversation.model);
      return;
    }

    // 如果有默认模型设置，使用它，否则使用第一个模型
    if (settings?.defaultModels?.[selectedService] &&
      modelsCache[selectedService]?.includes(settings.defaultModels[selectedService])) {
      setSelectedModel(settings.defaultModels[selectedService]);
    } else if (modelsCache[selectedService]?.length > 0) {
      setSelectedModel(modelsCache[selectedService][0]);
    }
  }, [selectedService, modelsCache, settings, currentConversation]);

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation?.messages]);

  // 根据不同的AI服务显示不同的颜色
  const getServiceBadgeColor = (service) => {
    switch (service) {
      case 'openai':
        return 'green';
      default:
        return 'default';
    }
  };
  // 渲染一条消息
  const renderMessage = (message, index) => {
    const isUser = message.role === 'user';
    const isLoading = message.isLoading === true;

    // agent模式下，AI消息固定名称和头像
    const isAgentAI = !isUser && isAgentMode;
    const agentModelName = '积木智能体';
    const agentAvatar = logo0;

    // 检测消息是否为工具调用结果或状态更新
    if (!isUser && (message.type === 'tool_result' || message.type === 'status_update')) {
      // 获取聚合后的消息数据
      // 如果当前消息是工具执行结果，找出相关的状态更新和其他工具执行结果
      let relatedMessages = [message];

      // 如果是工具调用结果，尝试寻找相关的状态更新消息和相同工具任务的其他结果
      if (message.type === 'tool_result' && message.taskName) {
        const taskName = message.taskName;
        const conversationId = message.conversation_id;

        // 向前查找相关的状态更新消息
        for (let i = index - 1; i >= 0; i--) {
          const prevMsg = currentConversation.messages[i];
          // 如果找到了开始执行该任务的状态更新，添加并停止查找
          const content = prevMsg.content || '';
          const hasTaskStartMark =
            content.includes('开始执行任务') ||
            content.includes('执行任务') ||
            content.includes('开始工作') ||
            content.includes('Starting task');

          if (prevMsg.type === 'status_update' &&
            content &&
            hasTaskStartMark &&
            content.includes(taskName) &&
            (!conversationId || prevMsg.conversation_id === conversationId)) {
            relatedMessages.unshift(prevMsg);
            break;
          }
        }

        // 向后查找相同任务名的其他工具结果
        for (let i = index + 1; i < currentConversation.messages.length; i++) {
          const nextMsg = currentConversation.messages[i];
          if (nextMsg.type === 'tool_result' &&
            nextMsg.taskName === taskName &&
            (!conversationId || nextMsg.conversation_id === conversationId)) {
            relatedMessages.push(nextMsg);
          }
          // 如果遇到了下一个任务的状态更新，停止查找
          const nextContent = nextMsg.content || '';
          const nextHasTaskStartMark =
            nextContent.includes('开始执行任务') ||
            nextContent.includes('执行任务') ||
            nextContent.includes('开始工作') ||
            nextContent.includes('Starting task');

          if (nextMsg.type === 'status_update' &&
            nextContent &&
            nextHasTaskStartMark &&
            (!conversationId || nextMsg.conversation_id === conversationId)) {
            break;
          }
        }
      }

      // 将相关消息传递给AgentTaskProgress组件
    return (
        <div
          key={index}
          className={`message-container ai-message`}
        >
          <div className="message-avatar">
            {isAgentAI ? (
              <Avatar
                src={agentAvatar}
                className="ai-avatar with-model-icon"
                alt={agentModelName}
              />
            ) : message.model ? (
              <Avatar
                src={getModelIconUrl(message.model)}
                className="ai-avatar with-model-icon"
                alt={message.model || 'AI'}
              />
            ) : (
              <Avatar
                icon={<RobotOutlined />}
                className="ai-avatar"
              />
            )}
          </div>
          <div className="message-main">
            <div className="message-header">
              <div className="ai-header-top-line">
                <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
                <Badge
                  color={getServiceBadgeColor(message.service || selectedService)}
                  text={message.service || selectedService}
                  className="service-badge"
                />
              </div>
              <Typography.Text type="secondary" className="message-time">
                {message.timestamp ? formatTimestamp(message.timestamp) : ''}
              </Typography.Text>
            </div>
            <div className="message-content">
              <div className="message-body">
                <div className="agent-task-container">
                  <AgentTaskProgress
                    messages={relatedMessages}
                    loading={isLoading}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // 尝试解析可能包含的JSON字符串
    if (!isUser && message.content && typeof message.content === 'string') {
      try {
        // 检查消息内容是否包含多个连续的JSON对象 (没有分隔符)
        const jsonMessages = parseJsonMessages(message.content);

        if (jsonMessages && jsonMessages.length > 0) {
          console.log(`[renderMessage] 从消息#${index}中解析出 ${jsonMessages.length} 个JSON对象`);

          // 过滤出工具调用相关的消息
          const toolMessages = jsonMessages.filter(msg =>
            msg.type === 'tool_result' || msg.type === 'status_update'
          );

          if (toolMessages.length > 0) {
            return (
              <div
                key={index}
                className={`message-container ai-message`}
              >
                <div className="message-avatar">
                  {isAgentAI ? (
                    <Avatar
                      src={agentAvatar}
                      className="ai-avatar with-model-icon"
                      alt={agentModelName}
                    />
                  ) : message.model ? (
                    <Avatar
                      src={getModelIconUrl(message.model)}
                      className="ai-avatar with-model-icon"
                      alt={message.model || 'AI'}
                    />
                  ) : (
                    <Avatar
                      icon={<RobotOutlined />}
                      className="ai-avatar"
                    />
                  )}
                </div>
                <div className="message-main">
                  <div className="message-header">
                    <div className="ai-header-top-line">
                      <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
                      <Badge
                        color={getServiceBadgeColor(message.service || selectedService)}
                        text={message.service || selectedService}
                        className="service-badge"
                      />
                    </div>
                    <Typography.Text type="secondary" className="message-time">
                      {message.timestamp ? formatTimestamp(message.timestamp) : ''}
                    </Typography.Text>
                  </div>
                  <div className="message-content">
                    <div className="message-body">
                      <div className="agent-task-container">
                        <AgentTaskProgress
                          messages={toolMessages}
                          loading={isLoading}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          }
        }
      } catch (error) {
        console.error('解析JSON消息失败:', error);
        // 继续执行普通渲染
      }
    }

    // 如果是加载中的消息，显示特殊样式
    if (!isUser && isLoading) {
      // 获取模型图标
      const modelIcon = isAgentAI ? agentAvatar : (message.model ? getModelIconUrl(message.model) : null);

      return (
        <div
          key={index}
          className={`message-container ai-message`}
        >
          <div className="message-avatar">
            {modelIcon ? (
              <Avatar
                src={modelIcon}
                className="ai-avatar with-model-icon"
                alt={isAgentAI ? agentModelName : (message.model || 'AI')}
              />
            ) : (
              <Avatar
                icon={<RobotOutlined />}
                className="ai-avatar"
              />
            )}
          </div>
          {/* 使用与普通消息相同的结构 */}
          <div className="message-main">
            <div className="message-header">
              <div className="ai-header-top-line">
                <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
                <Badge
                  color={getServiceBadgeColor(message.service || selectedService)}
                  text={message.service || selectedService}
                  className="service-badge"
                />
              </div>
              <Typography.Text type="secondary" className="message-time">
                {message.timestamp ? formatTimestamp(message.timestamp) : ''}
              </Typography.Text>
            </div>
            <div className="message-content">
              <div className="message-body">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // 获取AI消息的模型图标
    const modelIcon = isAgentAI ? agentAvatar : (!isUser && message.model ? getModelIconUrl(message.model) : null);

    return (
      <div
        key={index}
        className={`message-container ${isUser ? 'user-message' : 'ai-message'}`}
      >
        <div className="message-avatar">
          {isUser ? (
            <Avatar
              icon={<UserOutlined />}
              className="user-avatar"
            />
          ) : modelIcon ? (
            <Avatar
              src={modelIcon}
              className="ai-avatar with-model-icon"
              alt={isAgentAI ? agentModelName : (message.model || 'AI')}
            />
          ) : (
            <Avatar
              icon={<RobotOutlined />}
              className="ai-avatar"
            />
          )}
        </div>
        {/* Wrap content and header for better flex control */}
        <div className="message-main">
          {/* Moved Header outside the content bubble */}
          <div className="message-header">
            {isUser ? (
              // User header: Username above Timestamp
              <>
                <Typography.Text strong>用户</Typography.Text>
                <Typography.Text type="secondary" className="message-time">
                  {message.timestamp ? formatTimestamp(message.timestamp) : ''}
                </Typography.Text>
              </>
            ) : (
              // AI header: Username and Badge above Timestamp
              <>
                <div className="ai-header-top-line">
                  <Typography.Text strong>{isAgentAI ? agentModelName : (message.model || 'AI')}</Typography.Text>
                  <Badge
                    color={getServiceBadgeColor(message.service || selectedService)}
                    text={message.service || selectedService}
                    className="service-badge"
                  />
                </div>
                <Typography.Text type="secondary" className="message-time">
                  {message.timestamp ? formatTimestamp(message.timestamp) : ''}
                </Typography.Text>
              </>
            )}
          </div>

          {/* Message Content Bubble */}
          <div className="message-content">
            <div className="message-body">
              {/* 使用普通的 EnhancedMarkdown 渲染内容 */}
              <div className="markdown-wrapper" style={{ fontSize: '0.75rem', lineHeight: '1.3' }}>
                {Array.isArray(message.content) ? (
                  message.content.map((item, idx) => {
                    if (item.type === 'text') {
                      return <EnhancedMarkdown key={idx}>{item.text}</EnhancedMarkdown>;
                    } else if (item.type === 'image_url' && item.image_url && item.image_url.url) {
                      return (
                        <div key={idx} style={{ margin: '8px 0' }}>
                          <ImagePreview
                            src={item.image_url.url}
                            alt="图片"
                            showDownloadButton={false}
                            style={{
                              width: '100%',
                              maxWidth: '100%',
                              maxHeight: 200,
                              borderRadius: 8,
                              border: '1px solid #eee',
                              display: 'block',
                              objectFit: 'contain',
                            }}
                          />
                        </div>
                      );
                    } else if (item.type === 'file' && item.filename) {
                      return (
                        <EnhancedFileDisplay 
                          key={idx}
                          filename={item.filename}
                          size={item.size}
                          onDownload={() => {
                            // 可以添加下载逻辑
                            console.log('下载文件:', item.filename);
                          }}
                          onPreview={() => {
                            // 可以添加预览逻辑
                            console.log('预览文件:', item.filename);
                          }}
                        />
                      );
                    } else {
                      return null;
                    }
                  })
                ) : (
                  <EnhancedMarkdown>{message.content}</EnhancedMarkdown>
                )}
              </div>

              {message.function_call && (
                <Card
                  title={
                    <div className="card-title-with-toggle">
                      <span>函数调用</span>
                      <Button
                        type="text"
                        size="small"
                        className="toggle-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          const messageId = `${message.id || message.timestamp || 'unknown'}-function-call-2`;
                          setFunctionCallExpanded(prev => ({
                            ...prev,
                            [messageId]: !prev[messageId]
                          }));
                        }}
                      >
                        {functionCallExpanded[`${message.id || message.timestamp || 'unknown'}-function-call-2`] ? '收起' : '展开'}
                      </Button>
                    </div>
                  }
                  size="small"
                  className="function-call-card"
                >
                  {functionCallExpanded[`${message.id || message.timestamp || 'unknown'}-function-call-2`] && (
                    <div>
                      <Typography.Text strong>函数名:</Typography.Text> {message.function_call.name}
                      <br />
                      <Typography.Text strong>参数:</Typography.Text>
                      <pre>{JSON.stringify(JSON.parse(message.function_call.arguments), null, 2)}</pre>
                    </div>
                  )}
                </Card>
              )}

              {message.function_result && (
                <Card
                  title={
                    <div className="card-title-with-toggle">
                      <span>函数结果</span>
                      <Button
                        type="text"
                        size="small"
                        className="toggle-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          const messageId = `${message.id || message.timestamp || 'unknown'}-function-result-2`;
                          setFunctionResultExpanded(prev => ({
                            ...prev,
                            [messageId]: !prev[messageId]
                          }));
                        }}
                      >
                        {functionResultExpanded[`${message.id || message.timestamp || 'unknown'}-function-result-2`] ? '收起' : '展开'}
                      </Button>
                    </div>
                  }
                  size="small"
                  className="function-result-card"
                >
                  {functionResultExpanded[`${message.id || message.timestamp || 'unknown'}-function-result-2`] && (
                    <div>
                      <pre>{JSON.stringify(message.function_result, null, 2)}</pre>
                    </div>
                  )}
                </Card>
              )}
            </div>

            {/* 添加消息操作按钮区域 */}
            <div className="message-actions">
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => handleCopyMessage(message.content)}
              >
                复制文字
              </Button>
              {!isUser && (
                <Button
                  type="text"
                  size="small"
                  icon={<RedoOutlined />}
                  onClick={() => handleRegenerateMessage(index)}
                  disabled={currentLoading}
                >
                  重新发送
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 添加首页参数状态
  const [homeSystemPrompt, setHomeSystemPrompt] = useState('');
  const [homeMaxTokens, setHomeMaxTokens] = useState('');
  // 添加首页角色选择状态，使用localStorage中存储的值初始化
  const [homeSelectedRoleId, setHomeSelectedRoleId] = useState(() => getRoleIdFromStorage());
  // 添加首页团队选择状态，使用localStorage中存储的值初始化
  const [homeSelectedTeamId, setHomeSelectedTeamId] = useState(() => getTeamIdFromStorage());

  // 添加状态变化的调试日志
  useEffect(() => {
    console.log('[状态监听] homeSelectedTeamId 变化:', homeSelectedTeamId);
  }, [homeSelectedTeamId]);

  useEffect(() => {
    console.log('[状态监听] homeSelectedRoleId 变化:', homeSelectedRoleId);
  }, [homeSelectedRoleId]);

  // 页面加载时检查并恢复localStorage中的选择状态
  useEffect(() => {
    const savedTeamId = getTeamIdFromStorage();
    const savedRoleId = getRoleIdFromStorage();
    
    console.log('[页面加载] 检查localStorage状态:', { savedTeamId, savedRoleId });
    
    // 如果localStorage中有保存的teamId，但当前状态为空，则恢复
    if (savedTeamId && !homeSelectedTeamId && !selectedTeamId) {
      console.log('[页面加载] 恢复团队选择:', savedTeamId);
      setHomeSelectedTeamId(savedTeamId);
      setSelectedTeamId(savedTeamId);
    }
    
    // 如果localStorage中有保存的roleId，但当前状态为空，则恢复
    if (savedRoleId && !homeSelectedRoleId && !selectedRoleId) {
      console.log('[页面加载] 恢复角色选择:', savedRoleId);
      setHomeSelectedRoleId(savedRoleId);
      setSelectedRoleId(savedRoleId);
    }
  }, []); // 只在组件挂载时执行一次

  // 当当前会话变化时，同步会话的团队和角色状态到UI
  useEffect(() => {
    if (currentConversation && view !== 'home') {
      console.log('[会话变化] 同步会话状态到UI:', {
        conversationId: currentConversation.id,
        team_id: currentConversation.team_id,
        role_id: currentConversation.role_id
      });
      
      // 同步团队状态
      if (currentConversation.team_id && currentConversation.team_id !== selectedTeamId) {
        setSelectedTeamId(currentConversation.team_id);
      }
      
      // 同步角色状态
      if (currentConversation.role_id && currentConversation.role_id !== selectedRoleId) {
        setSelectedRoleId(currentConversation.role_id);
      }
    }
  }, [currentConversation]); // 监听会话变化

  // localStorage操作工具函数
  const saveToLocalStorage = (key, value) => {
    try {
      if (value) {
        localStorage.setItem(key, value);
      } else {
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`保存${key}到localStorage失败:`, error);
    }
  };

  // 处理角色选择变更的函数
  const handleRoleChange = (roleId) => {
    console.log('[角色选择] 选择角色:', roleId);
    setHomeSelectedRoleId(roleId);
    saveToLocalStorage(ROLE_STORAGE_KEY, roleId);
    
    // 如果选择了角色，清除团队选择（互斥）
    if (roleId && homeSelectedTeamId) {
      console.log('[角色选择] 清除团队选择');
      setHomeSelectedTeamId('');
      saveToLocalStorage('jimu_selected_team_id', null);
    }
  };

  // 处理团队选择变更的函数
  const handleTeamChange = (teamId) => {
    console.log('[团队选择] 选择团队:', teamId);
    setHomeSelectedTeamId(teamId);
    saveToLocalStorage('jimu_selected_team_id', teamId);
    
    // 如果选择了团队，清除角色选择（互斥）
    if (teamId && homeSelectedRoleId) {
      console.log('[团队选择] 清除角色选择');
      setHomeSelectedRoleId('');
      saveToLocalStorage(ROLE_STORAGE_KEY, null);
    }
  };

  const handleHomeSend = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    const trimmedMessage = homeMessage.trim();

    // 检查是否所有输入都为空
    if (!trimmedMessage && !homeImagePreview && !homeAttachmentInfo) {
      message.error('到会话应该传递图片或者文件');
      return; // 阻止继续执行发送逻辑
    }

    if (!currentLoading) {
      let contentArr = [];
      if (trimmedMessage) {
        contentArr.push({ type: 'text', text: trimmedMessage });
      }
      if (homeImagePreview) {
        contentArr.push({ type: 'image_url', image_url: { url: homeImagePreview } });
      }
      if (homeAttachmentInfo) {
        contentArr.push({
          type: 'file',
          filename: homeAttachmentInfo.filename,
          filetype: homeAttachmentInfo.filetype,
          text: homeAttachmentInfo.text, // 只传递到后端，不展示
        });
      }

      // 构建选项对象
      const options = {
        is_web_search_enabled: homeWebSearchEnabled,
        systemPrompt: homeSystemPrompt,
        maxTokens: homeMaxTokens,
      };

      // 团队和角色互斥逻辑
      if (homeSelectedTeamId) {
        options.team_id = homeSelectedTeamId;
        console.log('[发送消息] 使用团队模式, team_id:', homeSelectedTeamId);
      } else if (homeSelectedRoleId) {
        options.role_id = homeSelectedRoleId;
        console.log('[发送消息] 使用角色模式, role_id:', homeSelectedRoleId);
      } else {
        console.log('[发送消息] 使用默认模式');
      }

      console.log('[发送消息] 完整选项:', options);

      onNewConversation(
        contentArr, // Pass contentArr instead of trimmedMessage
        selectedModel || settings?.defaultModels?.[selectedService],
        options
      );

      // 手动更新会话状态为loading
      if (currentConversation && onUpdateConversationModel) {
        onUpdateConversationModel(currentConversation.model, { loading: true });
      }

      setHomeMessage('');
      setHomeImagePreview(null);
      setHomeImageFile(null);
      setHomeAttachmentInfo(null);
      
      // 导航到聊天页面（重要！确保状态同步）
      navigate('/chat');
    }
  };

  const handleHomeKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleHomeSend();
    }
  };

  // 新增：处理刷新提示词的函数 (Implement refresh logic)
  const handleRefreshPrompts = () => {
    setSuggestions(getRandomSuggestions(4)); // 获取4个新的随机提示词
  };

  // 修改：处理提示词卡片点击的函数，添加导航
  const handleSuggestionClick = (suggestionText) => {
    // 构建选项对象，实现团队和角色互斥
    const options = {
      is_web_search_enabled: homeWebSearchEnabled,
      systemPrompt: homeSystemPrompt,
      maxTokens: homeMaxTokens,
    };

    // 团队和角色互斥逻辑
    if (homeSelectedTeamId) {
      options.team_id = homeSelectedTeamId;
    } else {
      options.role_id = homeSelectedRoleId;
    }

    // 调用重构后的 onNewConversation，传递文本和当前选定的模型
    onNewConversation(
      suggestionText,
      selectedModel || settings?.defaultModels?.[selectedService],
      options
    );
    
    // 导航到聊天页面（重要！确保状态同步）
    navigate('/chat');
  };

  // 添加setMcpServerSelectorVisible的修改，使其在打开时自动刷新MCP服务器状态
  const handleOpenMcpServerSelector = async () => {
    // 先打开弹窗
    setMcpServerSelectorVisible(true);
    // 然后刷新MCP服务器状态
    if (onUpdateMcpConfig) {
      await onUpdateMcpConfig();
    }
  };

  // 修改切换工具调用面板的函数，添加空数组判断
  const handleToggleToolPanel = () => {
    if (isToolPanelOpen) {
      handleCloseToolPanel();
    } else {
      // 直接打开面板，即使没有数据也允许打开
      handleOpenToolPanel();
    }
  };

  // 修改打开工具调用面板的函数，移除重复的数据检查
  const handleOpenToolPanel = () => {
    // 打开面板
    setIsToolPanelOpen(true);

    // 设置显示最新的工具调用 (因为我们已经确认 toolCallsData 非空)
    // 再次检查以防万一，虽然理论上不应发生
    if (toolCallsData && toolCallsData.length > 0) {
      setCurrentToolCallIndex(toolCallsData.length - 1);
    }
  };

  // 关闭工具调用面板
  const handleCloseToolPanel = () => {
    setIsToolPanelOpen(false);
  };

  // 导航到指定的工具调用
  const handleNavigateToolCall = (newIndex) => {
    setCurrentToolCallIndex(newIndex);
  };

  // 当会话数据更新且工具面板打开时，更新工具调用数据
  useEffect(() => {
    if (isToolPanelOpen && currentConversation) {
      try {
        console.log('[MainContent] 更新工具面板数据');
        const formattedToolCalls = formatToolCallsData(currentConversation);

        if (formattedToolCalls && formattedToolCalls.length > 0) {
          console.log(`[MainContent] 已格式化 ${formattedToolCalls.length} 个工具调用`);
          const completedToolCalls = completeStreamingToolCalls(formattedToolCalls, currentLoading);
          setToolCallsData(completedToolCalls);

          // 如果当前索引超出范围，重置为最后一个
          if (currentToolCallIndex >= completedToolCalls.length) {
            setCurrentToolCallIndex(completedToolCalls.length - 1);
          }
        } else {
          console.log('[MainContent] 没有检测到工具调用');
        }
      } catch (error) {
        console.error('更新工具调用数据时出错:', error);
      }
    }
  }, [currentConversation, currentLoading, isToolPanelOpen, currentToolCallIndex, formatToolCallsData, completeStreamingToolCalls]);

  // 更新useEffect钩子，当isToolPanelOpen状态变化时添加或移除布局类
  useEffect(() => {
    // 获取主内容布局元素
    const mainContentLayout = document.querySelector('.main-content-layout');
    if (mainContentLayout) {
      if (isToolPanelOpen) {
        mainContentLayout.classList.add('with-tool-panel');
        // 添加整体页面类以适应悬浮面板
        document.body.classList.add('has-floating-panel');
      } else {
        mainContentLayout.classList.remove('with-tool-panel');
        document.body.classList.remove('has-floating-panel');
      }
    }
  }, [isToolPanelOpen]);

  // 修改监听toolResults的useEffect，提升实时性和过滤精确度
  useEffect(() => {
    // 处理实时工具结果，不限于Agent模式，提高响应速度
    if (toolResults && toolResults.length > 0) {
      // 使用更精确的去重逻辑
      const currentToolCallIds = new Set(toolCallsData.map(t => t.id));
      const uniqueNewResults = toolResults.filter(r => r && r.id && !currentToolCallIds.has(r.id));

      if (uniqueNewResults.length > 0) {
        console.log(`[MainContent] Appending ${uniqueNewResults.length} new real-time tool result(s).`);

        // 追加到当前状态并更新为最新索引
        setToolCallsData(prevData => {
          const newData = [...prevData, ...uniqueNewResults];
          // 自动更新索引到最新工具调用
          setTimeout(() => {
            setCurrentToolCallIndex(newData.length - 1);
          }, 0);
          return newData;
        });
      }
    }
    // 依赖项不包含toolCallsData，避免循环依赖问题
  }, [toolResults]);

  // 修改从当前会话加载工具结果的函数，改为增量加载模式
  useEffect(() => {
    // 只在会话切换时执行初始化加载
    if (currentConversation) {
      console.log('[MainContent] 初始化会话工具调用历史', currentConversation.id);

      try {
        // 使用格式化函数提取工具调用历史
        const extractedToolCalls = formatToolCallsData(currentConversation);

        if (extractedToolCalls && extractedToolCalls.length > 0) {
          console.log(`[MainContent] 已从会话中提取 ${extractedToolCalls.length} 个工具调用`);

          // 完成流式调用处理
          const completedCalls = completeStreamingToolCalls(extractedToolCalls, currentLoading);

          // 设置工具调用数据和索引
          setToolCallsData(completedCalls);
          setCurrentToolCallIndex(Math.max(0, completedCalls.length - 1));

          // 移除自动打开工具面板的逻辑，只保留数据加载
          // 保证即使有工具调用数据，也不会在切换模式时自动打开面板
        } else {
          console.log(`[MainContent] 会话 ${currentConversation.id}: 不包含工具调用历史`);
          // 如果没有工具调用，清空数据
          setToolCallsData([]);
          setCurrentToolCallIndex(0);
        }
      } catch (error) {
        console.error('提取工具调用历史时出错:', error);
      }
    }
  }, [currentConversation, formatToolCallsData, completeStreamingToolCalls, currentLoading, isAgentMode]);

  // 处理打开参数设置模态框
  const handleOpenParamSettings = () => {
    setParamSettingsVisible(true);
  };

  // 处理保存参数设置
  const handleSaveParamSettings = (values) => {
    if (view === 'home') {
      // 首页设置 - 保存到本地状态
      setHomeSystemPrompt(values.systemPrompt || '');
      setHomeMaxTokens(values.maxTokens || '');
      MessageService.success('参数设置已保存，将应用于新会话');
    } else if (currentConversation && onUpdateConversationModel) {
      // 会话页面设置 - 更新当前会话
      onUpdateConversationModel(currentConversation.model, {
        systemPrompt: values.systemPrompt,
        maxTokens: values.maxTokens
      });
      MessageService.success('参数设置已保存，将应用于后续消息');
    }
    setParamSettingsVisible(false);
  };

  // 复制对话链接
  const handleCopyLink = () => {
    if (currentConversation && currentConversation.id) {
      const baseUrl = window.location.origin;
      const conversationLink = `${baseUrl}/chat/${currentConversation.id}`;
      
      navigator.clipboard.writeText(conversationLink)
        .then(() => {
          message.success('对话链接已复制到剪贴板');
        })
        .catch(err => {
          message.error('复制失败: ' + err);
        });
    }
  };

  // 新增: 标签栏相关处理函数
  const handleTabClick = (tabId) => {
    // 切换到指定的标签页
    setActiveTabId(tabId);
    // 如果当前会话ID不是点击的标签ID，则需要切换会话
    if (currentConversation?.id !== tabId) {
      onSelectConversation({ id: tabId });
    }
  };

  const handleTabClose = (e, tabId) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发标签点击
    // 从标签列表中移除指定标签
    const newTabs = openTabs.filter(tab => tab.id !== tabId);
    setOpenTabs(newTabs);
    // 同步到后端
    syncOpenTabsToServer(newTabs);

    // 如果关闭的是当前激活的标签，则切换到其他标签
    if (tabId === activeTabId) {
      if (newTabs.length > 0) {
        // 切换到最后一个标签
        const newActiveTab = newTabs[newTabs.length - 1];
        setActiveTabId(newActiveTab.id);
        onSelectConversation({ id: newActiveTab.id });
      } else {
        // 如果没有标签了，回到首页或创建新会话
        navigate('/');
      }
    }
  };

  const handleNewTab = () => {
    // 防御性检查确保onNewConversation是一个函数
    if (typeof onNewConversation !== 'function') {
      console.error('onNewConversation不是一个函数，无法创建新会话');
      // 使用导航作为备选方案
      navigate('/chat');
      return;
    }
    
    // 根据当前视图选择正确的状态变量
    const currentTeamId = view === 'home' ? homeSelectedTeamId : selectedTeamId;
    const currentRoleId = view === 'home' ? homeSelectedRoleId : selectedRoleId;
    const currentWebSearchEnabled = view === 'home' ? homeWebSearchEnabled : isWebSearchEnabled;
    const currentSystemPrompt = view === 'home' ? homeSystemPrompt : (currentConversation?.systemPrompt || '');
    const currentMaxTokens = view === 'home' ? homeMaxTokens : (currentConversation?.maxTokens || '');
    
    console.log('[新建标签] 当前页面状态:', {
      view,
      currentTeamId,
      currentRoleId,
      currentWebSearchEnabled
    });
    
    // 构建选项对象，实现团队和角色互斥
    const options = {
      is_web_search_enabled: currentWebSearchEnabled,
      systemPrompt: currentSystemPrompt,
      maxTokens: currentMaxTokens,
    };

    // 团队和角色互斥逻辑
    if (currentTeamId) {
      options.team_id = currentTeamId;
      console.log('[新建标签] 使用团队模式, team_id:', currentTeamId);
    } else if (currentRoleId) {
      options.role_id = currentRoleId;
      console.log('[新建标签] 使用角色模式, role_id:', currentRoleId);
    } else {
      console.log('[新建标签] 使用默认模式');
    }

    console.log('[新建标签] 完整选项:', options);

    // 创建新会话，使用与handleSuggestionClick相似的方式调用onNewConversation
    onNewConversation(
      null,
      selectedModel || settings?.defaultModels?.[selectedService],
      options
    );
    
    // 导航到聊天页面（重要！确保状态同步）
    navigate('/chat');
  };

  // 同步打开的标签到服务器
  const syncOpenTabsToServer = async (tabs) => {
    try {
      await fetch('/api/user/open-tabs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          tabs,
          activeTabId: activeTabId
        }),
      });
    } catch (error) {
      console.error('同步标签状态到服务器失败:', error);
    }
  };

  // 从服务器加载打开的标签
  const loadOpenTabsFromServer = async () => {
    try {
      const response = await fetch('/api/user/open-tabs');
      if (response.ok) {
        const data = await response.json();
        if (data.tabs && Array.isArray(data.tabs)) {
          setOpenTabs(data.tabs);
          // 如果有激活的标签，设置它
          if (data.activeTabId) {
            setActiveTabId(data.activeTabId);
          } else if (data.tabs.length > 0) {
            setActiveTabId(data.tabs[0].id);
          }
        }
      }
    } catch (error) {
      console.error('从服务器加载标签状态失败:', error);
    }
  };

  // 当会话变更时，更新标签栏
  useEffect(() => {
    if (currentConversation) {
      // 检查当前会话是否已在标签列表中
      const tabExists = openTabs.some(tab => tab.id === currentConversation.id);
      
      if (!tabExists) {
        // 如果不存在，添加到标签列表
        const newTabs = [...openTabs, {
          id: currentConversation.id,
          title: currentConversation.title,
          loading: currentConversation.loading || false
        }];
        setOpenTabs(newTabs);
        syncOpenTabsToServer(newTabs);
      } else {
        // 如果存在，更新标签标题和加载状态
        const updatedTabs = openTabs.map(tab => 
          tab.id === currentConversation.id 
            ? { ...tab, title: currentConversation.title, loading: currentConversation.loading || false }
            : tab
        );
        setOpenTabs(updatedTabs);
      }

      // 设置当前活动标签
      setActiveTabId(currentConversation.id);
    }
  }, [currentConversation]);

  // 初始化时加载标签状态
  useEffect(() => {
    loadOpenTabsFromServer();
  }, []);

  // 当activeTabId变化时同步到服务器
  useEffect(() => {
    if (activeTabId && openTabs.length > 0) {
      syncOpenTabsToServer(openTabs);
    }
  }, [activeTabId, openTabs]);

  // 新增：处理标签折叠的函数
  const handleTabsOverflow = useCallback(() => {
    if (!tabsContainerRef.current) return;
    
    const container = tabsContainerRef.current;
    const containerWidth = container.clientWidth;
    
    // 计算控制区域的宽度
    const controlsWidth = 100; // 预留100px给控制按钮区域（新建标签按钮+可能的下拉菜单）
    
    // 可用于标签显示的最大宽度
    const availableWidth = containerWidth - controlsWidth;
    
    // 获取所有标签元素
    const tabsElements = container.querySelectorAll('.tab');
    
    // 如果没有标签，则直接返回
    if (tabsElements.length === 0) return;
    
    // 重置折叠状态
    let collapsedTabIds = [];
    let totalWidth = 0;
    
    // 使用状态中的activeTabId，不要创建同名局部变量
    let activeTabWidth = 0;
    
    // 首先计算所有标签总宽度，标识活动标签
    for (let i = 0; i < tabsElements.length; i++) {
      const tab = tabsElements[i];
      const width = tab.offsetWidth;
      
      // 如果是活动标签，记录宽度
      if (tab.classList.contains('active')) {
        activeTabWidth = width;
      } else {
        // 累计非活动标签的总宽度
        totalWidth += width;
      }
    }
    
    // 如果总宽度（加上活动标签宽度）超过可用宽度，开始折叠
    if (totalWidth + activeTabWidth > availableWidth) {
      // 确保活动标签始终可见
      let remainingWidth = availableWidth - activeTabWidth;
      
      // 从右向左折叠标签
      for (let i = tabsElements.length - 1; i >= 0; i--) {
        const tab = tabsElements[i];
        
        // 跳过活动标签
        if (tab.classList.contains('active')) continue;
        
        const width = tab.offsetWidth;
        
        // 如果剩余宽度不足以显示此标签，则将其折叠
        if (remainingWidth < width) {
          const tabId = tab.getAttribute('data-tab-id');
          if (tabId) {
            collapsedTabIds.push(tabId);
          }
        } else {
          // 减少剩余宽度
          remainingWidth -= width;
        }
      }
      
      // 更新折叠标签列表
      if (collapsedTabIds.length > 0) {
        const collapsedTabsData = openTabs.filter(tab => collapsedTabIds.includes(tab.id));
        setCollapsedTabs(collapsedTabsData);
      } else {
        setCollapsedTabs([]);
      }
    } else {
      // 如果总宽度没有超过可用宽度，不需要折叠任何标签
      setCollapsedTabs([]);
    }
  }, [openTabs, activeTabId]);

  // 监听窗口大小变化和标签变化，重新计算折叠
  useEffect(() => {
    const handleResize = () => {
      handleTabsOverflow();
    };
    
    window.addEventListener('resize', handleResize);
    handleTabsOverflow(); // 初始计算
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [openTabs, handleTabsOverflow]);

  // 渲染浏览器风格的标签栏
  const renderTabBar = () => {
    if (view !== 'history' || !currentConversation) return null;
    
    return (
      <div className="browser-style-tabs">
        {/* 将tabs-container改为单独一行 */}
        <div className="tabs-container-wrapper">
          <div className="tabs-container" ref={tabsContainerRef}>
            {openTabs
              .filter(tab => !collapsedTabs.some(ct => ct.id === tab.id))
              .map(tab => (
                <div 
                  key={tab.id}
                  data-tab-id={tab.id}
                  className={`tab ${tab.id === activeTabId ? 'active' : ''}`}
                  onClick={() => handleTabClick(tab.id)}
                >
                  {tab.loading && <LoadingOutlined style={{ marginRight: 5 }} />}
                  <span className="tab-title">{tab.title}</span>
                  <Button
                    type="text"
                    size="small"
                    className="close-tab-btn"
                    icon={<CloseOutlined />}
                    onClick={(e) => handleTabClose(e, tab.id)}
                  />
                </div>
              ))}
              
            {/* 将下拉菜单按钮移动到此处，确保它显示在最右边 */}
            <div className="tab-controls">
              {/* 折叠标签的下拉菜单按钮 */}
              {collapsedTabs.length > 0 && (
                <Dropdown
                  overlay={
                    <Menu>
                      {collapsedTabs.map(tab => (
                        <Menu.Item key={tab.id} onClick={() => handleTabClick(tab.id)}>
                          <div className="dropdown-tab-item">
                            {tab.loading && <LoadingOutlined style={{ marginRight: 5 }} />}
                            <span>{tab.title}</span>
                            <Button
                              type="text"
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleTabClose(e, tab.id);
                              }}
                            />
                          </div>
                        </Menu.Item>
                      ))}
                    </Menu>
                  }
                  onVisibleChange={setShowTabsDropdown}
                  visible={showTabsDropdown}
                  placement="bottomRight"
                  trigger={['click']}
                >
                  <Button
                    type="text"
                    size="small"
                    className="tabs-dropdown-btn"
                    icon={<DownOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowTabsDropdown(!showTabsDropdown);
                    }}
                  >
                    {collapsedTabs.length}
                  </Button>
                </Dropdown>
              )}
              
              {/* 仅在onNewConversation是函数时显示新建标签按钮 */}
              {typeof onNewConversation === 'function' && (
                <Button
                  type="text"
                  size="small"
                  className="new-tab-btn"
                  icon={<PlusOutlined />}
                  onClick={handleNewTab}
                />
              )}
            </div>
          </div>
        </div>
        
        {/* 将tabs-actions改为单独一行 */}
        <div className="tabs-actions-wrapper">
          <div className="tabs-actions">
            {/* 根据Agent模式决定是否显示参数设置按钮 */}
            {!isAgentMode && (
              <Button
                icon={<SettingOutlined />}
                type="text"
                onClick={handleOpenParamSettings}
                title="参数设置"
              >
                参数设置
              </Button>
            )}
            
            {/* 添加积木搜索按钮 */}
            <Button
              icon={<SearchOutlined />}
              type="text"
              onClick={handleOpenSearch}
              title="积木搜索"
            >
              积木搜索
            </Button>
            
            {/* 添加插件市场按钮 */}
            <Button
              icon={<AppstoreOutlined />}
              type="text"
              onClick={handleOpenMarketplace}
              title="插件市场"
            >
              插件市场
            </Button>
            
            <Button
              icon={<HistoryOutlined />}
              type="text"
              onClick={onToggleConversationDrawer}
            >
              历史记录
            </Button>
            <Popconfirm
              title="确定要清空当前会话历史吗？"
              onConfirm={onClearHistory}
              okText="确定"
              cancelText="取消"
            >
              <Button icon={<ClearOutlined />} type="text">清空历史</Button>
            </Popconfirm>
          </div>
        </div>
      </div>
    );
  };

  const renderHomeView = () => (
    <div className="home-view-container">
      <div className="welcome-header">
        <Title level={1} className="welcome-greeting">
          你好,
          <span className="greeting-name-gradient">John</span>
        </Title>
        <Title level={1} className="welcome-prompt">想了解些什么？</Title>
        <Text className="welcome-subtitle">使用以下常见提示词</Text>
      </div>
      
      <div className="prompt-suggestions">
        {suggestions.map((suggestion, index) => (
          <Card
            key={index}
            className="suggestion-card"
            hoverable
            onClick={() => handleSuggestionClick(suggestion.text)}
          >
            <Text className="suggestion-card-text">{suggestion.text}</Text>
            <div className="suggestion-card-icon-bottom">
              {suggestion.icon}
            </div>
          </Card>
        ))}
      </div>
      <Button
        className="refresh-prompts-button"
        icon={<SyncOutlined />}
        onClick={handleRefreshPrompts}
      >
        刷新提示词
      </Button>


      <div className="home-input-section">
        <TextArea
          value={homeMessage}
          onChange={(e) => {
            // 注释掉字符限制检查
            // if (e.target.value.length <= MAX_CHARS) {
              setHomeMessage(e.target.value);
            // }
          }}
          onKeyDown={handleHomeKeyDown}
          placeholder="随便问点什么吧..."
          autoSize={{ minRows: 2, maxRows: 6 }}
          disabled={loading}
        />
        <div className="home-input-actions">
          <div className="left-actions">
            <Tooltip title="添加附件">
              <Button
                type="text"
                icon={homeUploadingAttachment ? <LoadingOutlined /> : <PaperClipOutlined />}
                disabled={currentLoading || homeUploadingAttachment}
                onClick={() => document.getElementById('home-attachment-file-input')?.click()}
              />
              <input
                id="home-attachment-file-input"
                type="file"
                accept=".pdf,.docx,.pptx,.xls,.xlsx"
                style={{ display: 'none' }}
                multiple={false}
                onChange={handleHomeAttachmentChange}
              />
            </Tooltip>
            <Tooltip title="使用图片">
              <Button
                type="text"
                icon={<FileImageOutlined />}
                disabled={currentLoading}
                onClick={() => document.getElementById('home-image-file-input')?.click()}
              />
              <input
                id="home-image-file-input"
                type="file"
                accept=".xbm,.tif,.tiff,.jfif,.pjp,.apng,.jpeg,.heif,.ico,.webp,.svgz,.jpg,.heic,.gif,.svg,.png,.bmp,.pjpeg,.avif"
                style={{ display: 'none' }}
                multiple={false}
                onChange={handleHomeImageChange}
              />
            </Tooltip>
            {/* 根据Agent模式决定是否显示模型选择按钮 */}
            {!isAgentMode && (
              <Tooltip title="选择模型">
                <Button
                  type="text"
                  icon={
                    selectedModel ? (
      <div style={{
                        borderRadius: '4px',
                        padding: '2px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <img
                          src={getModelIconUrl(selectedModel)}
                          alt={selectedModel}
                          style={{ width: 16, height: 16 }}
                        />
                      </div>
                    ) : (
                      <AppstoreOutlined />
                    )
                  }
                  onClick={() => setModelSelectorVisible(true)}
                  disabled={loading}
                />
              </Tooltip>
            )}
            {/* 添加角色选择器图标按钮 */}
            {!isAgentMode && (
              <div className="role-selector-icon-wrapper">
                <RoleSelector 
                  value={homeSelectedRoleId}
                  onChange={handleRoleChange}
                  disabled={loading}
                  className="home-role-selector"
                />
              </div>
            )}
            {/* 添加团队选择器图标按钮 */}
            {!isAgentMode && (
              <div className="team-selector-icon-wrapper">
                <TeamSelector 
                  value={homeSelectedTeamId}
                  onChange={handleTeamChange}
                  disabled={loading}
                  className="home-team-selector"
                />
              </div>
            )}
            {/* 根据Agent模式决定是否显示MCP服务器选择按钮 */}
            {!isAgentMode && !homeSelectedRoleId && !homeSelectedTeamId && (
              <Tooltip title="选择 MCP 服务器">
                <Button
                  type="text"
                  icon={
                    <div style={{
                      borderRadius: '4px',
                      padding: '2px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <CloudServerOutlined style={{ color: '#4285F4' }} />
                    </div>
                  }
                  onClick={handleOpenMcpServerSelector}
                />
              </Tooltip>
            )}
            {/* 添加 Agent 模式切换按钮 */}
            {!homeSelectedRoleId && !homeSelectedTeamId && (
              <Tooltip title={isAgentMode ? "Agent 模式：开启" : "Agent 模式：关闭"}>
                <Button
                  type="text"
                  icon={
                    <div style={{
                      borderRadius: '4px',
                      padding: '2px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <RobotFilled style={{ color: isAgentMode ? '#52c41a' : '#bfbfbf' }} />
                    </div>
                  }
                  onClick={onToggleAgentMode}
                />
              </Tooltip>
            )}

            {/* 根据Agent模式决定是否显示参数设置按钮，即使选择了角色也显示 */}
            {!isAgentMode && (
              <Tooltip title="参数设置">
                <Button
                  type="text"
                  icon={
                    <div style={{
                      borderRadius: '4px',
                      padding: '2px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <SettingOutlined style={{ color: '#555' }} />
                    </div>
                  }
                  onClick={handleOpenParamSettings}
                />
              </Tooltip>
            )}

            {/* 添加积木搜索按钮 */}
            <Tooltip title="积木搜索">
              <Button
                type="text"
                icon={
                  <div style={{
                    borderRadius: '4px',
                    padding: '2px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <SearchOutlined style={{ color: '#52c41a' }} />
                  </div>
                }
                onClick={handleOpenSearch}
              />
            </Tooltip>

            {/* 添加插件市场按钮 */}
            <Tooltip title="插件市场">
              <Button
                type="text"
                icon={
                  <div style={{
                    borderRadius: '4px',
                    padding: '2px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <AppstoreOutlined style={{ color: '#1890ff' }} />
                  </div>
                }
                onClick={handleOpenMarketplace}
              />
            </Tooltip>
          </div>
          <div className="right-actions">
            {/* 在agent模式下或选择了角色/团队时隐藏网络搜索按钮 */}
            {!isAgentMode && !homeSelectedRoleId && !homeSelectedTeamId && (
              <Button
                className="mode-selector"
                disabled={currentLoading}
                type={homeWebSearchEnabled ? "primary" : "default"}
                style={{
                  backgroundColor: homeWebSearchEnabled ? '#8b5cf6' : '#f3f4f6',
                  color: homeWebSearchEnabled ? 'white' : '#4b5563',
                  borderColor: homeWebSearchEnabled ? '#8b5cf6' : 'transparent'
                }}
                onClick={() => {
                  // 先更新本地状态，确保UI立即响应
                  const newState = !homeWebSearchEnabled;
                  setHomeWebSearchEnabled(newState);

                  if (newState) {
                    MessageService.success('已切换网络搜索模式');
                  } else {
                    MessageService.success('已关闭网络搜索模式');
                  }

                  // 强制重新渲染以确保状态更新反映在UI上
                  setTimeout(() => {
                    setHomeWebSearchEnabled(prev => prev);
                  }, 10);
                }}
              >
                <GlobalOutlined />
                全网搜索
              </Button>
            )}

            {/* 注释掉字符计数显示 */}
            {/* <Text className="character-count">
              {homeMessage.length} / {MAX_CHARS}
            </Text> */}
            <Tooltip title="发送 (Enter)">
              <Button
                type="primary"
                icon={currentLoading ? <LoadingOutlined /> : <SendOutlined />}
                onClick={handleHomeSend}
                disabled={(!homeMessage.trim() && !homeImagePreview && !homeAttachmentInfo) || currentLoading}
                className="send-button"
              />
            </Tooltip>
          </div>
        </div>
      </div>
      {/* 附件和图片预览 */}
      <div style={{ marginTop: 8 }}>
        {homeImagePreview && (
          <div style={{ position: 'relative', display: 'inline-block', maxWidth: 200, marginRight: 16 }}>
            <img
              src={homeImagePreview}
              alt="预览"
              style={{ maxWidth: 200, maxHeight: 120, borderRadius: 8, border: '1px solid #eee' }}
            />
            <Button
              size="small"
              type="text"
              style={{
                position: 'absolute',
                top: 2,
                right: 2,
                background: 'rgba(255,255,255,0.9)',
                color: '#ff4d4f',
                border: '1px solid #ff4d4f',
                borderRadius: '50%',
                width: 24,
                height: 24,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                transition: 'background 0.2s, color 0.2s, border 0.2s',
                cursor: 'pointer',
                zIndex: 2
              }}
              icon={<CloseOutlined />}
              onClick={handleRemoveHomeImage}
            />
          </div>
        )}
        {homeAttachmentInfo && (
          <div style={{ display: 'flex', alignItems: 'center', background: '#f0f2f5', padding: '8px 12px', borderRadius: 8 }}>
            <PaperClipOutlined style={{ fontSize: 18, marginRight: 8, color: '#7e22ce' }} />
            <span>{homeAttachmentInfo.filename}</span>
            <Button
              size="small"
              type="text"
              icon={<CloseOutlined />}
              onClick={handleRemoveHomeAttachment}
              style={{ marginLeft: 8 }}
            />
            {homeUploadingAttachment && <Spin size="small" style={{ marginLeft: 8 }} />}
          </div>
        )}
      </div>
    </div>
  );

  // 修改渲染工具调用侧边面板的函数，确保传递完整消息数据
  const renderToolCallSidePanel = () => {
    if (!isToolPanelOpen) return null;

    // 总是传递完整的消息数据，不区分是否Agent模式
    const messagesData = currentConversation?.messages || [];
    // 确保 toolCalls 至少是一个空数组
    const toolCalls = toolCallsData || [];

    return (
      <ToolCallSidePanel
        isOpen={isToolPanelOpen}
        onClose={handleCloseToolPanel}
        toolCalls={toolCalls}
        currentIndex={currentToolCallIndex}
        onNavigate={handleNavigateToolCall}
        messages={messagesData}
        agentStatus={currentLoading ? "thinking" : "ready"}
        project={null}
      />
    );
  };

  // 处理打开插件市场
  const handleOpenMarketplace = () => {
    setMarketplaceVisible(true);
  };

  // 处理关闭插件市场
  const handleCloseMarketplace = () => {
    setMarketplaceVisible(false);
  };

  // 处理打开积木搜索
  const handleOpenSearch = () => {
    setSearchModalVisible(true);
  };

  // 处理关闭积木搜索
  const handleCloseSearch = () => {
    setSearchModalVisible(false);
  };

  // 根据视图类型决定显示内容
  // 主页视图：显示欢迎页面
  if (view === 'home') {
    return (
      <>
        <Layout className={`main-content-layout ${isToolPanelOpen ? 'with-tool-panel' : ''}`} style={{ 'background-color': '#ffffff'}}>
          <Content className="main-content" style={{ 'background-color': '#ffffff',display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            {renderHomeView()}
          </Content>
        </Layout>

        {/* 添加模型选择弹窗 */}
        <ModelSelector
          isOpen={modelSelectorVisible}
          onClose={() => setModelSelectorVisible(false)}
          modelsCache={modelsCache}
          selectedModel={selectedModel}
          onModelSelect={(model, service) => {
            setSelectedModel(model);
            // 如果服务发生变化，同时更新服务
            if (service && service !== selectedService) {
              onServiceChange(service);
            }
            // 更新当前会话的模型属性
            if (currentConversation && onUpdateConversationModel) {
              onUpdateConversationModel(model);
            }
          }}
          selectedService={selectedService}
          onRefreshModels={onRefreshModels}
        />

        {/* 添加 MCP 服务选择弹窗 */}
        <Modal
          title="选择 MCP 服务器"
          open={mcpServerSelectorVisible}
          onCancel={() => setMcpServerSelectorVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setMcpServerSelectorVisible(false)}>
              关闭
            </Button>
          ]}
        >
          <MCPServerSelector
            mcpServers={mcpServers}
            mcpServersStatus={mcpServersStatus}
            selectedServers={selectedServers || []}
            onSelectServers={onSelectServers}
            onRefreshStatus={async (status) => {
              // 更新App组件中的MCP服务器状态
              if (onUpdateMcpConfig) {
                await onUpdateMcpConfig();
              }
            }}
          />
        </Modal>

        {/* 渲染工具调用侧边面板 */}
        {renderToolCallSidePanel()}

        {/* 渲染参数设置模态框 */}
        <ParamSettingsModal
          visible={paramSettingsVisible}
          onClose={() => setParamSettingsVisible(false)}
          onSave={handleSaveParamSettings}
          conversation={view === 'home' ?
            // 首页视图 - 使用本地状态作为初始值
            { systemPrompt: homeSystemPrompt, maxTokens: homeMaxTokens } :
            // 会话页面 - 使用当前会话
            currentConversation
          }
        />

        {/* 渲染插件市场模态框 */}
        <MCPMarketplace 
          visible={marketplaceVisible} 
          onClose={handleCloseMarketplace}
          installedServers={mcpServers}
        />

        {/* 渲染积木搜索模态框 */}
        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <SearchOutlined style={{ color: '#52c41a', fontSize: '18px' }} />
              <span style={{ fontSize: '16px', fontWeight: 600 }}>积木搜索</span>
              <span style={{ fontSize: '12px', color: '#666', fontWeight: 400 }}>- 四大搜索引擎聚合</span>
            </div>
          }
          open={searchModalVisible}
          onCancel={handleCloseSearch}
          footer={null}
          width={1400}
          style={{ top: 20 }}
          bodyStyle={{ 
            padding: '16px', 
            height: '82vh', 
            overflow: 'hidden',
            backgroundColor: '#fafafa'
          }}
          className="search-modal"
        >
          <div style={{ 
            height: '100%', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }}>
            <SearchBrowser />
          </div>
        </Modal>
      </>
    );
  }

  // 历史记录视图
  if (view === 'history') {
    return (
      <>
        <Layout className={`main-content-layout ${isToolPanelOpen ? 'with-tool-panel' : ''}`}>
          <Content className="main-content">
            {!currentConversation ? (
              <div style={{ display: 'flex', height: '100%', alignItems: 'center', justifyContent: 'center' }}>
                <Empty description="请从侧边栏选择或创建一个新会话" />
              </div>
            ) : (
              <>
                {/* 替换原会话标题区域为标签栏 */}
                {renderTabBar()}

                <div className="messages-container" ref={messagesContainerRef}>
                  {error && (
                    <Alert
                      message="发生错误"
                      description={error}
                      type="error" showIcon closable
                      className="error-alert"
                      style={{ marginBottom: '16px' }}
                    />
                  )}
                  {currentConversation && currentConversation.messages && currentConversation.messages.map((msg, idx) => (
        <ChatMessageItem
                      key={msg.id || idx} // Prefer msg.id if available and unique, otherwise fallback to index
                      message={msg}
                      index={idx}
                      currentConversationMessages={currentConversation.messages}
          isAgentMode={isAgentMode}
          selectedService={selectedService}
                      loading={loading} // Pass global loading state
          onCopyMessage={handleCopyMessage}
          onRegenerateMessage={handleRegenerateMessage}
          functionCallExpanded={functionCallExpanded}
          setFunctionCallExpanded={setFunctionCallExpanded}
          functionResultExpanded={functionResultExpanded}
          setFunctionResultExpanded={setFunctionResultExpanded}
        />
                  ))}
                  {loading && (
                    <div className="ai-thinking">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
      </div>
                      {/* <Button
                        className="stop-generation-button"
                        onClick={onStopGeneration}
                        danger
                      >
                        停止生成
                      </Button> */}
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                  {showScrollToBottom && (
                    <Button
                      className="scroll-to-bottom-button"
                      type="primary"
                      shape="circle"
                      icon={<DownOutlined />}
                      onClick={handleScrollToBottom}
                    />
                  )}
                </div>

                <div className="input-container">
                  <div className="service-selector">
                    {/* 根据Agent模式决定是否显示模型供应商选择器 */}
                    {!isAgentMode && (
                      <Select
                        value={selectedService}
                        onChange={onServiceChange}
                        suffixIcon={<AppstoreOutlined />}
                      >
                        <Option value="openai">
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <img
                              src={openaiIcon}
                              alt="OpenAI"
                              style={{ width: 16, height: 16 }}
                            />
                            <span style={{ marginLeft: '8px' }}>OpenAI</span>
                          </div>
                        </Option>
                      </Select>
                    )}

                    {/* 根据Agent模式决定是否显示模型选择按钮 */}
                    {!isAgentMode && (
                      <Tooltip title="选择模型">
                        <Button
                          onClick={() => setModelSelectorVisible(true)}
                          loading={!modelsCache[selectedService]}
                          icon={
                            selectedModel ? (
                              <img
                                src={getModelIconUrl(selectedModel)}
                                alt={selectedModel}
                              />
                            ) : (
                              <AppstoreOutlined />
                            )
                          }
                        >
                          {selectedModel ? selectedModel : '选择模型'}
                        </Button>
                      </Tooltip>
                    )}

                    {/* 添加角色选择器到顶部工具栏 */}
                    {!isAgentMode && !selectedTeamId && (
                      <Tooltip title="选择角色">
                        <div>
                          <RoleSelector 
                            value={selectedRoleId}
                            onChange={(roleId) => {
                              console.log('[会话页面角色选择] 选择角色:', roleId);
                              setSelectedRoleId(roleId);
                              
                              // 保存到localStorage（重要！）
                              saveToLocalStorage(ROLE_STORAGE_KEY, roleId);
                              
                              // 如果选择了角色，清除团队选择（互斥）
                              if (roleId && selectedTeamId) {
                                console.log('[会话页面角色选择] 清除团队选择');
                                setSelectedTeamId('');
                                saveToLocalStorage('jimu_selected_team_id', null);
                              }
                              
                              if (currentConversation && onUpdateConversationModel) {
                                onUpdateConversationModel(currentConversation.model, { role_id: roleId, team_id: null });
                              }
                            }}
                            disabled={loading}
                          />
                        </div>
                      </Tooltip>
                    )}

                    {/* 添加团队选择器到顶部工具栏 */}
                    {!isAgentMode && !selectedRoleId && (
                      <Tooltip title="选择团队">
                        <div>
                          <TeamSelector 
                            value={selectedTeamId}
                            onChange={(teamId) => {
                              console.log('[会话页面团队选择] 选择团队:', teamId);
                              setSelectedTeamId(teamId);
                              
                              // 保存到localStorage（重要！）
                              saveToLocalStorage('jimu_selected_team_id', teamId);
                              
                              // 如果选择了团队，清除角色选择（互斥）
                              if (teamId && selectedRoleId) {
                                console.log('[会话页面团队选择] 清除角色选择');
                                setSelectedRoleId('');
                                saveToLocalStorage(ROLE_STORAGE_KEY, null);
                              }
                              
                              if (currentConversation && onUpdateConversationModel) {
                                onUpdateConversationModel(currentConversation.model, { team_id: teamId, role_id: null });
                              }
                            }}
                            disabled={loading}
                          />
                        </div>
                      </Tooltip>
                    )}

                    {/* 根据Agent模式和角色ID/团队ID决定是否显示MCP服务器选择按钮 */}
                    {!isAgentMode && !selectedRoleId && !selectedTeamId && (
                      <Tooltip title="选择MCP服务器">
                        <Button
                          icon={<CloudServerOutlined style={{ color: '#4285F4' }} />}
                          onClick={handleOpenMcpServerSelector}
                        >
                          {(selectedServers || []).length ? `已选 ${(selectedServers || []).length} 个服务器` : '选择MCP服务器'}
                        </Button>
                      </Tooltip>
                    )}

                    {/* 只在没有选择角色或团队时显示Agent模式切换按钮 */}
                    {!selectedRoleId && !selectedTeamId && (
                      <Tooltip title={isAgentMode ? "Agent模式已开启" : "开启Agent模式"}>
                        <Button
                          icon={<RobotFilled style={{ color: isAgentMode ? '#52c41a' : '#bfbfbf' }} />}
                          onClick={onToggleAgentMode}
                          type={isAgentMode ? "primary" : "default"}
                        >
                          {isAgentMode ? "Agent模式" : "普通模式"}
                        </Button>
                      </Tooltip>
                    )}
                  </div>

                  <MessageInput
                    onSendMessage={onSendMessage}
                    loading={currentLoading}
                    selectedService={selectedService}
                    models={modelsCache[selectedService] || []}
                    selectedModel={selectedModel}
                    onModelChange={setSelectedModel}
                    functionTools={functionTools}
                    onStopGeneration={onStopGeneration}
                    mcpServers={mcpServers}
                    mcpServersStatus={mcpServersStatus}
                    selectedServers={selectedServers || []}
                    onSelectServers={onSelectServers}
                    isAgentMode={isAgentMode}  // 传递Agent模式状态给MessageInput组件
                    // 仅在非Agent模式且未选择角色或团队时传递网络搜索相关属性
                    isWebSearchEnabled={!isAgentMode && !selectedRoleId && !selectedTeamId && isWebSearchEnabled}
                    role_id={selectedRoleId}  // 传递角色ID
                    team_id={selectedTeamId}  // 传递团队ID
                    onUpdateConversationModel={(model, options) => {
                      // 确保正确处理网络搜索状态更新
                      if (!isAgentMode && !selectedRoleId && options && typeof options.is_web_search_enabled !== 'undefined') {
                        // 更新本地状态
                        setIsWebSearchEnabled(options.is_web_search_enabled);
                      }
                      // 调用父组件的回调
                      if (onUpdateConversationModel) {
                        onUpdateConversationModel(model, options);
                      }
                    }}
                  />
                </div>
              </>
            )}
          </Content>
        </Layout>

        {/* 添加模型选择弹窗 */}
        <ModelSelector
          isOpen={modelSelectorVisible}
          onClose={() => setModelSelectorVisible(false)}
          modelsCache={modelsCache}
          selectedModel={selectedModel}
          onModelSelect={(model, service) => {
            setSelectedModel(model);
            // 如果服务发生变化，同时更新服务
            if (service && service !== selectedService) {
              onServiceChange(service);
            }
            // 更新当前会话的模型属性
            if (currentConversation && onUpdateConversationModel) {
              onUpdateConversationModel(model);
            }
          }}
          selectedService={selectedService}
        />

        {/* 添加 MCP 服务选择弹窗 */}
        <Modal
          title="选择 MCP 服务器"
          open={mcpServerSelectorVisible}
          onCancel={() => setMcpServerSelectorVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setMcpServerSelectorVisible(false)}>
              关闭
            </Button>
          ]}
        >
          <MCPServerSelector
            mcpServers={mcpServers}
            mcpServersStatus={mcpServersStatus}
            selectedServers={selectedServers || []}
            onSelectServers={onSelectServers}
            onRefreshStatus={async (status) => {
              // 更新App组件中的MCP服务器状态
              if (onUpdateMcpConfig) {
                await onUpdateMcpConfig();
              }
            }}
          />
        </Modal>

        {/* 渲染工具调用侧边面板 */}
        {renderToolCallSidePanel()}

        {/* 渲染参数设置模态框 */}
        <ParamSettingsModal
          visible={paramSettingsVisible}
          onClose={() => setParamSettingsVisible(false)}
          onSave={handleSaveParamSettings}
          conversation={view === 'home' ?
            // 首页视图 - 使用本地状态作为初始值
            { systemPrompt: homeSystemPrompt, maxTokens: homeMaxTokens } :
            // 会话页面 - 使用当前会话
            currentConversation
          }
        />

        {/* 渲染插件市场模态框 */}
        <MCPMarketplace 
          visible={marketplaceVisible} 
          onClose={handleCloseMarketplace}
          installedServers={mcpServers}
        />

        {/* 渲染积木搜索模态框 */}
        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <SearchOutlined style={{ color: '#52c41a', fontSize: '18px' }} />
              <span style={{ fontSize: '16px', fontWeight: 600 }}>积木搜索</span>
              <span style={{ fontSize: '12px', color: '#666', fontWeight: 400 }}>- 四大搜索引擎聚合</span>
            </div>
          }
          open={searchModalVisible}
          onCancel={handleCloseSearch}
          footer={null}
          width={1400}
          style={{ top: 20 }}
          bodyStyle={{ 
            padding: '16px', 
            height: '82vh', 
            overflow: 'hidden',
            backgroundColor: '#fafafa'
          }}
          className="search-modal"
        >
          <div style={{ 
            height: '100%', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }}>
            <SearchBrowser />
          </div>
        </Modal>
      </>
    );
  }

  // 添加设置视图的处理
  if (view === 'settings') {
    return (
      <Layout className="main-content-layout">
        <Content className="main-content" style={{ padding: '20px' }}>
          <Settings
            settings={settings}
            onSave={onUpdateSettings}
            modelsCache={modelsCache}
            mcpServers={mcpServers}
            onUpdateMcpConfig={onUpdateMcpConfig}
            onRefreshModels={onRefreshModels}
          />
        </Content>
      </Layout>
    );
  }

  // Default Fallback (e.g., for image view or others)
  // This part needs implementation based on how other views are handled
  return (
    <>
      <Layout className={`main-content-layout ${isToolPanelOpen ? 'with-tool-panel' : ''}`}>
        <Content className="main-content" style={{ padding: '20px' }}>
          {/* Placeholder for other views like AI Image Page */}
          <p>视图 '{view}' 的内容待实现。</p>
        </Content>
      </Layout>

      {/* 添加模型选择弹窗 */}
      <ModelSelector
        isOpen={modelSelectorVisible}
        onClose={() => setModelSelectorVisible(false)}
        modelsCache={modelsCache}
        selectedModel={selectedModel}
        onModelSelect={(model, service) => {
          setSelectedModel(model);
          // 如果服务发生变化，同时更新服务
          if (service && service !== selectedService) {
            onServiceChange(service);
          }
          // 更新当前会话的模型属性
          if (currentConversation && onUpdateConversationModel) {
            onUpdateConversationModel(model);
          }
        }}
        selectedService={selectedService}
      />

      {/* 添加 MCP 服务选择弹窗 */}
      <Modal
        title="选择 MCP 服务器"
        open={mcpServerSelectorVisible}
        onCancel={() => setMcpServerSelectorVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setMcpServerSelectorVisible(false)}>
            关闭
          </Button>
        ]}
      >
        <MCPServerSelector
          mcpServers={mcpServers}
          mcpServersStatus={mcpServersStatus}
          selectedServers={selectedServers || []}
          onSelectServers={onSelectServers}
          onRefreshStatus={async (status) => {
            // 更新App组件中的MCP服务器状态
            if (onUpdateMcpConfig) {
              await onUpdateMcpConfig();
            }
          }}
        />
      </Modal>

      {/* 渲染工具调用侧边面板 */}
      {renderToolCallSidePanel()}

      {/* 渲染参数设置模态框 */}
      <ParamSettingsModal
        visible={paramSettingsVisible}
        onClose={() => setParamSettingsVisible(false)}
        onSave={handleSaveParamSettings}
        conversation={view === 'home' ?
          // 首页视图 - 使用本地状态作为初始值
          { systemPrompt: homeSystemPrompt, maxTokens: homeMaxTokens } :
          // 会话页面 - 使用当前会话
          currentConversation
        }
      />

        {/* 渲染插件市场模态框 */}
        <MCPMarketplace 
          visible={marketplaceVisible} 
          onClose={handleCloseMarketplace}
          installedServers={mcpServers}
        />

        {/* 渲染积木搜索模态框 */}
        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <SearchOutlined style={{ color: '#52c41a', fontSize: '18px' }} />
              <span style={{ fontSize: '16px', fontWeight: 600 }}>积木搜索</span>
              <span style={{ fontSize: '12px', color: '#666', fontWeight: 400 }}>- 四大搜索引擎聚合</span>
            </div>
          }
          open={searchModalVisible}
          onCancel={handleCloseSearch}
          footer={null}
          width={1400}
          style={{ top: 20 }}
          bodyStyle={{ 
            padding: '16px', 
            height: '82vh', 
            overflow: 'hidden',
            backgroundColor: '#fafafa'
          }}
          className="search-modal"
        >
          <div style={{ 
            height: '100%', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }}>
            <SearchBrowser />
          </div>
        </Modal>
      </>
    );
  };

export default MainContent; 