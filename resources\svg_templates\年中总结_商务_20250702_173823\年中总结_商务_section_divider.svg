<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <title>年中总结 - 章节分隔页模板</title>
  <desc>
    高质量SVG模板，用于年中总结报告的章节分隔页。
    设计风格：商务，统一蓝色系配色，强调视觉冲击和专业性。
    页面序号：3/10，内容焦点：章节切换。
    遵循设计规范约束，包含占位符，兼容现代浏览器。
    本模板严格遵循“统一蓝色系配色”和“商务风格”要求。对于增强美观度中提及的“纯黑色底配合特斯拉红色”，与主要配色方案和风格存在冲突，故未采用。
    “Bento Grid”风格通过结构化、分层的布局和模块化区块设计来体现，高亮色为蓝色系中的强调色。
  </desc>

  <defs>
    <!-- Color Scheme Variables -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF; /* Dark Blue */
        --secondary-color: #475569; /* Slate Gray */
        --accent-color: #3B82F6; /* Bright Blue */
        --background-color: #F8FAFC; /* Off-White */
        --text-primary: #1E293B; /* Dark Gray */
        --text-secondary: #64748B; /* Medium Gray */
        --text-light: #94A3B8; /* Light Gray */
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --hover-color: #7DD3FC;
        --active-color: #1E40AF;
        --disabled-color: #64748B;
      }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }
      .font-medium { font-weight: 500; }

      /* Text Colors */
      .text-primary-color { fill: var(--text-primary); }
      .text-secondary-color { fill: var(--text-secondary); }
      .text-accent-color { fill: var(--accent-color); }
      .text-light-color { fill: var(--text-light); }

      /* Shadows */
      .shadow-md {
        filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06));
      }
      .shadow-lg {
        filter: drop-shadow(0px 10px 15px rgba(0, 0, 0, 0.1)) drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.05));
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--primary-color)"/>
      <stop offset="100%" stop-color="var(--secondary-color)"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--accent-color)"/>
      <stop offset="100%" stop-color="var(--primary-color)"/>
    </linearGradient>

    <!-- Highlighting gradient for tech feel (accent color with opacity) -->
    <linearGradient id="techAccentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--accent-color)" stop-opacity="0"/>
      <stop offset="50%" stop-color="var(--accent-color)" stop-opacity="0.6"/>
      <stop offset="100%" stop-color="var(--accent-color)" stop-opacity="0"/>
    </linearGradient>

    <!-- Faded blue gradient for background elements -->
    <linearGradient id="fadedBlueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--container-background)" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="var(--container-background)" stop-opacity="0.8"/>
    </linearGradient>

    <!-- Filter for subtle blur/glow -->
    <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur"/>
      <feFlood flood-color="var(--accent-color)" flood-opacity="0.7" result="flood"/>
      <feComposite in="flood" in2="blur" operator="in" result="glow"/>
      <feBlend in="SourceGraphic" in2="glow" mode="screen"/>
    </filter>

    <!-- Icon for data/analysis (simple outline style) -->
    <symbol id="icon-data-analysis" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
      <path d="M13 17h-2v-6h2v6zm0-8h-2V7h2v2z" fill="currentColor"/>
    </symbol>
    
    <!-- Icon for progress/growth (simple outline style) -->
    <symbol id="icon-growth" viewBox="0 0 24 24">
      <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.3L22 12V6z" fill="currentColor"/>
    </symbol>
    
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-color)"/>

  <!-- Decorative Elements - Bento Grid inspired structure and tech feel -->
  <!-- Top Left Corner Accent (faded blue blocks) -->
  <rect x="0" y="0" width="400" height="200" fill="url(#fadedBlueGradient)" rx="20" ry="20" opacity="0.6"/>
  <rect x="0" y="0" width="200" height="400" fill="url(#fadedBlueGradient)" rx="20" ry="20" opacity="0.4"/>

  <!-- Dynamic Wave/Data Flow Line (subtle, blue shades) -->
  <path d="M0 800 C 300 700, 600 900, 960 800 S 1600 700, 1920 800"
        stroke="var(--accent-color)" stroke-width="2" fill="none" opacity="0.3"/>
  <path d="M0 850 C 350 950, 700 750, 1000 850 S 1650 950, 1920 850"
        stroke="var(--primary-color)" stroke-width="1" fill="none" opacity="0.2"/>

  <!-- Large circle accents with glow effect -->
  <circle cx="1700" cy="200" r="150" fill="var(--accent-color)" opacity="0.1" filter="url(#glowEffect)"/>
  <circle cx="250" cy="900" r="100" fill="var(--primary-color)" opacity="0.08" filter="url(#glowEffect)"/>

  <!-- Geometric shapes for structure (Bento Grid elements) -->
  <rect x="1500" y="0" width="420" height="300" fill="var(--container-background)" opacity="0.5" rx="15" ry="15"/>
  <rect x="1600" y="50" width="250" height="150" fill="var(--accent-color)" opacity="0.2" rx="10" ry="10"/>

  <!-- Main Content Area - positioned with margins -->
  <g class="font-primary">
    <!-- Page Number (Top Right, respecting margins) -->
    <text x="1840" y="100" text-anchor="end" class="small-text font-semibold text-secondary-color">
      3/10
    </text>

    <!-- Logo Placeholder (Top Left, respecting margins) -->
    <g transform="translate(80, 60)">
        <rect x="0" y="0" width="160" height="40" fill="none" stroke="var(--text-light)" stroke-dasharray="5 5" rx="5" ry="5"/>
        <text x="80" y="27" text-anchor="middle" class="small-text text-light-color font-semibold">
            {logo_url}
        </text>
    </g>

    <!-- Chapter Title Group - centered vertically for visual impact -->
    <g transform="translate(80, 450)">
      <!-- Large Chapter Number -->
      <text x="0" y="0" class="hero-title text-accent-color font-bold">
        03
      </text>

      <!-- Chapter Title (Chinese, bold, large) -->
      <text x="120" y="0" class="main-title text-primary-color font-bold">
        <tspan>{title}</tspan>
      </text>

      <!-- Chapter Subtitle/Description (English, smaller, as embellishment) -->
      <text x="120" y="60" class="content-title text-secondary-color font-medium">
        <tspan>{subtitle}</tspan>
      </text>
    </g>

    <!-- Decorative Elements around Title (suggesting a content card/section) -->
    <g transform="translate(180, 500)">
      <rect x="-100" y="-100" width="1000" height="200" fill="var(--card-background)" rx="20" ry="20" class="shadow-md" opacity="0.8"/>
      <rect x="-90" y="-90" width="980" height="180" fill="url(#techAccentGradient)" opacity="0.1"/>
      
      <!-- Abstract data lines (simple outline graphics) -->
      <line x1="0" y1="100" x2="900" y2="100" stroke="var(--accent-color)" stroke-width="1" stroke-dasharray="10 5" opacity="0.4"/>
      <line x1="0" y1="120" x2="900" y2="120" stroke="var(--secondary-color)" stroke-width="0.5" opacity="0.2"/>
    </g>

    <!-- Bottom right decorative element (abstract data/progress visualization Bento Grid style) -->
    <g transform="translate(1400, 750)">
      <rect x="0" y="0" width="400" height="250" fill="var(--container-background)" rx="20" ry="20" opacity="0.7" class="shadow-md"/>
      <use xlink:href="#icon-growth" x="30" y="30" width="60" height="60" fill="var(--accent-color)" opacity="0.7"/>
      <use xlink:href="#icon-data-analysis" x="120" y="30" width="60" height="60" fill="var(--primary-color)" opacity="0.6"/>
      <text x="30" y="120" class="content-title text-primary-color font-bold">
        <tspan>数据洞察</tspan>
      </text>
      <text x="30" y="155" class="small-text text-secondary-color">
        <tspan>Data Insights 和#38; Progress</tspan>
      </text>
      <!-- Simple bar chart like elements (mimicking data visualization) -->
      <rect x="30" y="180" width="60" height="50" fill="var(--accent-color)" opacity="0.8" rx="5" ry="5"/>
      <rect x="100" y="190" width="60" height="40" fill="var(--primary-color)" opacity="0.7" rx="5" ry="5"/>
      <rect x="170" y="200" width="60" height="30" fill="var(--secondary-color)" opacity="0.6" rx="5" ry="5"/>
    </g>

  </g>
</svg>