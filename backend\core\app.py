"""
应用主模块，负责应用的生命周期管理和初始化
"""
import os
import sys
import json
import logging
import threading
import asyncio
import time
import psutil
import signal
from pathlib import Path
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor

import webview

# 首先导入存储管理器和日志管理器
from backend.core.storage_manager import storage_manager
from backend.core.log_manager import log_manager

# 从core模块导入
from backend.core.utils import terminate_child_processes

# 从API模块导入
from backend.api.server import start_api_server
from backend.api.js_api import API
from backend.api.websocket import manager

# 从AI模块导入
from backend.ai.manager import ai_manager

# 从MCP模块导入
from backend.mcp.server_manager import mcp_server

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取应用根目录
ROOT_DIR = Path(__file__).parent.parent.parent

# 创建全局API服务器变量
api_server = None

async def get_models_update():
    """获取模型更新"""
    is_update = True
    models = await ai_manager.get_models(is_update)
    return {"models": models}

def inject_client_environment(window):
    """在页面加载完成后注入客户端环境变量和桥接API"""
    try:
        # 注入TKINTER_CLIENT全局变量
        window.evaluate_js("""
            // 设置Tkinter客户端环境标志
            window.TKINTER_CLIENT = true;
            
            // 创建客户端桥接API
            window.tkinterBridge = {
                saveImage: function(imageData, filename) {
                    console.log('Tkinter客户端保存图片请求:', { 
                        数据类型: typeof imageData, 
                        文件名: filename 
                    });
                    
                    try {
                        // 调用Python API的文件保存功能
                        return window.pywebview.api.save_image_to_file(imageData, filename);
                    } catch (error) {
                        console.error('保存图片失败:', error);
                        throw error;
                    }
                }
            };
            
            // 备用桥接方式
            window.tkinter = window.tkinterBridge;
            
            console.log('Tkinter客户端环境已成功注入');
            console.log('可用的桥接方法:', Object.keys(window.tkinterBridge));
        """)
        
        logger.info("客户端环境变量和桥接API已成功注入")
    except Exception as e:
        logger.error(f"注入客户端环境时出错: {e}")

def init_app_storage():
    """初始化应用存储"""
    # 存储管理器在导入时已经初始化，这里记录日志
    logging.info(f"应用数据目录: {storage_manager.app_data_dir}")
    logging.info(f"会话保存目录: {storage_manager.conversations_dir}")
    logging.info(f"配置保存目录: {storage_manager.config_dir}")
    logging.info(f"日志保存目录: {storage_manager.logs_dir}")

def main():
    """主函数"""
    # 初始化应用存储
    init_app_storage()
    
    logger.info("启动AI Chat Desktop应用")
    
    # 确保事件循环已启动（这一步很快，保留在主线程）
    logger.info("初始化MCP服务器事件循环...")
    #mcp_server._ensure_loop_running()
    
    # 创建 MCP 服务启动线程
    def start_mcp_services():
        try:
            logger.info("后台启动MCP服务器...")
            
            # 在 macOS 上，等待一段时间确保所有初始化完成
            if sys.platform == "darwin":
                logger.info("macOS 系统，等待初始化完成...")
                time.sleep(3.0)  # 等待2秒让系统完全初始化
            
            try:
                # 启动MCP服务器管理器
                success = mcp_server.start_servers_sync(timeout=60.0)
                if success:
                    logger.info("MCP服务器启动成功")
                else:
                    logger.warning("部分MCP服务器启动失败，应用将继续运行")
            except Exception as e:
                logger.error(f"启动MCP服务器时发生错误: {e}")
                logger.warning("MCP服务器启动失败，应用将继续运行")
        except Exception as e:
            logger.error(f"MCP服务器启动线程出错: {str(e)}")
    
    # 启动 MCP 服务线程
    mcp_thread = threading.Thread(
        target=start_mcp_services, 
        daemon=True,
        name="MCPServicesStartupThread"
    )
    mcp_thread.start()
    
    # 然后立即启动API服务器，不等待MCP完成
    logger.info("启动API服务器...")
    thread = threading.Thread(target=start_api_server, daemon=True).start()
    asyncio.run(get_models_update())
    
    # 等待API服务器完全启动
    logger.info("等待API服务器完全启动...")
    time.sleep(2)
    
    # 从配置文件获取窗口配置
    from backend.core.config import config
    window_config = config.get("app.window_size", {})
    width = window_config.get("width", 1200)
    height = window_config.get("height", 800)
    min_width = window_config.get("min_width", 800)
    min_height = window_config.get("min_height", 600)
    title = config.get("app.window_title", "AI Chat Desktop")
    
    # 创建窗口
    window = webview.create_window(
        title=title,
        url="http://127.0.0.1:8080",
        width=width,
        height=height,
        min_size=(min_width, min_height),
        js_api=API(),
        frameless=False,  # 有边框
        easy_drag=True,   # 允许拖动
        resizable=True,   # 可调整大小
        fullscreen=False, # 非全屏
        on_top=False,     # 不置顶
        confirm_close=True, # 确保这个选项为 True
        text_select=True,  # 允许文本选择
        transparent=False  # 不透明
    )
    
    # 注册页面加载完成事件，注入客户端环境变量
    window.events.loaded += lambda: inject_client_environment(window)
    
    chinese = {
    'global.quitConfirmation': u'确定要关闭应用吗？',
    }
    # 在应用退出前确保服务器正确关闭
    try:
        # 启动主循环
        debug_mode = os.environ.get('JimuDebug', '').lower() == 'true'
        webview.start(localization=chinese,gui='edgechromium', debug=debug_mode)
        # 修复macOS WebKit认证问题
        try:
            webview.start(localization=chinese, debug=debug_mode)
        except Exception as e:
            logger.error(f"WebView启动异常: {e}")
            # 尝试不使用本地化启动
            webview.start(debug=debug_mode)
    finally:
        # 在应用关闭时关闭服务器
        logger.info("应用正在关闭，停止服务器...")
        
        # 定义一个终止超时函数，在一定时间后强制退出
        def exit_after_timeout():
            time.sleep(5)  # 给常规关闭流程5秒时间
            logger.warning("关闭超时，强制终止进程")
            os._exit(1)
            
        # 启动终止超时线程
        exit_thread = threading.Thread(target=exit_after_timeout, daemon=True)
        exit_thread.start()
        
        try:
            # 0. 关闭所有WebSocket连接
            try:
                # 创建一个临时事件循环用于异步关闭WebSocket连接
                close_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(close_loop)
                close_loop.run_until_complete(manager.close_all())
                close_loop.close()
            except Exception as e:
                logger.error(f"关闭WebSocket连接时出错: {e}")
            
            # 1. 首先关闭MCP服务器
            try:
                mcp_server.shutdown()
                logger.info("MCP服务器管理器已关闭")
            except Exception as e:
                logger.error(f"关闭MCP服务器时出错: {e}")
            
            # 2. 然后关闭API服务器
            try:
                if api_server:
                    api_server.stop()
            except Exception as e:
                logger.error(f"关闭API服务器时出错: {e}")
                
            # 3. 终止所有可能剩余的子进程
            try:
                parent = psutil.Process(os.getpid())
                children = parent.children(recursive=True)
                if children:
                    logger.warning(f"检测到 {len(children)} 个剩余子进程，尝试终止")
                    for child in children:
                        try:
                            child.terminate()
                        except:
                            pass
                    
                    # 等待子进程终止
                    gone, alive = psutil.wait_procs(children, timeout=1)
                    
                    # 如果仍有进程存活，强制杀死
                    for child in alive:
                        try:
                            child.kill()
                        except:
                            pass
            except Exception as e:
                logger.error(f"终止剩余子进程时出错: {e}")
                
            logger.info("所有服务器已关闭，应用退出")
            
            # 4. 等待一段时间确保日志被输出
            time.sleep(0.5)
        except Exception as e:
            logger.error(f"应用退出过程中发生异常: {e}")
        
        # 5. 强制退出，确保没有遗留进程
        os._exit(0) 