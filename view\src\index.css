/* 引入现代字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI Variable', 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  /* 禁用默认右键菜单 */
  -webkit-context-menu: none;
  -moz-context-menu: none;
  context-menu: none;
}

code {
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 
    'Cascadia Code', 'Roboto Mono', source-code-pro, Menlo, Consolas, 
    'Courier New', monospace;
  font-feature-settings: 'liga' 1, 'calt' 1;
}

/* 媒体查询，为移动设备设置样式 */
@media screen and (max-width: 576px) {
  .app {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    padding: 8px 0;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .sidebar-buttons {
    flex-direction: row;
  }
  
  .main-content {
    padding: 16px;
    background-color: #ffffff;
  }
  
  .greeting, .question {
    font-size: 1.75rem;
  }
}