#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 智能体记忆生命周期演示

展示智能体如何自动加载和保存记忆
"""

import asyncio
import json
from datetime import datetime


async def simulate_agent_memory_lifecycle():
    """模拟智能体记忆的完整生命周期"""
    
    print("🔄 智能体记忆生命周期演示")
    print("=" * 50)
    
    try:
        from backend.langgraph_enhancement.agents.context_optimized_agent import ContextOptimizedAgent
        
        # 创建智能体
        config = {
            "role_name": "演示智能体",
            "enable_memory_access": True
        }
        
        agent = ContextOptimizedAgent(
            agent_id="demo_agent",
            config=config,
            system_prompt="你是一个演示智能体，用于展示记忆功能。"
        )
        
        print(f"✅ 创建智能体: {agent}")
        
        # 🧠 第一步：初始化一些记忆
        print("\n📝 步骤1: 初始化记忆")
        
        await agent.store_my_memory("user_preferences", {
            "language": "中文",
            "detail_level": "详细",
            "response_style": "友好"
        })
        
        await agent.store_my_memory("domain_knowledge", {
            "AI": "有基础了解",
            "编程": "熟练",
            "市场分析": "初学者"
        })
        
        print("✅ 初始记忆已设置")
        
        # 📊 查看初始记忆状态
        initial_stats = await agent.get_my_memory_stats()
        print(f"📊 初始记忆统计: {initial_stats['total_memories']} 项记忆")
        
        # 🚀 第二步：第一次交互（展示自动加载和保存）
        print("\n🚀 步骤2: 第一次交互 - 自动记忆处理")
        
        state1 = {
            "messages": [
                {"role": "user", "content": "请帮我分析一下AI市场的发展趋势"}
            ]
        }
        
        print("🔍 处理前 - 检查记忆状态...")
        before_stats = await agent.get_my_memory_stats()
        print(f"   记忆数量: {before_stats['total_memories']}")
        
        # 使用记忆增强处理
        print("⚡ 执行记忆增强处理...")
        result1 = await agent.process_with_memory_enhancement(state1)
        
        print("💾 处理后 - 检查记忆更新...")
        after_stats = await agent.get_my_memory_stats()
        print(f"   记忆数量: {after_stats['total_memories']}")
        
        # 检查自动保存的记忆
        interaction_history = await agent.get_my_memory("interaction_history", [])
        last_context = await agent.get_my_memory("last_context", {})
        processing_stats = await agent.get_my_memory("processing_stats", {})
        
        print(f"✅ 自动保存记忆:")
        print(f"   - 交互历史: {len(interaction_history)} 条记录")
        print(f"   - 最后上下文: {'已保存' if last_context else '未保存'}")
        print(f"   - 处理统计: 总计 {processing_stats.get('total_processed', 0)} 次处理")
        
        # 🔄 第三步：第二次交互（展示记忆累积）
        print("\n🔄 步骤3: 第二次交互 - 记忆累积效果")
        
        state2 = {
            "messages": [
                {"role": "user", "content": "基于刚才的分析，你觉得哪些AI技术最有前景？"}
            ]
        }
        
        print("🧠 第二次处理（会加载之前的记忆）...")
        result2 = await agent.process_with_memory_enhancement(state2)
        
        # 检查记忆累积
        updated_history = await agent.get_my_memory("interaction_history", [])
        updated_stats = await agent.get_my_memory("processing_stats", {})
        
        print(f"📈 记忆累积效果:")
        print(f"   - 交互历史增长: {len(interaction_history)} → {len(updated_history)} 条")
        print(f"   - 处理次数增长: {processing_stats.get('total_processed', 0)} → {updated_stats.get('total_processed', 0)} 次")
        
        # 🔍 第四步：展示记忆搜索和应用
        print("\n🔍 步骤4: 记忆搜索和应用")
        
        # 搜索相关记忆
        ai_related = await agent.search_my_memories("AI")
        market_related = await agent.search_my_memories("市场")
        
        print(f"🔍 记忆搜索结果:")
        print(f"   - 'AI' 相关记忆: {len(ai_related)} 项")
        print(f"   - '市场' 相关记忆: {len(market_related)} 项")
        
        # 🎯 第五步：手动记忆操作
        print("\n🎯 步骤5: 手动记忆操作")
        
        # 手动保存重要信息
        await agent.store_my_memory("research_insights", {
            "topic": "AI市场趋势",
            "key_findings": ["技术发展快速", "应用场景扩大", "竞争激烈"],
            "timestamp": datetime.now().isoformat(),
            "confidence": 0.8
        })
        
        print("💡 手动保存研究洞察")
        
        # 获取并显示重要记忆
        insights = await agent.get_my_memory("research_insights")
        preferences = await agent.get_my_memory("user_preferences")
        
        print(f"📋 重要记忆内容:")
        print(f"   - 研究洞察: {insights['topic'] if insights else '无'}")
        print(f"   - 用户偏好: {preferences.get('language', '未设置') if preferences else '无'}")
        
        # 📊 最终统计
        print("\n📊 最终记忆统计")
        final_stats = await agent.get_my_memory_stats()
        memory_keys = await agent.list_my_memories()
        
        print(f"🧠 最终记忆状态:")
        print(f"   - 总记忆数: {final_stats['total_memories']}")
        print(f"   - 记忆键列表: {memory_keys}")
        print(f"   - 总访问次数: {final_stats.get('total_access_count', 0)}")
        
        if final_stats.get('most_accessed'):
            most_accessed_key, access_count = final_stats['most_accessed']
            print(f"   - 最常访问: {most_accessed_key} ({access_count} 次)")
        
        print("\n🎉 记忆生命周期演示完成！")
        
        # 💡 总结说明
        print("\n💡 记忆自动保存机制总结:")
        print("✅ 每次使用 process_with_memory_enhancement() 时：")
        print("   1. 自动加载相关记忆（用户偏好、历史交互、相关知识）")
        print("   2. 将记忆信息添加到AI提示中")
        print("   3. 执行AI处理")
        print("   4. 自动保存新的交互记录和统计信息")
        print("\n✅ 智能体可以随时手动操作记忆：")
        print("   - get_my_memory() - 获取记忆")  
        print("   - store_my_memory() - 保存记忆")
        print("   - search_my_memories() - 搜索记忆")
        print("   - clear_my_memory() - 清除记忆")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def quick_memory_demo():
    """快速记忆演示"""
    
    print("\n🚀 快速记忆功能演示")
    print("-" * 30)
    
    try:
        from backend.langgraph_enhancement.agents.context_optimized_agent import ContextOptimizedAgent
        
        agent = ContextOptimizedAgent(
            "quick_demo",
            {"enable_memory_access": True},
            "演示智能体"
        )
        
        # 快速演示所有记忆操作
        print("1. 💾 存储记忆")
        await agent.store_my_memory("demo_key", "演示值")
        
        print("2. 📖 读取记忆")
        value = await agent.get_my_memory("demo_key")
        print(f"   读取到: {value}")
        
        print("3. 🔍 搜索记忆")
        results = await agent.search_my_memories("演示")
        print(f"   搜索结果: {len(results)} 项")
        
        print("4. 📊 记忆统计")
        stats = await agent.get_my_memory_stats()
        print(f"   记忆总数: {stats['total_memories']}")
        
        print("5. 📋 记忆列表")
        keys = await agent.list_my_memories()
        print(f"   记忆键: {keys}")
        
        print("✅ 快速演示完成！")
        
    except Exception as e:
        print(f"❌ 快速演示失败: {e}")


if __name__ == "__main__":
    print("🧠 智能体记忆系统演示")
    print("=" * 60)
    
    # 运行完整演示
    success = asyncio.run(simulate_agent_memory_lifecycle())
    
    # 运行快速演示
    asyncio.run(quick_memory_demo())
    
    if success:
        print("\n🎯 总结：智能体记忆系统完全可用！")
        print("💡 每个智能体都能主动访问记忆，并在处理完成后自动保存")
    else:
        print("\n❌ 演示中发现问题，请检查配置")