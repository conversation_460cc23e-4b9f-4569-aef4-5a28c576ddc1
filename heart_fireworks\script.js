// Get the canvas and context
const canvas = document.getElementById('fireworksCanvas');
const ctx = canvas.getContext('2d');

// Set canvas size to full window
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

// Handle window resize
window.addEventListener('resize', () => {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
});

// --- Particle Class ---
class Particle {
    constructor(x, y, vx, vy, color, lifespan, fade = true) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.color = color;
        this.alpha = 1;
        this.lifespan = lifespan; // Max frames to live
        this.currentLife = 0;
        this.gravity = 0.03; // Slight gravity
        this.fade = fade;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity; // Apply gravity

        if (this.fade) {
            this.currentLife++;
            this.alpha = 1 - (this.currentLife / this.lifespan);
        }

        return this.alpha > 0.05; // Return true if still alive, false if faded out
    }

    draw() {
        ctx.save();
        ctx.globalAlpha = Math.max(0, this.alpha);
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, 1.5, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// --- Heart Shape Generator ---
function getHeartPoints(cx, cy, size, numPoints) {
    const points = [];
    for (let i = 0; i < numPoints; i++) {
        const t = (i / numPoints) * (2 * Math.PI);
        const x = cx + size * (16 * Math.sin(t) ** 3);
        const y = cy - size * (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t));
        points.push({ x, y });
    }
    return points;
}

// --- Firework Class ---
class Firework {
    constructor(startX, startY, endX, endY) {
        this.x = startX;
        this.y = startY;
        this.endX = endX;
        this.endY = endY;
        this.color = `hsl(${Math.random() * 360}, 100%, 50%)`;
        this.particles = [];
        this.exploded = false;
        this.speed = 10; // Speed of the initial rocket
        this.distance = Math.hypot(endX - startX, endY - startY);
        this.angle = Math.atan2(endY - startY, endX - startX);
        this.vx = Math.cos(this.angle) * this.speed;
        this.vy = Math.sin(this.angle) * this.speed;
    }

    update() {
        if (!this.exploded) {
            this.x += this.vx;
            this.y += this.vy;

            // Check if reached destination or passed it
            if (Math.hypot(this.x - this.endX, this.y - this.endY) < this.speed || (this.vy > 0 && this.y >= this.endY) || (this.vy < 0 && this.y <= this.endY) || (this.vx > 0 && this.x >= this.endX) || (this.vx < 0 && this.x <= this.endX)) {
                this.exploded = true;
                this.explode();
            }
        } else {
            this.particles = this.particles.filter(p => p.update());
        }
        return this.exploded && this.particles.length === 0; // Return true if firework and its particles are done
    }

    draw() {
        if (!this.exploded) {
            // Draw the rocket trail (optional, but nice)
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(this.x, this.y, 2, 0, Math.PI * 2);
            ctx.fill();
        } else {
            this.particles.forEach(p => p.draw());
        }
    }

    explode() {
        const heartPoints = getHeartPoints(this.x, this.y, 10, 100); // 10 is scale, 100 is number of points
        const color = this.color;

        heartPoints.forEach(point => {
            // Calculate velocity to make particles move from explosion center to heart point
            const angle = Math.atan2(point.y - this.y, point.x - this.x);
            const distance = Math.hypot(point.x - this.x, point.y - this.y);
            const speed = distance * 0.05; // Adjust speed based on distance from center

            const vx = Math.cos(angle) * speed + (Math.random() - 0.5) * 0.5; // Add some randomness
            const vy = Math.sin(angle) * speed + (Math.random() - 0.5) * 0.5;

            this.particles.push(new Particle(this.x, this.y, vx, vy, color, 120)); // Lifespan 120 frames
        });
    }
}

// --- Animation Loop ---
const fireworks = [];
let lastFireworkTime = 0;
const fireworkInterval = 500; // milliseconds

function animate(currentTime) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'; // Clear with a slight fade effect
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    if (currentTime - lastFireworkTime > fireworkInterval) {
        // Launch new firework from bottom center to a random top position
        const startX = canvas.width / 2;
        const startY = canvas.height + 10;
        const endX = Math.random() * (canvas.width * 0.8) + (canvas.width * 0.1); // Random x within 10%-90% width
        const endY = Math.random() * (canvas.height * 0.5); // Random y within top 50% height

        fireworks.push(new Firework(startX, startY, endX, endY));
        lastFireworkTime = currentTime;
    }

    // Update and draw fireworks, filter out dead ones
    fireworks.forEach((firework, index) => {
        if (firework.update()) {
            fireworks.splice(index, 1); // Remove if done
        }
        firework.draw();
    });

    requestAnimationFrame(animate);
}

// Start the animation
animate(0);
