<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- Placeholder for logo image -->
    <!-- Note: For a real image, replace {logo_url} with the actual URL. -->
    <image id="logoPlaceholder" width="160" height="40" xlink:href="{logo_url}" />
  </defs>

  <style>
    /* Base Styles - Defined using CSS Variables for easy modification */
    :root {
      --primary-color: #1E40AF;
      --secondary-color: #475569;
      --accent-color: #3B82F6;
      --background-color: #F8FAFC;
      --text-primary: #1E293B;
      --text-secondary: #64748B;
      --text-light: #94A3B8;
      --card-background: #FFFFFF;
      --card-border: #BAE6FD;
      --container-background: #E0F2FE; /* Lighter blue for subtle background elements */
      --hover-color: #7DD3FC;
    }

    /* Font System */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    .hero-title { font-size: 72px; font-weight: 700; fill: var(--text-primary); line-height: 1.1; letter-spacing: -0.025em; }
    .main-title { font-size: 56px; font-weight: 700; fill: var(--text-primary); line-height: 1.1; letter-spacing: -0.025em; }
    .section-title { font-size: 36px; font-weight: 600; fill: var(--text-primary); line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 500; fill: var(--text-primary); line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; fill: var(--text-secondary); line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; fill: var(--text-secondary); line-height: 1.6; }
    .caption { font-size: 14px; font-weight: 400; fill: var(--text-light); line-height: 1.6; }

    /* Layout & Spacing - For reference, actual positioning uses fixed coordinates */
    /* page_margins: {horizontal: 80, vertical: 60} */

    /* Visual Elements */
    .card {
      fill: var(--card-background);
      stroke: var(--card-border);
      stroke-width: 1px;
      /* Shadow effect */
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.06));
    }

    .icon-style {
      stroke: var(--accent-color);
      stroke-width: 2px;
      fill: none;
    }

    /* Decorative elements */
    .decor-fill-primary { fill: var(--primary-color); }
    .decor-fill-accent { fill: var(--accent-color); }
    .decor-stroke-primary { stroke: var(--primary-color); stroke-width: 2px; fill: none; }
    .decor-stroke-accent { stroke: var(--accent-color); stroke-width: 2px; fill: none; }
    .decor-stroke-secondary { stroke: var(--secondary-color); stroke-width: 1px; fill: none; }
    .decor-light-fill { fill: var(--container-background); }

    /* Specific for this template */
    .title-shadow {
      filter: drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2));
    }
  </style>

  <!-- Background Layer: Subtle gradient from light background to slightly lighter blue -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative Elements: Bento Grid inspired, abstract shapes with transparency -->
  <g id="background-decorations">
    <!-- Large, subtle rectangle on the left/top -->
    <rect x="0" y="0" width="800" height="600" rx="20" ry="20" fill="var(--container-background)" opacity="0.3"/>
    <!-- Another subtle rectangle, overlapping -->
    <rect x="700" y="200" width="1000" height="700" rx="20" ry="20" fill="var(--container-background)" opacity="0.1"/>

    <!-- Abstract corner shapes using primary and accent colors with low opacity -->
    <path d="M120 0 L0 120 V0 L120 0 Z" fill="var(--accent-color)" opacity="0.15"/>
    <path d="M1920 960 L1800 1080 H1920 V960 Z" fill="var(--primary-color)" opacity="0.15"/>
    
    <!-- Gradient bars for dynamic feel -->
    <rect x="1500" y="0" width="400" height="100" rx="10" fill="url(#accentGradient)" opacity="0.15"/>
    <rect x="0" y="980" width="400" height="100" rx="10" fill="url(#primaryGradient)" opacity="0.15"/>

    <!-- Circles on the right side for visual interest -->
    <circle cx="1800" cy="540" r="150" fill="var(--accent-color)" opacity="0.05"/>
    <circle cx="1700" cy="400" r="80" fill="var(--primary-color)" opacity="0.08"/>
    <circle cx="1850" cy="700" r="100" fill="var(--secondary-color)" opacity="0.06"/>

    <!-- Abstract "data wave" or "connection" lines -->
    <path d="M1500 200 C1600 150, 1700 250, 1800 200" stroke="var(--accent-color)" stroke-width="2" fill="none" opacity="0.3" stroke-dasharray="10 5"/>
    <path d="M1500 800 C1600 850, 1700 750, 1800 800" stroke="var(--primary-color)" stroke-width="2" fill="none" opacity="0.3" stroke-dasharray="10 5"/>

    <!-- A more prominent accent element on top right -->
    <rect x="1750" y="0" width="170" height="30" fill="url(#accentGradient)" rx="15" ry="15" opacity="0.8"/>
  </g>

  <!-- Main Content Area -->
  <g id="main-content-area">
    <!-- Logo Section (Top Left, prominent) -->
    <g id="logo-section">
      <!-- Background card for the logo -->
      <rect x="80" y="60" width="180" height="60" fill="var(--card-background)" rx="8" ry="8" class="card"/>
      <!-- Actual logo image placeholder -->
      <use xlink:href="#logoPlaceholder" x="90" y="70" width="160" height="40" />
      <!-- Small text next to logo for context/tagline -->
      <text x="280" y="95" class="caption font-primary" fill="var(--text-light)">
        <tspan>业务汇报</tspan>
      </text>
    </g>

    <!-- Title and Subtitle Section -->
    <g id="title-section" transform="translate(80, 250)">
      <!-- Main Title: Large, bold, primary text color, with subtle shadow -->
      <text x="0" y="0" class="hero-title font-primary title-shadow" fill="var(--text-primary)">
        <tspan>{title}</tspan>
      </text>
      <!-- Subtitle: Slightly smaller, secondary text color -->
      <text x="0" y="80" class="section-title font-primary" fill="var(--text-secondary)">
        <tspan>{subtitle}</tspan>
      </text>
      <!-- English descriptor: Smaller, light text, for international audience/professional touch -->
      <text x="0" y="130" class="body-text font-secondary" fill="var(--text-light)">
        <tspan>Mid-Year Performance Review</tspan>
      </text>
    </g>

    <!-- Decorative line under title using accent gradient -->
    <rect x="80" y="450" width="500" height="4" rx="2" fill="url(#accentGradient)"/>

    <!-- Placeholder for key metrics/highlights (Bento Grid inspired cards) -->
    <g id="key-highlights" transform="translate(80, 530)">
      <!-- Card 1: Key Achievement -->
      <rect x="0" y="0" width="500" height="200" rx="12" ry="12" class="card"/>
      <text x="30" y="60" class="content-title font-primary" fill="var(--accent-color)">
        <tspan>核心成果</tspan>
      </text>
      <text x="30" y="100" class="body-text font-secondary" fill="var(--text-primary)">
        <tspan>目标达成率：95%</tspan>
      </text>
      <text x="30" y="130" class="small-text font-secondary" fill="var(--text-secondary)">
        <tspan>Key Performance Indicator</tspan>
      </text>
      <!-- Icon for achievement (simple outline style) -->
      <g transform="translate(400, 75)">
        <path d="M20 0 L20 100 M-30 50 H70 M-5 25 L20 0 L45 25 M-5 75 L20 100 L45 75" class="icon-style" stroke-linecap="round" stroke-linejoin="round"/>
      </g>

      <!-- Card 2: Growth Data -->
      <rect x="540" y="0" width="450" height="200" rx="12" ry="12" class="card"/>
      <text x="570" y="60" class="content-title font-primary" fill="var(--accent-color)">
        <tspan>业务增长</tspan>
      </text>
      <text x="570" y="100" class="body-text font-secondary" fill="var(--text-primary)">
        <tspan>市场份额提升 15%</tspan>
      </text>
      <text x="570" y="130" class="small-text font-secondary" fill="var(--text-secondary)">
        <tspan>Market Share Growth</tspan>
      </text>
      <!-- Icon for growth (simple line chart style) -->
      <g transform="translate(830, 75)">
        <polyline points="0 65 20 25 40 45 60 5 80 35" class="icon-style"/>
        <circle cx="60" cy="5" r="5" fill="var(--accent-color)" stroke="none"/>
      </g>

      <!-- Card 3: Future Outlook (smaller, below the first card) -->
      <rect x="0" y="220" width="480" height="150" rx="12" ry="12" class="card"/>
      <text x="30" y="270" class="content-title font-primary" fill="var(--accent-color)">
        <tspan>未来展望</tspan>
      </text>
      <text x="30" y="300" class="small-text font-secondary" fill="var(--text-secondary)">
        <tspan>Strategic Planning</tspan>
      </text>
      <!-- Icon for future/vision -->
      <g transform="translate(390, 260)">
        <path d="M10 10 C-10 -10, -10 30, 10 10 M30 10 C10 -10, 10 30, 30 10" class="icon-style" stroke-linecap="round"/>
        <line x1="0" y1="10" x2="40" y2="10" class="icon-style" stroke-linecap="round"/>
      </g>
    </g>

    <!-- Page Number and Date/Author Footer -->
    <g id="footer-info">
      <!-- Page number (bottom left) -->
      <text x="80" y="1020" class="small-text font-primary" fill="var(--text-light)">
        <tspan>页面 {page_number}/10</tspan>
      </text>
      <!-- Date and Author (bottom right) -->
      <text x="1840" y="1020" text-anchor="end" class="small-text font-primary" fill="var(--text-light)">
        <tspan>{date} | {author}</tspan>
      </text>
    </g>

  </g>
</svg>