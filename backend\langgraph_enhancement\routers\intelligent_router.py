# -*- coding: utf-8 -*-
"""
🔥 智能路由器 - LangGraph 增强系统 (V2 - 重构版)

核心功能：
- 统一的确定性评估与返工流程
- 清晰的状态驱动路由决策 (新任务, 触发评估, 返工)
- 结合规则和语义理解
- 任务完成状态检测
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional

from ..utils import get_ai_manager_bridge
from ..core.state_definitions import EnhancedTeamState
from ..config.config_manager import get_config_manager
from .hybrid_relevance_evaluator import get_hybrid_relevance_evaluator

logger = logging.getLogger(__name__)


class IntelligentRouter:
    """
    🔥 智能路由器 V2
    
    功能：
    - 统一的评估与返工流程
    - 规则 + AI 混合路由决策
    - 智能体性能分析和记忆
    """
    
    def __init__(self, context_manager=None):
        """初始化智能路由器"""
        self.context_manager = context_manager
        ai_bridge = get_ai_manager_bridge()
        self.ai_manager = ai_bridge.ai_manager
        
        self.routing_stats = {
            "total_decisions": 0,
            "rule_based_decisions": 0,
            "ai_based_decisions": 0,
            "rework_decisions": 0,
            "quality_assurance_cycles": 0
        }
        
        logger.info("🔥 智能路由器(V2)初始化完成")
    
    async def _direct_evaluate_content(self, state: EnhancedTeamState) -> str:
        """
        🎯 使用确定性算法直接评估内容
        """
        try:
            messages = state.get("messages", [])
            
            if len(messages) < 2:
                logger.warning("⚠️ 消息历史不足，无法进行评估")
                return "quality_failed: 消息历史不足"
            
            user_request = self._extract_user_request(messages)
            agent_response = self._extract_agent_response(messages)
            
            if not user_request or not agent_response:
                logger.warning("⚠️ 未找到有效的用户请求或智能体回答")
                return "quality_failed: 未找到有效的用户请求或智能体回答"
            
            evaluator = get_hybrid_relevance_evaluator()
            evaluation_result = evaluator.evaluate(user_request, agent_response)
            
            logger.info(f"🎯 算法评估结果: {evaluation_result.result}")
            logger.info(f"📊 相关性得分: {evaluation_result.score:.3f}")
            logger.info(f"🔍 请求类型: {evaluation_result.request_type}")
            logger.debug(f"📋 评估详情: {evaluation_result.details}")
            
            if evaluation_result.result == "quality_approved":
                logger.info("✅ 算法评估通过")
                return "quality_approved"
            else:
                failure_reason = evaluation_result.details.get("reason", "相关性不足")
                logger.info(f"❌ 算法评估失败，原因: {failure_reason}")
                return f"quality_failed: {failure_reason}"
                
        except Exception as e:
            logger.error(f"❌ 算法评估异常: {e}", exc_info=True)
            return f"quality_failed: 评估器异常 - {e}"

    async def smart_route_decision(self, state: EnhancedTeamState, 
                                  routing_conditions: Dict, 
                                  available_agents: List[str]) -> str:
        """
        🔥 智能路由决策主方法 - V3修复版
        清晰地处理三种状态：
        1. 触发评估
        2. 处理返工
        3. 常规路由 (新任务)
        """
        self.routing_stats["total_decisions"] += 1
        messages = state.get("messages", [])
        last_executed_agent = state.get("last_executed_agent", None)

        # 🔥 状态 1: 智能体完成后的处理逻辑 - 区分工作流类型
        if last_executed_agent and last_executed_agent not in ["intelligent_router", "result_merger"]:
            # 🔥 修复：检查工作流类型，planning_router需要特殊处理
            team_config = state.get("_team_config", {})
            workflow_type = team_config.get("workflow_type", "conditional")
            
            if workflow_type == "planning_router":
                # 计划路由工作流：只有task_progress_tracker可以决定是否继续
                if last_executed_agent == "task_progress_tracker":
                    # task_progress_tracker已经设置了has_pending_tasks状态，继续路由逻辑
                    logger.info(f"📋 计划路由工作流: task_progress_tracker 已完成，检查是否有待处理任务...")
                else:
                    # 其他智能体完成后，应该转到task_progress_tracker
                    logger.info(f"📋 计划路由工作流: {last_executed_agent} 已完成，需要转到任务进度跟踪器")
                    # 这里不直接返回，让后续逻辑处理
            else:
                # 其他工作流类型（conditional等）：智能体完成后直接结束
                logger.info(f"✅ Agent '{last_executed_agent}' 已完成执行，直接结束工作流。")
                return "result_merger"

        # 移除评估状态处理逻辑，不再需要评估环节

        # 状态 2: 常规路由，用于新任务或工作流的初始步骤
        logger.info("🚦 开始常规路由流程...")
        
        # 🔥 修复：为 planning_router 工作流添加特殊路由逻辑
        team_config = state.get("_team_config", {})
        workflow_type = team_config.get("workflow_type", "conditional")
        
        if workflow_type == "planning_router":
            # 计划路由工作流：基于任务分配进行路由
            next_task = state.get("next_task")
            if next_task and isinstance(next_task, dict):
                assigned_agent = next_task.get("assigned_agent")
                if assigned_agent and assigned_agent in available_agents:
                    logger.info(f"📋 计划路由: 将任务分配给 {assigned_agent}")
                    self.routing_stats["rule_based_decisions"] += 1
                    return assigned_agent
                else:
                    logger.warning(f"📋 计划路由: 分配的智能体 {assigned_agent} 不可用，使用兜底策略")
            
            # 检查是否有待处理任务
            has_pending_tasks = state.get("has_pending_tasks", False)
            if not has_pending_tasks:
                logger.info("📋 计划路由: 所有任务已完成，结束工作流")
                return "result_merger"
        
        # 3.1 尝试规则匹配（快速路径）
        rule_result = await self._evaluate_rule_based_routing(state, routing_conditions, available_agents)
        if rule_result and rule_result != "no_match":
            logger.info(f"🎯 规则匹配成功: {rule_result}")
            self.routing_stats["rule_based_decisions"] += 1
            return rule_result
        
        # 3.2 使用AI进行智能判断
        ai_result = await self._ai_route_decision(state, available_agents)
        if ai_result:
            logger.info(f"🤖 AI路由决策: {ai_result}")
            self.routing_stats["ai_based_decisions"] += 1
            return ai_result
        
        # 3.3 兜底策略
        user_request = self._extract_user_request(messages)
        return await self._fallback_route_decision(available_agents, user_request)

    async def _route_for_rework(self, state: EnhancedTeamState, available_agents: List[str], failure_message: str) -> str:
        """
        🔥 AI驱动的返工决策 - V2 优化版
        根据评估失败的意见和失败的上下文，智能选择最合适的智能体进行修正。
        """
        self.routing_stats["rework_decisions"] += 1
        
        try:
            # 提取所需信息
            failure_reason = failure_message.replace("quality_failed:", "").strip()
            user_request = self._extract_user_request(state.get("messages", []))
            failed_response = self._extract_agent_response(state.get("messages", [])) # 获取失败的回答
            last_agent = state.get("last_executed_agent", "Unknown")

            # 构建智能体选项描述
            agent_descriptions = self._get_agent_descriptions(available_agents)
            
            # AI返工决策提示
            rework_prompt = f"""
一个任务未能通过质量评估，需要返工。请分析上下文并选择最合适的专家来修正错误。

**原始用户请求:**
{user_request}

**上一个执行的专家:** {last_agent}

**该专家的失败回答:**
{failed_response}

**评估失败原因:**
{failure_reason}

**可用专家列表 (用于修正任务):**
{agent_descriptions}

**你的任务:**
1.  分析为何上一个专家的回答会失败。
2.  根据失败原因和用户请求，从列表中选择一个最能解决问题的专家。
3.  请只返回最适合的专家ID（例如：technical_writer），不要返回其他任何内容。
"""
            
            ai_messages = [
                {'role': 'system', 'content': 'You are an expert AI project manager. Analyze the quality feedback and the failed response, then assign the rework task to the most suitable agent. Return only the agent ID.'},
                {'role': 'user', 'content': rework_prompt}
            ]
            
            response = await self.ai_manager.chat_completion(
                messages=ai_messages,
                model='gemini-2.5-flash-preview-05-20',
                handle_tools=False
            )
            
            decision = response.get('content', '').strip()

            if decision in available_agents:
                logger.info(f"🤖 AI返工决策: 将任务分配给 {decision}")
                return decision
            else:
                logger.warning(f"AI返工决策返回无效的智能体ID '{decision}'，将使用兜底策略。")
                return await self._fallback_route_decision(available_agents, user_request)

        except Exception as e:
            logger.error(f"❌ AI返工决策失败: {e}", exc_info=True)
            user_request = self._extract_user_request(state.get("messages", []))
            return await self._fallback_route_decision(available_agents, user_request)

    async def _evaluate_rule_based_routing(self, state: EnhancedTeamState, 
                                          routing_conditions: Dict,
                                          available_agents: List[str]) -> str:
        """
        🔥 基于规则的路由评估
        """
        try:
            messages = state.get("messages", [])
            message_count = len(messages)
            latest_content = self._extract_user_request(messages)
            
            if not latest_content:
                return "no_match"

            for condition_name, condition_config in routing_conditions.items():
                condition_str = condition_config.get('condition', '')
                
                if self._evaluate_condition_safely(condition_str, message_count, latest_content):
                    target_agent = condition_config.get('agent') or (condition_config.get('agents', [])[0] if condition_config.get('agents') else None)
                    if target_agent and target_agent in available_agents:
                        return target_agent
            
            return "no_match"
            
        except Exception as e:
            logger.error(f"❌ 规则路由评估失败: {e}", exc_info=True)
            return "no_match"
    
    def _evaluate_condition_safely(self, condition: str, message_count: int, content: str) -> bool:
        """
        安全评估条件表达式
        """
        try:
            import re
            
            logger.debug(f"原始条件: {condition}")
            logger.debug(f"消息数量: {message_count}")
            logger.debug(f"内容摘要: {content[:100] if content else 'None'}...")
            
            # 清理条件字符串 - 去掉外层引号
            cleaned_condition = condition.strip().strip('"\'')
            logger.debug(f"清理后条件: {cleaned_condition}")
            
            # 处理 keyword_match 函数
            def evaluate_keyword_match(match):
                try:
                    keywords_str = match.group(1)
                    # 更好的关键词解析，处理中文引号
                    keywords_str = keywords_str.replace('"', '"').replace('"', '"').replace(''', "'").replace(''', "'")
                    keywords = [k.strip().strip("'\"") for k in keywords_str.split(',') if k.strip()]
                    # 返回布尔值而不是字符串
                    result = any(keyword.lower() in content.lower() for keyword in keywords if keyword.strip())
                    logger.debug(f"keyword_match 结果: {result}, 关键词: {keywords[:5]}...")  # 只显示前5个关键词
                    return "True" if result else "False"  # 直接返回 Python 布尔值字符串
                except Exception as e:
                    logger.error(f"keyword_match 评估错误: {e}")
                    return "False"
            
            # 更精确的正则表达式匹配 keyword_match 函数
            processed_condition = re.sub(r"keyword_match\(\s*\[(.*?)\]\s*\)", evaluate_keyword_match, cleaned_condition)
            
            # 转换逻辑运算符为Python语法
            processed_condition = processed_condition.replace(" OR ", " or ").replace(" AND ", " and ").replace(" NOT ", " not ")
            logger.debug(f"处理后条件: {processed_condition}")
            
            # 替换变量
            safe_globals = {
                "__builtins__": None,
                "message_count": message_count,
                "True": True,
                "False": False
            }
            # 使用eval进行安全的布尔表达式评估
            result = bool(eval(processed_condition, safe_globals))
            logger.debug(f"条件评估结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"条件评估失败: '{condition}', 错误: {e}")
            return False
    
    async def _ai_route_decision(self, state: EnhancedTeamState, 
                                available_agents: List[str]) -> Optional[str]:
        """
        🔥 AI驱动的路由决策
        """
        try:
            messages = state.get("messages", [])
            if not messages:
                return None
            
            recent_context = self._build_context_for_ai_routing(messages)
            agent_descriptions = self._get_agent_descriptions(available_agents)
            
            routing_prompt = f"""
基于以下对话上下文，请选择最适合处理当前任务的智能体：

=== 对话上下文 ===
{recent_context}

=== 可用智能体 ===
{agent_descriptions}

请分析用户的真实意图和需求，并从可用智能体中选择一个最合适的。
请只返回智能体ID（如：market_researcher），不要返回其他内容。
如果任务看起来已经完成或者不需要任何操作，请返回：COMPLETED
"""
            
            ai_messages = [
                {'role': 'system', 'content': 'You are an expert AI task router. Analyze user intent and select the most appropriate agent. Return only the agent ID or COMPLETED.'},
                {'role': 'user', 'content': routing_prompt}
            ]
            
            response = await self.ai_manager.chat_completion(
                messages=ai_messages,
                model='gemini-2.5-flash-preview-05-20',
                handle_tools=False
            )
            
            decision = response.get('content', '').strip()
            
            if decision == 'COMPLETED':
                return "result_merger"
            elif decision in available_agents:
                return decision
            else:
                logger.warning(f"AI返回了无效的智能体ID: {decision}")
                return None
                
        except Exception as e:
            logger.error(f"❌ AI路由决策失败: {e}", exc_info=True)
            return None
    
    def _extract_user_request(self, messages: List[Dict]) -> str:
        """提取用户的原始请求"""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                return msg.get("content", "")
        return ""
    
    def _extract_agent_response(self, messages: List[Dict]) -> str:
        """提取最近的智能体回答"""
        for msg in reversed(messages):
            # 修正：确保找到的是智能体的回答，而不是评估器的回答
            role = msg.get("role")
            agent = msg.get("agent", "Unknown")
            if role == "assistant" and agent != "evaluator_agent":
                return msg.get("content", "")
        # 如果只有评估器的回答，则返回空
        return ""
    
    def _build_context_for_ai_routing(self, messages: List[Dict]) -> str:
        """构建用于AI路由的上下文"""
        recent_messages = messages[-5:]
        context_parts = []
        for msg in recent_messages:
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            if len(content) > 300:
                content = content[:300] + "..."
            context_parts.append(f"[{role}]: {content}")
        return "\n".join(context_parts)
    
    def _get_agent_descriptions(self, available_agents: List[str]) -> str:
        """动态获取智能体描述"""
        try:
            config_manager = get_config_manager()
            descriptions = []
            for agent_id in available_agents:
                agent_config = config_manager.get_agent_config(agent_id)
                if agent_config:
                    role_name = agent_config.get('role_name', agent_id)
                    description = agent_config.get('description', '专业智能体')
                    descriptions.append(f"- {agent_id}: {role_name} - {description}")
                else:
                    descriptions.append(f"- {agent_id}: 专业智能体")
            return "\n".join(descriptions)
        except Exception as e:
            logger.error(f"❌ 获取智能体描述失败: {e}")
            return "\n".join([f"- {agent_id}: 专业智能体" for agent_id in available_agents])
    
    async def _fallback_route_decision(self, available_agents: List[str], user_request: str = "") -> str:
        """团队内兜底路由决策"""
        logger.warning("⚠️  规则匹配和AI路由都失败，启用团队内兜底策略")
        if not available_agents:
            logger.info("📋 当前团队无可用智能体，结束工作流")
            return "result_merger"
        
        # 在兜底策略中，我们使用AI根据用户原始请求选择最相关的智能体
        try:
            logger.info("🤖 开始AI兜底决策...")
            best_agent = await self._ai_topic_relevance_selection(available_agents, user_request)
            if best_agent:
                logger.info(f"🎯 兜底选择: {best_agent}")
                return best_agent
        except Exception as e:
            logger.error(f"❌ AI兜底决策失败: {e}", exc_info=True)
        
        # 如果AI兜底也失败，则选择第一个
        fallback_choice = available_agents[0]
        logger.info(f"🔄 终极兜底选择: {fallback_choice}")
        return fallback_choice
    
    async def _ai_topic_relevance_selection(self, available_agents: List[str], user_request: str) -> Optional[str]:
        """基于用户原始请求的AI智能体选择"""
        if not user_request:
            logger.warning("用户请求为空，无法进行相关性选择。")
            return None

        agent_descriptions = self._get_agent_descriptions(available_agents)
        relevance_prompt = f"""
从以下团队内的智能体中，选择最适合处理用户请求的智能体：

=== 用户原始请求 ===
{user_request}

=== 团队内可用智能体 ===
{agent_descriptions}

请分析用户请求的核心内容和需求类型，然后选择最匹配的智能体。
请只返回最相关的智能体ID（如：market_researcher），不要返回其他内容。
"""
        
        ai_messages = [
            {'role': 'system', 'content': 'You are an expert at matching user requests with specialist agents. Select the most relevant agent ID based on topic alignment.'},
            {'role': 'user', 'content': relevance_prompt}
        ]
        
        response = await self.ai_manager.chat_completion(messages=ai_messages, model='gemini-2.5-flash-preview-05-20', handle_tools=False)
        decision = response.get('content', '').strip()
        
        if decision in available_agents:
            return decision
        else:
            logger.warning(f"AI返回了无效的智能体ID: {decision}，无法完成相关性选择。")
            return None

    def get_routing_stats(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        total = self.routing_stats["total_decisions"]
        stats = self.routing_stats.copy()
        if total > 0:
            stats["rule_based_ratio"] = self.routing_stats["rule_based_decisions"] / total
            stats["ai_based_ratio"] = self.routing_stats["ai_based_decisions"] / total
        return stats


# 全局智能路由器实例
def get_intelligent_router(context_manager=None) -> "IntelligentRouter":
    """获取全局智能路由器实例"""
    if not hasattr(get_intelligent_router, '_instance'):
        get_intelligent_router._instance = IntelligentRouter(context_manager)
    return get_intelligent_router._instance