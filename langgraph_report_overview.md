## 2. LangGraph 概述

### 2.1 定义与目的
LangGraph 是一个专门用于构建有状态、多智能体大型语言模型（LLM）应用的框架。它的核心目的是通过提供一个图谱式的架构来编排智能体之间的交互、管理内存以及定义复杂的工作流，从而克服传统单轮提示或线性链式 LLM 应用的局限性。LangGraph 使得开发者能够构建需要上下文感知、深度理解和动态决策的复杂 AI 系统。

### 2.2 与 LangChain 的区别
LangGraph 在 LangChain 的基础上进行了关键的扩展，主要区别在于其对**有状态性**和**循环工作流**的显式支持。
*   **LangChain:** 主要通过链（Chains）和代理（Agents）构建线性或简单的顺序工作流，通常是无状态的，或者状态管理需要外部组件来完成。其扁平化的检索方式限制了其对相互关联概念进行推理的能力。
*   **LangGraph:** 受 NetworkX 等图论库的启发，采用图谱式架构，将智能体（Nodes）和它们之间的交互（Edges）明确定义为工作流的一部分。这使得构建能够进行迭代式推理、循环调用 LLM 并支持人类介入（Human-in-the-loop）的复杂、有状态应用成为可能。它通过图谱式推理来遍历概念之间的关系，从而实现更深层次的理解和动态决策。