/* 标题样式 */
h1 {
  font-size: 1.5rem; /* 缩小标题字体 */
  margin-top: 0.8rem;
  margin-bottom: 0.5rem;
}

h2 {
  font-size: 1.3rem; /* 缩小标题字体 */
  margin-top: 0.7rem;
  margin-bottom: 0.4rem;
}

h3 {
  font-size: 1.1rem; /* 缩小标题字体 */
  margin-top: 0.6rem;
  margin-bottom: 0.3rem;
}

h4, h5, h6 {
  font-size: 1rem; /* 缩小标题字体 */
  margin-top: 0.5rem;
  margin-bottom: 0.3rem;
}

/* 双行分割线样式 */
hr {
  margin: 0.8rem 0; /* 减少边距 */
  border: 0;
  height: 1px;
  background-color: #eee;
}

/* 调整段落间距 */
p {
  margin-top: 0.3rem; /* 减少边距 */
  margin-bottom: 0.3rem; /* 减少边距 */
  line-height: 0.6; /* 减少行高 */
}

/* 调整列表项间距 */
ul, ol {
  margin-top: 0.4rem; /* 减少边距 */
  margin-bottom: 0.4rem; /* 减少边距 */
  padding-left: 1.3rem; /* 减少缩进 */
}

/* li {
  margin-bottom: 0.2rem; 减少边距
} */

/* 代码块样式 (提高特异性) */
.markdown-wrapper pre { /* 使用 .markdown-wrapper 限定 */
  margin: 0;
  overflow: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.85rem;
  padding: 0.6rem; /* 减少内边距 */
  background-color: #f6f8fa; /* 确保浅灰背景 */
  border-radius: 0 0 3px 3px;
}

.markdown-wrapper code { /* 使用 .markdown-wrapper 限定 */
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(27, 31, 35, 0.05); /* 确保行内代码浅灰背景 */
  border-radius: 3px;
  padding: 0.15em 0.3em; /* 减少内边距 */
  font-size: 0.85em;
}

/* 确保 pre 内的 code 背景透明 */
.markdown-wrapper pre code { /* 使用 .markdown-wrapper 限定 */
  background-color: transparent;
  padding: 0;
  font-size: inherit;
}


/* 针对具体的styled-components类的覆盖 */
div[class*="hqMzAy"] pre,
div[class*="sc-h"] pre,
.sc-hZARmv pre {
  background: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #e1e4e8 !important;
  padding: 0.15em 0.3em !important;
  border-radius: 3px !important;
}

div[class*="hqMzAy"] pre code,
div[class*="sc-h"] pre code,
.sc-hZARmv pre code {
  color: #000000 !important;
  background: transparent !important;
}

/* 复制按钮样式 */
.code-copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.code-copy-button:hover {
  background: #fff;
  border-color: #ccc;
}

.code-block-container {
  position: relative;
  margin: 0;
}

/* 工具调用和结果的容器样式 */
.tool-section {
  margin: 0.5em 0; /* 减少边距 */
}

/* 工具调用块样式 */
.tool-call-block {
  margin: 0.3rem 0; /* 减少边距 */
  border-radius: 0.4rem; /* 减少圆角 */
  overflow: hidden;
}

.tool-call-header {
  padding: 0.3rem 0.6rem; /* 减少内边距 */
  background-color: #e6f7ff; /* 修改为淡蓝色背景，类似 final-result */
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 0.85rem; /* 减小字体 */
  border-bottom: 1px solid #e0e0e0; /* 添加底部边框，增加卡片感 */
  color: #1890ff; /* 修改字体颜色以适应背景 */
}

.tool-call-name {
  margin-left: 0.4rem; /* 减少边距 */
}

/* 工具结果块样式 */
.tool-result-block {
  margin: 0.3rem 0; /* 减少边距 */
  border-radius: 0.4rem; /* 减少圆角 */
  overflow: hidden;
}

.tool-result-header {
  padding: 0.3rem 0.6rem; /* 减少内边距 */
  background-color: rgba(0, 180, 0, 0.1);
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #005500;
  font-size: 0.85rem; /* 减小字体 */
}

/* 最终结果块样式 */
.final-result-block {
  margin: 0.6rem 0; /* 减少边距 */
  border: 1px solid #e0e0e0;
  border-radius: 0.4rem; /* 减少圆角 */
  overflow: hidden;
  background-color: rgba(240, 249, 255, 0.6);
}

.final-result-header {
  padding: 0.3rem 0.6rem; /* 减少内边距 */
  background-color: #e6f7ff;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.final-result-icon {
  margin-right: 0.4rem; /* 减少边距 */
}

.final-result-title {
  font-weight: 600;
  color: #1890ff;
  font-size: 0.9rem; /* 减小字体 */
}

.final-result-content {
  padding: 0.6rem; /* 减少内边距 */
}

.final-result-content p:first-child {
  margin-top: 0;
}

.final-result-content p:last-child {
  margin-bottom: 0;
}

/* 代码块 */
.code-block {
  margin: 0.5rem 0; /* 减少边距 */
  border-radius: 0.4rem; /* 减少圆角 */
  overflow: hidden;
}

/* EnhancedMarkdown Table Styles - Using .markdown-wrapper */
.markdown-wrapper table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  border: 1px solid #dfe2e5; /* Light grey border */
  font-size: 0.9em; /* Slightly smaller font for tables */
  overflow-x: auto; /* Add horizontal scroll for wide tables */
  display: block; /* Needed for overflow-x to work */
}

.markdown-wrapper th,
.markdown-wrapper td {
  border: 1px solid #dfe2e5; /* Light grey border for cells */
  padding: 8px 12px; /* Padding inside cells */
  text-align: left;
  line-height: 1.5;
  hyphens: auto; /* Help with word breaking in cells */
  word-break: break-word;
}

.markdown-wrapper th {
  background-color: #f6f8fa; /* Light background for headers */
  font-weight: 600; /* Bolder font for headers */
}

.markdown-wrapper tr:nth-child(even) {
  background-color: #f6f8fa; /* Zebra striping for rows */
}

.markdown-wrapper tr:hover {
  background-color: #f0f2f5; /* Hover effect for rows */
}

/* 图片预览组件样式 */
.image-preview-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 0.5rem 0;
  max-width: 100%;
  position: relative;
  min-height: 100px;
}

.image-preview-wrapper {
  position: relative;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview-wrapper-loaded {
  opacity: 1;
}

.image-preview-image {
  max-width: 80%;
  max-height: 400px;
  border-radius: 4px;
  object-fit: contain;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-preview-loading {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 120px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin: 0.5rem 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.image-preview-loading-hidden {
  opacity: 0;
  pointer-events: none;
}

.image-preview-error {
  padding: 0.8rem;
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #cf1322;
  text-align: center;
  width: 100%;
  max-width: 500px;
  margin: 0.5rem 0;
}

.image-preview-caption {
  font-size: 0.8rem;
  color: #888;
  margin-top: 0.3rem;
  text-align: center;
  padding: 2px 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  position: absolute;
  bottom: 8px;
  right: 8px;
}

/* 阿里云图片特殊样式 */
.alipay-image {
  border: 1px solid #e8e8e8;
  background-color: #fff;
  padding: 4px;
}

/* Remove the previous comment about adding a wrapper class */
/* The styles now correctly target the existing .markdown-wrapper */

.image-preview-zoom-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #666;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.image-preview-wrapper:hover .image-preview-zoom-icon {
  opacity: 0.8;
}

.image-preview-zoom-icon:hover {
  opacity: 1 !important;
  background-color: rgba(255, 255, 255, 0.95);
  color: #1890ff;
}

/* Modal预览样式 */
.image-preview-modal .ant-modal-content {
  background-color: transparent;
  box-shadow: none;
}

.image-preview-modal .ant-modal-close {
  color: white;
  top: 16px;
  right: 16px;
}

.image-preview-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.image-preview-modal-caption {
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 4px 12px;
  border-radius: 16px;
  margin-top: 12px;
  font-size: 14px;
}

.image-preview-close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s;
}

.image-preview-close-button:hover {
  background-color: rgba(0, 0, 0, 0.7) !important;
  transform: scale(1.1);
}

/* 文件附件显示样式 */
.markdown-wrapper .file-attachment {
  display: flex;
  align-items: flex-start;
  margin: 12px 0;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid rgba(139, 92, 246, 0.15);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  position: relative;
  overflow: visible;
  animation: fileSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  min-height: 60px;
  width: 100%;
  box-sizing: border-box;
}

.file-attachment::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(59, 130, 246, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-attachment:hover {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: rgba(139, 92, 246, 0.25);
}

.file-attachment:hover::before {
  opacity: 1;
}

.file-attachment:active {
  transform: translateY(-1px) scale(0.99);
  transition: transform 0.1s ease;
}

@keyframes fileSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateX(2px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.file-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #8b5cf6;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  margin-top: 2px;
}

.file-attachment:hover .file-icon {
  color: #7c3aed;
  transform: scale(1.1) rotate(5deg);
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 2;
  min-width: 0;
  overflow: visible;
}

.file-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0;
  transition: color 0.3s ease;
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.4;
  flex: 1;
  min-width: 0;
  max-width: none;
}

.file-attachment:hover .file-name {
  color: #7c3aed;
}

.file-meta {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  flex-shrink: 0;
}

.file-size {
  background: rgba(139, 92, 246, 0.1);
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 500;
}

.file-type {
  background: rgba(59, 130, 246, 0.1);
  padding: 2px 8px;
  border-radius: 8px;
  color: #3b82f6;
  font-weight: 500;
  text-transform: uppercase;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.file-attachment:hover .file-actions {
  opacity: 1;
  transform: translateX(0);
}

.file-action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-action-btn:hover {
  background: #8b5cf6;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 不同文件类型的图标颜色 */
.file-icon.pdf { color: #dc2626; }
.file-icon.doc { color: #2563eb; }
.file-icon.docx { color: #2563eb; }
.file-icon.xls { color: #059669; }
.file-icon.xlsx { color: #059669; }
.file-icon.ppt { color: #d97706; }
.file-icon.pptx { color: #d97706; }
.file-icon.txt { color: #64748b; }
.file-icon.zip { color: #7c2d12; }
.file-icon.rar { color: #7c2d12; }
.file-icon.image { color: #10b981; }
.file-icon.video { color: #8b5cf6; }
.file-icon.audio { color: #f59e0b; }
.file-icon.code { color: #374151; }

/* 文件类型标签颜色 */
.file-type.pdf { background: rgba(220, 38, 38, 0.1); color: #dc2626; }
.file-type.doc { background: rgba(37, 99, 235, 0.1); color: #2563eb; }
.file-type.xls { background: rgba(5, 150, 105, 0.1); color: #059669; }
.file-type.ppt { background: rgba(217, 119, 6, 0.1); color: #d97706; }
.file-type.txt { background: rgba(100, 116, 139, 0.1); color: #64748b; }
.file-type.zip { background: rgba(124, 45, 18, 0.1); color: #7c2d12; }
.file-type.image { background: rgba(16, 185, 129, 0.1); color: #10b981; }
.file-type.video { background: rgba(139, 92, 246, 0.1); color: #8b5cf6; }
.file-type.audio { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.file-type.code { background: rgba(55, 65, 81, 0.1); color: #374151; }

/* 响应式优化 */
@media (max-width: 768px) {
  .markdown-wrapper .file-attachment {
    padding: 10px 12px;
  }
  
  .file-icon {
    font-size: 18px;
    margin-right: 10px;
  }
  
  .file-info {
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .file-name {
    font-size: 13px;
  }
  
  .file-meta {
    font-size: 11px;
    gap: 6px;
  }
  
  .file-actions {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 480px) {
  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .file-name {
    font-size: 12px;
  }
  
  .file-meta {
    justify-content: flex-start;
  }
}