#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 工作流可视化生成器

这个脚本演示如何生成和查看LangGraph工作流的可视化图
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def generate_sample_workflow_visualization():
    """生成示例工作流可视化图"""
    print("🎨 正在生成工作流可视化图...")
    
    try:
        from core.workflow_engine import StateGraph, END
        from core.state_definitions import EnhancedTeamState
        
        # 创建示例工作流
        graph = StateGraph(EnhancedTeamState)
        
        # 定义示例节点
        async def start_node(state, config=None):
            """工作流开始节点"""
            state["workflow_started"] = True
            return state
        
        async def data_analysis_node(state, config=None):
            """数据分析节点"""
            state["data_analyzed"] = True
            return state
        
        async def report_generation_node(state, config=None):
            """报告生成节点"""
            state["report_generated"] = True
            return state
        
        async def quality_check_node(state, config=None):
            """质量检查节点"""
            state["quality_checked"] = True
            return state
        
        def route_condition(state):
            """路由条件函数"""
            if state.get("data_analyzed"):
                return "generate_report"
            else:
                return "end"
        
        # 构建图结构
        graph.add_node("start", start_node)
        graph.add_node("data_analysis", data_analysis_node)
        graph.add_node("generate_report", report_generation_node)
        graph.add_node("quality_check", quality_check_node)
        
        # 添加边
        graph.add_edge("start", "data_analysis")
        graph.add_conditional_edges(
            "data_analysis",
            route_condition,
            {
                "generate_report": "generate_report",
                "end": END
            }
        )
        graph.add_edge("generate_report", "quality_check")
        graph.add_edge("quality_check", END)
        
        # 设置入口点
        graph.set_entry_point("start")
        
        # 生成可视化图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        visualization_file = f"sample_workflow_{timestamp}.png"
        
        print(f"📊 正在生成可视化图: {visualization_file}")
        graph.visualize(visualization_file)
        
        print(f"✅ 可视化图已生成: {visualization_file}")
        print(f"📁 图片位置: {os.path.abspath(visualization_file)}")
        
        # 检查文件是否生成成功
        if os.path.exists(visualization_file):
            file_size = os.path.getsize(visualization_file)
            print(f"📏 文件大小: {file_size} 字节")
            
            # 提供查看建议
            print("\n🔍 查看可视化图的方法:")
            print(f"1. 直接打开图片文件: {os.path.abspath(visualization_file)}")
            print("2. 在IDE中打开图片文件")
            print("3. 使用系统默认图片查看器")
            
            return visualization_file
        else:
            print("❌ 可视化图生成失败，可能需要安装matplotlib和networkx")
            print("💡 安装命令: pip install matplotlib networkx")
            return None
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保相关模块可以正确导入")
        return None
    except Exception as e:
        print(f"❌ 生成可视化图失败: {e}")
        return None

async def create_complex_workflow_example():
    """创建复杂工作流示例"""
    print("\n🚀 正在生成复杂工作流示例...")
    
    try:
        from core.workflow_engine import StateGraph, END
        from core.state_definitions import EnhancedTeamState
        
        graph = StateGraph(EnhancedTeamState)
        
        # 创建更复杂的工作流
        nodes = [
            ("coordinator", "工作流协调器"),
            ("researcher", "研究智能体"),
            ("analyst", "分析智能体"),
            ("writer", "写作智能体"),
            ("reviewer", "审核智能体"),
            ("parallel_processor", "并行处理器"),
            ("result_merger", "结果合并器")
        ]
        
        # 添加所有节点
        for node_id, description in nodes:
            async def node_func(state, config=None, node_name=node_id):
                state[f"{node_name}_completed"] = True
                return state
            graph.add_node(node_id, node_func)
        
        # 构建复杂的边关系
        graph.add_edge("coordinator", "researcher")
        graph.add_edge("researcher", "parallel_processor")
        graph.add_edge("parallel_processor", "analyst")
        graph.add_edge("parallel_processor", "writer")
        graph.add_edge("analyst", "result_merger")
        graph.add_edge("writer", "result_merger")
        graph.add_edge("result_merger", "reviewer")
        graph.add_edge("reviewer", END)
        
        graph.set_entry_point("coordinator")
        
        # 生成复杂工作流可视化
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        complex_file = f"complex_workflow_{timestamp}.png"
        
        print(f"📊 正在生成复杂工作流图: {complex_file}")
        graph.visualize(complex_file)
        
        if os.path.exists(complex_file):
            print(f"✅ 复杂工作流图已生成: {complex_file}")
            print(f"📁 图片位置: {os.path.abspath(complex_file)}")
            return complex_file
        else:
            print("❌ 复杂工作流图生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 复杂工作流生成失败: {e}")
        return None

def show_viewing_instructions():
    """显示查看说明"""
    print("\n📖 查看工作流可视化图的详细说明:")
    print("="*50)
    print("1. 🖼️  图片文件位置:")
    print("   - 生成的PNG文件在当前目录中")
    print("   - 文件名格式: workflow_YYYYMMDD_HHMMSS.png")
    print()
    print("2. 🔍 查看方法:")
    print("   - Windows: 双击PNG文件，使用默认图片查看器")
    print("   - macOS: 双击PNG文件，使用预览应用")
    print("   - Linux: 使用 eog, feh, 或其他图片查看器")
    print("   - IDE: 在VS Code等IDE中直接打开PNG文件")
    print()
    print("3. 📊 图表内容:")
    print("   - 蓝色圆圈: 工作流节点")
    print("   - 黑色箭头: 固定边连接")
    print("   - 虚线箭头: 条件边连接")
    print("   - 节点标签: 显示节点名称")
    print()
    print("4. 💡 如果看不到图片:")
    print("   - 检查是否安装了matplotlib: pip install matplotlib")
    print("   - 检查是否安装了networkx: pip install networkx")
    print("   - 确认文件是否生成成功")

async def main():
    """主函数"""
    print("🎨 LangGraph 工作流可视化生成器")
    print("="*40)
    
    # 生成示例工作流
    simple_file = await generate_sample_workflow_visualization()
    
    # 生成复杂工作流
    complex_file = await create_complex_workflow_example()
    
    # 显示查看说明
    show_viewing_instructions()
    
    # 汇总结果
    print("\n📋 生成结果汇总:")
    if simple_file:
        print(f"✅ 简单工作流: {simple_file}")
    if complex_file:
        print(f"✅ 复杂工作流: {complex_file}")
    
    if simple_file or complex_file:
        print(f"\n🎉 工作流可视化图生成完成！")
        print(f"📁 请在当前目录查看生成的PNG文件")
    else:
        print("\n❌ 可视化图生成失败")
        print("💡 请安装依赖: pip install matplotlib networkx")

if __name__ == "__main__":
    asyncio.run(main())