"""
DeerFlow深度研究插件实现

基于LangGraph的深度研究框架，提供智能研究、报告生成、
播客制作、PPT生成等多媒体内容创作功能。

Created: 2025-06-09T08:26:53+08:00
"""

import logging
import sys
import io
import builtins
import functools
import uuid # 新增导入

# 终极解决方案：在Windows上全面强制UTF-8编码
# 这个问题通常发生在Windows上，因为其默认编码是GBK/CP936
if sys.platform == "win32":
    # 1. 重新包装标准输入/输出/错误流
    try:
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
        sys.stdin = io.TextIOWrapper(sys.stdin.buffer, encoding='utf-8', errors='replace')
    except (AttributeError, TypeError, io.UnsupportedOperation):
        # 在某些环境（如IDE、测试运行器）中，流可能无法重新包装
        pass

    # 2. 猴子补丁内置的open()函数
    # 这是最后的手段，用以捕获所有未指定编码的文件操作
    _original_open = builtins.open

    @functools.wraps(_original_open)
    def utf8_open(file, mode='r', buffering=-1, encoding=None, errors=None, newline=None, closefd=True, opener=None):
        # 只在文本模式下强制使用UTF-8
        if 'b' not in mode:
            if encoding is None:
                encoding = 'utf-8'
            # 为了安全，将错误处理设置为'replace'
            if errors is None:
                errors = 'replace'
        return _original_open(file, mode, buffering, encoding, errors, newline, closefd, opener)

    # 应用猴子补丁
    builtins.open = utf8_open

import asyncio
import logging
from math import log
import subprocess
import json
import aiohttp
import base64
from pathlib import Path
from typing import Dict, List, Any, Optional
import os
from backend.utils.key_manager import KeyManager
from backend.plugin_system.interfaces import IWorkflowPlugin, PluginStatus
from backend.plugin_system.exceptions import PluginError

# 安全导入backend.tools.minimaxi，处理打包环境中的导入失败
try:
    from backend.tools.minimaxi import MiniMaxTTSClient
    MINIMAX_AVAILABLE = True
except ImportError as e:
    logger.warning(f"MiniMax TTS not available: {e}")
    MiniMaxTTSClient = None
    MINIMAX_AVAILABLE = False

logger = logging.getLogger(__name__)

tavily_key = None
async def get_tavily_key():
    global tavily_key
    if tavily_key is None:
        tavily_key = await KeyManager().get_key_from_api("tavily")
    return tavily_key

def _is_packaged_environment():
    """检测是否在打包环境中运行"""
    # PyInstaller打包后的环境特征
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

def _setup_deer_flow_environment():
    """设置DeerFlow运行环境"""
    try:
        paths_to_add = []
        
        # 获取当前插件目录
        current_file = Path(__file__).parent
        plugin_src = current_file / "src"
        
        # 优先使用插件目录下的src
        if plugin_src.exists():
            paths_to_add.extend([str(plugin_src), str(current_file)])
            logger.info(f"Found plugin src directory: {plugin_src}")
        else:
            # 回退到原来的deer-flow根目录逻辑（保持兼容性）
            deer_flow_root = Path("deer-flow").absolute()
            deer_flow_src = deer_flow_root / "src"
            
            if deer_flow_src.exists():
                paths_to_add.extend([str(deer_flow_src), str(deer_flow_root)])
                logger.info(f"Found root deer-flow directory: {deer_flow_root}")
        
        # 在打包环境中，查找插件内的src
        if _is_packaged_environment():
            try:
                if hasattr(sys, '_MEIPASS'):
                    # 先尝试插件内的src
                    packaged_plugin_src = Path(sys._MEIPASS) / "backend" / "plugins" / "deer_flow" / "src"
                    if packaged_plugin_src.exists():
                        paths_to_add.insert(0, str(packaged_plugin_src))
                        paths_to_add.insert(0, str(packaged_plugin_src.parent))
                        logger.info(f"Found packaged plugin src: {packaged_plugin_src}")
                    else:
                        # 回退到根目录
                        packaged_deer_flow = Path(sys._MEIPASS) / "deer-flow" / "src"
                        if packaged_deer_flow.exists():
                            paths_to_add.insert(0, str(packaged_deer_flow))
                            paths_to_add.insert(0, str(packaged_deer_flow.parent))
                            logger.info(f"Found packaged root deer-flow: {packaged_deer_flow}")
            except Exception as e:
                logger.warning(f"Failed to setup packaged deer-flow path: {e}")
        
        # 添加路径到sys.path
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
                
        logger.info(f"Added deer-flow paths to Python path: {paths_to_add}")
        return True
    except Exception as e:
        logger.error(f"Failed to setup deer-flow environment: {e}")
        return False

class DeerFlowService:
    """DeerFlow服务管理类 - 支持子进程和集成两种模式"""
    
    def __init__(self):
        self.process = None
        self.deer_flow_path = None
        self.is_running = False
        self.port = 8001
        self.mode = None  # 'subprocess' 或 'integrated'
        
        # 集成模式相关
        self._integrated_graph = None
        # 注意：播客、PPT、散文生成现在通过专门的生成器类实现
        # 不再需要独立的图形对象
        
        # 子进程模式相关
        self._stdout_reader_task = None
        self._stderr_reader_task = None
        self._health_monitor_task = None
        self._process_output_buffer = []
        self._max_buffer_size = 1000  # 最大缓冲区行数
        self._monitor_interval = 30   # 健康检查间隔（秒）
        
    def _determine_mode(self) -> str:
        """确定启动模式"""
        is_packaged = _is_packaged_environment()
        
        # 强制集成模式的环境变量
        force_integrated = os.getenv('DEER_FLOW_INTEGRATED', '').lower() in ('true', '1', 'yes')
        
        # 在打包环境中，优先使用独立的deer-flow可执行文件
        if is_packaged:
            # 检查是否存在独立的deer-flow可执行文件
            deer_flow_exe = self._find_deer_flow_executable()
            if deer_flow_exe:
                logger.info("Using subprocess mode with standalone deer-flow executable (source protected)")
                return 'subprocess'
            elif force_integrated:
                logger.info("Using integrated mode (forced by environment variable)")
                return 'integrated'
            else:
                logger.warning("No deer-flow executable found in packaged environment, disabling DeerFlow")
                return 'disabled'
        
        # 开发环境的原有逻辑
        if force_integrated:
            logger.info("Using integrated mode (forced by environment variable)")
            return 'integrated'
        
        # 检查deer-flow目录是否存在
        deer_flow_exists = self._find_deer_flow_path() is not None
        if not deer_flow_exists:
            logger.warning("deer-flow directory not found, falling back to integrated mode")
            return 'integrated'
            
        logger.info("Using subprocess mode (development environment)")
        return 'subprocess'
    
    async def start(self):
        """启动DeerFlow服务"""
        try:
            await get_tavily_key()
            
            # 设置环境
            if not _setup_deer_flow_environment():
                raise Exception("Failed to setup deer-flow environment")
            
            # 确定启动模式
            await self._start_integrated_mode()    
            self.mode = 'integrated'
            logger.info(f"DeerFlow service started successfully in {self.mode} mode")
            
        except Exception as e:
            logger.error(f"Failed to start DeerFlow service: {e}")
            # 在打包环境中，如果子进程模式失败，要尝试集成模式
            is_packaged = _is_packaged_environment()
            if self.mode == 'subprocess' and not is_packaged:
                # 只在开发环境中才尝试集成模式作为回退
                logger.info("Subprocess mode failed, attempting integrated mode...")
                try:
                    self.mode = 'integrated'
                    await self._start_integrated_mode()
                    self.is_running = True
                    logger.info("Successfully started in integrated mode as fallback")
                except Exception as e2:
                    logger.error(f"Integrated mode also failed: {e2}")
                    self.is_running = False
                    logger.warning("DeerFlow plugin will run in inactive mode")
            else:
                # 在打包环境中或其他模式失败时，直接禁用
                self.is_running = False
                if is_packaged:
                    logger.warning("DeerFlow plugin disabled in packaged environment due to startup failure")
                else:
                    logger.warning("DeerFlow plugin will run in inactive mode")
    
    async def _start_integrated_mode(self):
        """启动集成模式"""
        try:
            logger.info("Starting DeerFlow in integrated mode...")
            
            # 在打包环境中检查关键依赖
            if _is_packaged_environment():
                missing_deps = self._check_integrated_dependencies()
                if missing_deps:
                    logger.error(f"Missing dependencies in packaged environment: {missing_deps}")
                    raise ImportError(f"Required dependencies not available: {', '.join(missing_deps)}")
            
            # 在集成模式下设置 deer_flow_path
            if not self.deer_flow_path:
                # 获取当前插件目录
                current_plugin_dir = Path(__file__).parent
                plugin_src = current_plugin_dir / "src"
                
                # 优先使用插件目录下的src
                if plugin_src.exists():
                    self.deer_flow_path = current_plugin_dir
                    logger.info(f"Using plugin deer-flow path: {self.deer_flow_path}")
                else:
                    # 回退到原来的查找逻辑
                    self.deer_flow_path = self._find_deer_flow_path()
                    if not self.deer_flow_path:
                        # 如果找不到，尝试使用打包环境中的路径
                        if _is_packaged_environment() and hasattr(sys, '_MEIPASS'):
                            # 先尝试插件内的src
                            packaged_plugin_dir = Path(sys._MEIPASS) / "backend" / "plugins" / "deer_flow"
                            packaged_plugin_src = packaged_plugin_dir / "src"
                            if packaged_plugin_src.exists():
                                self.deer_flow_path = packaged_plugin_dir
                                logger.info(f"Using packaged plugin deer-flow path: {self.deer_flow_path}")
                            else:
                                # 回退到根目录
                                packaged_deer_flow = Path(sys._MEIPASS) / "deer-flow"
                                if packaged_deer_flow.exists():
                                    self.deer_flow_path = packaged_deer_flow
                                    logger.info(f"Using packaged root deer-flow path: {self.deer_flow_path}")
                                else:
                                    logger.warning("No deer-flow path found, using current directory")
                                    self.deer_flow_path = Path(".")
                        else:
                            logger.warning("No deer-flow path found, using current directory")
                            self.deer_flow_path = Path(".")
            
            # 设置环境变量（重要：集成模式下也需要设置环境变量）
            deer_flow_env = self._get_deer_flow_env()
            for key, value in deer_flow_env.items():
                if key not in ["PYTHONPATH", "PYTHONIOENCODING", "PYTHONLEGACYWINDOWSSTDIO", "LC_ALL", "LANG"]:
                    os.environ[key] = value
                    logger.debug(f"Set environment variable: {key}")
            
            # 强制设置 PYTHONUTF8 环境变量为 1，解决 Windows 下的编码问题
            # os.environ["PYTHONUTF8"] = "1"
            # logger.info("强制设置 PYTHONUTF8 环境变量为 1，以解决编码问题")
            
            # 使用 KeyManager 获取最新的 TAVILY_API_KEY
            try:
                tavily_key = await get_tavily_key()  # 使用全局函数，已经调用了 KeyManager
                if tavily_key:
                    os.environ["TAVILY_API_KEY"] = tavily_key
                    logger.info(f"Set TAVILY_API_KEY from KeyManager for integrated mode: {tavily_key[:10]}...")
                else:
                    logger.warning("Failed to get TAVILY_API_KEY from KeyManager")
            except Exception as e:
                logger.error(f"Error getting TAVILY_API_KEY from KeyManager: {e}")
            
            # 确保所有 deer-flow 相关的包路径被添加到 sys.path
            if self.deer_flow_path:
                deer_flow_src = self.deer_flow_path / "src"
                if deer_flow_src.exists() and str(deer_flow_src) not in sys.path:
                    sys.path.insert(0, str(deer_flow_src))
                    logger.info(f"Added deer-flow src to sys.path: {deer_flow_src}")
                
                if str(self.deer_flow_path) not in sys.path:
                    sys.path.insert(0, str(self.deer_flow_path))
                    logger.info(f"Added deer-flow root to sys.path: {self.deer_flow_path}")
            
            # 导入并初始化 deer-flow 图形，使用绝对导入
            logger.info(f"Current sys.path (first 5): {sys.path[:5]}")
            
            # 使用绝对导入路径
            from backend.plugins.deer_flow.src.graph.builder import build_graph_with_memory
            logger.info("Successfully imported build_graph_with_memory using absolute import")
            
            self._integrated_graph = build_graph_with_memory()
            
            self.is_running = True
            self.mode = 'integrated'
            logger.info("DeerFlow integrated mode started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start integrated mode: {e}")
            self.is_running = False
            raise
    
    
    def _get_recent_output(self, lines: int = 50) -> str:
        """获取最近的进程输出用于诊断"""
        if not self._process_output_buffer:
            return "No process output available"
        
        recent_lines = self._process_output_buffer[-lines:] if lines > 0 else self._process_output_buffer
        return "\n".join(recent_lines)
    
    async def stop(self):
        """停止DeerFlow服务进程"""
        logger.info(f"Stopping DeerFlow service ({self.mode} mode)...")
        self.is_running = False
        if self.mode == 'subprocess':
            # 清理监控和读取任务
            await self._cleanup_tasks()
            # 停止子进程
            if self.process:
                logger.info("Terminating DeerFlow subprocess...")
                self.process.terminate()
                try:
                    await asyncio.wait_for(self.process.wait(), timeout=10)
                    logger.info("DeerFlow subprocess terminated gracefully")
                except asyncio.TimeoutError:
                    logger.warning("DeerFlow subprocess did not terminate gracefully, killing...")
                    self.process.kill()
                    await self.process.wait()
                    logger.info("DeerFlow subprocess killed")
                self.process = None
        elif self.mode == 'integrated':
            # 清理集成模式资源
            self._integrated_graph = None
            # 注意：不再使用独立的_podcast_graph、_ppt_graph、_prose_graph
            # 这些功能现在通过生成器类实现，无需特殊清理
            logger.info("DeerFlow integrated mode resources cleaned up")
        logger.info("DeerFlow service stopped")
        
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: [2025-01-19T10:10:00+08:00]
        # Reason: 更新状态报告，反映使用生成器类而不是图形对象的新架构
        # Principle_Applied: 真实性 - 准确反映当前架构状态; 一致性 - 与代码实现保持一致
        # Optimization: 现在所有内容生成都使用可用的生成器类，不再依赖图形初始化
        # Architectural_Note (AR): 反映使用生成器类的新架构设计
        # Documentation_Note (DW): 更新状态信息以匹配实际实现
        # }}
        return {
            "is_running": self.is_running,
            "mode": self.mode,
            "port": self.port if self.mode == 'subprocess' else None,
            "graphs_initialized": {
                "research": self._integrated_graph is not None,
                "podcast": True,  # 播客使用PodcastGenerator类，总是可用
                "ppt": True,      # PPT使用PPTGenerator类，总是可用
                "prose": True     # 散文使用ProseGenerator类，总是可用
            } if self.mode == 'integrated' else None,
            "subprocess_running": self.process is not None and self.process.returncode is None if self.mode == 'subprocess' else None
        }
    
    async def _cleanup_tasks(self):
        """清理异步任务"""
        tasks_to_cleanup = [
            ("stdout_reader", self._stdout_reader_task),
            ("stderr_reader", self._stderr_reader_task),
            ("health_monitor", self._health_monitor_task)
        ]
        
        for task_name, task in tasks_to_cleanup:
            if task and not task.done():
                logger.info(f"Cancelling {task_name} task...")
                task.cancel()
                try:
                    await asyncio.wait_for(task, timeout=5)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    logger.debug(f"{task_name} task cancelled")
                except Exception as e:
                    logger.warning(f"Error cancelling {task_name} task: {e}")
        
        # 重置任务引用
        self._stdout_reader_task = None
        self._stderr_reader_task = None
        self._health_monitor_task = None
    
    def _find_deer_flow_path(self) -> Optional[Path]:
        """查找DeerFlow安装路径"""
        # 在项目目录中查找deer-flow
        possible_paths = [
            Path("deer-flow"),
            Path("../deer-flow"),
            Path("./deer-flow")
        ]
        
        for path in possible_paths:
            if path.exists() and (path / "src").exists():
                return path.absolute()
        return None
    
    
    
    def _get_deer_flow_env(self) -> Dict[str, str]:
        """获取DeerFlow运行环境变量"""
        env = os.environ.copy()
        
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: [2025-01-16T12:00:00+08:00]
        # Reason: 添加PYTHONPATH设置，解决子进程找不到src模块的问题
        # Principle_Applied: 环境隔离 - 通过环境变量解决模块导入问题; 兼容性 - 保持跨平台兼容
        # Optimization: 外部解决方案，无需修改deer-flow源码
        # Architectural_Note (AR): 保持插件与子服务的解耦，通过环境配置解决问题
        # Documentation_Note (DW): 记录模块路径配置的解决方案
        # }}
        
        # 设置PYTHONPATH，包含deer-flow根目录，这样子进程就能找到src模块
        if self.deer_flow_path:
            current_pythonpath = env.get("PYTHONPATH", "")
            deer_flow_paths = [str(self.deer_flow_path)]
            
            if current_pythonpath:
                # 如果已有PYTHONPATH，则追加deer-flow路径
                deer_flow_paths.append(current_pythonpath)
            
            env["PYTHONPATH"] = os.pathsep.join(deer_flow_paths)
            logger.info(f"Set PYTHONPATH for deer-flow subprocess: {env['PYTHONPATH']}")
        
        # 强制使用UTF-8编码，解决Windows GBK编码问题
        env["PYTHONIOENCODING"] = "utf-8"
        env["PYTHONLEGACYWINDOWSSTDIO"] = "utf-8"
        env["PYTHONUTF8"] = "1" # 新增：强制Python在所有模式下使用UTF-8
        #env["SEARCH_API"] = "duckduckgo"
        # 设置语言环境为UTF-8
        env["LC_ALL"] = "C.UTF-8"
        env["LANG"] = "C.UTF-8"
        
        global tavily_key
        # 从JIMU配置中获取API密钥
        try:
            from backend.core.config import Config
            
            config = Config()
            
            # 设置各种API密钥
            openai_key = config.get_api_key("openai")
            if openai_key:
                env["OPENAI_API_KEY"] = openai_key
                
            google_key = config.get_api_key("gemini") or config.get_api_key("google")
            if google_key:
                env["GOOGLE_API_KEY"] = google_key
                
            # 使用全局的 tavily_key（通过 KeyManager 获取）
            if tavily_key:
                env["TAVILY_API_KEY"] = tavily_key
                logger.info(f"Set TAVILY_API_KEY from KeyManager for deer-flow: {tavily_key[:10]}...")
            else:
                logger.warning("TAVILY_API_KEY not available from KeyManager")
                
            # 设置默认模型配置
            env["BASIC_MODEL_API_KEY"] =  "sk-Z0MdU0NAXCmiwYF_iz-gu4aqoEg8XSYGUL3IR32geJ7ZlaflLmzJVENtrEk"
            env["BASIC_MODEL_BASE_URL"] = "http://jimu.ffa.chat/v1"
            env["BASIC_MODEL_MODEL"] = "gemini-2.5-flash-preview-05-20"
                
        except Exception as e:
            logger.warning(f"Failed to load JIMU config for API keys: {e}")
            
        return env
    
    async def _wait_for_service(self, timeout=10):
        """等待服务启动"""
        for _ in range(timeout):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"http://127.0.0.1:{self.port}/") as resp:
                        if resp.status in [200, 404]:  # 404也表示服务已启动
                            return
            except:
                pass
            await asyncio.sleep(1)
        
        raise Exception("DeerFlow service failed to start within timeout")
    
    def _check_dependencies(self) -> List[str]:
        """检查DeerFlow关键依赖是否安装"""
        # {{CHENGQI:
        # Action: Added
        # Timestamp: [2025-06-09T10:00:39+08:00]
        # Reason: 实现依赖检查功能，在启动前验证关键模块，避免运行时错误
        # Principle_Applied: 错误预防 - 早期发现依赖问题; 用户体验 - 明确的错误提示
        # Optimization: 检查关键依赖模块，提供安装建议
        # Architectural_Note (AR): 增强系统健壮性
        # Documentation_Note (DW): 记录依赖检查逻辑
        # }}
        missing_deps = []
        required_modules = [
            'langchain_core',
            'langgraph', 
            'langchain_community',
            'fastapi',
            'uvicorn'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_deps.append(module)
                
        return missing_deps
    
    def _check_integrated_dependencies(self) -> List[str]:
        """检查集成模式的关键依赖"""
        missing_deps = []
        
        # 集成模式的最小依赖集
        critical_packages = [
            'langchain',
            'langchain_openai',
            'langchain_core',
            'langgraph'
        ]
        
        logger.info("Starting integrated dependencies check...")
        
        for package in critical_packages:
            try:
                module = __import__(package)
                logger.debug(f"✅ Dependency check passed: {package} (version: {getattr(module, '__version__', 'unknown')})")
            except ImportError as e:
                logger.warning(f"❌ Dependency check failed: {package} - {e}")
                
                # 在打包环境中提供更详细的调试信息
                if _is_packaged_environment():
                    logger.debug(f"Running in packaged environment, sys.path contains:")
                    for i, path in enumerate(sys.path[:10]):  # 只显示前10个路径
                        logger.debug(f"  [{i}] {path}")
                    
                    # 尝试查找可能的模块位置
                    try:
                        import pkgutil
                        found_modules = []
                        for importer, modname, ispkg in pkgutil.iter_modules():
                            if package in modname:
                                found_modules.append(modname)
                        
                        if found_modules:
                            logger.debug(f"Found similar modules: {found_modules[:5]}")
                        else:
                            logger.debug(f"No modules found containing '{package}'")
                    except Exception as debug_e:
                        logger.debug(f"Failed to debug module search: {debug_e}")
                
                missing_deps.append(package)
        
        if missing_deps:
            logger.error(f"Missing critical dependencies: {missing_deps}")
            
            # 在打包环境中，提供更友好的错误信息
            if _is_packaged_environment():
                logger.error("This appears to be a packaging issue. The following dependencies were not properly included in the build:")
                for dep in missing_deps:
                    logger.error(f"  - {dep}")
                logger.error("Please ensure the build process includes all DeerFlow dependencies from requirements_deer_flow.txt")
        else:
            logger.info("✅ All integrated dependencies check passed")
                
        return missing_deps
    
    async def research_query(self, query: str, **kwargs) -> Dict[str, Any]:
        """执行研究查询"""
        try:
            if not self.is_running:
                return {"success": False, "error": "DeerFlow service not running"}
            if self.mode == 'integrated':
                return await self._research_query_integrated(query, **kwargs)
            else:
                return await self._research_query_subprocess(query, **kwargs)
        except Exception as e:
            logger.error(f"Research query failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _research_query_integrated(self, query: str, **kwargs) -> Dict[str, Any]:
        """集成模式下的研究查询"""
        try:
            from uuid import uuid4
            from langchain_core.messages import HumanMessage
            
            if not self._integrated_graph:
                raise Exception("Integrated graph not initialized")
                
            # 准备参数
            max_plan_iterations = kwargs.get('max_plan_iterations', 1)
            max_step_num = kwargs.get('max_step_num', 3)
            enable_background_investigation = kwargs.get('enable_background_investigation', True)
            auto_accepted_plan = kwargs.get('auto_accepted_plan', True)
            
            # 构建输入
            thread_id = str(uuid4())
            input_data = {
                "messages": [{"role": "user", "content": query}],
                "plan_iterations": 0,
                "final_report": "",
                "current_plan": None,
                "observations": [],
                "auto_accepted_plan": auto_accepted_plan,
                "enable_background_investigation": enable_background_investigation,
                "research_topic": query,
            }
            
            config = {
                "thread_id": thread_id,
                "resources": [],
                "max_plan_iterations": max_plan_iterations,
                "max_step_num": max_step_num,
                "max_search_results": 10,
                "mcp_settings": {},
                "report_style": "academic",
                "recursion_limit": 100,
            }
            
            logger.info(f"Starting integrated research for: {query}")
            
            # 修复后的状态处理逻辑
            final_state = None
            result = ""
            message_content = []
            
            # 方法1：优先使用直接invoke获取最终状态
            try:
                logger.debug("Attempting direct invoke method...")
                final_state = await self._integrated_graph.ainvoke(input_data, config=config)
                logger.debug(f"Direct invoke final_state type: {type(final_state)}")
                logger.info(f"Final state keys: {list(final_state.keys()) if isinstance(final_state, dict) else 'Not a dict'}")
                
                # 增强的状态验证
                if isinstance(final_state, dict):
                    # 记录所有状态字段的详细信息
                    for key, value in final_state.items():
                        if isinstance(value, str):
                            logger.debug(f"State field {key}: {len(value)} chars")
                        elif isinstance(value, list):
                            logger.debug(f"State field {key}: {len(value)} items")
                        else:
                            logger.debug(f"State field {key}: {type(value)}")
                    
                    # 提取最终报告
                    raw_result = final_state.get('final_report', '')
                    if raw_result and isinstance(raw_result, str) and len(raw_result.strip()) > 0:
                        try:
                            raw_result.encode('utf-8')
                            result = raw_result
                            logger.info(f"Direct invoke extracted final_report, length: {len(result)}")
                        except UnicodeEncodeError as e:
                            logger.warning(f"Direct invoke result has encoding issues: {e}")
                            try:
                                result = raw_result.encode('utf-8', errors='replace').decode('utf-8')
                                logger.info(f"Fixed encoding, result length: {len(result)}")
                            except Exception as encode_error:
                                logger.error(f"Failed to fix encoding: {encode_error}")
                                result = ""
                    else:
                        logger.warning(f"Direct invoke final_report is empty or invalid: type={type(raw_result)}, length={len(str(raw_result))}")
                else:
                    logger.warning(f"Direct invoke returned non-dict state: {type(final_state)}")
                
            except Exception as invoke_error:
                # 检查是否是递归限制错误
                error_str = str(invoke_error)
                if "recursion limit" in error_str.lower() or "graph_recursion_limit" in error_str.lower():
                    logger.error(f"Recursion limit exceeded: {invoke_error}")
                    # 尝试从当前状态中提取已有的研究数据
                    if hasattr(invoke_error, 'graph_state') and invoke_error.graph_state:
                        logger.info("Attempting to recover intermediate state from recursion error...")
                        final_state = invoke_error.graph_state
                    else:
                        logger.warning("No intermediate state available from recursion error")
                        final_state = None
                else:
                    logger.warning(f"Direct invoke failed: {invoke_error}, trying stream method...")
                    final_state = None
                
            # 方法2：如果直接invoke失败或结果无效，使用流模式
            if not result or len(result.strip()) < 100:
                logger.debug("Starting enhanced stream processing...")
                chunk_count = 0
                latest_chunk = None
                all_chunks = []
                
                try:
                    async for chunk in self._integrated_graph.astream(
                        input_data,
                        config=config,
                        stream_mode=["values"],  # 使用values模式获取完整状态
                    ):
                        chunk_count += 1
                        logger.debug(f"Processing chunk {chunk_count}, type: {type(chunk)}")
                        
                        if isinstance(chunk, dict):
                            # 深拷贝保存状态，避免引用问题
                            latest_chunk = chunk.copy()
                            all_chunks.append(latest_chunk)
                            
                            # 详细记录chunk内容用于调试
                            chunk_keys = list(chunk.keys()) if isinstance(chunk, dict) else []
                            logger.debug(f"Chunk {chunk_count} keys: {chunk_keys}")
                            
                            # 实时检查是否包含final_report
                            if 'final_report' in chunk and chunk['final_report']:
                                report_content = chunk['final_report']
                                if isinstance(report_content, str) and len(report_content.strip()) > 100:
                                    logger.info(f"Found substantial final_report in chunk {chunk_count}, length: {len(report_content)}")
                                    # 验证内容质量
                                    preview = report_content.strip()[:200] + "..." if len(report_content.strip()) > 200 else report_content.strip()
                                    logger.debug(f"Final report preview: {preview}")
                            
                            # 收集消息内容作为备用
                            if 'messages' in chunk and chunk['messages']:
                                messages = chunk['messages']
                                if messages:
                                    last_msg = messages[-1]
                                    content = ""
                                    if hasattr(last_msg, 'content'):
                                        content = last_msg.content
                                    elif isinstance(last_msg, dict) and 'content' in last_msg:
                                        content = last_msg['content']
                                    
                                    if content and isinstance(content, str) and len(content.strip()) > 100:
                                        message_content.append(content)
                                        logger.debug(f"Collected message content in chunk {chunk_count}, length: {len(content)}")
                            
                            # 检查observations字段
                            if 'observations' in chunk and chunk['observations']:
                                obs_count = len(chunk['observations'])
                                logger.debug(f"Chunk {chunk_count} contains {obs_count} observations")
                        else:
                            logger.warning(f"Chunk {chunk_count} is not a dict: {type(chunk)}")
                
                    logger.info(f"Stream processing completed, processed {chunk_count} chunks")
                    
                    # 详细的状态分析
                    if all_chunks:
                        logger.info(f"Collected {len(all_chunks)} chunks for analysis")
                        # 分析每个chunk中final_report的变化
                        for i, chunk in enumerate(all_chunks):
                            if 'final_report' in chunk:
                                fr_len = len(chunk['final_report']) if chunk['final_report'] else 0
                                logger.debug(f"Chunk {i+1} final_report length: {fr_len}")
                    
                    # 使用最新的chunk作为final_state
                    if latest_chunk:
                        final_state = latest_chunk
                        logger.debug(f"Using latest chunk as final_state, keys: {list(final_state.keys())}")
                        
                        # 详细验证final_state
                        if 'final_report' in final_state:
                            fr_content = final_state['final_report']
                            logger.info(f"Final state final_report type: {type(fr_content)}, length: {len(fr_content) if isinstance(fr_content, str) else 'N/A'}")
                            if isinstance(fr_content, str) and fr_content.strip():
                                logger.info(f"Final state final_report preview: {fr_content.strip()[:200]}...")
                            else:
                                logger.warning("Final state final_report is empty or invalid")
                        else:
                            logger.warning("Final state does not contain final_report field")
                    else:
                        logger.error("No chunks collected from stream")
                    
                    # 从final_state提取结果
                    if final_state and isinstance(final_state, dict):
                        raw_result = final_state.get('final_report', '')
                        if raw_result and isinstance(raw_result, str):
                            try:
                                raw_result.encode('utf-8')
                                if len(raw_result.strip()) > 0:
                                    result = raw_result
                                    logger.info(f"Stream extracted final_report, length: {len(result)}")
                                else:
                                    logger.warning("Stream final_report is empty after strip")
                            except UnicodeEncodeError as e:
                                logger.warning(f"Stream result has encoding issues: {e}")
                                try:
                                    result = raw_result.encode('utf-8', errors='replace').decode('utf-8')
                                    logger.info(f"Fixed stream encoding, result length: {len(result)}")
                                except Exception as encode_error:
                                    logger.error(f"Failed to fix stream encoding: {encode_error}")
                        else:
                            logger.warning(f"Stream final_report is invalid: type={type(raw_result)}, length={len(str(raw_result))}")
                    else:
                        logger.warning("No valid final_state from stream")
                        
                except Exception as stream_error:
                    logger.error(f"Stream processing failed: {stream_error}")
             
            # 增强的结果提取策略
            if not result or len(result.strip()) < 100:
                logger.warning(f"Primary extraction failed (length: {len(result)}), trying enhanced extraction...")
                
                # 尝试多种备用策略
                if final_state and isinstance(final_state, dict):
                    logger.debug("Attempting enhanced result extraction from final_state...")
                    
                    # 策略1：清理并重新检查final_report
                    if 'final_report' in final_state:
                        raw_final_report = final_state['final_report']
                        if raw_final_report and isinstance(raw_final_report, str):
                            cleaned_result = raw_final_report.strip()
                            if len(cleaned_result) >= 100:
                                result = cleaned_result
                                logger.info(f"Enhanced extraction: cleaned final_report, length: {len(result)}")
                    
                    # 策略2：从observations中提取最完整的内容
                    if (not result or len(result.strip()) < 100) and 'observations' in final_state:
                        observations = final_state.get('observations', [])
                        if observations:
                            # 查找最长的有效observation
                            valid_observations = [obs for obs in observations if obs and isinstance(obs, str) and len(obs.strip()) >= 100]
                            if valid_observations:
                                longest_observation = max(valid_observations, key=len)
                                result = longest_observation
                                logger.info(f"Enhanced extraction: longest observation, length: {len(result)}")
                            else:
                                # 合并所有observations
                                combined_obs = '\n\n'.join(str(obs) for obs in observations if obs)
                                if len(combined_obs.strip()) >= 100:
                                    result = combined_obs
                                    logger.info(f"Enhanced extraction: combined observations, length: {len(result)}")
                    
                    # 策略3：从messages提取最详细的内容
                    if (not result or len(result.strip()) < 100) and 'messages' in final_state:
                        messages = final_state.get('messages', [])
                        longest_content = ""
                        for message in reversed(messages):  # 从最新开始
                            content = ""
                            if hasattr(message, 'content'):
                                content = message.content
                            elif isinstance(message, dict) and 'content' in message:
                                content = message['content']
                            
                            if content and isinstance(content, str) and len(content.strip()) > len(longest_content):
                                longest_content = content.strip()
                        
                        if longest_content and len(longest_content) >= 100:
                            result = longest_content
                            logger.info(f"Enhanced extraction: longest message content, length: {len(result)}")
                
                # 策略4：使用收集的消息内容作为最后备用
                if (not result or len(result.strip()) < 100) and message_content:
                    # 查找最长的消息内容
                    longest_msg = max(message_content, key=len, default='')
                    if longest_msg and len(longest_msg.strip()) >= 100:
                        try:
                            longest_msg.encode('utf-8')
                            result = longest_msg
                            logger.info(f"Enhanced extraction: fallback message content, length: {len(result)}")
                        except UnicodeEncodeError as e:
                            logger.warning(f"Message content has encoding issues: {e}")
                            try:
                                result = longest_msg.encode('utf-8', errors='replace').decode('utf-8')
                                logger.info(f"Enhanced extraction: fixed fallback encoding, length: {len(result)}")
                            except Exception:
                                logger.error("Failed to fix fallback encoding")
            
            # 最终检查和错误报告
            if not result or len(result.strip()) < 100:
                logger.error("All extraction strategies failed")
                logger.error(f"Final state summary: {final_state.keys() if isinstance(final_state, dict) else 'Not a dict'}")
                
                if final_state and isinstance(final_state, dict):
                    for key, value in final_state.items():
                        if isinstance(value, str):
                            logger.error(f"  {key}: {len(value)} chars - {'[EMPTY]' if not value.strip() else value[:100]}")
                        elif isinstance(value, list):
                            logger.error(f"  {key}: {len(value)} items")
                        else:
                            logger.error(f"  {key}: {type(value)}")
                
                result = f"研究查询 '{query}' 已完成，但未能生成详细报告。请检查研究主题或稍后重试。"
                logger.warning("Used default message due to extraction failure")
            else:
                logger.info(f"Successfully extracted result, final length: {len(result)}")
            
            logger.info(f"Integrated research completed, result length: {len(result)}")
            return {"success": True, "data": result}
            
        except Exception as e:
            logger.error(f"Integrated research query failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _research_query_subprocess(self, query: str, **kwargs) -> Dict[str, Any]:
        """子进程模式下的研究查询（原有逻辑）"""
        try:
            payload = {
                "messages": [{"role": "user", "content": query}],
                "thread_id": "__default__",
                "resources": [],
                "max_plan_iterations": kwargs.get('max_plan_iterations', 1),
                "max_step_num": kwargs.get('max_step_num', 3),
                "max_search_results": 10,
                "auto_accepted_plan": kwargs.get('auto_accepted_plan', True),
                "interrupt_feedback": "",
                "mcp_settings": {},
                "enable_background_investigation": kwargs.get('enable_background_investigation', True),
                "report_style": "academic"
            }
            
            logger.info(f"Starting research query for: {query}")
            
            # 调用DeerFlow API
            result = await self._call_deer_flow_api("/api/chat/stream", payload, "stream")
            
            if result:
                logger.info(f"Research completed, result length: {len(result)}")
                return {"success": True, "data": result}
            else:
                return {"success": False, "error": "No result from DeerFlow API"}
                
        except Exception as e:
            logger.error(f"Subprocess research query failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def generate_content(self, research_data: str, content_type: str, **kwargs) -> Dict[str, Any]:
        """生成内容（播客、PPT、散文等）"""
        try:
            if not self.is_running:
                return {"success": False, "error": "DeerFlow service not running"}
            if self.mode == 'integrated':
                return await self._generate_content_integrated(research_data, content_type, **kwargs)
            else:
                return await self._generate_content_subprocess(research_data, content_type, **kwargs)
        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_content_integrated(self, research_data: str, content_type: str, **kwargs) -> Dict[str, Any]:
        """集成模式下的内容生成"""
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: [2025-01-19T10:00:00+08:00]
        # Reason: 修复 "graph not initialized" 错误，使用相应的生成器类而不是依赖未初始化的图形对象
        # Principle_Applied: SOLID - 依赖倒置原则，使用具体的生成器实现而不是抽象图形; DRY - 复用已有的生成器逻辑
        # Optimization: 直接使用已验证可用的生成器类，避免图形初始化问题
        # Architectural_Note (AR): 统一使用生成器类架构，保持代码一致性
        # Documentation_Note (DW): 解决集成模式下内容生成功能失效的问题
        # }}
        try:
            logger.info(f"Starting integrated {content_type} generation")
            
            # 使用对应的生成器类而不是图形对象
            if content_type == "podcast":
                from .generators.podcast_generator import PodcastGenerator
                generator = PodcastGenerator(self)
                
                # 根据请求参数决定生成播客音频还是脚本
                result = await generator._generate_podcast(research_data, **kwargs)
                    
            elif content_type == "ppt":
                from .generators.ppt_generator import PPTGenerator
                generator = PPTGenerator(self)
                result = await generator._generate_ppt(research_data, **kwargs)
                
            elif content_type == "prose":
                from .generators.prose_generator import ProseGenerator
                generator = ProseGenerator(self)
                result = await generator._generate_prose(research_data, **kwargs)

            else:
                raise ValueError(f"Unsupported content type: {content_type}")
            
            # 统一返回格式
            if isinstance(result, dict):
                if result.get('success', True):  # 默认成功，除非明确标记失败
                    logger.info(f"Integrated {content_type} generation completed")
                    #result是字典，怎么增加success字段
                    result["success"] = True
                    return result
                else:
                    error = result.get('error', 'Unknown error')
                    logger.error(f"Integrated {content_type} generation failed: {error}")
                    return {"success": False, "error": error}
            else:
                # 如果不是字典格式，直接作为数据返回
                logger.info(f"Integrated {content_type} generation completed")
                return {"success": True, "data": result}
            
        except Exception as e:
            logger.error(f"Integrated {content_type} generation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_content_subprocess(self, research_data: str, content_type: str, **kwargs) -> Dict[str, Any]:
        """子进程模式下的内容生成（原有逻辑）"""
        try:
            # 构建API endpoint
            endpoint_map = {
                "podcast": "/api/podcast/generate",
                "ppt": "/api/ppt/generate", 
                "prose": "/api/prose/generate"
            }
            
            endpoint = endpoint_map.get(content_type)
            if not endpoint:
                raise ValueError(f"Unsupported content type: {content_type}")
            
            # 构建payload - 修复字段名匹配问题
            if content_type == "podcast":
                # DeerFlow服务期望 'content' 字段，不是 'research_data'
                payload = {
                    "content": research_data
                }
            elif content_type == "ppt":
                # PPT可能期望不同的字段名，需要确认
                payload = {
                    "content": research_data
                }
            elif content_type == "prose":
                # 散文生成可能有不同的字段要求
                payload = {
                    "prompt": research_data,
                    "option": kwargs.get('style', 'professional'),
                    "command": kwargs.get('requirements', '')
                }
            else:
                # 默认使用content字段
                payload = {
                    "content": research_data
                }
            
            # 添加其他参数（如果提供）
            if content_type == "podcast":
                # 播客特定参数
                if kwargs.get('target_duration'):
                    payload["target_duration"] = kwargs['target_duration']
                if kwargs.get('voice_style'):
                    payload["voice_style"] = kwargs['voice_style']
            elif content_type == "ppt":
                # PPT特定参数
                if kwargs.get('template'):
                    payload["template"] = kwargs['template']
                if kwargs.get('slide_count'):
                    payload["slide_count"] = kwargs['slide_count']
            
            logger.info(f"Starting subprocess {content_type} generation with correct field names")
            
            # 调用DeerFlow API
            result = await self._call_deer_flow_api(endpoint, payload)
            
            if result and result.get('success'):
                logger.info(f"Subprocess {content_type} generation completed")
                return result
            else:
                error = result.get('error', 'Unknown error') if result else 'No response'
                return {"success": False, "error": error}
                
        except Exception as e:
            logger.error(f"Subprocess {content_type} generation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def generate_outline(self, research_data: str, **kwargs) -> Dict[str, Any]:
        """单独生成PPT大纲供用户编辑"""
        if not self.is_running:
            return {"success": False, "error": "DeerFlow服务未运行"}
            
        try:
            logger.info(f"生成PPT大纲，内容长度: {len(research_data)} 字符")
            
            # 使用PPT生成器来生成大纲
            from .generators.ppt_generator import PPTGenerator
            generator = PPTGenerator(self)
            outline_content = await generator._prepare_outline_for_template(research_data, **kwargs)
            
            if not outline_content or len(outline_content.strip()) < 50:
                # 如果生成失败，使用简单大纲
                outline_content = generator._create_simple_outline(research_data)
            
            logger.info(f"PPT大纲生成成功: {len(outline_content)} 字符")
            
            return {
                "success": True,
                "outline": outline_content,
                "message": "PPT大纲生成成功，您可以根据需要编辑后再生成PPT"
            }
            
        except Exception as e:
            logger.error(f"PPT大纲生成失败: {e}")
            return {
                "success": False, 
                "error": f"大纲生成失败: {str(e)}"
            }
    
    async def generate_podcast_script(self, research_data: str, **kwargs) -> Dict[str, Any]:
        """生成播客脚本"""
        if not self.is_running:
            return {"success": False, "error": "DeerFlow服务未运行"}
            
        try:
            # 使用播客生成器来生成脚本
            from .generators.podcast_generator import PodcastGenerator
            generator = PodcastGenerator(self)
            result = await generator.generate_podcast_script(research_data, **kwargs)
            
            return result
            
        except Exception as e:
            logger.error(f"播客脚本生成失败: {e}")
            return {
                "success": False, 
                "error": f"脚本生成失败: {str(e)}"
            }

    # {{CHENGQI:
    # Action: Added
    # Timestamp: 2025-06-22T15:00:00+08:00
    # Reason: 新增SVG内容生成服务方法，支持前端SVG+PPT功能，集成AIPresenterSVGGenerator
    # Principle_Applied: SOLID单一职责 - 专门处理SVG生成; DRY - 复用现有AIPresenterSVGGenerator
    # Optimization: 异步处理，支持进度查询，严格输入验证
    # Architectural_Note (AR): 集成现有SVG生成器，保持服务一致性
    # Documentation_Note (DW): 遵循现有服务方法设计模式
    # }}
    async def generate_svg_content(self, content: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成SVG内容数组
        
        Args:
            content: 用户输入的源文档内容
            config: SVG生成配置参数
            
        Returns:
            包含SVG页面数组的字典或错误信息
        """
        if not self.is_running:
            return {"success": False, "error": "DeerFlow服务未运行"}
            
        try:
            # 输入验证
            if not content or not content.strip():
                return {"success": False, "error": "输入内容不能为空"}
                
            if len(content) > 50 * 1024 * 1024:  # 50MB限制
                return {"success": False, "error": "输入内容过大，最大支持50MB"}
            
            # 默认配置
            svg_config = {
                "canvas_width": 1920,
                "canvas_height": 1080,
                "model": "gemini-2.5-flash",
                "temperature": 0.7,
                "enable_streaming": False,
                "custom_requirements": ""
            }
            
            # 合并用户配置
            if config:
                svg_config.update(config)
                
            logger.info(f"开始生成SVG内容，内容长度: {len(content)}字符，配置: {svg_config}")
            
            # 导入并初始化SVG生成器
            from backend.utils.svg_generator import AIPresenterSVGGenerator, GenerationConfig
            
            # 创建生成配置
            generation_config = GenerationConfig(
                model=svg_config.get("model", "gemini-2.5-flash"),
                temperature=svg_config.get("temperature", 0.7),
                canvas_width=svg_config.get("canvas_width", 1920),
                canvas_height=svg_config.get("canvas_height", 1080),
                enable_streaming=svg_config.get("enable_streaming", False)
            )
            
            # 创建SVG生成器实例
            svg_generator = AIPresenterSVGGenerator(generation_config)
            
            # 第一阶段：生成设计规范
            logger.info("第一阶段：生成设计规范")
            design_result = await svg_generator.generate_design_specification(
                content=content,
                custom_requirements=svg_config.get("custom_requirements", "")
            )
            
            if "error" in design_result:
                logger.error(f"设计规范生成失败: {design_result['error']}")
                return {"success": False, "error": f"设计规范生成失败: {design_result['error']}"}
            
            # 确认设计规范
            confirm_result = await svg_generator.confirm_design_specification()
            if "error" in confirm_result:
                logger.error(f"设计规范确认失败: {confirm_result['error']}")
                return {"success": False, "error": f"设计规范确认失败: {confirm_result['error']}"}
            
            # 第二阶段：生成所有SVG页面
            logger.info("第二阶段：生成SVG页面")
            pages_result = await svg_generator.generate_all_pages()
            
            if "error" in pages_result:
                logger.error(f"SVG页面生成失败: {pages_result['error']}")
                return {"success": False, "error": f"SVG页面生成失败: {pages_result['error']}"}
            
            # 获取生成的SVG页面数据
            svg_pages = svg_generator.get_all_pages_svg()
            
            if not svg_pages:
                logger.error("未生成任何SVG页面")
                return {"success": False, "error": "未生成任何SVG页面"}
            
            # 验证和清理SVG内容
            validated_pages = []
            for page in svg_pages:
                try:
                    if page.get("svg_code"):
                        # 使用生成器内置的验证方法
                        cleaned_svg = svg_generator._validate_and_clean_svg(page["svg_code"])
                        page["svg_code"] = cleaned_svg
                        validated_pages.append(page)
                except Exception as e:
                    logger.warning(f"页面 {page.get('page_number', '未知')} SVG验证失败: {e}")
                    continue
            
            if not validated_pages:
                return {"success": False, "error": "所有SVG页面验证失败"}
            
            # 构建成功响应
            response_data = {
                "svg_pages": validated_pages,
                "design_spec": {
                    "total_pages": len(validated_pages),
                    "theme": svg_generator.design_spec.theme_selection.get("name", "professional") if svg_generator.design_spec else "professional",
                    "color_palette": svg_generator.design_spec.color_palette if svg_generator.design_spec else {},
                    "canvas_size": {
                        "width": svg_config["canvas_width"],
                        "height": svg_config["canvas_height"]
                    }
                },
                "generation_id": f"svg_{int(asyncio.get_event_loop().time())}",
                "status": "completed"
            }
            
            logger.info(f"SVG内容生成成功，共生成 {len(validated_pages)} 个页面")
            
            return {
                "success": True,
                "data": response_data,
                "message": "SVG内容生成成功"
            }
            
        except ImportError as e:
            logger.error(f"SVG生成器导入失败: {e}")
            return {"success": False, "error": "SVG生成功能不可用，请检查依赖安装"}
        except Exception as e:
            logger.error(f"SVG内容生成失败: {e}", exc_info=True)
            return {"success": False, "error": f"SVG生成失败: {str(e)}"}
    
    async def _process_stream_response(self, response) -> str:
        """处理流式响应 - 增强版本，支持多种响应格式"""
        result_parts = []
        events_received = 0
        chunk_count = 0
        logger.info(f"Processing stream response with robust error handling")
        
        try:
            # 核心逻辑：使用iter_chunked读取，并准备好处理中断
            logger.info("Attempting chunked stream reading...")
            
            async for chunk in response.content.iter_chunked(1024):
                chunk_str = chunk.decode('utf-8', errors='ignore').strip()
                chunk_count += 1
                
                # 只在debug级别记录详细chunk信息，避免循环日志
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(f"Chunk {chunk_count}: {len(chunk_str)} bytes")
                # 每50个chunk记录一次进度，避免过度日志
                elif chunk_count % 50 == 0:
                    logger.info(f"Processed {chunk_count} chunks, {len(result_parts)} content parts")
                    
                if not chunk_str:
                    continue
                    
                lines = chunk_str.split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith('data: '):
                        try:
                            data_str = line[6:]
                            if data_str and data_str != '[DONE]':
                                data = json.loads(data_str)
                                if data.get('content'):
                                    result_parts.append(data['content'])
                                    # 只在debug级别记录内容详情
                                    if logger.isEnabledFor(logging.DEBUG):
                                        logger.debug(f"Received content chunk: {data['content'][:100]}...")
                        except json.JSONDecodeError as e:
                            if logger.isEnabledFor(logging.DEBUG):
                                logger.debug(f"JSON decode error for line: {line[:100]}, error: {e}")
                            continue
                    elif line.startswith('event: '):
                        events_received += 1
                        event_type = line[7:]
                        if logger.isEnabledFor(logging.DEBUG):
                            logger.debug(f"Received event: {event_type}")
                        continue
                        
        except Exception as stream_error:
            # 关键修复：当流中断时（例如TransferEncodingError），不再尝试读取。
            # 只记录警告，然后继续处理已经收到的部分。
            logger.warning(f"Stream reading was interrupted (type: {type(stream_error).__name__}). Processing {len(result_parts)} received parts.")

        # 处理结果（无论流是否被中断）
        if result_parts:
            final_result = ''.join(result_parts)
            logger.info(f"Successfully processed stream response: {len(final_result)} characters, {chunk_count} chunks, {events_received} events")
            return final_result
        else:
            if events_received > 0:
                logger.info(f"No content but {events_received} events were processed, query likely succeeded")
                return "研究查询已提交并开始处理，由于网络问题无法显示完整结果，请稍后查看"
            else:
                logger.warning(f"No content received from stream response after {chunk_count} chunks")
                return "研究查询已完成，但未收到内容。请检查网络连接或稍后重试。"
    
    async def _call_deer_flow_api(self, endpoint: str, payload: Dict[str, Any], response_type: str = "json") -> Any:
        """通用的deer-flow API调用方法"""
        # {{CHENGQI:
        # Action: Added
        # Timestamp: [2025-06-09T10:00:39+08:00]
        # Reason: 实现通用HTTP客户端方法，支持不同响应类型，应用DRY原则
        # Principle_Applied: DRY - 避免重复HTTP调用代码; 单一职责 - 专门处理HTTP通信; 开闭原则 - 易于扩展新的API端点
        # Optimization: 统一超时控制、错误处理、安全验证
        # Architectural_Note (AR): 抽象HTTP调用层，便于维护和测试
        # Documentation_Note (DW): 详细文档化API调用参数和返回值
        # }}
        if not self.is_running:
            raise Exception("DeerFlow service is not running")
            
        url = f"http://127.0.0.1:{self.port}{endpoint}"
        timeout = aiohttp.ClientTimeout(total=300)  # 5分钟超时
        
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API call failed with status {response.status}: {error_text}")
                    
                    if response_type == "binary":
                        # 处理二进制文件响应
                        content = await response.read()
                        content_type = response.headers.get('content-type', 'application/octet-stream')
                        return {"data": content, "content_type": content_type}
                    elif response_type == "stream":
                        # 处理流式响应
                        return await self._process_stream_response(response)
                    else:
                        # 默认JSON响应
                        return await response.json()
                        
        except asyncio.TimeoutError:
            raise Exception("API call timed out")
        except Exception as e:
            logger.error(f"Failed to call deer-flow API {endpoint}: {e}")
            raise
    
    def _load_tavily_key_from_config(self) -> Optional[str]:
        """
        已弃用：从deer-flow配置文件加载Tavily API密钥
        现在完全使用 KeyManager 获取最新的 TAVILY_API_KEY
        """
        # 不再使用配置文件加载，完全依赖 KeyManager
        logger.warning("_load_tavily_key_from_config is deprecated, use KeyManager instead")
        return None

class DeerFlowPlugin(IWorkflowPlugin):
    """DeerFlow深度研究插件实现"""
    
    def __init__(self):
        super().__init__()
        self.plugin_id = "deer_flow"
        self.version = "1.0.0"
        self.service = None
        self.tasks: Dict[str, asyncio.Task] = {} # 初始化任务存储
        self.task_results: Dict[str, Dict[str, Any]] = {} # 初始化任务结果存储
        
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: [2025-06-09T10:30:00+08:00]
        # Reason: 修改插件初始化策略，服务启动失败时仍允许插件初始化成功但处于非活动状态
        # Principle_Applied: 健壮性 - 优雅降级; 用户体验 - 不阻塞主应用; 单一职责 - 初始化与服务启动分离
        # Optimization: 插件初始化成功，服务状态独立管理
        # Architectural_Note (AR): 保持插件可用性，提供清晰的状态反馈
        # Documentation_Note (DW): 记录初始化和服务启动的分离策略
        # }}
        try:
            self.config = config
            self.service = DeerFlowService()
            
            # 尝试启动DeerFlow服务，但不让启动失败阻塞插件初始化
            try:
                await self.service.start()
                self.is_initialized = True
                self.set_status(PluginStatus.RUNNING)
                logger.info("DeerFlow插件初始化成功，服务正常运行")
            except Exception as service_error:
                # 服务启动失败，但插件可以处于非活动状态
                self.is_initialized = True  # 插件本身初始化成功
                self.set_status(PluginStatus.ERROR)  # 但服务状态为错误
                logger.warning(f"DeerFlow服务启动失败，插件将以非活动状态运行: {service_error}")
                logger.info("插件已初始化，用户可查看状态和重试启动服务")
            
            return True  # 总是返回True，允许插件加载
            
        except Exception as e:
            # 只有在插件本身初始化失败时才返回False
            logger.error(f"DeerFlow插件初始化失败: {e}")
            self.set_status(PluginStatus.ERROR)
            return False
    
    async def execute(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """执行插件功能"""
        if not self.is_initialized:
            return {
                "success": False,
                "error": "插件未初始化"
            }
        
        action = request.get("action", "research")
        
        # 特殊处理状态查询，即使服务未运行也要返回状态信息
        if action == "status":
            return {
                "success": True,
                "data": {
                    "service_running": self.service.is_running if self.service else False,
                    "plugin_status": self.status.value,
                    "deer_flow_path": str(self.service.deer_flow_path) if self.service and self.service.deer_flow_path else None
                }
            }
        
        # 检查服务是否运行
        if not self.service or not self.service.is_running:
            return {
                "success": False,
                "error": "DeerFlow服务未运行。请检查依赖安装：pip install -r requirements_deer_flow.txt",
                "service_status": "stopped",
                "suggestion": "检查控制台日志以获取详细错误信息，或尝试重启应用"
            }
        
        try:
            if action == "research":
                query = request.get("query", "")
                if not query:
                    return {"success": False, "error": "查询内容不能为空"}
                
                task_id = str(uuid.uuid4())
                logger.info(f"创建研究任务: {task_id} for query: {query}")

                # 启动研究任务
                # 使用一个包装函数来在任务完成时存储结果
                async def run_research_task():
                    try:
                        result = await self.service.research_query(
                            query=query,
                            max_plan_iterations=request.get("max_plan_iterations", 1),
                            max_step_num=request.get("max_step_num", 3),
                            auto_accepted_plan=request.get("auto_accepted_plan", True),
                            enable_background_investigation=request.get("enable_background_investigation", True)
                        )
                        logger.info(f"研究任务 {task_id} 结果: {result}")
                        # 如果研究成功，尝试提取图片信息
                        if result.get("success", False) and result.get("data"):
                            try:
                                # 导入图片提取功能
                                from backend.search.tavily import extract_images_from_result, append_extracted_images_to_result
                                
                                logger.info(f"研究任务 {task_id} 开始提取图片信息")
                                
                                # 提取图片信息
                                image_extraction_result = await extract_images_from_result(result["data"])
                                
                                if image_extraction_result.get("success", False):
                                    # 如果成功提取到图片，更新结果
                                    if image_extraction_result.get("extracted_images"):
                                        # 使用处理过的结果（包含图片描述）
                                        enhanced_content = append_extracted_images_to_result(
                                            image_extraction_result.get("processed_result", result["data"]),
                                            image_extraction_result["extracted_images"]
                                        )
                                        
                                        # 更新研究结果
                                        result["data"] = enhanced_content
                                        result["image_info"] = {
                                            "extracted_images": image_extraction_result["extracted_images"],
                                            "stats": image_extraction_result.get("stats", {})
                                        }
                                        stats = image_extraction_result.get("stats", {})
                                        qr_filtered = stats.get("qr_filtered_count", 0)
                                        if qr_filtered > 0:
                                            logger.info(f"研究任务 {task_id} 成功提取 {len(image_extraction_result['extracted_images'])} 张图片，过滤掉 {qr_filtered} 个二维码")
                                        else:
                                            logger.info(f"研究任务 {task_id} 成功提取 {len(image_extraction_result['extracted_images'])} 张图片")
                                    else:
                                        logger.info(f"研究任务 {task_id} 未找到可提取的图片")
                                else:
                                    logger.warning(f"研究任务 {task_id} 图片提取失败: {image_extraction_result.get('error', '未知错误')}")
                                    
                            except Exception as e:
                                logger.warning(f"研究任务 {task_id} 图片提取过程中出错: {str(e)}")
                                # 图片提取失败不影响主要研究结果
                                pass

                        self.task_results[task_id] = result
                        logger.info(f"研究任务 {task_id} 执行成功")
                    except Exception as e:
                        logger.error(f"研究任务 {task_id} 执行失败: {e}")
                        self.task_results[task_id] = {"success": False, "error": str(e)}

                task = asyncio.create_task(run_research_task())
                self.tasks[task_id] = task

                return {"success": True, "task_id": task_id, "status": "pending"}
                
            elif action == "generate_content":
                # {{CHENGQI:
                # Action: Modified
                # Timestamp: [2025-01-19T15:30:00+08:00] 
                # Reason: 为播客生成添加异步任务模式，参考深度研究的实现，解决生成时间长和重复点击问题
                # Principle_Applied: 一致性 - 与研究查询使用相同的异步模式; 用户体验 - 防止重复点击和长时间等待
                # Optimization: 播客生成使用异步任务，其他内容类型保持同步处理
                # Architectural_Note (AR): 统一异步任务处理模式，提升系统响应性
                # Documentation_Note (DW): 实现播客生成的异步任务功能
                # }}
                content_type = request.get("content_type", "prose")
                
                # 播客生成使用异步任务模式
                if content_type == "podcast":
                    if not self.service or not self.service.is_running:
                        return {
                            "success": False,
                            "error": "DeerFlow服务未运行。请检查依赖安装：pip install -r requirements_deer_flow.txt",
                            "service_status": "stopped",
                            "suggestion": "检查控制台日志以获取详细错误信息，或尝试重启应用"
                        }

                    research_data = request.get("research_data", "")
                    if not research_data:
                        return {"success": False, "error": "研究数据不能为空"}

                    task_id = str(uuid.uuid4())
                    logger.info(f"创建播客生成任务: {task_id}")

                    # 启动播客生成任务
                    async def run_podcast_task():
                        try:
                            result = await self.service.generate_content(
                                research_data=research_data,
                                content_type=content_type,
                                **request.get("options", {}),
                                **{k: v for k, v in request.items() if k not in ["research_data", "content_type", "options"]}
                            )
                            self.task_results[task_id] = result
                            logger.info(f"播客生成任务 {task_id} 执行成功")
                        except Exception as e:
                            logger.error(f"播客生成任务 {task_id} 执行失败: {e}")
                            self.task_results[task_id] = {"success": False, "error": str(e)}

                    task = asyncio.create_task(run_podcast_task())
                    self.tasks[task_id] = task

                    return {"success": True, "task_id": task_id, "status": "pending"}
                
                # 其他内容类型保持原有的同步处理方式
                else:
                    return await self.execute({"action": "generate_content", **request})
                
            elif action == "get_templates":
                # 获取PPT模板列表
                from .generators.ppt_generator import PPTGenerator
                generator = PPTGenerator(self.service)
                result = await generator.get_ppt_templates(**request.get("options", {}))
                return result
                
            elif action == "generate_outline":
                # 生成PPT大纲
                # {{CHENGQI:
                # Action: Added
                # Timestamp: [2025-01-16T16:15:00+08:00]
                # Reason: 在execute方法中添加generate_outline action的处理逻辑
                # Principle_Applied: 单一职责 - 专门处理大纲生成逻辑; DRY - 复用已有的业务逻辑
                # Optimization: 验证输入参数，调用业务逻辑方法
                # Architectural_Note (AR): 完善API功能，支持大纲生成
                # Documentation_Note (DW): 新增大纲生成业务逻辑处理
                # }}
                research_data = request.get("research_data", "")
                if not research_data:
                    return {"success": False, "error": "研究数据不能为空"}
                
                result = await self.service.generate_outline(
                    research_data=research_data,
                    **request.get("options", {})
                )
                return result
                
            elif action == "generate_podcast_script":
                # {{CHENGQI:
                # Action: Modified
                # Timestamp: [2025-01-19T17:15:00+08:00]
                # Reason: 修复脚本生成异步任务的严重错误：正确存储任务和结果，修复任务ID不存在问题
                # Principle_Applied: 一致性 - 与研究查询和播客音频生成使用相同的任务管理模式; 数据一致性 - 正确分离tasks和task_results
                # Optimization: 修复任务存储逻辑，确保任务轮询正常工作
                # Architectural_Note (AR): 统一异步任务处理模式，修复任务管理缺陷
                # Documentation_Note (DW): 解决任务ID不存在的核心问题
                # }}
                research_data = request.get("research_data", "")
                if not research_data:
                    return {"success": False, "error": "研究数据不能为空"}
                
                # 创建异步任务
                task_id = str(uuid.uuid4())
                logger.info(f"创建脚本生成任务: {task_id}")
                
                async def run_script_task():
                    try:
                        result = await self.service.generate_podcast_script(
                            research_data=research_data,
                            **request.get("options", {})
                        )
                        # 修复：存储到task_results而不是tasks
                        self.task_results[task_id] = result
                        logger.info(f"脚本生成任务完成: {task_id}")
                    except Exception as e:
                        logger.error(f"脚本生成任务失败: {task_id}, 错误: {e}")
                        # 修复：存储到task_results而不是tasks
                        self.task_results[task_id] = {"success": False, "error": str(e)}
                
                # 修复：创建并存储任务对象到tasks
                task = asyncio.create_task(run_script_task())
                self.tasks[task_id] = task
                
                return {"success": True, "task_id": task_id, "message": "脚本生成任务已启动"}
                
            elif action == "generate_svg_content":
                # {{CHENGQI:
                # Action: Modified
                # Timestamp: 2025-06-22T15:15:00+08:00
                # Reason: 修复递归调用问题，直接调用service层的generate_svg_content方法
                # Principle_Applied: SOLID单一职责 - 直接调用业务逻辑; KISS - 简化调用链
                # Optimization: 避免递归调用，提高性能和稳定性
                # Architectural_Note (AR): 修复API调用逻辑，确保正确的数据流
                # Documentation_Note (DW): 解决execute方法中的递归调用问题
                # }}
                content = request.get("content", "")
                if not content:
                    return {"success": False, "error": "内容不能为空"}
                
                config = request.get("config", {})
                result = await self.service.generate_svg_content(
                    content=content,
                    config=config
                )
                return result
                
            elif action == "task_result":
                return await self._handle_get_task_result(request)
                
            else:
                return {"success": False, "error": f"未知操作: {action}"}
                
        except Exception as e:
            logger.error(f"执行插件功能失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup(self) -> bool:
        """清理插件资源 - 方案3架构改进版本"""
        try:
            logger.info("Starting DeerFlow plugin cleanup with enhanced monitoring...")
            
            if self.service:
                # 额外的诊断信息
                if hasattr(self.service, '_process_output_buffer') and self.service._process_output_buffer:
                    logger.info(f"Plugin cleanup: clearing {len(self.service._process_output_buffer)} buffered output lines")
                
                await self.service.stop()
                
            self.is_initialized = False
            self.set_status(PluginStatus.UNLOADED)
            logger.info("DeerFlow插件资源清理完成 (Enhanced)")
            return True
        except Exception as e:
            logger.error(f"DeerFlow插件清理失败: {e}")
            return False
    
    def get_api_routes(self) -> List[Dict[str, Any]]:
        """获取API路由定义"""
        return [
            {
                "path": "/api/plugins/deer_flow/research",
                "methods": ["POST"],
                "handler": self._handle_research,
                "description": "深度研究查询接口"
            },
            {
                "path": "/api/plugins/deer_flow/generate_content",
                "methods": ["POST"],
                "handler": self._handle_generate_content,
                "description": "内容生成接口"
            },
            {
                "path": "/api/plugins/deer_flow/generate_outline",
                "methods": ["POST"],
                "handler": self._handle_generate_outline,
                "description": "生成PPT大纲接口"
            },
            {
                "path": "/api/plugins/deer_flow/generate_podcast_script",
                "methods": ["POST"],
                "handler": self._handle_generate_podcast_script,
                "description": "生成播客脚本接口"
            },
            {
                "path": "/api/plugins/deer_flow/task_result", # 新增任务结果查询接口
                "methods": ["POST"],
                "handler": self._handle_get_task_result,
                "description": "获取异步任务结果接口"
            },
            {
                "path": "/api/plugins/deer_flow/status",
                "methods": ["GET"],
                "handler": self._handle_status,
                "description": "获取服务状态接口"
            },
            {
                "path": "/api/plugins/deer_flow/cancel",
                "methods": ["POST"],
                "handler": self._handle_cancel,
                "description": "取消任务接口"
            },
            {
                "path": "/api/plugins/deer_flow/settings",
                "methods": ["GET", "POST"],
                "handler": self._handle_settings,
                "description": "插件设置接口"
            },
            {
                "path": "/api/plugins/deer_flow/voices",
                "methods": ["GET"],
                "handler": self._handle_get_voices,
                "description": "获取可用语音ID接口"
            },
            # {{CHENGQI:
            # Action: Added
            # Timestamp: 2025-01-17 10:38:00 +08:00
            # Reason: 添加清除voices缓存的API端点，允许用户手动刷新语音列表
            # Principle_Applied: YAGNI - 只在需要时添加功能; Single Responsibility - 独立的缓存清理功能
            # Optimization: 提供用户控制缓存的能力，增强用户体验
            # }}
            {
                "path": "/api/plugins/deer_flow/voices/clear_cache",
                "methods": ["POST"],
                "handler": self._handle_clear_voices_cache,
                "description": "清除语音缓存接口"
            },
            {
                "path": "/api/plugins/deer_flow/templates",
                "methods": ["GET"],
                "handler": self._handle_get_templates,
                "description": "获取PPT模板列表接口"
            },
            {
                "path": "/api/plugins/deer_flow/save_file",
                "methods": ["POST"],
                "handler": self._handle_save_file,
                "description": "文件保存接口"
            },
            # {{CHENGQI:
            # Action: Added
            # Timestamp: 2025-06-22T15:15:00+08:00
            # Reason: 添加SVG内容生成API路由，支持前端SVG+PPT功能
            # Principle_Applied: SOLID单一职责 - 专门的SVG生成端点; API一致性 - 保持统一的路由格式
            # Optimization: 独立的API端点，便于前端调用和测试
            # Architectural_Note (AR): 扩展API路由，支持SVG内容生成
            # Documentation_Note (DW): 新增SVG内容生成API路由配置
            # }}
            {
                "path": "/api/plugins/deer_flow/generate_svg_content",
                "methods": ["POST"],
                "handler": self._handle_generate_svg_content,
                "description": "SVG内容生成接口"
            }
        ]
    
    def get_frontend_config(self) -> Dict[str, Any]:
        """获取前端配置"""
        return {
            "component": "DeerFlow",
            "props": {
                "pluginId": self.plugin_id,
                "version": self.version,
                "status": self.status.value,
                "config": self.config
            },
            "routes": [
                {
                    "path": "/deer-flow/research",
                    "component": "DeerFlowResearch",
                    "title": "深度研究"
                },
                {
                    "path": "/deer-flow/content",
                    "component": "DeerFlowContent",
                    "title": "内容生成"
                },
                {
                    "path": "/deer-flow/settings",
                    "component": "DeerFlowSettings",
                    "title": "设置"
                }
            ],
            "menu": {
                "label": "深度研究",
                "icon": "search",
                "order": 1
            },
            "workflow_apps": [
                {
                    "id": "deer_flow_research",
                    "name": "深度研究",
                    "description": "使用DeerFlow进行深度研究和报告生成",
                    "icon": "search",
                    "component": "DeerFlowResearch"
                },
                {
                    "id": "deer_flow_content",
                    "name": "内容生成",
                    "description": "生成播客、PPT、散文等多媒体内容",
                    "icon": "file-text",
                    "component": "DeerFlowContent"
                }
            ]
        }
    
    async def _handle_research(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理研究查询请求，以任务形式异步执行"""
        # 直接调用execute方法中的完整实现，避免重复代码
        return await self.execute({"action": "research", **request})
    
    async def _handle_generate_content(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理内容生成请求"""
        return await self.execute({"action": "generate_content", **request})
    
    async def _handle_generate_outline(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理生成PPT大纲请求"""
        # {{CHENGQI:
        # Action: Added
        # Timestamp: [2025-01-16T16:10:00+08:00]
        # Reason: 添加处理生成PPT大纲请求的API处理方法
        # Principle_Applied: 单一职责 - 专门处理大纲生成请求; API一致性 - 保持与其他处理方法一致的格式
        # Optimization: 使用统一的execute方法调用业务逻辑
        # Architectural_Note (AR): 扩展API功能，支持大纲生成
        # Documentation_Note (DW): 新增大纲生成API端点
        # }}
        return await self.execute({"action": "generate_outline", **request})
    
    async def _handle_generate_podcast_script(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理生成播客脚本请求"""
        # {{CHENGQI:
        # Action: Added
        # Timestamp: [2025-01-17T16:00:00+08:00]
        # Reason: 添加处理生成播客脚本请求的API处理方法
        # Principle_Applied: 单一职责 - 专门处理脚本生成请求; API一致性 - 保持与其他处理方法一致的格式
        # Optimization: 使用统一的execute方法调用业务逻辑
        # Architectural_Note (AR): 扩展API功能，支持播客脚本生成
        # Documentation_Note (DW): 新增播客脚本生成API端点
        # }}
        return await self.execute({"action": "generate_podcast_script", **request})
    
    async def _handle_status(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理状态查询请求"""
        try:
            if not self.service:
                return {
                    "success": True,
                    "data": {
                        "is_running": False,
                        "mode": None,
                        "error": "Service not initialized"
                    }
                }
            
            # 获取详细的服务状态
            status = self.service.get_status()
            
            # 添加插件级别的状态信息
            status.update({
                "plugin_status": self.status.value,
                "plugin_initialized": self.is_initialized,
                "version": self.version,
                "is_packaged": _is_packaged_environment()
            })
            
            return {
                "success": True,
                "data": status
            }
            
        except Exception as e:
            logger.error(f"Failed to get status: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {
                    "is_running": False,
                    "mode": None,
                    "error": str(e)
                }
            }
    
    async def _handle_cancel(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理取消任务请求"""
        return await self.execute({"action": "cancel", **request})
    
    async def _handle_settings(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理设置请求"""
        method = request.get("method", "GET")
        
        if method == "GET":
            # 返回当前设置
            return {
                "success": True,
                "data": self.config
            }
        elif method == "POST":
            # 更新设置
            new_settings = request.get("settings", {})
            self.config.update(new_settings)
            return {
                "success": True,
                "message": "设置已更新"
            }
        else:
            return {"success": False, "error": f"不支持的方法: {method}"}
    
    async def _handle_get_voices(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取可用语音请求"""
        try:
            # {{CHENGQI:
            # Action: Added
            # Timestamp: 2025-01-17 10:35:00 +08:00
            # Reason: 添加voices.json本地缓存功能，避免每次都调用MiniMax API，提升性能和用户体验
            # Principle_Applied: DRY - 避免重复API调用; High Cohesion - 缓存逻辑内聚在同一方法中
            # Optimization: 添加24小时缓存过期机制，平衡数据新鲜度和性能
            # Architectural_Note (AR): 使用用户数据目录存储缓存文件，符合应用数据管理架构
            # Documentation_Note (DW): 添加详细的缓存逻辑说明和错误处理
            # }}
            import os
            import json
            from pathlib import Path
            from datetime import datetime, timedelta
            
            # 获取用户数据目录
            from backend.core.storage_manager import StorageManager
            storage_manager = StorageManager()
            user_data_dir = Path(storage_manager._get_app_data_dir())
            voices_cache_file = user_data_dir / "voices.json"
            
            # 检查缓存文件是否存在且有效（24小时内）
            if voices_cache_file.exists():
                try:
                    with open(voices_cache_file, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    
                    # 检查缓存时间
                    cache_time = datetime.fromisoformat(cache_data.get('timestamp', ''))
                    if datetime.now() - cache_time < timedelta(hours=24):
                        logger.info("使用缓存的语音列表数据")
                        return {
                            "success": True,
                            "data": cache_data.get('voices', []),
                            "cached": True
                        }
                    else:
                        logger.info("缓存已过期，重新获取语音列表")
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    logger.warning(f"读取语音缓存失败: {e}，重新获取")
            
            # 检查MiniMax TTS客户端是否可用
            if not MINIMAX_AVAILABLE or MiniMaxTTSClient is None:
                return {
                    "success": False,
                    "error": "MiniMax TTS服务不可用，可能是打包环境中缺少依赖"
                }
            
            # 获取MiniMax API密钥
            minimax = await KeyManager().get_key_from_api("mimax")
            if not minimax:
                return {
                    "success": False,
                    "error": "MiniMax API密钥未配置"
                }
            
            # 解析API密钥
            minimax_api_key = minimax.split("----")[0]
            minimax_groups_id = minimax.split("----")[1]
            
            # 创建MiniMax TTS客户端
            tts_client = MiniMaxTTSClient(api_key=minimax_api_key, group_id=minimax_groups_id)
            
            # 获取可用语音
            voices = await tts_client.get_available_voices()
            
            # 保存到缓存文件
            try:
                # 确保目录存在
                user_data_dir.mkdir(parents=True, exist_ok=True)
                
                cache_data = {
                    'timestamp': datetime.now().isoformat(),
                    'voices': voices
                }
                
                with open(voices_cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"语音列表已缓存到: {voices_cache_file}")
            except Exception as cache_error:
                logger.warning(f"保存语音缓存失败: {cache_error}")
            
            return {
                "success": True,
                "data": voices,
                "cached": False
            }
            
        except Exception as e:
            logger.error(f"Failed to get available voices: {e}")
            return {
                "success": False,
                "error": f"获取语音列表失败: {str(e)}"
            }
    
    async def _handle_get_templates(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取PPT模板列表请求"""
        return await self.execute({"action": "get_templates", "options": request}) 
    
    async def _handle_save_file(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理文件保存请求，使用Tkinter文件对话框"""
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: [2025-01-17T21:35:00+08:00]
        # Reason: 修复音频文件保存逻辑，正确处理audio_base64编码的复杂音频数据结构
        # Principle_Applied: 数据完整性 - 正确解析复杂数据结构; KISS - 简化解码逻辑
        # Optimization: 区分不同编码类型的处理方式，确保音频数据完整性
        # Architectural_Note (AR): 增强后端文件保存功能，支持复杂音频数据格式
        # Documentation_Note (DW): 解决音频下载损坏问题的后端修复
        # }}
        try:
            # 获取请求参数
            filename = request.get("filename", "download")
            data = request.get("data", "")
            content_type = request.get("content_type", "application/octet-stream")
            encoding = request.get("encoding", "base64")
            
            logger.info(f"处理文件保存请求: {filename}, 类型: {content_type}, 编码: {encoding}")
            
            if not data:
                return {"success": False, "error": "没有提供文件数据"}
            
            # 解码文件数据
            try:
                if encoding == "audio_base64":
                    # {{CHENGQI:
                    # Action: Modified
                    # Timestamp: 2025-06-14T02:12:41+08:00
                    # Reason: 修复音频下载失败问题，简化audio_base64处理逻辑，与前端发送的数据格式匹配
                    # Principle_Applied: KISS - 移除不必要的复杂处理; 数据匹配 - 与前端数据格式一致
                    # Optimization: 直接base64解码，提高可靠性和性能
                    # Architectural_Note (AR): 简化音频数据处理逻辑，增强系统稳定性
                    # Documentation_Note (DW): 解决用户报告的音频下载UTF-8解码失败问题
                    # }}
                    # 音频数据：直接base64解码（前端发送的是标准base64编码的音频数据）
                    logger.info("处理音频base64数据，直接解码")
                    file_bytes = base64.b64decode(data)
                    logger.info(f"音频base64解码完成，大小: {len(file_bytes)} bytes")
                    
                elif encoding == "base64":
                    # 标准base64解码
                    file_bytes = base64.b64decode(data)
                    logger.info(f"标准base64解码完成，大小: {len(file_bytes)} bytes")
                else:
                    # 假设是文本数据
                    file_bytes = data.encode('utf-8')
                    logger.info(f"文本编码完成，大小: {len(file_bytes)} bytes")
                
            except Exception as decode_error:
                logger.error(f"文件数据解码失败: {decode_error}")
                return {"success": False, "error": f"文件数据解码失败: {str(decode_error)}"}
            
            # 使用webview文件对话框保存文件
            try:
                import webview
                
                # 根据文件类型确定文件扩展名和对话框选项
                file_types = self._get_file_types_for_content_type(content_type, filename)
                
                logger.info(f"显示文件保存对话框，文件类型: {file_types}")
                
                # 尝试使用webview文件对话框
                result = None
                try:
                    # 方法1：使用静态方法
                    result = webview.create_file_dialog(
                        webview.SAVE_DIALOG,
                        save_filename=filename,
                        file_types=file_types
                    )
                except Exception as dialog_error:
                    logger.warning(f"静态文件对话框失败: {dialog_error}")
                    # 方法2：使用窗口实例
                    if webview.windows and len(webview.windows) > 0:
                        result = webview.windows[0].create_file_dialog(
                            webview.SAVE_DIALOG,
                            save_filename=filename,
                            file_types=file_types
                        )
                    else:
                        raise Exception("无可用的webview窗口实例")
                
                logger.info(f"文件对话框返回结果: {result}")
                
                if result and len(result) > 0:
                    save_path = result[0] if isinstance(result, (list, tuple)) else result
                    
                    if save_path:
                        # 确保目录存在
                        from pathlib import Path
                        save_dir = Path(save_path).parent
                        save_dir.mkdir(parents=True, exist_ok=True)
                        
                        # 写入文件
                        with open(save_path, 'wb') as f:
                            f.write(file_bytes)
                        
                        # 验证文件是否成功写入
                        if Path(save_path).exists():
                            file_size = Path(save_path).stat().st_size
                            logger.info(f"文件已成功保存到: {save_path}，文件大小: {file_size} 字节")
                            return {
                                "success": True,
                                "path": save_path,
                                "size": file_size,
                                "message": f"文件已保存到: {Path(save_path).name}"
                            }
                        else:
                            return {"success": False, "error": "文件保存后不存在"}
                    else:
                        return {"success": False, "error": "文件对话框返回了空路径"}
                else:
                    return {"success": False, "error": "用户取消保存"}
                    
            except Exception as webview_error:
                logger.error(f"webview文件对话框失败: {webview_error}")
                return {"success": False, "error": f"文件对话框创建失败: {str(webview_error)}"}
            
        except Exception as e:
            logger.error(f"文件保存过程中出错: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {"success": False, "error": f"保存文件失败: {str(e)}"}
    
    def _get_file_types_for_content_type(self, content_type: str, filename: str) -> tuple:
        """根据内容类型获取文件对话框的文件类型选项"""
        # 从文件名推断扩展名
        ext = ""
        if "." in filename:
            ext = filename.split(".")[-1].lower()
        
        # 根据内容类型和扩展名确定文件类型选项
        if content_type.startswith("audio/") or ext in ["mp3", "wav", "m4a", "aac"]:
            return ('音频文件 (*.mp3;*.wav;*.m4a;*.aac)', '所有文件 (*.*)')
        elif content_type.startswith("application/vnd.openxmlformats-officedocument.presentationml") or ext == "pptx":
            return ('PowerPoint文件 (*.pptx)', '所有文件 (*.*)')
        elif content_type.startswith("text/") or ext in ["txt", "md", "doc", "docx"]:
            return ('文本文件 (*.txt;*.md;*.doc;*.docx)', '所有文件 (*.*)')
        elif content_type.startswith("application/pdf") or ext == "pdf":
            return ('PDF文件 (*.pdf)', '所有文件 (*.*)')
        else:
            return ('所有文件 (*.*)',)
    
    async def _handle_clear_voices_cache(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理清除语音缓存请求"""
        # {{CHENGQI:
        # Action: Added
        # Timestamp: 2025-01-17 10:40:00 +08:00
        # Reason: 实现清除voices缓存功能，允许用户强制刷新语音列表
        # Principle_Applied: Single Responsibility - 专门负责缓存清理的方法
        # Optimization: 提供灵活的缓存管理，用户可以按需刷新数据
        # }}
        try:
            import os
            from pathlib import Path
            
            # 获取用户数据目录
            from backend.core.storage_manager import StorageManager
            storage_manager = StorageManager()
            user_data_dir = Path(storage_manager._get_app_data_dir())
            voices_cache_file = user_data_dir / "voices.json"
            
            # 删除缓存文件
            if voices_cache_file.exists():
                os.remove(voices_cache_file)
                logger.info(f"语音缓存文件已删除: {voices_cache_file}")
                return {
                    "success": True,
                    "message": "语音缓存已清除"
                }
            else:
                return {
                    "success": True,
                    "message": "语音缓存文件不存在"
                }
                
        except Exception as e:
            logger.error(f"清除语音缓存失败: {e}")
            return {
                "success": False,
                "error": f"清除语音缓存失败: {str(e)}"
            }

    # {{CHENGQI:
    # Action: Modified
    # Timestamp: 2025-06-22T15:15:00+08:00
    # Reason: 修复递归调用问题，直接调用service层的generate_svg_content方法
    # Principle_Applied: SOLID单一职责 - 专门处理SVG内容生成请求; KISS - 简化调用链
    # Optimization: 避免递归调用，直接调用业务逻辑
    # Architectural_Note (AR): 修复API处理逻辑，确保正确的数据流
    # Documentation_Note (DW): 解决_handle方法中的递归调用问题
    # }}
    async def _handle_generate_svg_content(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理SVG内容生成请求"""
        try:
            # 验证插件状态
            if not self.is_initialized:
                return {"success": False, "error": "插件未初始化"}
            
            if not self.service or not self.service.is_running:
                return {"success": False, "error": "DeerFlow服务未运行"}
            
            # 提取请求参数
            content = request.get("content", "")
            if not content:
                return {"success": False, "error": "内容不能为空"}
            
            config = request.get("config", {})
            
            # 调用服务层方法
            result = await self.service.generate_svg_content(
                content=content,
                config=config
            )
            return result
            
        except Exception as e:
            logger.error(f"处理SVG内容生成请求失败: {e}")
            return {"success": False, "error": f"SVG生成失败: {str(e)}"}

    async def _handle_get_task_result(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取异步任务结果请求"""
        task_id = request.get("task_id")
        if not task_id:
            return {"success": False, "error": "task_id 不能为空"}

        task = self.tasks.get(task_id)
        if not task:
            # 检查任务是否已在结果中（即已完成并已从活动任务中移除）
            if task_id in self.task_results:
                return self.task_results.get(task_id, {"success": False, "error": "任务结果未找到或已清理"})
            return {"success": False, "error": "任务ID不存在"}

        if task.done():
            try:
                # 从 task_results 字典中检索结果
                result = self.task_results.get(task_id)
                if result:
                    # 可选：在检索后从内存中清理任务和结果
                    del self.tasks[task_id]
                    del self.task_results[task_id]
                    logger.info(f"任务 {task_id} 已完成并结果已返回，已从内存中清理。")
                    return result
                else:
                    return {"success": False, "error": "任务已完成，但结果未找到。"}
            except Exception as e:
                logger.error(f"获取任务 {task_id} 结果失败: {e}")
                return {"success": False, "error": f"获取任务结果失败: {str(e)}"}
        else:
            logger.info(f"任务 {task_id} 仍在进行中...")
            return {"success": True, "status": "pending", "message": "任务仍在进行中"}

