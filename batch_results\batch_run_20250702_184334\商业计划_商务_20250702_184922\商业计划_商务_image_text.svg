<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- CSS Variables for Color Palette -->
    <style>
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6; /* Same as accent */

        /* Font Families */
        --primary-font: "Microsoft YaHei", "Segoe UI", sans-serif;
        --secondary-font: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif;
        --accent-font: "Times New Roman", serif;

        /* Font Sizes */
        --font-hero-title: 72px;
        --font-main-title: 56px;
        --font-section-title: 36px;
        --font-content-title: 28px;
        --font-body-text: 22px;
        --font-small-text: 16px;
        --font-caption: 14px;

        /* Font Weights */
        --font-weight-normal: 400;
        --font-weight-medium: 500;
        --font-weight-semibold: 600;
        --font-weight-bold: 700;

        /* Line Heights */
        --line-height-tight: 1.1;
        --line-height-normal: 1.4;
        --line-height-relaxed: 1.6;

        /* Spacing (based on layout principles) */
        --spacing-xs: 4px;
        --spacing-sm: 8px;
        --spacing-md: 16px;
        --spacing-lg: 24px;
        --spacing-xl: 32px;
        --spacing-2xl: 48px;
        --spacing-3xl: 64px;
        --spacing-4xl: 96px;

        /* Margins */
        --margin-horizontal: 80px;
        --margin-vertical: 60px;
        --content-max-width: 1760px; /* 1920 - 2*80 */
      }

      /* General Styles */
      .page-background {
        fill: var(--background-color);
      }

      .text-primary {
        fill: var(--text-primary);
        font-family: var(--primary-font);
      }

      .text-secondary {
        fill: var(--text-secondary);
        font-family: var(--primary-font);
      }

      .main-title {
        font-size: var(--font-main-title);
        font-weight: var(--font-weight-semibold);
        line-height: var(--line-height-tight);
      }

      .section-title {
        font-size: var(--font-section-title);
        font-weight: var(--font-weight-semibold);
        line-height: var(--line-height-normal);
      }

      .content-title {
        font-size: var(--font-content-title);
        font-weight: var(--font-weight-semibold);
        line-height: var(--line-height-normal);
      }

      .body-text {
        font-size: var(--font-body-text);
        font-weight: var(--font-weight-normal);
        line-height: var(--line-height-relaxed);
      }

      .small-text {
        font-size: var(--font-small-text);
        font-weight: var(--font-weight-normal);
        line-height: var(--line-height-relaxed);
      }

      .caption-text {
        font-size: var(--font-caption);
        font-weight: var(--font-weight-normal);
        line-height: var(--line-height-normal);
        fill: var(--text-light);
      }

      .accent-text {
        fill: var(--accent-color);
      }

      .primary-fill {
        fill: var(--primary-color);
      }

      .accent-fill {
        fill: var(--accent-color);
      }

      .secondary-fill {
        fill: var(--secondary-color);
      }

      .card {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        rx: 12; /* border-radius */
        ry: 12; /* border-radius */
        filter: url(#dropShadow); /* Apply shadow */
      }

      /* Image frame style */
      .image-frame {
        stroke: var(--card-border);
        stroke-width: 2px;
        rx: 8;
        ry: 8;
      }

      /* Divider style */
      .divider {
        stroke: var(--card-border);
        stroke-width: 1px;
      }

      /* Icon style (placeholder, not used in this template but defined) */
      .icon {
        fill: none;
        stroke: var(--primary-color);
        stroke-width: 2;
        stroke-linecap: round;
        stroke-linejoin: round;
      }
    </style>

    <!-- Drop Shadow Filter for cards -->
    <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="dropShadowColor" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feOffset result="offOutSmall" in="SourceAlpha" dx="0" dy="2"/>
      <feGaussianBlur result="blurOutSmall" in="offOutSmall" stdDeviation="2"/>
      <feColorMatrix result="dropShadowColorSmall" in="blurOutSmall" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0"/>
      <feMerge>
        <feMergeNode in="dropShadowColor"/>
        <feMergeNode in="dropShadowColorSmall"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients (as per visual elements, not mutually exclusive with solid colors) -->
    <linearGradient id="primaryGradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#475569;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="accentTextGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

  </defs>

  <!-- Page Background -->
  <rect x="0" y="0" width="1920" height="1080" class="page-background" />

  <!-- Decorative Background Elements (Subtle geometric shapes with low opacity) -->
  <rect x="0" y="0" width="300" height="1080" class="primary-fill" opacity="0.05" />
  <rect x="1620" y="0" width="300" height="1080" class="accent-fill" opacity="0.05" />

  <!-- Header Section -->
  <g class="header">
    <!-- Logo Placeholder (positioned based on horizontal and vertical margins) -->
    <image x="80" y="60" width="120" height="40" href="{logo_url}" />

    <!-- Main Title (horizontally centered) -->
    <text x="960" y="120" text-anchor="middle" class="text-primary main-title">
      <tspan>{title}</tspan>
    </text>
  </g>

  <!-- Main Content Area -->
  <!-- Content starts below header, adjusted by vertical margin and some spacing -->
  <g transform="translate(80, 200)">
    <!-- Image Card/Container (Left Column) -->
    <g class="image-section">
      <rect x="0" y="0" width="800" height="600" class="card" />
      <!-- Image Placeholder (inset within the card) -->
      <image x="20" y="20" width="760" height="560" href="{image_url}" preserveAspectRatio="xMidYMid slice" />
      <!-- Image Frame/Border -->
      <rect x="20" y="20" width="760" height="560" class="image-frame" fill="none" />
      <!-- Image Caption -->
      <text x="400" y="630" text-anchor="middle" class="caption-text">
        <tspan>图片说明：商业洞察和市场趋势</tspan>
      </text>
    </g>

    <!-- Text Content (Right Column) -->
    <!-- Positioned relative to the main content group, with spacing from the image section -->
    <g transform="translate(880, 0)">
      <!-- Subtitle for the text section -->
      <text class="text-primary section-title">
        <tspan x="0" y="0">{subtitle}</tspan>
      </text>

      <!-- Main Body Content -->
      <text class="text-secondary body-text" y="var(--spacing-3xl)" x="0">
        <tspan x="0" dy="0">{content}</tspan>
        <tspan x="0" dy="var(--spacing-lg)">市场分析和战略规划是商业成功的基石。</tspan>
        <tspan x="0" dy="var(--spacing-lg)">我们深入洞察行业动态和消费者行为，</tspan>
        <tspan x="0" dy="var(--spacing-lg)">构建稳健的增长策略。</tspan>

        <!-- Highlighted Number / Key Metric (Large Font Emphasis) -->
        <tspan x="0" dy="var(--spacing-3xl)" class="text-primary accent-text" font-size="72px" font-weight="var(--font-weight-bold)">
          +35%
        </tspan>
        <tspan x="200" dy="-50" class="text-secondary small-text">
          市场份额增长预测 (未来3年)
        </tspan>

        <tspan x="0" dy="var(--spacing-3xl)">
          财务预测和风险评估确保了项目的可行性，
        </tspan>
        <tspan x="0" dy="var(--spacing-lg)">
          为投资人提供了清晰的价值主张和回报预期。
        </tspan>
        <tspan x="0" dy="var(--spacing-lg)">
          我们致力于创新和持续优化，以应对市场挑战。
        </tspan>
      </text>

      <!-- Simple Line Graphic / Data Visualization Placeholder -->
      <g transform="translate(0, 480)">
        <text x="0" y="0" class="text-primary content-title">关键指标</text>
        <line x1="0" y1="30" x2="600" y2="30" class="divider" />

        <!-- Placeholder line chart data -->
        <polyline points="0,100 100,80 200,90 300,70 400,60 500,50 600,40"
                  stroke="var(--accent-color)" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round" />
        <text x="0" y="115" class="caption-text">趋势分析</text>
        <text x="580" y="35" class="small-text accent-text" text-anchor="end">数据增长</text>
      </g>
    </g>
  </g>

  <!-- Footer Section -->
  <g class="footer">
    <!-- Date and Author (Left aligned) -->
    <text x="80" y="1020" class="caption-text">
      <tspan>{date} | {author}</tspan>
    </text>
    <!-- Page Number (Right aligned) -->
    <text x="1840" y="1020" text-anchor="end" class="caption-text">
      <tspan>页面 5/10</tspan>
    </text>
  </g>
</svg>