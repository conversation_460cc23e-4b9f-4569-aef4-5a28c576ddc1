#!/usr/bin/env python3
"""
LangGraph测试快速运行脚本
简化版本，适合快速验证基本功能

使用方法：
python docs/run_langgraph_test.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def check_dependencies():
    """检查依赖"""
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        print("❌ 请安装: pip install requests")
        return False
    
    try:
        from langgraph.graph import StateGraph, END
        print("✅ LangGraph 已安装")
    except ImportError:
        print("❌ 请安装: pip install langgraph")
        return False
    
    return True

def quick_test():
    """快速测试"""
    print("🚀 开始LangGraph快速测试")
    print("-" * 40)
    
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装所需包")
        return
    
    try:
        # 导入测试模块
        import importlib.util
        test_file_path = Path(__file__).parent / "langgraph-tutorial-test.py"
        spec = importlib.util.spec_from_file_location("langgraph_tutorial_test", test_file_path)
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        TestRunner = test_module.TestRunner
        
        # 创建测试运行器
        runner = TestRunner()
        
        print(f"📡 使用模型: {runner.llm_manager.current_model}")
        print(f"🔍 搜索服务: {runner.search_manager.current_provider}")
        print()
        
        # 运行基础测试
        print("=== 快速功能验证 ===")
        
        # 测试LLM连接
        try:
            messages = [{"role": "user", "content": "Hello, 请用中文回答：你是什么？"}]
            response = runner.llm_manager.call_llm(messages)
            print(f"✅ LLM测试: {response[:80]}...")
        except Exception as e:
            print(f"❌ LLM测试失败: {e}")
        
        # 测试搜索功能
        try:
            results = runner.search_manager.search("Python 编程", max_results=1)
            if results and not results[0].get('error'):
                print(f"✅ 搜索测试: 成功获取{len(results)}条结果")
            else:
                print(f"❌ 搜索测试: {results}")
        except Exception as e:
            print(f"❌ 搜索测试失败: {e}")
        
        # 简单工作流测试
        try:
            from langgraph.graph import StateGraph, END
            from typing_extensions import TypedDict
            
            class SimpleState(TypedDict):
                input: str
                output: str
            
            def process_node(state: SimpleState) -> SimpleState:
                messages = [{"role": "user", "content": f"请简单回答: {state['input']}"}]
                response = runner.llm_manager.call_llm(messages)
                return {"input": state["input"], "output": response}
            
            # 构建工作流
            workflow = StateGraph(SimpleState)
            workflow.add_node("process", process_node)
            workflow.set_entry_point("process")
            workflow.add_edge("process", END)
            
            app = workflow.compile()
            result = app.invoke({"input": "什么是LangGraph？", "output": ""})
            
            print(f"✅ 工作流测试: {result['output'][:80]}...")
            
        except Exception as e:
            print(f"❌ 工作流测试失败: {e}")
        
        print("\n🎉 快速测试完成！")
        print("\n如需完整测试，请运行: python docs/langgraph-tutorial-test.py")
        
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test() 