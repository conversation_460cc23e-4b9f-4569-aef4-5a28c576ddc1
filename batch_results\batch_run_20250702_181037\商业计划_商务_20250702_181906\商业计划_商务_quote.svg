<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors from design_spec_constraints -->
    <style type="text/css"><![CDATA[
      /* Basic Colors */
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .card-background-color { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }

      /* Fonts from design_spec_constraints */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* Hero Title */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* Main Title */
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* Section Title */
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* Content Title */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* Body Text */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; } /* Small Text */
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; } /* Caption */

      /* Text Alignment */
      .text-center { text-anchor: middle; }
      .text-left { text-anchor: start; }
      .text-right { text-anchor: end; }

      /* Shadows */
      .shadow-md {
        filter: url(#shadowFilter);
      }
    ]]></style>

    <!-- Shadow filter for card_style shadow -->
    <filter id="shadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/>
      <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter2"/>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter2" result="shadowBlurOuter2"/>
      <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"/>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"/>
        <feMergeNode in="shadowMatrixOuter2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Primary Gradient -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>

    <!-- Accent Gradient -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Subtle background gradient for "emotional resonance" -->
    <linearGradient id="backgroundSubtleGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>

  </defs>

  <!-- Main Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundSubtleGradient)"/>

  <!-- Decorative Elements: Subtle geometric shapes -->
  <!-- Top-left accent shape -->
  <rect x="80" y="60" width="200" height="10" rx="5" fill="#3B82F6" opacity="0.4"/>
  <rect x="80" y="80" width="10" height="100" rx="5" fill="#3B82F6" opacity="0.4"/>

  <!-- Bottom-right accent shape -->
  <rect x="1640" y="1010" width="200" height="10" rx="5" fill="#1E40AF" opacity="0.4"/>
  <rect x="1830" y="910" width="10" height="100" rx="5" fill="#1E40AF" opacity="0.4"/>

  <!-- Central card-like container for the quote -->
  <rect x="260" y="220" width="1400" height="640" rx="12" class="card-background-color" stroke="#BAE6FD" stroke-width="1" filter="url(#shadowFilter)"/>

  <!-- Large decorative opening quotation mark -->
  <text x="360" y="450" class="font-accent hero-title text-primary-color" fill="url(#accentGradient)" opacity="0.3" font-size="200px" font-weight="900" transform="scale(1, 1.2)">“</text>

  <!-- Large decorative closing quotation mark -->
  <text x="1560" y="800" class="font-accent hero-title text-primary-color" fill="url(#accentGradient)" opacity="0.3" font-size="200px" font-weight="900" text-anchor="end" transform="scale(1, 1.2)">”</text>

  <!-- Quote Content -->
  <text x="960" y="460" class="font-primary main-title text-primary-color text-center">
    <tspan x="960" dy="0">“商业的未来在于创新</tspan>
    <tspan x="960" dy="80">和持续适应市场。”</tspan>
    <tspan x="960" dy="120" class="font-primary body-text text-secondary-color">"The future of business lies in innovation</tspan>
    <tspan x="960" dy="40" class="font-primary body-text text-secondary-color">and continuous market adaptation."</tspan>
  </text>

  <!-- Source Information -->
  <text x="960" y="780" class="font-primary content-title text-secondary-color text-center">
    <tspan x="960" dy="0">- {author}</tspan>
    <tspan x="960" dy="40" class="font-primary small-text text-secondary-color">来源: {source_info}，{date}</tspan>
  </text>

  <!-- Logo Placeholder (Top-left) -->
  <rect x="80" y="60" width="180" height="60" fill="#FFFFFF" rx="8" filter="url(#shadowFilter)"/>
  <text x="170" y="98" class="font-primary section-title text-primary-color text-center">
    <tspan x="170" dy="0">LOGO</tspan>
  </text>
  <!-- If {logo_url} is an actual image URL, use image tag:
  <image x="80" y="60" width="180" height="60" xlink:href="{logo_url}" preserveAspectRatio="xMidYMid meet"/>
  -->

  <!-- Page Number/Date Placeholder (Bottom-right) -->
  <text x="1840" y="1020" class="font-primary small-text text-secondary-color text-right">
    <tspan x="1840" dy="0">9 / 10</tspan>
    <tspan x="1840" dy="20" class="caption-text">{date}</tspan>
  </text>

</svg>