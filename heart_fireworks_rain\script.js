const canvas = document.getElementById('fireworksCanvas');
const ctx = canvas.getContext('2d');

let fireworks = [];
let particles = [];
let hearts = [];

// Set canvas dimensions
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

window.addEventListener('resize', () => {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
});

// --- Heart Shape Drawing Function ---
function drawHeart(x, y, size, color) {
    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.moveTo(x, y + size / 4);
    ctx.bezierCurveTo(x + size / 2, y - size / 2, x + size, y, x, y + size);
    ctx.bezierCurveTo(x - size, y, x - size / 2, y - size / 2, x, y + size / 4);
    ctx.closePath();
    ctx.fill();
}

// --- Particle Class (for fireworks and falling hearts) ---
class Particle {
    constructor(x, y, color, velocityX, velocityY, isHeart = false) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.velocity = {
            x: velocityX,
            y: velocityY
        };
        this.alpha = 1;
        this.friction = 0.98;
        this.gravity = 0.08; // Increased gravity for faster fall
        this.isHeart = isHeart;
        this.size = this.isHeart ? Math.random() * 10 + 5 : Math.random() * 3 + 1;
    }

    update() {
        if (this.isHeart) {
            this.velocity.y += this.gravity;
            this.x += this.velocity.x;
            this.y += this.velocity.y;
            this.alpha -= 0.005; // Hearts fade slower
        } else {
            this.velocity.x *= this.friction;
            this.velocity.y *= this.friction;
            this.velocity.y += this.gravity;
            this.x += this.velocity.x;
            this.y += this.velocity.y;
            this.alpha -= 0.01; // Fireworks particles fade faster
        }
    }

    draw() {
        ctx.save();
        ctx.globalAlpha = this.alpha;
        if (this.isHeart) {
            drawHeart(this.x, this.y, this.size, this.color);
        } else {
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2, false);
            ctx.fill();
        }
        ctx.restore();
    }
}

// --- Firework Class ---
class Firework {
    constructor(x, y, targetX, targetY) {
        this.x = x;
        this.y = y;
        this.targetX = targetX;
        this.targetY = targetY;
        this.distanceToTarget = Math.sqrt((targetX - x) ** 2 + (targetY - y) ** 2);
        this.angle = Math.atan2(targetY - y, targetX - x);
        this.velocity = {
            x: Math.cos(this.angle) * 5,
            y: Math.sin(this.angle) * 5
        };
        this.trail = [];
        this.trailLength = 5;
        this.hue = Math.random() * 360;
        this.brightness = Math.random() * 20 + 70; // 70-90
        this.exploded = false;
    }

    update() {
        if (this.exploded) return;

        this.trail.push({ x: this.x, y: this.y, hue: this.hue, brightness: this.brightness });
        if (this.trail.length > this.trailLength) {
            this.trail.shift();
        }

        this.x += this.velocity.x;
        this.y += this.velocity.y;

        let distanceTraveled = Math.sqrt((this.x - (this.targetX - this.velocity.x)) ** 2 + (this.y - (this.targetY - this.velocity.y)) ** 2);
        if (distanceTraveled >= this.distanceToTarget) {
            this.exploded = true;
            this.explode();
        }
    }

    draw() {
        if (this.exploded) return;

        // Draw trail
        for (let i = 0; i < this.trail.length; i++) {
            let p = this.trail[i];
            let opacity = (i + 1) / this.trail.length;
            ctx.save();
            ctx.globalAlpha = opacity;
            ctx.fillStyle = `hsl(${p.hue}, 100%, ${p.brightness}%)`;
            ctx.beginPath();
            ctx.arc(p.x, p.y, 1.5, 0, Math.PI * 2, false);
            ctx.fill();
            ctx.restore();
        }

        // Draw firework head
        ctx.save();
        ctx.globalAlpha = 1;
        ctx.fillStyle = `hsl(${this.hue}, 100%, ${this.brightness}%)`;
        ctx.beginPath();
        ctx.arc(this.x, this.y, 2.5, 0, Math.PI * 2, false);
        ctx.fill();
        ctx.restore();
    }

    explode() {
        const numParticles = 80; // Number of particles for explosion
        for (let i = 0; i < numParticles; i++) {
            const angle = Math.random() * Math.PI * 2;
            const strength = Math.random() * 5 + 1; // Particle explosion strength
            const velocityX = Math.cos(angle) * strength;
            const velocityY = Math.sin(angle) * strength;
            const hue = this.hue + (Math.random() * 40 - 20); // Slightly varied hue
            const color = `hsl(${hue}, 100%, 70%)`;
            particles.push(new Particle(this.x, this.y, color, velocityX, velocityY, true)); // True for heart particles
        }
    }
}

// --- Animation Loop ---
function animate() {
    requestAnimationFrame(animate);
    ctx.fillStyle = 'rgba(0, 0, 0, 0.05)'; // Fading trail effect
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    for (let i = fireworks.length - 1; i >= 0; i--) {
        fireworks[i].update();
        fireworks[i].draw();
        if (fireworks[i].exploded) {
            fireworks.splice(i, 1);
        }
    }

    for (let i = particles.length - 1; i >= 0; i--) {
        particles[i].update();
        particles[i].draw();
        if (particles[i].alpha <= 0) {
            particles.splice(i, 1);
        }
    }

    // Remove hearts that have faded or fallen off screen
    for (let i = hearts.length - 1; i >= 0; i--) {
        hearts[i].update();
        hearts[i].draw();
        if (hearts[i].alpha <= 0 || hearts[i].y > canvas.height + hearts[i].size) {
            hearts.splice(i, 1);
        }
    }
}

// --- Trigger Fireworks on Click ---
canvas.addEventListener('click', (e) => {
    const startX = canvas.width / 2; // Start from bottom center
    const startY = canvas.height;
    const targetX = e.clientX;
    const targetY = e.clientY;
    fireworks.push(new Firework(startX, startY, targetX, targetY));
});

// --- Heart Rain Effect ---
setInterval(() => {
    const x = Math.random() * canvas.width;
    const y = -Math.random() * 200; // Start above canvas
    const color = `hsl(${Math.random() * 360}, 100%, 70%)`;
    const velocityX = (Math.random() - 0.5) * 1; // Slight horizontal drift
    const velocityY = Math.random() * 2 + 1; // Faster initial fall
    hearts.push(new Particle(x, y, color, velocityX, velocityY, true));
}, 100); // Create a new heart every 100ms


// Start the animation
animate();