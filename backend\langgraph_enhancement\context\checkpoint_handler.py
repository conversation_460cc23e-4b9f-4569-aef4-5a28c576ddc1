# -*- coding: utf-8 -*-
"""
LangGraph 增强系统 - 检查点处理器

核心功能：
- LangGraph 检查点创建和恢复
- 状态压缩存储
- 增量更新机制
"""

import logging
import json
import pickle
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path
import uuid

try:
    from langgraph.checkpoint.memory import MemorySaver
except ImportError:
    class MemorySaver:
        def __init__(self):
            pass

logger = logging.getLogger(__name__)


class EnhancedCheckpointHandler:
    """增强检查点处理器 - LangGraph 状态保存和恢复"""
    
    def __init__(self, 
                 storage_dir: str = "data/checkpoints",
                 max_checkpoints_per_session: int = 10):
        """初始化检查点处理器"""
        
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_checkpoints_per_session = max_checkpoints_per_session
        self.checkpoint_cache: Dict[str, Dict] = {}
        self.session_metadata: Dict[str, Dict] = {}
        self.memory_saver = MemorySaver()
        
        self.stats = {
            "total_checkpoints_created": 0,
            "total_checkpoints_restored": 0,
            "last_cleanup": datetime.now()
        }
        
        logger.info("增强检查点处理器初始化完成")
    
    async def create_checkpoint(self, 
                               session_id: str, 
                               state: Dict[str, Any],
                               checkpoint_type: str = "auto") -> str:
        """创建检查点"""
        
        checkpoint_id = f"{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        checkpoint_data = {
            "checkpoint_id": checkpoint_id,
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "checkpoint_type": checkpoint_type,
            "state": state,
            "version": "1.0"
        }
        
        state_hash = self._calculate_state_hash(state)
        checkpoint_data["state_hash"] = state_hash
        
        if session_id in self.session_metadata:
            last_hash = self.session_metadata[session_id].get("last_state_hash")
            if last_hash == state_hash and checkpoint_type == "auto":
                logger.debug(f"状态未变化，跳过检查点创建: {session_id}")
                return self.session_metadata[session_id]["last_checkpoint_id"]
        
        checkpoint_file = self.storage_dir / f"{checkpoint_id}.checkpoint"
        await self._save_checkpoint_file(checkpoint_file, checkpoint_data)
        
        self.checkpoint_cache[checkpoint_id] = checkpoint_data
        
        if session_id not in self.session_metadata:
            self.session_metadata[session_id] = {
                "checkpoints": [],
                "created_at": datetime.now().isoformat()
            }
        
        self.session_metadata[session_id]["checkpoints"].append(checkpoint_id)
        self.session_metadata[session_id]["last_checkpoint_id"] = checkpoint_id
        self.session_metadata[session_id]["last_state_hash"] = state_hash
        
        await self._cleanup_old_checkpoints(session_id)
        self.stats["total_checkpoints_created"] += 1
        
        logger.info(f"创建检查点: {checkpoint_id}")
        return checkpoint_id
    
    async def restore_checkpoint(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """恢复检查点"""
        
        if checkpoint_id in self.checkpoint_cache:
            checkpoint_data = self.checkpoint_cache[checkpoint_id]
        else:
            checkpoint_file = self.storage_dir / f"{checkpoint_id}.checkpoint"
            if not checkpoint_file.exists():
                logger.warning(f"检查点文件不存在: {checkpoint_id}")
                return None
            
            checkpoint_data = await self._load_checkpoint_file(checkpoint_file)
            if not checkpoint_data:
                return None
            
            self.checkpoint_cache[checkpoint_id] = checkpoint_data
        
        state = checkpoint_data["state"]
        self.stats["total_checkpoints_restored"] += 1
        
        logger.info(f"恢复检查点: {checkpoint_id}")
        return state
    
    async def _save_checkpoint_file(self, file_path: Path, data: Dict) -> None:
        """保存检查点文件"""
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.error(f"保存检查点文件失败 {file_path}: {e}")
            raise
    
    async def _load_checkpoint_file(self, file_path: Path) -> Optional[Dict]:
        """加载检查点文件"""
        try:
            with open(file_path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            logger.error(f"加载检查点文件失败 {file_path}: {e}")
            return None
    
    def _calculate_state_hash(self, state: Dict[str, Any]) -> str:
        """计算状态哈希值"""
        try:
            serialized = json.dumps(state, sort_keys=True, default=str)
            return hashlib.md5(serialized.encode()).hexdigest()
        except Exception as e:
            logger.error(f"计算状态哈希失败: {e}")
            return str(hash(str(state)))
    
    async def _cleanup_old_checkpoints(self, session_id: str) -> None:
        """清理会话的旧检查点"""
        if session_id not in self.session_metadata:
            return
        
        checkpoints = self.session_metadata[session_id]["checkpoints"]
        while len(checkpoints) > self.max_checkpoints_per_session:
            oldest_checkpoint = checkpoints.pop(0)
            await self.delete_checkpoint(oldest_checkpoint)
    
    async def delete_checkpoint(self, checkpoint_id: str) -> bool:
        """删除指定检查点"""
        try:
            checkpoint_file = self.storage_dir / f"{checkpoint_id}.checkpoint"
            if checkpoint_file.exists():
                checkpoint_file.unlink()
            
            if checkpoint_id in self.checkpoint_cache:
                del self.checkpoint_cache[checkpoint_id]
            
            for session_id, metadata in self.session_metadata.items():
                if checkpoint_id in metadata["checkpoints"]:
                    metadata["checkpoints"].remove(checkpoint_id)
                    break
            
            logger.info(f"删除检查点: {checkpoint_id}")
            return True
        except Exception as e:
            logger.error(f"删除检查点失败 {checkpoint_id}: {e}")
            return False
    
    async def cleanup_expired_checkpoints(self, max_age_hours: int = 24) -> int:
        """清理过期的检查点"""
        cleaned_count = 0
        current_time = datetime.now()
        
        for checkpoint_file in self.storage_dir.glob("*.checkpoint"):
            try:
                # 检查文件年龄
                file_age_hours = (current_time - datetime.fromtimestamp(checkpoint_file.stat().st_mtime)).total_seconds() / 3600
                if file_age_hours > max_age_hours:
                    checkpoint_file.unlink()
                    cleaned_count += 1
            except Exception as e:
                logger.error(f"清理检查点文件失败 {checkpoint_file}: {e}")
        
        self.stats["last_cleanup"] = current_time
        logger.info(f"清理过期检查点: {cleaned_count} 个")
        return cleaned_count
    
    async def get_checkpoint_stats(self) -> Dict[str, Any]:
        """获取检查点统计信息"""
        total_sessions = len(self.session_metadata)
        total_cached = len(self.checkpoint_cache)
        
        disk_usage = sum(
            f.stat().st_size for f in self.storage_dir.glob("*.checkpoint")
        )
        
        stats = {
            **self.stats,
            "total_sessions": total_sessions,
            "total_cached_checkpoints": total_cached,
            "disk_usage_bytes": disk_usage,
            "storage_directory": str(self.storage_dir),
            "last_cleanup": self.stats["last_cleanup"].isoformat()
        }
        
        return stats
    
    def get_langgraph_memory_saver(self) -> MemorySaver:
        """获取 LangGraph 原生内存保存器"""
        return self.memory_saver


# 全局检查点处理器实例
def get_checkpoint_handler(**kwargs) -> EnhancedCheckpointHandler:
    """获取全局检查点处理器实例"""
    if not hasattr(get_checkpoint_handler, '_instance'):
        get_checkpoint_handler._instance = EnhancedCheckpointHandler(**kwargs)
    return get_checkpoint_handler._instance 