#!/usr/bin/env python3
"""
调试智能体实例获取问题
"""

import threading
import sys
import os

def analyze_get_current_agent_instance_logic():
    """分析_get_current_agent_instance方法的逻辑问题"""
    
    print("🔍 分析 _get_current_agent_instance 方法的潜在问题...")
    
    print("\n📋 当前逻辑分析:")
    print("1. 策略1: 如果智能体池中只有一个智能体，直接返回")
    print("   ❗ 问题: 这忽略了线程本地存储中的specific agent_id")
    print("   ❗ 如果多个线程使用同一个智能体池，会返回错误的智能体")
    
    print("\n2. 策略2: 检查线程本地存储")
    print("   ✅ 逻辑正确: hasattr(current_thread, 'current_agent_id')")
    print("   ✅ 逻辑正确: agent_pool.get_agent(agent_id)")
    print("   ❗ 潜在问题: agent_pool.get_agent() 可能返回None")
    
    print("\n3. 策略3: 使用最近活跃的智能体")
    print("   ❗ 问题: 这是fallback策略，但可能不是当前线程需要的智能体")
    
    print("\n🐛 建议的修复方案:")
    print("1. 优先使用线程本地存储 (策略2应该在策略1之前)")
    print("2. 添加更详细的调试日志")
    print("3. 确保智能体池的线程安全性")
    print("4. 验证agent_id是否正确添加到池中")

def proposed_fix():
    """建议的修复代码"""
    
    fix_code = '''
async def _get_current_agent_instance(self, ctx):
    """
    🔍 获取当前智能体实例 - 修复版本
    """
    try:
        from backend.langgraph_enhancement.agents.context_optimized_agent import get_agent_pool
        agent_pool = get_agent_pool()
        all_agents = agent_pool.get_all_agents()
        
        # 🔥 策略1 (优先): 尝试通过线程本地存储获取
        import threading
        current_thread = threading.current_thread()
        
        if hasattr(current_thread, 'current_agent_id'):
            agent_id = current_thread.current_agent_id
            logger.info(f"🧵 从线程本地存储获取 agent_id: {agent_id}")
            
            agent = agent_pool.get_agent(agent_id)
            if agent:
                logger.info(f"✅ 通过线程本地存储找到智能体: {agent_id}")
                return agent
            else:
                logger.warning(f"⚠️ 线程本地存储中的智能体ID {agent_id} 在池中不存在")
                # 添加调试信息
                logger.warning(f"🔍 智能体池中的智能体: {list(all_agents.keys())}")
        else:
            logger.info("ℹ️ 线程本地存储中没有 current_agent_id")
        
        # 🔥 策略2: 如果只有一个活跃智能体，直接返回
        if len(all_agents) == 1:
            agent_id, agent = next(iter(all_agents.items()))
            logger.info(f"🎯 智能体池中只有一个智能体，返回: {agent_id}")
            return agent
        
        # 🔥 策略3: 如果有多个智能体，尝试从最近活跃的获取
        if all_agents:
            latest_agent = None
            latest_time = None
            
            for agent_id, agent in all_agents.items():
                if hasattr(agent, 'last_processing_time') and agent.last_processing_time:
                    if latest_time is None or agent.last_processing_time > latest_time:
                        latest_time = agent.last_processing_time
                        latest_agent = agent
            
            if latest_agent:
                logger.info(f"🎯 通过最近活跃时间找到智能体: {latest_agent.agent_id}")
                return latest_agent
            
            # 如果没有处理时间信息，返回第一个
            first_agent = next(iter(all_agents.values()))
            logger.warning(f"⚠️ 使用第一个可用智能体: {first_agent.agent_id}")
            return first_agent
        
        logger.warning("⚠️ 无法找到任何活跃的智能体实例")
        return None
        
    except ImportError:
        logger.warning("⚠️ LangGraph增强系统不可用")
        return None
    except Exception as e:
        logger.error(f"❌ 获取智能体实例失败: {e}")
        import traceback
        logger.error(f"❌ 完整错误信息: {traceback.format_exc()}")
        return None
'''
    
    print("🛠️ 建议的修复代码:")
    print(fix_code)

def check_thread_safety_issues():
    """检查可能的线程安全问题"""
    
    print("\n🔒 线程安全问题分析:")
    
    print("1. 智能体池 (AgentPool):")
    print("   - 使用简单的字典存储: self._agents: Dict[str, ContextOptimizedAgent]")
    print("   - 没有锁机制保护并发访问")
    print("   - 在多线程环境下可能存在竞态条件")
    
    print("\n2. 线程本地存储:")
    print("   - ✅ 本身是线程安全的")
    print("   - ✅ 每个线程有独立的 current_agent_id")
    
    print("\n3. 潜在竞态条件:")
    print("   - 线程A设置current_agent_id")
    print("   - 线程B同时访问智能体池")
    print("   - 智能体池状态可能在检查和获取之间发生变化")

if __name__ == "__main__":
    analyze_get_current_agent_instance_logic()
    proposed_fix()
    check_thread_safety_issues()