<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      /* CSS Variables for colors */
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --hover-color: #7DD3FC;
        --active-color: #1E40AF;
      }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source <PERSON> Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* Text Sizes */
      .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .text-section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
      .text-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .text-small { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .text-caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* General Styles */
      .fill-primary { fill: var(--primary-color); }
      .fill-secondary { fill: var(--secondary-color); }
      .fill-accent { fill: var(--accent-color); }
      .fill-background { fill: var(--background-color); }
      .fill-text-primary { fill: var(--text-primary); }
      .fill-text-secondary { fill: var(--text-secondary); }
      .fill-card-background { fill: var(--card-background); }
      .stroke-primary { stroke: var(--primary-color); }
      .stroke-accent { stroke: var(--accent-color); }
      .stroke-secondary { stroke: var(--secondary-color); }
      .stroke-card-border { stroke: var(--card-border); }

      /* Card Styles */
      .card-style {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        filter: url(#card-shadow);
      }

      /* Hover Effects (Placeholder for interactivity, actual hover needs JS or specific SVG elements) */
      .button-hover-effect:hover {
        fill: var(--hover-color);
        cursor: pointer;
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Filters for Shadows -->
    <filter id="card-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" />
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2" />
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2" />
      <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="matrixOut2" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <filter id="text-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur" />
      <feOffset in="blur" dx="0" dy="2" result="offsetBlur" />
      <feFlood flood-color="#000000" flood-opacity="0.1" result="offsetColor" />
      <feComposite in="offsetColor" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icon: Business Growth (Placeholder - actual SVG path would be here) -->
    <g id="icon-growth" fill="none" stroke="var(--accent-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M3 18L13 8L21 16" />
      <polyline points="16 16 21 16 21 21" />
    </g>

    <!-- Icon: Target (Placeholder) -->
    <g id="icon-target" fill="none" stroke="var(--accent-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="12" cy="12" r="10" />
      <circle cx="12" cy="12" r="6" />
      <circle cx="12" cy="12" r="2" />
    </g>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Page Header -->
  <g id="page-header">
    <image x="80" y="60" width="120" height="40" xlink:href="{logo_url}" />
    <text x="1840" y="80" text-anchor="end" class="font-secondary text-small fill-text-secondary">
      5/10
    </text>
  </g>

  <!-- Main Content Area -->
  <g id="main-content" transform="translate(80, 150)">
    <!-- Title -->
    <text x="0" y="0" class="font-primary text-main-title fill-text-primary" dy="0.3em">
      市场洞察和战略规划
    </text>
    <text x="0" y="70" class="font-secondary text-content-title fill-text-secondary" dy="0.3em">
      Market Insights and Strategic Planning
    </text>

    <!-- Content Grid - Bento Style -->
    <g transform="translate(0, 150)">
      <!-- Left Section: Image and Key Metric -->
      <g id="left-section">
        <rect x="0" y="0" width="860" height="600" rx="12" ry="12" class="card-style" />
        <image x="20" y="20" width="820" height="400" preserveAspectRatio="xMidYMid slice" xlink:href="{image_url}" style="border-radius: 8px; filter: url(#text-shadow);" />

        <!-- Key Metric Highlight -->
        <g transform="translate(430, 420)" text-anchor="middle">
          <text x="0" y="0" class="font-primary text-hero-title" fill="url(#accentGradient)">
            +35%
          </text>
          <text x="0" y="80" class="font-secondary text-section-title fill-text-secondary">
            市场份额增长预测
          </text>
          <text x="0" y="120" class="font-secondary text-small fill-text-light">
            Projected Market Share Growth
          </text>
          <use xlink:href="#icon-growth" transform="translate(-12, 140) scale(1.5)" />
        </g>
      </g>

      <!-- Right Section: Text Content and Data Visualization -->
      <g id="right-section" transform="translate(900, 0)">
        <!-- Top Text Card -->
        <rect x="0" y="0" width="860" height="290" rx="12" ry="12" class="card-style" />
        <text x="40" y="40" class="font-primary text-content-title fill-text-primary" dy="0.3em">
          深入市场分析
        </text>
        <text x="40" y="80" class="font-secondary text-small fill-text-secondary" dy="0.3em">
          In-depth Market Analysis
        </text>
        <text x="40" y="130" class="font-secondary text-body fill-text-secondary" dy="0.3em">
          {content}
          <tspan x="40" dy="1.6em">我们对当前市场环境进行了全面分析，</tspan>
          <tspan x="40" dy="1.6em">识别出关键增长机遇和潜在挑战。</tspan>
          <tspan x="40" dy="1.6em">通过数据驱动的洞察，我们能更精准地</tspan>
          <tspan x="40" dy="1.6em">制定发展策略，确保业务持续领先。</tspan>
        </text>

        <!-- Bottom Data Visualization Card -->
        <rect x="0" y="310" width="860" height="290" rx="12" ry="12" class="card-style" />
        <text x="40" y="350" class="font-primary text-content-title fill-text-primary" dy="0.3em">
          关键指标和财务预测
        </text>
        <text x="40" y="390" class="font-secondary text-small fill-text-secondary" dy="0.3em">
          Key Metrics and Financial Projections
        </text>

        <!-- Simple Bar Chart Placeholder -->
        <g transform="translate(60, 440)">
          <line x1="0" y1="120" x2="740" y2="120" stroke="var(--text-light)" stroke-width="1" />
          <text x="-10" y="125" text-anchor="end" class="font-secondary text-caption fill-text-light">0</text>
          <text x="-10" y="85" text-anchor="end" class="font-secondary text-caption fill-text-light">50M</text>
          <text x="-10" y="45" text-anchor="end" class="font-secondary text-caption fill-text-light">100M</text>

          <!-- Bars -->
          <rect x="50" y="80" width="60" height="40" fill="var(--accent-color)" rx="4" ry="4" />
          <text x="80" y="135" text-anchor="middle" class="font-secondary text-small fill-text-secondary">2023</text>

          <rect x="180" y="40" width="60" height="80" fill="var(--primary-color)" rx="4" ry="4" />
          <text x="210" y="135" text-anchor="middle" class="font-secondary text-small fill-text-secondary">2024</text>

          <rect x="310" y="10" width="60" height="110" fill="url(#primaryGradient)" rx="4" ry="4" />
          <text x="340" y="135" text-anchor="middle" class="font-secondary text-small fill-text-secondary">2025</text>

          <rect x="440" y="20" width="60" height="100" fill="var(--accent-color)" opacity="0.7" rx="4" ry="4" />
          <text x="470" y="135" text-anchor="middle" class="font-secondary text-small fill-text-secondary">2026</text>

          <rect x="570" y="40" width="60" height="80" fill="var(--primary-color)" opacity="0.7" rx="4" ry="4" />
          <text x="600" y="135" text-anchor="middle" class="font-secondary text-small fill-text-secondary">2027</text>

          <use xlink:href="#icon-target" transform="translate(700, 40) scale(1.2)" />
          <text x="700" y="80" class="font-secondary text-small fill-text-secondary" text-anchor="middle">目标</text>
        </g>
      </g>
    </g>
  </g>

  <!-- Footer (simple line and date) -->
  <g id="page-footer">
    <line x1="80" y1="1020" x2="1840" y2="1020" stroke="var(--card-border)" stroke-width="1" />
    <text x="80" y="1040" class="font-secondary text-caption fill-text-secondary">
      {date}
    </text>
    <text x="1840" y="1040" text-anchor="end" class="font-secondary text-caption fill-text-secondary">
      {author}
    </text>
  </g>

</svg>