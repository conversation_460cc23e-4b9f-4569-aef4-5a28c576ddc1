"""
聊天相关路由处理模块
"""
import json
import logging
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
import datetime # Add datetime for timestamping file logs

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect

# 从其他模块导入
from backend.ai.manager import ai_manager
from backend.api.websocket import manager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# {{CHENGQI:
# Action: Added
# Timestamp: 2025-01-17 08:00:00 +08:00
# Reason: 添加消息内容提取函数，兼容字符串和对象数组两种content格式
# Principle_Applied: KISS - 简单的类型检查函数，DRY - 避免重复逻辑
# Optimization: 统一消息内容处理，提高代码健壮性
# }}
def extract_message_content(content):
    """
    安全提取消息内容，兼容多种格式
    Args:
        content: 字符串或对象数组
    Returns:
        str: 提取的文本内容
    """
    if isinstance(content, str):
        # 情况1：content是字符串
        return content
    elif isinstance(content, list) and len(content) > 0:
        # 情况2：content是对象数组，提取text字段
        text_parts = []
        for item in content:
            if isinstance(item, dict):
                if 'text' in item:
                    text_parts.append(str(item['text']))
                elif 'content' in item:
                    text_parts.append(str(item['content']))
        return ' '.join(text_parts) if text_parts else ""
    else:
        # 情况3：其他格式或空值
        logger.warning(f"未知的消息内容格式: {type(content)}, 内容: {content}")
        return ""

# ---> 添加文件日志记录辅助函数 <---
import os
import tempfile
LOG_FILE_PATH = os.path.join(tempfile.gettempdir(), "jimu_chat_routes.log")

def write_route_log(message):
    """将日志写入log.txt文件"""
    timestamp = datetime.datetime.now().isoformat()
    try:
        with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
            f.write(f"{timestamp} - CHAT_ROUTE - {message}\n") # Add prefix to distinguish source
    except Exception as e:
        # Fallback to console if file write fails
        print(f"Error writing to route log file: {e}")
        print(f"{timestamp} - CHAT_ROUTE - {message}")
# ---> 结束添加 <---

# 创建路由
router = APIRouter()

# 修改全局字典来跟踪每个会话的取消状态，使用会话ID而不是WebSocket对象
generation_cancelled_map = {}

@router.websocket("/chat")
async def websocket_chat(websocket: WebSocket):
    """
    处理WebSocket聊天 - 支持会话级连接池
    """
    # 首先建立基础连接（不绑定会话）
    await manager.connect(websocket)
    # 当前处理的会话ID
    current_conversation_id = None
    
    try:
        while True:
            data = await websocket.receive_text()
            data = json.loads(data)
            
            # 处理会话绑定请求
            if data.get("command") == "bind_session":
                session_id = data.get("session_id")
                if session_id:
                    await manager._bind_session(websocket, session_id)
                    await manager.send_text(
                        json.dumps({"type": "session_bound", "session_id": session_id}),
                        websocket
                    )
                    logger.info(f"WebSocket已绑定到会话: {session_id}")
                continue
            
            # 处理停止生成命令
            if data.get("command") == "stop_generation":
                # 获取会话ID
                current_conversation_id = data.get("conversation_id")
                if current_conversation_id:
                    # 使用会话ID作为键设置取消状态
                    generation_cancelled_map[current_conversation_id] = True
                    logger.info(f"通过WebSocket接收到会话 {current_conversation_id} 的终止命令")
                    
                # 发送到会话专用连接
                if current_conversation_id:
                    await manager.send_to_session(
                        current_conversation_id,
                        json.dumps({"type": "stop", "message": "消息生成已停止", "conversation_id": current_conversation_id})
                    )
                else:
                    await manager.send_text(
                        json.dumps({"type": "stop", "message": "消息生成已停止"}),
                        websocket
                    )
                continue
            
            # 处理客户端心跳
            if data.get("command") == "heartbeat":
                session_id = manager.connection_sessions.get(websocket, "未绑定")
                await manager.send_text(
                    json.dumps({"type": "heartbeat", "timestamp": time.time(), "session_id": session_id}),
                    websocket
                )
                continue
            
            # 获取会话ID
            current_conversation_id = data.get("conversation_id")
            if not current_conversation_id:
                await manager.send_text(
                    json.dumps({"error": "缺少会话ID", "type": "error"}),
                    websocket
                )
                continue
            
            # 确保WebSocket绑定到正确的会话
            if websocket not in manager.connection_sessions or manager.connection_sessions[websocket] != current_conversation_id:
                await manager._bind_session(websocket, current_conversation_id)
                logger.info(f"自动绑定WebSocket到会话: {current_conversation_id}")
            
            # 重置取消状态
            generation_cancelled_map[current_conversation_id] = False
            
            # ---> 将回调函数定义移到前面 <---
            main_loop = asyncio.get_running_loop()
            
            # 内部异步回调实现 - 添加会话ID验证
            async def _async_stream_callback_impl(content):
                # 验证会话ID - 确保消息发送到正确的会话
                callback_session_id = current_conversation_id
                if not callback_session_id:
                    logger.warning("回调函数缺少会话ID，跳过消息发送")
                    return
                
                # 验证会话连接是否仍然有效
                if callback_session_id not in manager.session_connections:
                    logger.warning(f"会话 {callback_session_id} 的连接已失效，跳过消息发送")
                    return
                
                # 如果收到取消信号，不继续发送内容
                cancelled = generation_cancelled_map.get(callback_session_id, False)
                if cancelled:
                    logger.info(f"stream_callback 检测到会话 {callback_session_id} 的取消信号，停止发送")
                    raise asyncio.CancelledError("消息生成已被用户在回调中取消")
                
                # 根据消息类型处理
                message_type = "chunk"
                message_content = str(content)
                extra_data = {}
                
                if isinstance(content, str):
                    if content.startswith("__status__"):
                         message_type = "status"
                         message_content = content[len("__status__: "):].strip()
                    elif content.startswith("__error__"):
                         message_type = "error"
                         message_content = content[len("__error__: "):].strip()
                         extra_data["error"] = message_content
                    elif content.startswith("__cancelled__"):
                         message_type = "cancelled"
                         message_content = content[len("__cancelled__: "):].strip()
                         extra_data["message"] = message_content
                    elif content.startswith("__final_result__"):
                         message_type = "final_result"
                         message_content = content[len("__final_result__: "):].strip()
                    elif content.startswith("__heartbeat__"):
                        message_type = "heartbeat"
                        message_content = None
                        extra_data["timestamp"] = time.time()
                    else:
                        message_type = "chunk"
                        message_content = content
                
                payload = {
                    "type": message_type,
                    "conversation_id": callback_session_id
                }
                if message_content is not None:
                    payload["content"] = message_content
                payload.update(extra_data)
                
                try:
                    logger.debug(f"[会话 {callback_session_id}] 发送消息类型: {payload.get('type')}")
                    # 使用会话专用连接发送消息
                    success = await manager.send_to_session(callback_session_id, json.dumps(payload))
                    if not success:
                        logger.error(f"[会话 {callback_session_id}] 消息发送失败")
                except Exception as e:
                     logger.error(f"[会话 {callback_session_id}] 发送WebSocket消息时出错: {e}", exc_info=True)
                
                # 给异步任务一个短暂的间隔，避免阻塞主循环
                await asyncio.sleep(0.01)

            # 同步回调包装器 - 添加会话ID验证
            async def sync_callback_wrapper(content):
                if main_loop.is_running():
                    # 验证会话ID
                    if not current_conversation_id:
                        logger.warning("sync_callback_wrapper: 缺少会话ID，跳过调度")
                        return False
                    
                    # 检查是否已经处于取消状态
                    if generation_cancelled_map.get(current_conversation_id, False):
                        logger.info(f"sync_callback_wrapper 检测到会话 {current_conversation_id} 取消状态，跳过调度")
                        return False
                    
                    # 验证会话连接是否仍然有效
                    if current_conversation_id not in manager.session_connections:
                        logger.warning(f"sync_callback_wrapper: 会话 {current_conversation_id} 连接已失效，跳过调度")
                        return False
                    
                    # 安全地调度异步回调到主循环
                    asyncio.run_coroutine_threadsafe(_async_stream_callback_impl(content), main_loop)
                else:
                    logger.warning("事件循环未运行，无法在 sync_callback_wrapper 中调度")
                return True
            # ---> 结束移动 <---

            messages = data.get("messages", [])
            service_name = data.get("service")
            model = data.get("model")
            
            if not messages:
                await manager.send_to_session(
                    current_conversation_id,
                    json.dumps({"error": "消息不能为空", "conversation_id": current_conversation_id, "type": "error"})
                )
                continue
            
            # 获取团队ID参数（如果有）
            team_id = data.get("team_id")
            
            # ---> 添加工作流触发逻辑 <---
            # 支持两种触发方式：1) 参数team_id 2) 命令/team
            team_name = None
            should_execute_workflow = False
            
            # 方式1：通过team_id参数
            if team_id:
                team_name = team_id
                should_execute_workflow = True
                logger.info(f"检测到team_id参数，准备执行团队工作流: {team_name}")
            
            # 方式2：通过/team命令（保持向后兼容）
            elif messages:
                raw_content = messages[-1].get("content", "")
                last_message_content = extract_message_content(raw_content).strip()
                if last_message_content.startswith("/team"):
                    parts = last_message_content.split()
                    if len(parts) > 1:
                        team_name = parts[1]
                        should_execute_workflow = True
                        logger.info(f"检测到/team命令，准备执行团队工作流: {team_name}")
                        
                        # 移除命令，保留纯粹的消息内容
                        prompt = last_message_content.replace(f"/team {team_name}", "").strip()
                        if prompt:
                            messages[-1]["content"] = prompt
                        else:
                            # 如果移除命令后没有内容，则使用默认提示
                            messages[-1]["content"] = "请帮助我处理这个任务。"
            
            # 执行团队工作流
            if should_execute_workflow and team_name:
                try:
                    # 导入工作流引擎
                    from backend.langgraph_enhancement.core.workflow_engine import DynamicWorkflowEngine
                    workflow_engine = DynamicWorkflowEngine()
                    
                    # 验证团队是否存在
                    from backend.utils.team_utils import TeamManager
                    team_manager = TeamManager()
                    team_info = team_manager.get_teams_summary()
                    
                    available_teams = [team["team_id"] for team in team_info.get("teams", [])]
                    if team_name not in available_teams:
                        logger.warning(f"团队 {team_name} 不存在，可用团队: {available_teams}")
                        await manager.send_to_session(
                            current_conversation_id,
                            json.dumps({
                                "type": "error", 
                                "error": f"团队 '{team_name}' 不存在。可用团队: {', '.join(available_teams)}", 
                                "conversation_id": current_conversation_id
                            })
                        )
                        continue
                    
                    # 🔥 准备完整的上下文作为工作流输入 + 用户主题提取
                    # 提取用户主题
                    user_topic = ""
                    if messages:
                        for msg in messages:
                            if msg.get('role') == 'user':
                                content = msg.get('content', '').strip()
                                if content and len(content) > 10:
                                    # 简单的主题提取
                                    import re
                                    content_clean = re.sub(r'^(请|帮我|我想|我需要|能否|可以)', '', content).strip()
                                    if len(content_clean) > 5:
                                        user_topic = content_clean[:50]  # 取前50个字符作为主题
                                    break
                    
                    workflow_input_state = {
                        "messages": messages,
                        "core_context": {
                            "conversation_id": current_conversation_id,
                            "user_id": data.get("user_id", "unknown"),
                            "team_id": team_name,
                            "user_topic": user_topic  # 🔥 新增：用户主题
                        },
                        "working_memory": {
                            "model": model,
                            "service_name": service_name,
                            "selected_servers": data.get("selected_servers", [])
                        }
                    }
                    
                    # 发送启动通知
                    await manager.send_to_session(
                        current_conversation_id,
                        json.dumps({
                            "type": "status", 
                            "content": f"正在启动团队工作流: {team_name}", 
                            "conversation_id": current_conversation_id
                        })
                    )
                    
                    # 🔥 执行工作流，并传入流式回调
                    workflow_result = await workflow_engine.execute_workflow(
                        team_name=team_name,
                        input_data=workflow_input_state,
                        config={
                            "configurable": {
                                "thread_id": current_conversation_id
                            }
                        },
                        stream_callback=sync_callback_wrapper  # 复用现有的流式回调
                    )
                    
                    # 发送工作流结果摘要
                    if workflow_result and workflow_result.get("status") == "success":
                        summary = workflow_result.get("workflow_summary", {})
                        await manager.send_to_session(
                            current_conversation_id,
                            json.dumps({
                                "type": "status", 
                                "content": f"团队工作流完成！执行时间: {workflow_result.get('execution_time', 0):.1f}秒，处理{summary.get('total_agents', 0)}个智能体", 
                                "conversation_id": current_conversation_id
                            })
                        )
                    
                    # 发送完成信号并跳过后续逻辑
                    await manager.send_to_session(
                        current_conversation_id,
                        json.dumps({"type": "complete", "conversation_id": current_conversation_id})
                    )
                    continue # 跳过标准聊天处理
                    
                except Exception as workflow_error:
                    logger.error(f"团队工作流执行失败: {workflow_error}", exc_info=True)
                    await manager.send_to_session(
                        current_conversation_id,
                        json.dumps({
                            "type": "error", 
                            "error": f"团队工作流执行失败: {str(workflow_error)}", 
                            "conversation_id": current_conversation_id
                        })
                    )
                    continue
            # ---> 结束工作流触发逻辑 <---
            
            # 获取选择的MCP服务器列表
            selected_servers = data.get("selected_servers", [])
            
            # 获取是否使用Agent模式
            is_agent = data.get("is_agent", False)
            
            # 获取思考预算参数（如果有）
            thinking_budget = data.get("thinking_budget")
                
            # 获取是否启用了网络搜索
            is_web_search_enabled = data.get("is_web_search_enabled", False)
            
            # 获取角色ID参数（如果有）
            role_id = data.get("role_id")
            print(f"---------------------role_id: {role_id}")
            
            response = None # Initialize response
            try:
                if is_agent:
                    # Agent 模式逻辑
                    from backend.agent.agent import process_agent_message
                    # 固定使用gemini-2.5-flash-preview-04-17模型，忽略前端传递的model参数
                    agent_model = "gemini-2.5-flash-preview-04-17"
                    logger.info(f"路由检测到 Agent 模式，调用 process_agent_message，固定使用模型: {agent_model}")
                    response = await process_agent_message(
                        messages=messages,
                        service_name=service_name,
                        model=agent_model,  # 固定使用指定模型
                        stream_callback=sync_callback_wrapper,
                        is_cancelled=lambda: generation_cancelled_map.get(current_conversation_id, False),
                        max_tokens=data.get("max_tokens", 1200000),
                        thinking_budget=thinking_budget,
                        is_web_search_enabled=is_web_search_enabled,
                        execute_tasks=True,
                        conversation_id=current_conversation_id
                    )
                    write_route_log(f"Returned from process_agent_message. Response type: {type(response)}")
                    if isinstance(response, dict):
                        write_route_log(f"Returned response keys: {list(response.keys())}")
                else:
                    # 非 Agent 模式逻辑
                    logger.info("路由检测到非 Agent 模式，调用 ai_manager.chat_completion")
                    response = await ai_manager.chat_completion(
                        messages=messages,
                        service_name=service_name,
                        model=model,
                        role_id=role_id,  # 添加角色ID参数
                        selected_servers=selected_servers,
                        stream_callback=sync_callback_wrapper,
                        handle_tools=True,
                        max_tool_calls=50,
                        is_agent=False,
                        is_cancelled=lambda: generation_cancelled_map.get(current_conversation_id, False),
                        max_tokens=data.get("max_tokens", None),
                        thinking_budget=thinking_budget,
                        is_web_search_enabled=is_web_search_enabled,
                    )
                    write_route_log(f"Returned from ai_manager.chat_completion. Response type: {type(response)}")
                    if isinstance(response, dict):
                        write_route_log(f"Returned response keys: {list(response.keys())}")
                
                # Check for errors returned explicitly by the service (common logic for both agent/non-agent)
                if response is None:
                    error_msg = "AI 服务返回了 None，可能是内部执行超时或发生未知错误。"
                    logger.error(error_msg)
                    await manager.send_to_session(
                        current_conversation_id,
                        json.dumps({
                            "type": "error",
                            "error": error_msg,
                            "error_type": "AI 服务内部错误",
                            "code": 500,
                            "conversation_id": current_conversation_id
                        })
                    )

                elif isinstance(response, dict) and response.get("error"):
                    error_info = response.get("error")
                    logger.error(f"AI 服务明确返回错误: {error_info}")
                    logger.info("准备向客户端发送 error 信号...")
                    try:
                        await manager.send_to_session(
                            current_conversation_id,
                            json.dumps({
                                "type": "error",
                                "error": f"AI 服务执行失败: {error_info}",
                                "error_type": "AI 执行错误",
                                "code": 500,
                                "conversation_id": current_conversation_id
                            })
                        )
                        logger.info("成功发送 error 信号给客户端。")
                    except Exception as send_err_detail:
                        logger.error(f"发送 error 信号给客户端时出错: {send_err_detail}", exc_info=True)
                else:
                     write_route_log("Response 不满足任何错误或直接内容发送条件，继续执行。")

            except asyncio.CancelledError:
                # 这是预期的，当用户点击停止时会触发
                logger.info(f"会话 {current_conversation_id}: 消息生成被明确取消。")

            except Exception as e:
                # 捕获所有其他来自 ai_manager.chat_completion 或 await 的异常
                logger.error(f"处理聊天请求时发生意外错误: {e}", exc_info=True)
                error_msg = f"处理您的请求时发生服务器内部错误: {str(e)}"
                try:
                    await manager.send_to_session(
                        current_conversation_id,
                        json.dumps({
                            "type": "error",
                            "error": error_msg,
                            "error_type": "服务器内部错误",
                            "code": 500,
                            "conversation_id": current_conversation_id
                        })
                    )
                except Exception as send_err:
                    logger.error(f"发送错误消息给客户端时也发生错误: {send_err}")

            finally:
                write_route_log("进入 finally 块...")
                write_route_log(f"finally 块收到的 response 类型: {type(response)}")
                if isinstance(response, dict):
                    write_route_log(f"finally 块收到的 response 键: {list(response.keys())}")
                    write_route_log(f"finally 块收到的 response 内容 (完整): {json.dumps(response, default=str)}") 
                elif response is not None:
                    write_route_log(f"finally 块收到的 response 内容: {str(response)}")
                else:
                    write_route_log("finally 块收到的 response 为 None")

                # 无论成功、失败还是取消，都发送一个完成信号给前端
                if not generation_cancelled_map.get(current_conversation_id, False):
                    write_route_log("尝试发送 complete 信号 (未被用户取消)...")
                    try:
                        complete_signal_data = {"type": "complete", "conversation_id": current_conversation_id}
                        
                        write_route_log(f"准备发送 standard complete 信号，数据: {json.dumps(complete_signal_data)}")
                        await manager.send_to_session(
                            current_conversation_id,
                            json.dumps(complete_signal_data)
                        )
                        write_route_log("成功发送 complete 信号给客户端。")
                    except Exception as send_err:
                         logger.error(f"发送 complete 信号给客户端时发生错误: {send_err}", exc_info=True)
                         write_route_log(f"ERROR: 发送 complete 信号给客户端时发生错误: {send_err}") 
                else:
                    write_route_log("检测到用户已取消，不发送 complete 信号。")
                
                # 重置取消状态
                logger.info("准备重置取消状态...")
                generation_cancelled_map[current_conversation_id] = False
                logger.info("已重置取消状态。 finally 块结束。")
    except WebSocketDisconnect:
        logger.info(f"WebSocket 连接已关闭: {websocket}")
        await manager.disconnect(websocket)
        # 清理此连接的取消状态
        if current_conversation_id in generation_cancelled_map:
            del generation_cancelled_map[current_conversation_id]
    except Exception as e:
        logger.error(f"WebSocket 聊天主循环出错: {e}", exc_info=True)
        try:
            # 尝试向客户端发送最后的错误消息
            if current_conversation_id:
                await manager.send_to_session(
                    current_conversation_id,
                    json.dumps({
                        "type": "error",
                        "error": "WebSocket 连接出现严重错误，请刷新重试。",
                        "error_type": "WebSocket 错误",
                        "code": 500,
                        "conversation_id": current_conversation_id
                    })
                )
            else:
                await manager.send_text(
                    json.dumps({
                        "type": "error",
                        "error": "WebSocket 连接出现严重错误，请刷新重试。",
                        "error_type": "WebSocket 错误",
                        "code": 500
                    }),
                    websocket
                )
        except Exception:
            pass
        finally:
            await manager.disconnect(websocket)
            # 清理此连接的取消状态
            if current_conversation_id in generation_cancelled_map:
                del generation_cancelled_map[current_conversation_id] 