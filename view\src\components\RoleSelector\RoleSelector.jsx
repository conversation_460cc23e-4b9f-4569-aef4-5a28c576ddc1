import React, { useState, useEffect } from 'react';
import { Button, Tooltip, message, Spin, Modal, Avatar, List, Space } from 'antd';
import { UserOutlined, InfoCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import './RoleSelector.css';
import RoleDetailModal from './RoleDetailModal';
import defaultLogo from '../../assets/images/logo-0.png';

// 持久化存储键名
const ROLE_STORAGE_KEY = 'jimu_selected_role_id';

/**
 * 角色选择器组件
 * 
 * @param {Object} props
 * @param {String} props.value - 当前选中的角色ID
 * @param {Function} props.onChange - 角色改变时的回调函数
 * @param {Boolean} props.disabled - 是否禁用
 * @param {String} props.className - 自定义类名
 * @returns {JSX.Element}
 */
const RoleSelector = ({ value, onChange, disabled = false, className = '' }) => {
  // 角色列表状态
  const [roles, setRoles] = useState([]);
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 当前选中的角色
  const [selectedRole, setSelectedRole] = useState(value);
  // 角色详情数据
  const [roleDetail, setRoleDetail] = useState(null);
  // 详情对话框是否可见
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  // 当前查看详情的角色ID
  const [viewingRoleId, setViewingRoleId] = useState(null);
  // 角色选择模态框是否可见
  const [roleModalVisible, setRoleModalVisible] = useState(false);

  // 默认角色配置
  const defaultRole = {
    role_id: '',
    role_name: '积木智能助手',
    description: '默认通用智能助手',
    avatar_url: defaultLogo
  };

  // 从localStorage获取保存的角色ID
  const getSavedRoleId = () => {
    try {
      return localStorage.getItem(ROLE_STORAGE_KEY);
    } catch (error) {
      console.error('从localStorage读取角色ID失败:', error);
      return null;
    }
  };

  // 保存角色ID到localStorage
  const saveRoleId = (roleId) => {
    try {
      if (roleId) {
        localStorage.setItem(ROLE_STORAGE_KEY, roleId);
      } else {
        localStorage.removeItem(ROLE_STORAGE_KEY);
      }
    } catch (error) {
      console.error('保存角色ID到localStorage失败:', error);
    }
  };

  // 获取所有角色信息
  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/roles');
      if (response.data && response.data.status === 'success') {
        setRoles(response.data.data || []);
      } else {
        message.error('获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表出错:', error);
      message.error('获取角色列表失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 查看角色详情
  const viewRoleDetails = async (roleId) => {
    if (!roleId) {
      message.info('默认角色无详细信息');
      return;
    }

    setViewingRoleId(roleId);
    
    try {
      const response = await axios.get(`/api/roles/${roleId}`);
      if (response.data && response.data.status === 'success') {
        setRoleDetail(response.data.data);
        setDetailModalVisible(true);
      } else {
        message.error('获取角色详情失败');
      }
    } catch (error) {
      console.error('获取角色详情出错:', error);
      message.error('获取角色详情失败: ' + (error.response?.data?.detail || error.message));
    }
  };

  // 关闭详情对话框
  const closeDetailModal = () => {
    setDetailModalVisible(false);
    setViewingRoleId(null);
  };

  // 处理选择变更
  const handleRoleSelect = (roleId) => {
    setSelectedRole(roleId);
    setRoleModalVisible(false);
    // 保存到localStorage
    saveRoleId(roleId);
    if (onChange) {
      onChange(roleId);
    }
  };

  // 打开角色选择模态框
  const openRoleModal = () => {
    fetchRoles(); // 获取最新角色数据
    setRoleModalVisible(true);
  };

  // 关闭角色选择模态框
  const closeRoleModal = () => {
    setRoleModalVisible(false);
  };

  // 组件加载时获取角色列表
  useEffect(() => {
    fetchRoles();
  }, []);

  // 组件加载时从localStorage读取保存的角色ID
  useEffect(() => {
    const savedRoleId = getSavedRoleId();
    // 如果外部未提供有效value且localStorage有保存的值，则使用保存的值
    if ((!value || value === '') && savedRoleId) {
      console.log('[RoleSelector] 页面刷新恢复roleId:', savedRoleId);
      setSelectedRole(savedRoleId);
      if (onChange) {
        onChange(savedRoleId);
      }
    } else {
      setSelectedRole(value);
    }
  }, []);

  // 当外部value变化时，更新内部状态
  useEffect(() => {
    if (value !== selectedRole) {
    setSelectedRole(value);
      // 同步更新localStorage
      if (value) {
        saveRoleId(value);
      }
    }
  }, [value]);

  // 获取当前选中的角色信息
  const getCurrentRole = () => {
    if (!selectedRole) {
      return defaultRole;
    }
    return roles.find(role => role.role_id === selectedRole) || defaultRole;
  };

  const currentRole = getCurrentRole();

  return (
    <div className={className}>
      <Button
        className="role-selector-button"
        onClick={openRoleModal}
        disabled={disabled}
        style={{ 
          display: 'flex', 
          alignItems: 'center',
          height: '32px',
          borderRadius: '4px',
          padding: className === 'home-role-selector' ? '0' : '4px 11px'
        }}
      >
        <Avatar 
          src={currentRole.avatar_url} 
          icon={<UserOutlined />} 
          size="small" 
          style={{ marginRight: className === 'home-role-selector' ? '0' : '-4' }} 
        />
        <span className="role-button-text">
          {currentRole.role_name || currentRole.role_definition || '选择角色'}
        </span>
      </Button>

      {/* 角色选择模态框 */}
      <Modal
        title="选择角色"
        open={roleModalVisible}
        onCancel={closeRoleModal}
        footer={[
          <Button key="cancel" onClick={closeRoleModal}>
            取消
          </Button>
        ]}
        width={500}
        className="role-selector-modal"
        centered
      >
        <Spin spinning={loading}>
          <List
            className="role-list"
            itemLayout="horizontal"
            dataSource={[defaultRole, ...roles]}
            renderItem={role => (
              <List.Item
                className={`role-list-item ${(selectedRole === role.role_id || (!selectedRole && !role.role_id)) ? 'role-selected' : ''}`}
                onClick={() => handleRoleSelect(role.role_id)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      src={role.avatar_url} 
                      icon={<UserOutlined />} 
                      size="large"
                      style={{ marginRight: 4 }}
                    />
                  }
                  title={<span className="role-name">{role.role_name || role.role_definition}</span>}
                  description={
                    role.description && (
                      <span className="role-desc">{role.description}</span>
                    )
                  }
                />
                <Space>
                  {role.role_id && (
                  <Button
                    type="link"
                    size="small"
                    className="role-detail-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      viewRoleDetails(role.role_id);
                    }}
                    icon={<InfoCircleOutlined />}
                  >
                    详情
                  </Button>
                  )}
                </Space>
              </List.Item>
            )}
          />
        </Spin>
      </Modal>
      
      <RoleDetailModal 
        visible={detailModalVisible}
        onClose={closeDetailModal}
        roleData={roleDetail}
      />
    </div>
  );
};

export default RoleSelector; 