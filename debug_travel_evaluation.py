#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 调试旅游评估问题
分析为什么旅游专家的回答得分很低
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from langgraph_enhancement.routers.hybrid_relevance_evaluator import HybridRelevanceEvaluator

def debug_travel_evaluation():
    """调试旅游评估问题"""
    
    print("🔍 开始调试旅游评估问题...")
    
    # 模拟实际的用户请求和智能体回答
    user_request = "东莞到新疆旅游，规划 路线"
    
    # 模拟实际的travel_info_collector回答（英文为主）
    agent_response = """I have already gathered information on transportation, popular attractions, and weather in Xinjiang. Here's what I found:

Transportation options from Dongguan to Xinjiang:
- Flight: Dongguan → Shenzhen → Urumqi (4-5 hours)
- Train: High-speed rail to Beijing, then train to Urumqi

Popular destinations in Xinjiang:
- Heavenly Lake (Tianchi)
- Kanas Lake
- Turpan
- Kashgar

Weather considerations:
- Large temperature differences between day and night
- Strong UV radiation, sunscreen recommended

Travel tips:
- Border permits required for some areas
- Pack warm clothes for temperature variations

关于东莞到新疆的旅游路线规划，我为您收集了以下信息：

## 🚗 交通方式选择
1. **飞机** - 最快选择
   - 东莞→深圳宝安机场→乌鲁木齐地窝堡机场
   - 飞行时间约4-5小时，转机1次
   
2. **高铁+火车** - 经济选择  
   - 东莞→广州南→北京西→乌鲁木齐
   - 总行程约30小时

## 🗺️ 新疆旅游路线推荐
1. **经典北疆环线**（8-10天）
   - 乌鲁木齐→天山天池→吐鲁番→喀纳斯→禾木→乌鲁木齐
   
2. **南疆丝路线**（10-12天）  
   - 乌鲁木齐→天山→库尔勒→库车→喀什→塔什库尔干

## 🏨 住宿建议
- 乌鲁木齐：市中心酒店，交通便利
- 喀纳斯：湖边小木屋，风景优美
- 吐鲁番：当地特色民宿

## 🍽️ 美食推荐
- 大盘鸡、羊肉串、抓饭
- 馕、奶茶、马奶子

## ⚠️ 注意事项
- 新疆昼夜温差大，需备好衣物
- 防晒措施要做足
- 提前办理边防证（部分地区需要）"""
    
    # 初始化评估器
    evaluator = HybridRelevanceEvaluator()
    
    print(f"\n📝 用户请求: {user_request}")
    print(f"📝 智能体回应: {agent_response[:100]}...")
    
    # 执行评估
    result = evaluator.evaluate(user_request, agent_response)
    
    print(f"\n📊 评估结果:")
    print(f"请求类型: {result.request_type}")
    print(f"评估结果: {result.result}")
    print(f"相关性得分: {result.score:.3f}")
    print(f"阈值: {evaluator.thresholds.get(result.request_type, 'N/A')}")
    print(f"详细信息: {result.details}")
    
    # 详细分析关键词提取
    print(f"\n🔍 关键词分析:")
    request_keywords = evaluator._extract_keywords_with_weight(user_request)
    response_keywords = evaluator._extract_keywords_with_weight(agent_response)
    
    print(f"用户请求关键词: {request_keywords}")
    print(f"智能体回应关键词: {dict(list(response_keywords.items())[:10])}...")  # 只显示前10个
    
    # 计算交集
    common_words = set(request_keywords.keys()) & set(response_keywords.keys())
    print(f"共同关键词: {common_words}")
    
    if common_words:
        weighted_relevance = evaluator._calculate_weighted_relevance(request_keywords, response_keywords)
        print(f"加权相关性: {weighted_relevance:.3f}")
    
    # 检查词典中的旅游相关词汇
    print(f"\n📚 词典中的旅游相关词汇:")
    travel_words = evaluator.hybrid_dict["semantic_categories"].get("travel_keywords", {}).get("words", [])
    print(f"旅游关键词: {travel_words}")
    
    # 检查用户请求和回应中包含的旅游词汇
    user_travel_words = [word for word in travel_words if word in user_request]
    response_travel_words = [word for word in travel_words if word in agent_response]
    
    print(f"用户请求中的旅游词汇: {user_travel_words}")
    print(f"智能体回应中的旅游词汇: {response_travel_words}")

if __name__ == "__main__":
    debug_travel_evaluation()