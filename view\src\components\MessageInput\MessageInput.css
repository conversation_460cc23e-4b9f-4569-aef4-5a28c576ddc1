.message-input-container {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

/* Enhanced wrapper for the actual input field */
.message-input-wrapper {
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 8px 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.message-input-wrapper:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.message-input-wrapper:hover {
  border-color: #40a9ff;
}

.message-textarea {
  flex: 1;
  border: none;
  resize: none;
  box-shadow: none;
  padding: 12px 0;
  background: transparent;
  font-size: 1rem;
  color: #1f2937;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  position: relative;
  z-index: 2;
}

.message-textarea:focus {
  box-shadow: none;
  outline: none;
}

.message-textarea::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

/* Enhanced actions area */
.input-message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
  width: 100%;
  z-index: 10;
  position: relative;
  opacity: 1 !important;
  visibility: visible !important;
}

.input-left-actions, .input-right-actions {
  display: flex;
  align-items: center;
  gap: 12px; 
  opacity: 1 !important; /* 强制显示 */
  visibility: visible !important; /* 强制显示 */
}

.input-left-actions .ant-btn {
  color: #6b7280;
  font-size: 18px;
  padding: 0;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  opacity: 1 !important;
  visibility: visible !important;
  transition: all 0.3s ease;
}

.input-left-actions .ant-btn:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
}

/* Enhanced mode selector */
.input-mode-selector {
  background-color: #f3f4f6;
  border-radius: 16px;
  padding: 4px 10px;
  font-size: 0.85rem;
  color: #4b5563;
  border: none;
  display: flex !important;
  align-items: center !important;
  gap: 6px;
  min-width: 100px;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 100;
}

/* Enhanced primary state for mode selector */
.input-mode-selector.ant-btn-primary {
  background-color: #1890ff !important;
  color: white !important;
}

.input-mode-selector .anticon {
  font-size: 1rem;
}

/* Enhanced send button */
.input-send-button {
  background-color: #1890ff;
  border-color: #1890ff;
  border-radius: 12px;
  width: 40px;
  height: 40px;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0;
  font-size: 20px;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 100;
}

.input-send-button .anticon {
  color: white;
}

.input-send-button:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.input-send-button:disabled {
  background-color: #d1d5db;
  border-color: #d1d5db;
  cursor: not-allowed;
}

.input-send-button:disabled .anticon {
  color: #9ca3af;
}

/* 新增：停止按钮样式 */
.input-stop-button {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  border-radius: 12px;
  width: 30px;
  height: 30px;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0;
  font-size: 20px;
}

.input-stop-button:hover {
  background-color: #ff7875;
  border-color: #ff7875;
}

/* 强制显示右侧按钮 */
.input-right-actions button {
  overflow: visible !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Removed old styles */
/*
.message-input {
  display: flex;
  align-items: flex-end;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  transition: all 0.3s;
}

.message-input:focus-within {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}
*/

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .message-input-wrapper {
    border-radius: 16px; /* Slightly smaller radius */
    padding: 6px 12px;
  }

  .input-message-actions {
    flex-wrap: wrap; /* Allow actions to wrap */
    padding: 0;
    gap: 8px;
  }

  .input-right-actions {
    /* Ensure right actions stay together */
    margin-left: auto;
  }
  
  .input-send-button {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
}

/* 添加Agent模式下的样式 */
.message-input-wrapper.agent-mode {
  border: 1px solid #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.agent-mode-indicator {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin-right: 8px;
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 4px;
  color: #52c41a;
  font-size: 12px;
}

/* Remove duplicate container styles - already defined above */

.message-input-wrapper {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
}

.message-input-wrapper:hover {
  border-color: #40a9ff;
}

.message-input-wrapper:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.message-textarea {
  border: none !important;
  box-shadow: none !important;
  resize: none !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.input-message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-left-actions,
.input-right-actions {
  display: flex;
  align-items: center;
}

.input-send-button {
  width: 32px;
  height: 32px;
  padding: 0 !important;
  border-radius: 50% !important;
}

.input-mode-selector {
  margin-right: 8px;
  height: 32px;
  font-size: 12px;
}

.tools-container {
  padding: 16px;
}

.tool-card {
  margin-bottom: 16px;
}

.tool-details {
  margin-top: 16px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .message-input-container {
    padding: 8px;
  }
  
  .input-message-actions {
    flex-direction: column;
    align-items: flex-end;
  }
  
  .input-left-actions {
    margin-bottom: 8px;
  }
}

.input-left-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.input-left-actions .role-selector-compact {
  margin-right: 8px;
}

/* 在消息输入区域内调整角色选择器的样式 */
.message-input-container .ant-select-selector {
  height: 32px;
  display: flex;
  align-items: center;
}