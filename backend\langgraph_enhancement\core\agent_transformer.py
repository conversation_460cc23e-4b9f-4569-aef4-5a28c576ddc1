"""
🔥 智能体转换工具 - LangGraph 增强系统的核心组件

将 YAML 配置转换为 LangGraph 智能体
100% 复用现有 JIMU 系统（AIManager、generate_custom_prompt、MCP）
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
import yaml

# 🔥 使用桥接器复用现有系统
from ..utils import (
    get_ai_manager_bridge,
    get_mcp_bridge,
    get_prompt_builder
)
from backend.mcp.prompts import (
    JIMU_ROLE_DEFINITION,
    SYSTEM_PROTECTION_GUIDELINES,
    MCP_TOOL_GUIDELINES
)

logger = logging.getLogger(__name__)


class LangGraphAgentTransformer:
    """
    🔥 智能体转换工具 - 项目核心组件
    
    功能：
    - 将 YAML 配置转换为 LangGraph 智能体
    - 100% 复用现有 generate_custom_prompt 系统
    - 零破坏性集成，完全兼容现有架构
    """
    
    def __init__(self):
        """初始化转换工具，复用现有系统组件"""
        # 🔥 使用桥接器零修改复用现有系统
        self.ai_bridge = get_ai_manager_bridge()
        self.mcp_bridge = get_mcp_bridge()
        self.prompt_builder = get_prompt_builder()
        
        # 保持向后兼容
        self.ai_manager = self.ai_bridge.ai_manager
        self.generate_custom_prompt = self.prompt_builder.generate_custom_prompt
        self.mcp_server = self.mcp_bridge.mcp_server
        
        logger.info("🔥 智能体转换工具初始化完成 - 通过桥接器复用现有系统")
    
    async def transform_config_to_agent(self, agent_id: str, agent_config: Dict, team_name: str = "", team_rules: list = None) -> 'ContextOptimizedAgent':
        """
        将 YAML 配置转换为可运行的 LangGraph 智能体
        
        Args:
            agent_id: 智能体唯一标识
            agent_config: YAML 配置字典
            team_name: 团队名称
            team_rules: 团队协作规则列表
            
        Returns:
            ContextOptimizedAgent: 可运行的智能体实例
        """
        try:
            logger.info(f"🔥 开始转换智能体配置: {agent_id}")
            
            # 1. 验证配置格式
            self._validate_agent_config(agent_config)
            
            # 2. 🔥 使用现有系统构建系统提示词，传递团队信息
            system_prompt = await self._build_system_prompt_with_existing_system(
                agent_config, 
                team_name=team_name, 
                team_rules=team_rules
            )
            
            # 3. 创建上下文优化的智能体
            from ..agents.context_optimized_agent import ContextOptimizedAgent
            
            agent = ContextOptimizedAgent(
                agent_id=agent_id,
                config=agent_config,
                system_prompt=system_prompt,
                ai_manager=self.ai_manager,  # 零修改复用
                context_strategy=agent_config.get('context_strategy', 'adaptive')
            )
            
            logger.info(f"✅ 智能体转换完成: {agent_id}")
            return agent
            
        except Exception as e:
            logger.error(f"❌ 智能体转换失败 {agent_id}: {str(e)}")
            raise
    
    async def _build_system_prompt_with_existing_system(self, config: Dict, team_name: str = "", team_rules: list = None) -> str:
        """
        🔥 使用现有 generate_custom_prompt 系统构建完整提示词
        
        完全复用：
        - generate_custom_prompt() 函数
        - mcp_server.list_tools_from_servers() 工具获取
        - JIMU_ROLE_DEFINITION 和 SYSTEM_PROTECTION_GUIDELINES
        
        Args:
            config: 智能体配置
            team_name: 团队名称
            team_rules: 团队协作规则列表
        """
        try:
            # 1. 获取关联的 MCP 工具（复用现有逻辑）
            mcp_tools = await self._get_mcp_tools_from_servers(
                config.get('associated_servers', [])
            )
            
            # 2. 构建上下文策略描述
            context_strategy_section = {}
            if config.get('context_strategy_description'):
                context_strategy_section = {
                    "Context Management Strategy": config.get('context_strategy_description', 'Adaptive context optimization')
                }
            
            # 3. 🔥 使用现有的 generate_custom_prompt 完全复用，添加团队信息
            # 🧠 从配置中读取记忆工具启用设置，默认为True（向后兼容）
            enable_memory_tools = config.get('enable_memory_tools', True)
            
            system_prompt = self.generate_custom_prompt(
                role_name=config.get('role_name', ''),
                role_definition=config.get('role_definition', ''),
                background=config.get('background', ''),
                description=config.get('description', ''),
                core_rules=config.get('core_rules', []),
                mcp_tools=mcp_tools,
                team_name=team_name,  # 🔥 新增：传递团队名称
                team_rules=team_rules,  # 🔥 新增：传递团队规则
                include_system_protection=True,
                include_mcp_capabilities=True,
                include_mcp_examples=True,
                include_mcp_definitions=True,
                custom_sections_before_tools=context_strategy_section,
                is_langgraph_agent=enable_memory_tools  # 🧠 根据配置决定是否启用记忆工具
            )
            
            logger.info(f"✅ 系统提示词构建完成，长度: {len(system_prompt)} 字符")
            return system_prompt
            
        except Exception as e:
            logger.error(f"❌ 系统提示词构建失败: {str(e)}")
            raise
    
    async def _get_mcp_tools_from_servers(self, server_names: List[str]) -> List[Dict]:
        """
        获取指定服务器的 MCP 工具列表
        复用现有的 mcp_server.list_tools_from_servers() 方法
        """
        if not server_names:
            return []
        
        try:
            logger.info(f"🔧 获取 MCP 工具: {server_names}")
            
            # 🔥 零修改复用现有系统
            mcp_tools = await self.mcp_server.list_tools_from_servers(server_names)
            
            # 确保每个工具都有 serverName 属性（复用现有逻辑）
            for tool in mcp_tools:
                if 'name' in tool and '_' in tool['name']:
                    server_name, tool_name = tool['name'].split('_', 1)
                    if 'serverName' not in tool:
                        tool['serverName'] = server_name
            
            logger.info(f"✅ 获取到 {len(mcp_tools)} 个 MCP 工具")
            return mcp_tools
            
        except Exception as e:
            logger.error(f"❌ MCP 工具获取失败: {str(e)}")
            return []
    
    def _validate_agent_config(self, config: Dict) -> None:
        """验证智能体配置格式"""
        required_fields = ['role_name', 'role_definition', 'description']
        
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"缺少必要字段: {field}")
        
        # 验证 core_rules 格式
        if 'core_rules' in config and not isinstance(config['core_rules'], list):
            raise ValueError("core_rules 必须是列表格式")
        
        # 验证 associated_servers 格式
        if 'associated_servers' in config and not isinstance(config['associated_servers'], list):
            raise ValueError("associated_servers 必须是列表格式")
        
        logger.info("✅ 智能体配置验证通过")
    
    async def transform_multiple_agents(self, agents_config: Dict) -> Dict[str, 'ContextOptimizedAgent']:
        """批量转换多个智能体"""
        agents = {}
        
        for agent_id, agent_config in agents_config.items():
            try:
                agent = await self.transform_config_to_agent(agent_id, agent_config)
                agents[agent_id] = agent
                logger.info(f"✅ 批量转换成功: {agent_id}")
            except Exception as e:
                logger.error(f"❌ 批量转换失败: {agent_id} - {str(e)}")
                # 继续处理其他智能体，不中断整个流程
                continue
        
        logger.info(f"🎯 批量转换完成，成功: {len(agents)} 个智能体")
        return agents


class ConfigDrivenAgentFactory:
    """
    🔥 配置驱动的智能体工厂
    
    高级功能：
    - 从 YAML 文件加载配置
    - 动态创建智能体实例
    - 配置热更新支持
    """
    
    def __init__(self, config_path: str = None):
        """初始化工厂"""
        self.transformer = LangGraphAgentTransformer()
        # 修复配置文件路径
        if config_path:
            self.config_path = config_path
        else:
            # 从当前文件位置计算配置文件路径
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.config_path = os.path.join(current_dir, "..", "config", "agents.yaml")
        self._agents_cache = {}
        
    async def load_agents_from_config(self) -> Dict[str, 'ContextOptimizedAgent']:
        """从配置文件加载所有智能体"""
        try:
            # 加载 YAML 配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            agents_config = config.get('agents', {})
            if not agents_config:
                logger.warning("⚠️ 配置文件中没有找到智能体定义")
                return {}
            
            # 批量转换智能体
            agents = await self.transformer.transform_multiple_agents(agents_config)
            
            # 缓存结果
            self._agents_cache = agents
            
            logger.info(f"🎯 从配置文件加载了 {len(agents)} 个智能体")
            return agents
            
        except Exception as e:
            logger.error(f"❌ 从配置文件加载智能体失败: {str(e)}")
            return {}
    
    async def get_agent(self, agent_id: str) -> Optional['ContextOptimizedAgent']:
        """获取指定智能体"""
        if agent_id in self._agents_cache:
            return self._agents_cache[agent_id]
        
        # 如果缓存中没有，尝试重新加载
        await self.load_agents_from_config()
        return self._agents_cache.get(agent_id)
    
    def get_available_agents(self) -> List[str]:
        """获取所有可用智能体ID列表"""
        return list(self._agents_cache.keys())
    
    async def create_agent_from_config(self, agent_id: str, config: Dict, team_name: str = "", team_rules: list = None) -> 'ContextOptimizedAgent':
        """从配置直接创建智能体（兼容方法名）"""
        return await self.transformer.transform_config_to_agent(agent_id, config, team_name=team_name, team_rules=team_rules)


# 🔥 全局工厂实例（单例模式）
_global_agent_factory = None

def get_agent_factory() -> ConfigDrivenAgentFactory:
    """获取全局智能体工厂实例"""
    global _global_agent_factory
    if _global_agent_factory is None:
        _global_agent_factory = ConfigDrivenAgentFactory()
    return _global_agent_factory


async def quick_transform_agent(agent_id: str, agent_config: Dict) -> 'ContextOptimizedAgent':
    """快速转换单个智能体的便捷函数"""
    transformer = LangGraphAgentTransformer()
    return await transformer.transform_config_to_agent(agent_id, agent_config)


# 导出主要类和函数
__all__ = [
    'LangGraphAgentTransformer',
    'ConfigDrivenAgentFactory', 
    'get_agent_factory',
    'quick_transform_agent'
] 