.main-content-layout {
  height: 100vh;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
}

.main-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  animation: pageSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 移除宽度限制，恢复全屏显示 */
  /* max-width: 900px; */
  /* margin: 0 auto; */
}

@keyframes pageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.messages-container {
  flex: 1;
  overflow-y: scroll;
  padding: 0 8px 8px 8px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #ffffff;
}

.scroll-to-bottom-button {
  position: fixed;
  bottom: 175px;
  right: 60px;
  z-index: 1000;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 50%;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scrollButtonFloat 3s ease-in-out infinite;
}

.scroll-to-bottom-button:hover {
  transform: translateY(-6px) scale(1.1);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.4);
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  animation: none;
}

.scroll-to-bottom-button .anticon {
  color: white;
  font-size: 18px;
  transition: transform 0.3s ease;
}

.scroll-to-bottom-button:hover .anticon {
  transform: scale(1.1);
}

@keyframes scrollButtonFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-3px) rotate(2deg);
  }
}
/* Enhanced scrollbar styles */
.messages-container::-webkit-scrollbar {
  width: 8px;
  background-color: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
}

.messages-container.scrolling::-webkit-scrollbar {
  width: 0px !important;
  background: transparent !important;
}

.messages-container.scrolling::-webkit-scrollbar-thumb {
  background: transparent !important;
  width: 0px !important;
}

.messages-container.scrolling::-webkit-scrollbar-track {
  background: transparent !important;
  width: 0px !important;
}

/* Firefox scrollbar styles */
.messages-container {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.messages-container.scrolling {
  scrollbar-width: none !important;
  scrollbar-color: transparent transparent !important;
  -ms-overflow-style: none !important;
}

/* 确保在所有浏览器中都隐藏滚动条 */
.messages-container.scrolling::-webkit-scrollbar-corner {
  background: transparent !important;
}

.messages-container.scrolling::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.conversation-title {
  position: sticky;
  top: 0;
  background-color: #ffffff;
  z-index: 10;
  padding-top: 14px;
  margin-top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 1.2rem;
  animation: titleSlideDown 0.5s ease-out;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.conversation-title:hover {
  background-color: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(8px);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

@keyframes titleSlideDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.conversation-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-to-list {
  font-size: 14px;
}

.history-header {
  padding: 16px;
}

.messages-list {
  flex: 1;
}

.message-container {
  display: flex;
  margin-bottom: 12px;
  animation: messageContainerFadeIn 0.4s ease-out;
}

@keyframes messageContainerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
  margin-bottom: 14px;
}

.message-avatar {
  margin-right: 4px;
  align-self: flex-start;
}

.user-message .message-avatar {
  margin-right: 0;
  margin-left: 4px;
}

.user-avatar {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 12px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
  box-shadow: 0 3px 12px rgba(24, 144, 255, 0.25), 0 1px 4px rgba(24, 144, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2px solid rgba(255, 255, 255, 0.9);
  animation: avatarScaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s both;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.35), 0 2px 8px rgba(24, 144, 255, 0.2);
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
}

.ai-avatar {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 12px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
  box-shadow: 0 3px 12px rgba(82, 196, 26, 0.25), 0 1px 4px rgba(82, 196, 26, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2px solid rgba(255, 255, 255, 0.9);
  animation: avatarScaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s both;
}

.ai-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(82, 196, 26, 0.35), 0 2px 8px rgba(82, 196, 26, 0.2);
  background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
}

/* 当AI头像中有图片时移除背景色 - 使用:has()选择器（现代浏览器） */
.ai-avatar:has(img) {
  background-color: transparent;
}

/* 兼容性更好的选择器 - 直接为图片设置样式 */
.ai-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
  background-color: transparent;
  transition: transform 0.3s ease;
}

.ai-avatar:hover img {
  transform: scale(1.05);
}

.user-message .message-main {
  align-items: flex-end;
}

.ai-message .message-main {
  align-items: flex-start;
}

.message-content {
  max-width: 100%;
  width: fit-content;
  min-width: 300px;
  border-radius: 16px;
  padding: 12px 16px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
  position: relative;
  background-color: #f9f9f9;
  font-size: 15px;
  line-height: 1.4;
  letter-spacing: 0.01em;
  font-weight: 400;
  color: #374151;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(0, 0, 0, 0.04);
  animation: bubbleScaleIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: center;
}

/* 当消息内容包含文件附件时，扩展容器宽度 */
.message-content:has(.file-attachment) {
  width: auto;
  min-width: 400px;
  max-width: 500px;
}

.user-message .message-content {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: 1px solid rgba(24, 144, 255, 0.3);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25), 0 2px 8px rgba(24, 144, 255, 0.15);
  animation: userBubbleScaleIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation-fill-mode: both;
  color: white;
  font-weight: 400;
}

.ai-message .message-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
  animation: aiBubbleScaleIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation-fill-mode: both;
  color: #1f2937;
  font-weight: 400;
}

/* 完全禁用消息气泡的hover效果以防止偏移 */
.message-content:hover {
  /* 彻底禁用所有可能导致偏移的效果 */
  transform: none !important;
  /* 保留原始阴影，不增强 */
  box-shadow: inherit !important;
  /* 保留原始背景 */
  background: inherit !important;
}

.user-message .message-content:hover {
  /* 彻底禁用所有可能导致偏移的效果 */
  transform: none !important;
  /* 保留原始阴影，不增强 */
  box-shadow: inherit !important;
  /* 保留原始背景 */
  background: inherit !important;
}

.ai-message .message-content:hover {
  /* 彻底禁用所有可能导致偏移的效果 */
  transform: none !important;
  /* 保留原始阴影，不增强 */
  box-shadow: inherit !important;
  /* 保留原始背景 */
  background: inherit !important;
}

/* Enhanced bubble scale animation */
@keyframes bubbleScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(15px);
  }
  30% {
    opacity: 0.7;
    transform: scale(0.7) translateY(5px);
  }
  60% {
    opacity: 0.9;
    transform: scale(1.05) translateY(-2px);
  }
  80% {
    opacity: 1;
    transform: scale(0.98) translateY(1px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Special animation for new messages */
.message-content.new-message {
  animation: bubbleScaleInNew 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes bubbleScaleInNew {
  0% {
    opacity: 0;
    transform: scale(0.2) translateY(20px) rotate(2deg);
  }
  25% {
    opacity: 0.5;
    transform: scale(0.6) translateY(10px) rotate(1deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.08) translateY(-3px) rotate(-0.5deg);
  }
  75% {
    opacity: 0.95;
    transform: scale(0.96) translateY(2px) rotate(0.2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotate(0deg);
  }
}

/* 用户消息专用动画 - 从右侧飞入 */
@keyframes userBubbleScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateX(30px) translateY(10px);
  }
  40% {
    opacity: 0.7;
    transform: scale(0.8) translateX(5px) translateY(3px);
  }
  70% {
    opacity: 0.9;
    transform: scale(1.05) translateX(-2px) translateY(-1px);
  }
  90% {
    opacity: 1;
    transform: scale(0.98) translateX(1px) translateY(0.5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateX(0) translateY(0);
  }
}

/* AI消息专用动画 - 从左侧飞入 */
@keyframes aiBubbleScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateX(-30px) translateY(10px);
  }
  40% {
    opacity: 0.7;
    transform: scale(0.8) translateX(-5px) translateY(3px);
  }
  70% {
    opacity: 0.9;
    transform: scale(1.05) translateX(2px) translateY(-1px);
  }
  90% {
    opacity: 1;
    transform: scale(0.98) translateX(-1px) translateY(0.5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateX(0) translateY(0);
  }
}

/* 头像动画 */
@keyframes avatarScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.2) rotate(-10deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(5deg);
  }
  80% {
    opacity: 1;
    transform: scale(0.95) rotate(-2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 时间戳延迟动画 */
@keyframes fadeInDelayed {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 全局流畅性优化 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 为所有交互元素添加统一的过渡，但排除工具调用和代码块相关组件 */
button:not(.function-call-card *):not(.function-result-card *):not(.tool-call-card *):not(.tool-result-card *):not(.tool-error-card *):not(.code-card *):not(.error-card *):not(.code-block-wrapper *), 
.ant-btn:not(.function-call-card *):not(.function-result-card *):not(.tool-call-card *):not(.tool-result-card *):not(.tool-error-card *):not(.code-card *):not(.error-card *):not(.code-block-wrapper *), 
.ant-select:not(.function-call-card *):not(.function-result-card *):not(.tool-call-card *):not(.tool-result-card *):not(.tool-error-card *):not(.code-card *):not(.error-card *):not(.code-block-wrapper *), 
.ant-input:not(.function-call-card *):not(.function-result-card *):not(.tool-call-card *):not(.tool-result-card *):not(.tool-error-card *):not(.code-card *):not(.error-card *):not(.code-block-wrapper *), 
.ant-card:not(.function-call-card):not(.function-result-card):not(.tool-call-card):not(.tool-result-card):not(.tool-error-card):not(.code-card):not(.error-card) {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* 确保工具调用和代码块相关的Ant Design组件不会有任何动画 */
.function-call-card .ant-card,
.function-result-card .ant-card,
.tool-call-card .ant-card,
.tool-result-card .ant-card,
.tool-error-card .ant-card,
.code-card .ant-card,
.error-card .ant-card,
.function-call-card *,
.function-result-card *,
.tool-call-card *,
.tool-result-card *,
.tool-error-card *,
.code-card *,
.error-card * {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* 确保代码块本身不会有动画 */
.message-content pre,
.message-content pre *,
.code-block-wrapper,
.code-block-wrapper * {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* 添加微妙的入场延迟动画 */
.message-container:nth-child(1) { animation-delay: 0s; }
.message-container:nth-child(2) { animation-delay: 0.1s; }
.message-container:nth-child(3) { animation-delay: 0.2s; }
.message-container:nth-child(4) { animation-delay: 0.3s; }
.message-container:nth-child(5) { animation-delay: 0.4s; }

/* 优化GPU加速 */
.message-content,
.ai-thinking,
.typing-indicator,
.scroll-to-bottom-button,
.user-avatar,
.ai-avatar {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 添加微交互动画 */
.ant-btn:active {
  transform: scale(0.95) !important;
  transition: transform 0.1s ease !important;
}

.message-content:active {
  /* 完全禁用点击时的transform效果以防止偏移 */
  transform: none !important;
  transition: none !important;
}

/* 页面切换动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateX(30px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced message header styling */
.message-header {
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.message-content:hover .message-header {
  opacity: 1;
}

/* Subtle inner glow for user messages */
.user-message .message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border-radius: 16px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-message .message-content:hover::before {
  opacity: 1;
}

/* Subtle shadow for AI messages */
.ai-message .message-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.02) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 8px;
  padding: 0 2px;
}

.user-message .message-header {
  /* Keep username first, then time */
  /* flex-direction: row; */ /* Changed from row */
  flex-direction: column; /* Stack username and time */
  align-items: flex-end; /* Align items to the right */
  gap: 2px; /* Reduce gap for vertical layout */
}

.ai-message .message-header {
  /* Keep AI name, badge, then time */
  /* flex-direction: row; */
  flex-direction: column; /* Stack elements vertically */
  align-items: flex-start; /* Align items to the left */
  gap: 2px; /* Reduce gap for vertical layout */
}

/* Style for the top line of AI header (name + badge) */
.ai-header-top-line {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px; /* Keep original gap for name and badge */
}

.service-badge {
  margin-left: 8px;
}

.message-time {
  font-size: 11px;
  color: #8c8c8c;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 8px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
  animation: fadeInDelayed 0.4s ease-out 0.3s both;
}

.message-time:hover {
  background: rgba(255, 255, 255, 0.95);
  color: #666;
  transform: scale(1.02);
}

.message-body {
  line-height: 1.4;
  font-size: 15px;
  letter-spacing: 0.01em;
  font-weight: 400;
}

.message-body p {
  margin-bottom: 0.6em;
  margin-top: 0;
  line-height: 1.4;
}

.message-body p:last-child {
  margin-bottom: 0;
}

.message-body pre {
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.function-call-card, 
.function-result-card {
  margin-top: 8px;
  border-radius: 8px;
}

.error-alert {
  margin-bottom: 16px;
}

.ai-thinking {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative !important;
  overflow: hidden;
  z-index: 100 !important;
  animation: aiThinkingAppear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* 确保 ai-thinking 组件在所有容器中都可见 */
.messages-container .ai-thinking {
  z-index: 100 !important;
  position: relative !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.ai-thinking::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: radial-gradient(circle at 50% 50%, rgba(24, 144, 255, 0.08) 0%, transparent 70%); */
  z-index: 99;
  animation: backgroundPulse 3s ease-in-out infinite;
}

@keyframes aiThinkingAppear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);
  /* padding: 16px 24px; */
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
  margin-bottom: 16px;
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  z-index: 50;
  backdrop-filter: blur(10px);
}

/* Enhanced message body typing indicator */
.message-body .typing-indicator {
  padding: 12px 16px;
  margin: 8px 0;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(64, 169, 255, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(24, 144, 255, 0.1);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.typing-indicator span {
  height: 10px;
  width: 10px;
  margin: 0 4px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.6s infinite ease-in-out both;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.3s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.6s;
}

/* Add a subtle text indicator */
.ai-thinking::after {
  content: 'AI正在思考中...';
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #64748b;
  z-index: 102;
  font-weight: 500;
  opacity: 0.8;
  animation: textFade 2s ease-in-out infinite;
}

@keyframes textFade {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Enhance the typing indicator with a subtle glow effect */
.typing-indicator {
  position: relative;
}

.typing-indicator::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.2) 0%, rgba(64, 169, 255, 0.1) 100%);
  border-radius: 18px;
  z-index: -1;
  opacity: 0;
  animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% { opacity: 0; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
}

/* Add a floating animation to the entire ai-thinking container */
.ai-thinking {
  animation: aiThinkingAppear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1), aiThinkingFloat 4s ease-in-out infinite 0.6s;
}

@keyframes aiThinkingFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-3px) scale(1.01); }
}

@keyframes typing {
  0% {
    transform: scale(1) translateY(0);
    opacity: 0.6;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }
  50% {
    transform: scale(1.3) translateY(-8px);
    opacity: 1;
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.5);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 0.6;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }
}

.stop-generation-button {
  margin-top: 12px;
  border-radius: 12px;
  font-weight: 600;
  padding: 8px 24px;
  height: 40px;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 16px rgba(255, 77, 79, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 60;
  overflow: hidden;
}

.stop-generation-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.stop-generation-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 24px rgba(255, 77, 79, 0.4);
  background: linear-gradient(135deg, #ff7875 0%, #ffa39e 100%);
}

.stop-generation-button:hover::before {
  left: 100%;
}

.stop-generation-button:active {
  transform: translateY(-1px) scale(0.98);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.5);
}

.input-container {
  padding: 12px;
  border-top: none; /* 移除顶部边框 */
  background-color: #ffffff;
  margin-right: 8px;
}

.service-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 10px;
  margin-left: 16px;
}

.service-selector .ant-select {
  width: 120px;
}

.service-selector .ant-btn {
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
}

.service-selector .ant-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-selector .ant-btn img {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.service-selector .ant-btn:hover img {
  transform: scale(1.1);
}

.service-selector .ant-btn-primary {
  background-color: #1890ff;
}

.service-selector .ant-btn-primary:hover {
  background-color: #40a9ff;
}

/* 修复按钮内图标和文字的对齐 */
.service-selector .ant-btn img,
.service-selector .ant-select-selection-item img {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  vertical-align: middle;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .main-content {
    padding-bottom: 60px; /* 为移动版的底部导航栏留出空间 */
    /* 移除宽度限制，恢复全屏显示 */
    max-width: none;
  }
  
  .messages-container {
    padding: 0 6px 6px 6px; /* 移动端进一步减少边距 */
  }
  
  .message-container {
    margin-bottom: 10px; /* 移动端减少消息间距 */
  }
  
  .user-message {
    margin-bottom: 12px; /* 移动端减少用户消息间距 */
  }
  
  .message-content {
    max-width: 95%;
    padding: 5px 8px; /* 移动端减少消息内容边距 */
    padding-bottom: 5px;
    font-size: 14px;
    line-height: 1.3;
  }
  
  .message-body {
    font-size: 14px;
    line-height: 1.3;
  }
  
  .message-body h1 {
    font-size: 1.3em;
  }
  
  .message-body h2 {
    font-size: 1.2em;
  }
  
  .message-body h3 {
    font-size: 1.05em;
  }
  
  .message-content code:not(pre code) {
    font-size: 0.85em;
    padding: 0.25em 0.5em;
  }
  
  .input-container {
    padding: 8px; /* 移动端减少输入容器边距 */
    margin-right: 6px;
  }
  
  .message-avatar {
    margin-right: 3px; /* 移动端减少头像边距 */
  }
  
  .user-message .message-avatar {
    margin-left: 3px;
  }
}

/* 超小屏幕优化 */
@media screen and (max-width: 480px) {
  .messages-container {
    padding: 0 4px 4px 4px; /* 超小屏幕最小边距 */
  }
  
  .message-container {
    margin-bottom: 8px; /* 超小屏幕最小消息间距 */
  }
  
  .user-message {
    margin-bottom: 10px; /* 超小屏幕用户消息间距 */
  }
  
  .message-content {
    padding: 4px 6px; /* 超小屏幕最小内边距 */
    padding-bottom: 4px;
    max-width: 98%; /* 超小屏幕最大宽度利用 */
    font-size: 13px;
    line-height: 1.3;
  }
  
  .message-body {
    font-size: 13px;
    line-height: 1.3;
  }
  
  .input-container {
    padding: 6px; /* 超小屏幕最小输入容器边距 */
    margin-right: 4px;
  }
  
  .message-avatar {
    margin-right: 2px; /* 超小屏幕最小头像边距 */
  }
  
  .user-message .message-avatar {
    margin-left: 2px;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1200px) {
  .messages-container {
    padding: 0 10px 10px 10px; /* 大屏幕适中边距 */
  }
  
  .message-container {
    margin-bottom: 14px; /* 大屏幕适中消息间距 */
  }
  
  .user-message {
    margin-bottom: 16px; /* 大屏幕适中用户消息间距 */
  }
}

/* 工具调用相关样式 */
.function-call-card,
.function-result-card,
.tool-call-card,
.tool-result-card,
.tool-error-card {
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 8px;
  /* 移除动画以防止在消息生成时的跳动效果 */
  /* animation: cardSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94); */
  /* 完全禁用动画和过渡效果，避免任何视觉跳动 */
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* 完全禁用工具调用卡片的hover效果，避免跳动 */
.function-call-card:hover,
.function-result-card:hover,
.tool-call-card:hover,
.tool-result-card:hover,
.tool-error-card:hover {
  /* 移除所有hover动画效果 */
  transform: none !important;
  box-shadow: none !important;
  background: inherit !important;
}

/* 注释掉cardSlideIn动画以防止跳动
@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
*/

.function-call-card {
  border-color: #1890ff;
}

.function-result-card {
  border-color: #52c41a;
}

.tool-call-card {
  border-color: #1890ff;
}

.tool-result-card {
  border-color: #52c41a;
}

.tool-error-card {
  border-color: #ff4d4f;
}

.card-title-with-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-button {
  padding: 0 8px;
  font-size: 12px;
}

.tool-calls-history {
  margin-top: 16px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

/* Styles for the Home/Welcome View */
.home-view-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
  background-color: #ffffff;
  max-width: 900px; /* 恢复最大宽度限制 */
  margin: 0 auto; /* 添加左右自动边距以居中 */
}

.welcome-header {
  margin-bottom: 30px;
  text-align: left;
  width: 100%;
}

.welcome-header h1,
.welcome-header .ant-typography {
  /* Override Ant Design Title default margins */
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Regular weight for "Hi there," */
.welcome-greeting {
  font-size: 2.8rem; 
  font-weight: 400; 
  color: #1f2937; 
  /* Removed margin-bottom from here */
  line-height: 1.05; /* Reduced line-height */
  display: inline;
}

/* Style for the name part "John" with gradient */
.greeting-name-gradient {
  font-size: 2.8rem; 
  font-weight: 700; 
  /* Reversed gradient */
  background: linear-gradient(to right, #6366f1, #a855f7); 
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-left: 0.25em;
  line-height: 1.05; 
}

/* Bold prompt with gradient */
.welcome-greeting,.welcome-prompt {
  font-size: 2.8rem; 
  font-weight: 700;
  margin-bottom: 8px !important; 
  /* Reversed gradient */
  background: linear-gradient(to right, rgb(44, 44, 44),#a855f7, #6366f1); 
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent !important; 
  line-height: 1.05; 
}

.welcome-subtitle {
  font-size: 1.0rem; 
  color: #6b7280; 
  margin: 0; 
  padding-left: 2px; 
  margin-top: 4px;
  /* Add max-width to force wrapping */
  max-width: 550px; 
}

.prompt-suggestions {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  width: 100%;
}

.suggestion-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 0px;
  width: 210px;
  height: 140px;
  display: flex;
  flex-direction: column;
  text-align: left;
  cursor: pointer;
  transition: box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
  position: relative;
}

.suggestion-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-3px);
}

.suggestion-card-text {
  font-size: 0.85rem;
  color: #000000;
  font-weight: bold;
  font-weight: 400;
  margin-bottom: 25px;
}

.suggestion-card-icon-bottom {
  font-size: 1.1rem;
  color: #9ca3af;
  position: absolute;
  bottom: 5px;
  left: 24px;
}

.refresh-prompts-button {
  color: #6b7280;
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  border-radius: 8px;
  transition: background-color 0.2s, color 0.2s;
  align-self: flex-start;
  margin-bottom: 30px;
}

.refresh-prompts-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.home-input-section {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 20px;
  width: 100%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.07);
  border: 1px solid #e5e7eb;
}

.home-input-section .ant-input {
  border: none;
  resize: none;
  box-shadow: none !important;
  padding: 10px 0;
  min-height: 60px;
}

.home-input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.home-input-actions .left-actions,
.home-input-actions .right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.home-input-actions .ant-btn-text {
  color: #6b7280;
}

.home-input-actions .ant-btn-text:hover {
  color: #8b5cf6;
  background-color: rgba(139, 92, 246, 0.05);
}

.home-input-actions .mode-selector {
  background-color: #f3f4f6;
  border-radius: 16px;
  padding: 4px 10px;
  font-size: 0.85rem;
  color: #4b5563;
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

/* 添加primary状态的样式 */
.home-input-actions .mode-selector.ant-btn-primary {
  background-color: #8b5cf6 !important;
  color: white !important;
  border-color: #8b5cf6 !important;
}

.home-input-actions .mode-selector .anticon {
  font-size: 1rem;
}

.home-input-actions .character-count {
  color: #9ca3af;
  font-size: 0.85rem;
}

.home-input-actions .send-button {
  background-color: #8b5cf6;
  border-color: #8b5cf6;
  border-radius: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 20px;
}

.home-input-actions .send-button .anticon {
  color: white;
}

.home-input-actions .send-button:hover {
  background-color: #7c3aed;
  border-color: #7c3aed;
}

.home-input-actions .send-button:disabled {
  background-color: #d1d5db;
  border-color: #d1d5db;
  cursor: not-allowed;
}

.home-input-actions .send-button:disabled .anticon {
  color: #9ca3af;
}

.home-mcp-selector {
  margin: 20px 0;
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 加载状态容器样式 */
.chat-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
}

.chat-loading-container .ant-spin {
  margin-bottom: 30px;
}

.chat-loading-container .ant-spin-text {
  margin-top: 15px;
  font-size: 16px;
  color: #666;
}

.message-main {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

/* 专门为有模型图标的头像添加样式 */
.ai-avatar.with-model-icon {
  background-color: transparent;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #f0f0f0; /* 添加浅色边框 */
}

/* 服务选择器相关样式 */
.service-selector .ant-select-selection-item {
  display: flex;
  align-items: center;
}

.service-selector .ant-btn {
  display: flex;
  align-items: center;
}

.service-selector .ant-btn .anticon + span,
.service-selector .ant-btn img + span {
  margin-left: 8px;
}

/* 修复按钮内图标和文字的对齐 */
.service-selector .ant-btn img {
  display: inline-block;
  vertical-align: middle;
}

/* 为所有服务选择器和按钮中的图标添加圆角 */
.service-selector img,
.service-selector .ant-select-selection-item img,
.service-selector .ant-btn img {
  border-radius: 4px;
}

/* 为模型图标添加圆角 */
button img,
.ant-select-item-option-content img {
  border-radius: 4px;
}

/* 使Option中的图标与文字垂直居中对齐 */
.ant-select-item-option-content {
  display: flex !important;
  align-items: center !important;
}

/* 添加一个小窗口下的媒体查询规则，用于在小窗口下缩小首页内容尺寸 */
@media screen and (max-width: 1200px) {
  .home-view-container {
    transform: scale(0.9);
    transform-origin: top center;
    /* 移除宽度限制 */
    /* max-width: 90%; */
    /* width: 100%; */ /* 注释掉或删除这一行以保持居中 */
  }
  
  .welcome-header .welcome-greeting,
  .welcome-header .greeting-name-gradient,
  .welcome-header .welcome-prompt {
    font-size: calc(2.8rem * 0.9);
  }
  
  .welcome-subtitle {
    font-size: calc(1.0rem * 0.9);
  }
  
  .suggestion-card {
    width: calc(210px * 0.9);
    height: calc(140px * 0.9);
  }
}

/* 为更小的窗口进一步缩小内容 */
@media screen and (max-width: 768px) {
  .main-content {
    padding-bottom: 60px; /* 为移动版的底部导航栏留出空间 */
    /* 移除宽度限制，恢复全屏显示 */
    max-width: none;
  }
  
  .message-content {
    max-width: 95%;
  }
}

/* Agent处理过程的样式 */
.agent-process {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 12px;
  margin: 10px 0;
}

.dark .agent-process {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Agent处理过程中的分隔线 */
.agent-process hr,
.message-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
  margin: 1em 0;
  opacity: 0.6;
}

.dark .agent-process hr,
.dark .message-content hr {
  background: linear-gradient(90deg, transparent 0%, #4b5563 50%, transparent 100%);
  opacity: 0.8;
}

/* 步骤标题样式 */
.agent-step-title {
  font-weight: bold;
  font-size: 0.95em; /* 减小字体大小 */
  margin: 8px 0; /* 减小上下边距 */
  color: #333;
}

.dark .agent-step-title {
  color: #ddd;
}

/* Agent名称样式 */
.agent-name {
  font-weight: bold;
  color: #1890ff;
  font-size: 0.75rem; /* 减小字体大小 */
}

.dark .agent-name {
  color: #40a9ff;
}

/* 任务名称样式 */
.task-name {
  font-weight: bold;
  color: #722ed1;
  font-size: 0.75rem; /* 减小字体大小 */
}

.dark .task-name {
  color: #9254de;
}

/* Emoji样式 */
.message-content em {
  font-style: normal;
  color: rgba(0, 0, 0, 0.65);
}

.dark .message-content em {
  color: rgba(255, 255, 255, 0.65);
}

/* 大型Emoji */
.message-content em[data-emoji] {
  font-size: 1.2em;
  display: inline-block;
  margin-right: 4px;
  vertical-align: middle;
}

/* Tool call和结果样式增强 */
.message-content strong {
  color: #1890ff;
  font-weight: 600;
}

.dark .message-content strong {
  color: #40a9ff;
}

/* 用户消息中的strong样式 */
.user-message .message-content strong {
  color: white;
  font-weight: 700;
}

/* 消息内容中的思考过程样式 */
.message-content .ai-thinking {
  padding: 6px 10px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 5px;
  font-style: italic;
  color: #606060;
  margin: 8px 0;
  position: static;
  z-index: auto;
  animation: none;
  backdrop-filter: none;
  box-shadow: none;
  border: none;
}

.dark .message-content .ai-thinking {
  background-color: rgba(255, 255, 255, 0.03);
  color: #a0a0a0;
}

/* 代码块增强样式 */
.message-content pre {
  margin: 1.5em 0;
  padding: 1.5em;
  border-radius: 14px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  overflow-x: auto;
  font-size: 0.9em;
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 2px 10px rgba(139, 92, 246, 0.15);
  /* 移除动画以防止在消息生成时的跳动效果 */
  /* animation: codeBlockFadeIn 0.4s ease-out; */
  /* 移除过渡效果以防止跳动 */
  /* transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); */
}

/* 移除代码块的hover效果以防止跳动 */
.message-content pre:hover {
  /* transform: scale(1.01); */
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 4px 15px rgba(139, 92, 246, 0.2); */
}

/* 代码块内的代码样式 */
.message-content pre code {
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', monospace;
  color: #e2e8f0;
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  font-feature-settings: 'liga' 1, 'calt' 1;
  line-height: 1.5;
}

/* 行内代码样式 */
.message-content code:not(pre code) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0.3em 0.7em;
  border-radius: 8px;
  font-size: 0.88em;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', monospace;
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: #e11d48;
  font-weight: 500;
  font-feature-settings: 'liga' 1, 'calt' 1;
  letter-spacing: 0.02em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

/* 注释掉代码块动画以防止跳动
@keyframes codeBlockFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
*/

/* 暗色模式代码样式 */
.dark .message-content pre {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-color: rgba(139, 92, 246, 0.4);
}

.dark .message-content code:not(pre code) {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #fbbf24;
  border-color: rgba(255, 255, 255, 0.1);
}

/* 暗色模式标题样式 */
.dark .message-body h1,
.dark .message-body h2,
.dark .message-body h3 {
  color: #f9fafb;
}

.dark .message-body h1 {
  color: #f3f4f6;
}

.dark .message-body h2 {
  color: #e5e7eb;
}

.dark .message-body h3 {
  color: #d1d5db;
}

/* 用户消息白色文字适配 */
.user-message .message-body h1,
.user-message .message-body h2,
.user-message .message-body h3,
.user-message .message-body h4,
.user-message .message-body h5,
.user-message .message-body h6 {
  color: white;
}

.user-message .message-content code:not(pre code) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.user-message .message-content pre {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.user-message .message-content pre code {
  color: #e2e8f0;
}

/* Agent执行过程分隔符 */
.agent-separator {
  text-align: center;
  overflow: hidden;
  margin: 10px 0; /* 减少上下边距，从15px减少到10px */
  position: relative; /* 添加相对定位 */
  white-space: nowrap; /* 防止文本换行 */
  display: block; /* 确保占据整行 */
  width: 100%; /* 占满容器宽度 */
  margin-bottom: 15px; /* 减少底部边距，从20px减少到15px */
}

.agent-separator span {
  display: inline-block;
  position: relative;
  color: #8c8c8c;
  background-color: #ffffff; /* 添加背景色，防止分割线穿过文字 */
  padding: 0 8px; /* 减少文字左右留空，从10px减少到8px */
  z-index: 1; /* 确保文字在线条上方 */
  font-size: 0.85rem; /* 减小字体大小，从0.9rem减少到0.85rem */
}

/* 新增：Agent头部容器样式，为标题设置独立行 */
.agent-header {
  display: block;
  width: 100%;
  margin-top: 15px; /* 减少顶部边距，从20px减少到15px */
  margin-bottom: 8px; /* 减少底部边距，从10px减少到8px */
}

/* 调整Agent名称标题样式，确保独占一行 */
.agent-title {
  display: block;
  margin: 12px 0; /* 减少上下边距，从15px减少到12px */
  white-space: normal; /* 允许标题正常换行 */
  line-height: 1.4; /* 减小行高，从1.5减少到1.4 */
}

.agent-separator span:before,
.agent-separator span:after {
  content: "";
  position: absolute;
  top: 50%;
  width: 100vw; /* 使线条足够长 */
  height: 1px;
  background-color: #ddd;
  z-index: 0; /* 确保线条在文字下方 */
}

.agent-separator span:before {
  right: 100%;
}

.agent-separator span:after {
  left: 100%;
}

.dark .agent-separator span {
  color: #a6a6a6;
}

.dark .agent-separator span:before,
.dark .agent-separator span:after {
  background-color: #444;
}

/* 添加消息操作按钮区域的样式 */
.message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.message-content:hover .message-actions {
  opacity: 1;
}

.message-actions .ant-btn {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  height: 24px;
  line-height: 1;
  color: #8c8c8c;
}

.message-actions .ant-btn:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.message-actions .ant-btn .anticon {
  font-size: 12px;
  margin-right: 4px;
}

.service-selector .ant-btn-primary .anticon,
.service-selector .ant-btn-primary img {
  color: #fff !important;
  filter: brightness(100) drop-shadow(0 0 1px #fff);
}

/* 为AgentTaskProgress组件添加样式 */
.agent-task-container {
  width: 100%;
  max-width: 100%;
  margin-bottom: 4px;
}

/* 调整Agent任务进度组件在聊天气泡中的样式 */
.message-body .agent-task-progress {
  background-color: transparent;
  padding: 0;
  margin: 0;
}

/* 在聊天气泡中调整内容样式 */
.message-body .agent-task-progress .ant-collapse-content-box {
  padding: 0 0 8px 0 !important;
}

/* 确保在气泡中的垂直线正确显示 */
.message-body .agent-task-progress .ant-collapse-item::before {
  left: 9px;
}

.message-body .agent-task-progress .ant-collapse-header::before {
  left: 4px;
}

.message-body .agent-task-progress .ant-collapse-header {
  padding: 3px 0 3px 32px !important;
}

.message-body .agent-task-progress .ant-collapse-content {
  padding-left: 32px;
}

/* 调整表格内容单元格的内边距 */
.markdown-content td {
  padding: 6px 8px;
  text-align: left;
  border: 1px solid #ddd;
  color: #000000;
  vertical-align: top;
  word-break: break-word;
  white-space: normal;
  font-size: 0.9rem;
  line-height: 1.2;
}

/* 调整表头单元格的内边距 */
.markdown-content th {
  padding: 6px 8px;
  text-align: left;
  border: 1px solid #ddd;
  font-weight: bold;
  background-color: #f8f8f8;
  color: #000000;
  word-break: break-word;
  white-space: normal;
  font-size: 0.9rem;
  line-height: 1.2;
}

/* 调整表格外边距 */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 12px;
  border: 1px solid #ddd;
  table-layout: fixed;
  animation: tableSlideIn 0.5s ease-out;
  transition: all 0.3s ease;
}

.markdown-content table:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

@keyframes tableSlideIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 调整表格容器样式 */
.markdown-content .table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 12px;
}

/* 修改自定义Ant Design表格样式 */
.custom-table .ant-table-cell {
  white-space: normal !important;
  word-break: break-word !important;
  text-overflow: unset !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  line-height: 1.2;
  vertical-align: top;
  padding: 6px 8px !important;
  font-size: 0.9rem;
}

/* 调整表格行高 */
.custom-table .ant-table-tbody > tr > td {
  height: auto;
  max-height: none;
  color: #000000;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

/* 确保表格不会有过大的内部边距 */
.ant-table-wrapper .ant-table-tbody > tr > td {
  padding: 6px 8px !important;
}

.ant-table-wrapper .ant-table-thead > tr > th {
  padding: 6px 8px !important;
}

/* 调整消息内容中的标题间距 */
.message-body h1,
.message-body h2,
.message-body h3,
.message-body h4,
.message-body h5,
.message-body h6 {
  margin-bottom: 0.6em;
  font-weight: 700;
  letter-spacing: -0.025em;
  color: #111827;
  line-height: 1.3;
}

.message-body h1 {
  font-size: 1.5em;
  color: #1f2937;
}

.message-body h2 {
  font-size: 1.3em;
  color: #374151;
}

.message-body h3 {
  font-size: 1.1em;
  color: #4b5563;
}

/* 第一个标题不需要顶部边距 */
.message-body > h1:first-child,
.message-body > h2:first-child,
.message-body > h3:first-child,
.message-body > h4:first-child,
.message-body > h5:first-child,
.message-body > h6:first-child {
  margin-top: 0.2em;
}

/* 调整消息内容中的列表间距 */
.message-body ul,
.message-body ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 1.5em;
}

.message-body li {
  padding: 0; /* 去掉内边距 */
  line-height: 1.1; /* 保持单行的紧凑行高 */
  min-height: 0.6em; /* 确保最小高度 */
  margin-bottom: 0.3em; /* 增加列表项间距，为换行提供空间 */
  overflow-wrap: break-word; /* 确保换行兼容性 */
}

/* 调整标题下方的列表间距 */
.message-body h1 + ul,
.message-body h2 + ul,
.message-body h3 + ul,
.message-body h4 + ul,
.message-body h5 + ul,
.message-body h6 + ul,
.message-body h1 + ol,
.message-body h2 + ol,
.message-body h3 + ol,
.message-body h4 + ol,
.message-body h5 + ol,
.message-body h6 + ol {
  margin-top: 0.1em; /* 从0.2em减小到0.1em */
}

.markdown-content li {
  color: #000000 !important;
  padding: 0 !important; /* 确保没有内边距 */
  margin-bottom: 0.1em !important; /* 确保底部间距一致 */
  line-height: 0.6 !important; /* 统一行高 */
}

/* 确保markdown样式不会影响用户消息气泡 */
.user-message .message-content {
  background-color: #e6f7ff !important; /* 恢复用户气泡的背景色 */
}

.ai-message .message-content {
  background-color: #f9f9f9 !important; /* 恢复AI气泡的背景色 */
}

/* 新增markdown包装元素样式，确保不影响气泡 */
.markdown-content-wrapper {
  background-color: transparent;
}

/* 确保用户消息中的markdown内容文字颜色保持一致 */
.user-message .markdown-content * {
  color: inherit;
}

/* 浏览器风格的标签栏 */
.browser-style-tabs {
  width: 100%;
  /* 修改: 改为flex-column方向，使子元素垂直排列 */
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

/* 两个包装器元素样式 */
.tabs-container-wrapper {
  width: 100%;
  padding: 2px 8px 0 5px;
  background-color: #ffffff;
  display: flex;
  overflow: hidden;
  position: relative;
}

.tabs-actions-wrapper {
  width: 100%;
  padding: 4px 8px 8px 8px;
  border-bottom: 0.2px solid #e0e0e0;
  background-color: #ffffff;
}

.tabs-container {
  display: flex;
  align-items: center;
  flex: 1;
  overflow-x: auto;
  white-space: nowrap;
  position: relative;
  
  /* 隐藏滚动条 */
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 隐藏Chrome/Safari滚动条 */
.tabs-container::-webkit-scrollbar {
  display: none;
}

/* 添加新的tab-controls样式，确保其位置固定在最右侧 */
.tab-controls {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  background-color: #ffffff;
  padding-left: 8px;
  z-index: 2;
  box-shadow: -5px 0 10px rgba(255, 255, 255, 0.9);
}

.tab {
  height: 36px;
  padding: 0 15px;
  background-color: #eaeaea;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  /* margin-right: 4px; */
  cursor: pointer;
  max-width: 200px;
  min-width: 100px;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-top: 4px;
  border: 1px solid #e0e0e0;
  border-bottom: none;
  transition: background-color 0.2s;
}

.tab:hover {
  background-color: #f0f0f0;
}

.tab.active {
  background-color: #ffffff;
  height: 39px;
  margin-top: 1px;
  z-index: 10;
}

.tab-title {
  margin-right: 6px;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.close-tab-btn {
  width: 18px;
  height: 18px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: auto;
}

.close-tab-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* 标签栏上下文菜单样式 */
.tab-context-menu {
  position: fixed;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 4px 0;
  z-index: 1000;
}

/* 修改标签下拉菜单按钮样式 */
.tabs-dropdown-btn {
  margin-left: 2px;
  margin-right: 2px;
  background-color: #f0f0f0;
  border-radius: 4px;
  height: 26px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  margin-top: 4px;
  font-size: 12px;
  flex-shrink: 0;
  z-index: 1;
}

.tabs-dropdown-btn:hover {
  background-color: #e6e6e6;
}

/* 新建标签按钮样式 */
.new-tab-btn {
  flex-shrink: 0;
  margin-left: 2px;
  height: 28px;
  width: 28px;
  align-self: center;
  margin-top: 4px;
  margin-right: 2px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.new-tab-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-tab-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 220px;
  padding: 4px 0;
}

.dropdown-tab-item span {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
}

/* 标签栏操作菜单 */
.tabs-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  width: 100%;
  margin-left: auto;
}

/* 修改标签下拉菜单样式 */
.ant-dropdown-menu-item .dropdown-tab-item {
  padding: 4px 8px;
}

.ant-dropdown-menu {
  padding: 4px 0;
  max-height: 400px;
  overflow-y: auto;
}

.ant-dropdown-menu-item {
  padding: 5px 0;
}

.ant-dropdown-menu-item:hover {
  background-color: #f5f5f5;
}

.dropdown-tab-item .ant-btn {
  opacity: 0.5;
  transition: opacity 0.2s;
}

.dropdown-tab-item:hover .ant-btn {
  opacity: 1;
}

/* 首页输入区域的左侧操作区 */
.left-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 首页中角色选择器的样式 */
.left-actions .role-selector-compact {
  margin-right: 8px;
}

/* 确保首页中角色选择器的高度与其他按钮一致 */
.home-input-actions .ant-select-selector {
  height: 32px;
  display: flex;
  align-items: center;
}

.home-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 8px;
  width: 100%;
}

.home-toolbar .ant-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  border-radius: 8px;
}

.home-toolbar img {
  margin-right: 8px;
}

.home-toolbar .role-selector-compact {
  margin-right: 0;
}

/* 优化角色选择器在工具栏中的样式 */
.service-selector .role-selector-compact,
.home-toolbar .role-selector-compact {
  min-width: 150px;
}

.service-selector .role-selector-compact .ant-select-selector,
.home-toolbar .role-selector-compact .ant-select-selector {
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 4px !important;
  padding: 0 11px !important;
}

/* 角色选择器首页样式 */
.role-selector-icon-wrapper {
  display: inline-block;
}

.home-role-selector .role-selector-button {
  min-width: unset;
  margin-right: 0;
  border: none;
  background-color: transparent;
  height: 32px;
  width: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
}

.home-role-selector .role-selector-button:hover {
  background-color: rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.home-role-selector .role-button-text {
  display: none;
}

.home-role-selector .role-selector-button .ant-avatar {
  margin-right: 0 !important;
  width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
  font-size: 12px !important;
}

.home-role-selector .role-selector-button .ant-avatar img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

/* 调整图标包装容器与其他工具栏按钮对齐 */
.left-actions .role-selector-icon-wrapper {
  display: flex;
  align-items: center;
}

/* 积木搜索模态框样式 */
.search-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  padding: 16px 24px;
}

.search-modal .ant-modal-title {
  color: white;
  font-weight: 600;
}

.search-modal .ant-modal-close {
  color: white;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.search-modal .ant-modal-close:hover {
  opacity: 1;
}

.search-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0,0,0,0.15);
}

.search-modal .ant-modal-body {
  background-color: #fafafa;
}

/* 搜索按钮悬停效果优化 */
.home-input-actions .left-actions .ant-btn[title="积木搜索"]:hover {
  background-color: #f0f9ff;
  border-color: #52c41a;
}

.tabs-actions .ant-btn[title="积木搜索"]:hover {
  background-color: #f0f9ff;
  color: #52c41a;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .search-modal {
    width: 95% !important;
    max-width: 1200px;
  }
}

@media (max-width: 768px) {
  .search-modal {
    width: 100% !important;
    margin: 0;
    top: 0 !important;
    max-height: 100vh;
  }
  
  .search-modal .ant-modal-body {
    height: calc(100vh - 55px) !important;
    padding: 8px !important;
  }
}