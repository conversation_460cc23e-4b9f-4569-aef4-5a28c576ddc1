<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette - Defines CSS variables for easy updates -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6;
        --hover-color: #7DD3FC;
      }

      /* Font System - Defines font families and sizes with weights */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; } /* Not heavily used in this template but defined */

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Color Utility Classes */
      .fill-primary { fill: var(--primary-color); }
      .fill-secondary { fill: var(--secondary-color); }
      .fill-accent { fill: var(--accent-color); }
      .fill-background { fill: var(--background-color); }
      .fill-card-background { fill: var(--card-background); }
      .fill-container-background { fill: var(--container-background); }
      .fill-text-primary { fill: var(--text-primary); }
      .fill-text-secondary { fill: var(--text-secondary); }
      .fill-text-light { fill: var(--text-light); }

      .stroke-primary { stroke: var(--primary-color); }
      .stroke-secondary { stroke: var(--secondary-color); }
      .stroke-accent { stroke: var(--accent-color); }
      .stroke-card-border { stroke: var(--card-border); }
      .stroke-divider { stroke: var(--card-border); }

      /* Card Styles */
      .card {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        rx: 12; /* border-radius for rectangles */
        ry: 12;
      }

      /* Shadow for cards - applied via filter attribute */
      .card-shadow {
        filter: url(#shadow);
      }
    </style>

    <!-- Gradients for visual appeal -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Drop Shadow Filter Definition -->
    <filter id="shadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode in="matrixOut"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon Definitions (using SVG symbols for reusability) -->
    <!-- Comparison Icon -->
    <symbol id="icon-compare" viewBox="0 0 24 24">
      <path d="M10 6L3 12L10 18M14 18L21 12L14 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- Lightbulb Icon (for conclusion) -->
    <symbol id="icon-bulb" viewBox="0 0 24 24">
      <path d="M12 2C9.243 2 7 4.243 7 7c0 2.227 1.157 4.242 3 5.304V15c0 1.104.896 2 2 2s2-.896 2-2v-2.696c1.843-1.062 3-3.077 3-5.304C17 4.243 14.757 2 12 2zM12 22a3 3 0 100-6 3 3 0 000 6z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
  </defs>

  <!-- Main Background -->
  <rect x="0" y="0" width="1920" height="1080" class="fill-background"/>

  <!-- Subtle Decorative Geometric Elements (for tech/modern feel) -->
  <rect x="1500" y="0" width="420" height="150" fill="var(--accent-color)" opacity="0.05"/>
  <rect x="0" y="930" width="300" height="150" fill="var(--primary-color)" opacity="0.05"/>
  <circle cx="960" cy="540" r="400" fill="var(--accent-color)" opacity="0.02"/>
  <line x1="80" y1="120" x2="1840" y2="120" class="stroke-divider" stroke-width="1" opacity="0.5"/>
  <line x1="80" y1="780" x2="1840" y2="780" class="stroke-divider" stroke-width="1" opacity="0.5"/>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="160" height="40" class="fill-primary" rx="8" ry="8"/>
    <text x="160" y="88" text-anchor="middle" class="small-text font-primary fill-card-background">
      <tspan>LOGO</tspan>
    </text>
    <!-- Page Title - Example of large Chinese text with smaller English embellishment -->
    <text x="960" y="88" text-anchor="middle" class="main-title font-primary fill-text-primary">
      <tspan>对比分析</tspan>
      <tspan x="960" dy="48" class="section-title font-secondary fill-text-secondary">Comparison Analysis</tspan>
    </text>
    <!-- Page Number -->
    <text x="1840" y="88" text-anchor="end" class="small-text font-primary fill-text-secondary">
      <tspan>7/10</tspan>
    </text>
  </g>

  <!-- Main Content Area - Comparison Layout (Bento Grid inspired segmentation) -->
  <g id="comparison-section">
    <!-- Subtitle for the comparison -->
    <text x="960" y="200" text-anchor="middle" class="section-title font-primary fill-text-secondary">
      <tspan>{subtitle}</tspan>
    </text>

    <!-- Left Comparison Card: Option A -->
    <g id="option-a-card">
      <rect x="160" y="260" width="700" height="480" class="card card-shadow"/>
      <text x="510" y="310" text-anchor="middle" class="content-title font-primary fill-text-primary">
        <tspan>方案A</tspan>
        <tspan x="510" dy="36" class="body-text font-secondary fill-text-secondary">Option A Overview</tspan>
      </text>

      <!-- Content for Option A - Using bullet points for clarity -->
      <g class="body-text font-secondary fill-text-primary">
        <text x="200" y="380">
          <tspan>和#x2022; 市场渗透率低，增长潜力大</tspan>
          <tspan x="200" dy="28" class="small-text fill-text-secondary">Low market penetration, high growth potential</tspan>
        </text>
        <text x="200" y="460">
          <tspan>和#x2022; 成本结构优化，利润空间可观</tspan>
          <tspan x="200" dy="28" class="small-text fill-text-secondary">Optimized cost structure, significant profit margin</tspan>
        </text>
        <text x="200" y="540">
          <tspan>和#x2022; 技术优势明显，竞争壁垒高</tspan>
          <tspan x="200" dy="28" class="small-text fill-text-secondary">Clear technological advantage, high competitive barrier</tspan>
        </text>
        <text x="200" y="620">
          <tspan>和#x2022; 风险可控，具备快速扩张能力</tspan>
          <tspan x="200" dy="28" class="small-text fill-text-secondary">Controllable risks, capability for rapid expansion</tspan>
        </text>
        <text x="200" y="700">
          <tspan>和#x2022; {content}</tspan>
        </text>
      </g>
    </g>

    <!-- Right Comparison Card: Option B -->
    <g id="option-b-card">
      <rect x="1060" y="260" width="700" height="480" class="card card-shadow"/>
      <text x="1410" y="310" text-anchor="middle" class="content-title font-primary fill-text-primary">
        <tspan>方案B</tspan>
        <tspan x="1410" dy="36" class="body-text font-secondary fill-text-secondary">Option B Overview</tspan>
      </text>

      <!-- Content for Option B -->
      <g class="body-text font-secondary fill-text-primary">
        <text x="1100" y="380">
          <tspan>和#x2022; 市场成熟，增长速度较慢</tspan>
          <tspan x="1100" dy="28" class="small-text fill-text-secondary">Mature market, slower growth rate</tspan>
        </text>
        <text x="1100" y="460">
          <tspan>和#x2022; 成本较高，利润空间受限</tspan>
          <tspan x="1100" dy="28" class="small-text fill-text-secondary">Higher costs, limited profit margin</tspan>
        </text>
        <text x="1100" y="540">
          <tspan>和#x2022; 技术普及，同质化竞争激烈</tspan>
          <tspan x="1100" dy="28" class="small-text fill-text-secondary">Widespread technology, fierce homogeneous competition</tspan>
        </text>
        <text x="1100" y="620">
          <tspan>和#x2022; 市场波动大，风险挑战高</tspan>
          <tspan x="1100" dy="28" class="small-text fill-text-secondary">High market volatility, significant risk challenges</tspan>
        </text>
        <text x="1100" y="700">
          <tspan>和#x2022; {content}</tspan>
        </text>
      </g>
    </g>

    <!-- Central Comparison Indicator / Difference Emphasis -->
    <g id="difference-indicator">
      <circle cx="960" cy="500" r="60" fill="url(#gradientAccent)" opacity="0.9"/>
      <use xlink:href="#icon-compare" x="936" y="476" width="48" height="48" stroke-width="2" class="stroke-card-background" fill="none"/>
      <text x="960" y="580" text-anchor="middle" class="small-text font-primary fill-text-primary">
        <tspan>核心差异</tspan>
        <tspan x="960" dy="20" class="caption font-secondary fill-text-secondary">Key Differences</tspan>
      </text>
    </g>
  </g>

  <!-- Conclusion Section - Prominently displayed -->
  <g id="conclusion-section">
    <rect x="80" y="820" width="1760" height="180" class="fill-container-background" rx="12" ry="12"/>
    <text x="960" y="870" text-anchor="middle" class="section-title font-primary fill-text-primary">
      <tspan>结论总结</tspan>
      <tspan x="960" dy="40" class="body-text font-secondary fill-text-secondary">Conclusion 和#38; Recommendation</tspan>
    </text>
    <text x="960" y="950" text-anchor="middle" class="body-text font-secondary fill-text-primary">
      <tspan>{content}</tspan>
      <tspan x="960" dy="28" class="small-text fill-text-secondary">基于全面分析，我们推荐选择方案A，因其具备更强的市场潜力和风险可控性。</tspan>
    </text>
    <use xlink:href="#icon-bulb" x="1700" y="840" width="48" height="48" class="stroke-accent" fill="none"/>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1030" class="caption font-primary fill-text-light">
      <tspan>Presented by: {author}</tspan>
    </text>
    <text x="1840" y="1030" text-anchor="end" class="caption font-primary fill-text-light">
      <tspan>Date: {date}</tspan>
    </text>
  </g>
</svg>