import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Typography } from 'antd';
import './CollapsibleCard.css';

const { Text } = Typography;

/**
 * 可折叠卡片组件
 * @param {string} title - 卡片标题
 * @param {React.ReactNode} children - 卡片内容
 * @param {string} type - 卡片类型 (工具调用、工具结果等)
 * @param {boolean} defaultCollapsed - 默认是否折叠
 * @param {function} onToggle - 折叠状态变化时的回调函数
 */
const CollapsibleCard = ({
  title,
  children,
  type = 'default',
  defaultCollapsed = true,
  onToggle
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  
  // 使用 useRef 来跟踪是否是首次渲染，避免后续 prop 变化时重新设置状态
  const isFirstRender = useRef(true);
  
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      setCollapsed(defaultCollapsed);
    }
  }, [defaultCollapsed]);
  
  // 根据类型获取卡片样式类名
  const getCardClassName = () => {
    switch (type) {
      case 'tool-call':
        return 'tool-call-card';
      case 'tool-result':
        return 'tool-result-card';
      case 'tool-error':
        return 'tool-error-card';
      case 'code':
        return 'code-card';
      case 'error':
        return 'error-card';
      default:
        return 'default-card';
    }
  };
  
  // 处理折叠状态变化
  const handleToggle = (e) => {
    // 防止事件冒泡，确保点击事件只在按钮上触发
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    const newCollapsedState = !collapsed;
    setCollapsed(newCollapsedState);
    
    // 如果提供了外部回调函数，则调用它
    if (typeof onToggle === 'function') {
      onToggle(newCollapsedState);
    }
  };
  
  return (
    <Card
      title={
        <div className="card-title-with-toggle">
          <span>{title}</span>
          <Button
            type="text"
            size="small"
            className="toggle-button"
            onClick={handleToggle}
          >
            {collapsed ? '展开' : '收起'}
          </Button>
        </div>
      }
      size="small"
      className={getCardClassName()}
      style={{ marginBottom: '8px' }}
      bodyStyle={{ padding: '8px' }}
      headStyle={{ padding: '0 8px', minHeight: '32px' }}
    >
      <div 
        style={{ 
          display: collapsed ? 'none' : 'block',
          /* 确保内容区域不影响按钮位置 */
          position: 'relative'
        }}
      >
        {children}
      </div>
    </Card>
  );
};

export default CollapsibleCard; 