<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette from Design Specification Constraints -->
    <style type="text/css">
      /* Unified Blue System Colors */
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; } /* This color is used for elements, but the main canvas background is black as per specific request. */
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .success-color { fill: #10B981; }
      .warning-color { fill: #F59E0B; }
      .error-color { fill: #EF4444; }
      .info-color { fill: #3B82F6; }
      .card-background { fill: #FFFFFF; }
      .container-background { fill: #E0F2FE; } /* Can be used for a lighter blue section background if needed */
      .hover-color { fill: #7DD3FC; }
      .active-color { fill: #1E40AF; }
      .disabled-color { fill: #64748B; }

      /* Tesla Red for highlights (as per aesthetic enhancement) */
      .tesla-red { fill: #E31937; }
      .tesla-red-stroke { stroke: #E31937; }

      /* Gradients for visual elements */
      .gradient-primary-bg { fill: url(#primaryGradientBg); }
      .gradient-accent-bg { fill: url(#accentGradientBg); }
      .tesla-red-gradient { fill: url(#teslaRedGradient); }

      /* Font Styles - Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Specific styles for this quote page content */
      .quote-text-cn { font-size: 48px; font-weight: 700; line-height: 1.4; } /* Chinese large bold */
      .quote-text-en { font-size: 28px; font-weight: 400; line-height: 1.4; } /* English small as dot */
      .quote-source { font-size: 28px; font-weight: 500; line-height: 1.4; }
      .quote-context { font-size: 20px; font-weight: 400; line-height: 1.4; }
      .page-number { font-size: 18px; font-weight: 400; }
      .logo-style { height: 60px; } /* Max height 80px, adjust for visual balance */

      /* Shadow for card style */
      .card-shadow { filter: url(#cardShadow); }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <!-- Tesla Red gradient with transparency for tech feel -->
    <linearGradient id="teslaRedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#E31937" stop-opacity="0.2"/>
    </linearGradient>

    <!-- Filter for Card Shadow -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
    </filter>

    <!-- Simple Outline Icon for decoration (e.g., for abstract background elements) -->
    <symbol id="icon-chart-line" viewBox="0 0 24 24">
      <path fill="none" stroke="currentColor" stroke-width="2" d="M3 18h18V6H3zM6 15l4-4 4 4 6-6"/>
    </symbol>

  </defs>

  <!-- Background Layer: Pure Black Base (from aesthetic enhancement requirement) -->
  <rect x="0" y="0" width="1920" height="1080" fill="#000000"/>

  <!-- Decorative Geometric Shapes / Bento Grid style elements (subtle, blue-toned for professionalism, low opacity) -->
  <!-- Top-left large rectangle/shape, primary color with transparency -->
  <rect x="0" y="0" width="800" height="400" class="primary-color" opacity="0.08" rx="20"/>
  <!-- Bottom-right subtle shape, secondary color with transparency -->
  <rect x="1200" y="700" width="720" height="380" class="secondary-color" opacity="0.06" rx="20"/>

  <!-- Main Content Area: Centered Card for the Quote -->
  <g id="quote-card" transform="translate(260, 180)"> <!-- Positioned to be roughly centered on the 1920x1080 canvas -->
    <rect x="0" y="0" width="1400" height="720" class="card-background" rx="24" class="card-shadow"/>

    <!-- Decorative Quote Marks (Tesla Red, large, with gradient) - Super large visual elements -->
    <!-- Top-left quote mark -->
    <text x="100" y="280" class="font-primary tesla-red-gradient" style="font-size: 280px; font-weight: 900; opacity: 0.8;">“</text>
    <!-- Bottom-right quote mark -->
    <text x="1300" y="680" text-anchor="end" class="font-primary tesla-red-gradient" style="font-size: 280px; font-weight: 900; opacity: 0.8;">”</text>

    <!-- Quote Content -->
    <text x="700" y="380" text-anchor="middle" class="font-primary text-primary">
      <!-- Main quote content (Chinese, large, bold) -->
      <tspan x="700" dy="0" class="quote-text-cn">{content}</tspan>
      <!-- English as embellishment (smaller) -->
      <tspan x="700" dy="60" class="quote-text-en text-secondary font-secondary">The future belongs to those who believe in the beauty of their dreams.</tspan>
    </text>

    <!-- Source Information -->
    <text x="700" y="550" text-anchor="middle" class="font-primary text-secondary">
      <tspan x="700" dy="0" class="quote-source">— {author}</tspan>
      <tspan x="700" dy="40" class="quote-context text-light font-secondary">
        {date}
      </tspan>
      <!-- Optional subtitle/context for the quote -->
      <tspan x="700" dy="30" class="quote-context text-secondary font-secondary">
        {subtitle}
      </tspan>
    </text>

  </g>

  <!-- Top Left Logo Placeholder -->
  <g id="logo" transform="translate(80, 60)">
    <!-- Ensure {logo_url} is a valid URL and any '&' within it are escaped as '&amp;' or '&#38;' -->
    <image xlink:href="{logo_url}" x="0" y="0" width="auto" height="60" class="logo-style"/>
    <!-- Fallback text if no image, using white fill for visibility on black background -->
    <text x="0" y="38" class="font-primary content-title" fill="#FFFFFF">
      {title}
    </text>
  </g>

  <!-- Page Number -->
  <text x="1840" y="1020" text-anchor="end" class="font-primary page-number text-light">
    9/10
  </text>

  <!-- Subtle Decorative Lines (Bento Grid style lines) -->
  <!-- Top Right - thin lines, accent color -->
  <line x1="1500" y1="80" x2="1800" y2="80" stroke="#3B82F6" stroke-width="2" opacity="0.3"/>
  <line x1="1800" y1="80" x2="1800" y2="280" stroke="#3B82F6" stroke-width="2" opacity="0.3"/>

  <!-- Bottom Left - thin lines, primary color -->
  <line x1="120" y1="900" x2="420" y2="900" stroke="#1E40AF" stroke-width="2" opacity="0.3"/>
  <line x1="120" y1="900" x2="120" y2="700" stroke="#1E40AF" stroke-width="2" opacity="0.3"/>

  <!-- Abstract outline icon (from icon system, for subtle context/data visualization feel) -->
  <use xlink:href="#icon-chart-line" x="1600" y="120" width="80" height="80" stroke="#3B82F6" opacity="0.2"/>
  <use xlink:href="#icon-chart-line" x="200" y="800" width="80" height="80" stroke="#1E40AF" opacity="0.2"/>

</svg>