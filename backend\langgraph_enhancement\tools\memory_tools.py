# -*- coding: utf-8 -*-
"""
🧠 记忆工具定义模块
将ContextOptimizedAgent的记忆API转换为MCP工具格式，
让智能体可以通过XML显式调用记忆功能
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

# 🔥 记忆作用域枚举 - 明确区分记忆类型
class MemoryScope(Enum):
    """记忆作用域类型枚举"""
    PERSONAL = "personal"      # 个人记忆：只有智能体自己能写入和读取
    TEAM = "team"             # 团队记忆：团队成员都能读写的共享记忆空间
    SHARED = "shared"         # 共享记忆：从其他智能体分享过来的记忆

def detect_memory_scope(agent_id: str, target_id: str = None) -> MemoryScope:
    """
    检测记忆作用域类型
    
    Args:
        agent_id: 当前智能体ID
        target_id: 目标agent_id参数值
        
    Returns:
        MemoryScope: 记忆作用域类型
    """
    # 检查是否为团队记忆 - 通过常见的团队ID模式判断
    if target_id and ("_team" in target_id or target_id.endswith("_team") or 
                      "team_" in target_id or target_id in [
                          "comprehensive_research_team", 
                          "comprehensive_travel_team",
                          "analytics_visualization_team",
                          "intelligent_routing_team",
                          "memory_testing_team"
                      ]):
        return MemoryScope.TEAM
    
    # 检查是否为个人记忆
    if not target_id or target_id == "self" or target_id == agent_id:
        return MemoryScope.PERSONAL
    
    # 其他情况视为共享记忆（访问其他智能体的记忆）
    return MemoryScope.SHARED

# 🧠 记忆工具定义 - 统一的跨智能体记忆访问工具
MEMORY_TOOLS_DEFINITIONS = [
    {
        "name": "get_memory",
        "server_name": "memory",
        "serverName": "memory",
        "description": "获取记忆内容。支持个人记忆、团队记忆和跨智能体记忆访问。优先使用team_id访问团队记忆。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string",
                    "description": "记忆键名。常用键名包括：user_requirements(用户需求)、team_progress(团队进度)、my_findings(我的发现)等"
                },
                "team_id": {
                    "type": "string",
                    "description": "团队ID。用于访问团队共享记忆空间。例如：'comprehensive_research_team'、'analytics_visualization_team' 等",
                    "default": None
                },
                "agent_id": {
                    "type": "string",
                    "description": "智能体ID。用于跨智能体记忆访问。如果不提供team_id和agent_id，则获取个人记忆",
                    "default": "self"
                },
                "default": {
                    "description": "当记忆不存在时返回的默认值。可以是任何类型：字符串、数字、对象、数组等",
                    "default": None
                }
            },
            "required": ["key"]
        }
    },
    {
        "name": "store_memory",
        "server_name": "memory", 
        "serverName": "memory",
        "description": "智能记忆存储工具。支持个人记忆、团队记忆和跨智能体记忆。默认追加模式，适合团队协作场景。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string",
                    "description": "记忆键名。建议使用描述性的名称，如 'user_requirements'、'team_progress'、'my_findings' 等"
                },
                "value": {
                    "description": "要存储的值。可以是任何类型的数据：字符串、数字、对象、数组等。复杂数据建议使用JSON对象格式"
                },
                "team_id": {
                    "type": "string",
                    "description": "团队ID。用于访问团队共享记忆空间。例如：'comprehensive_research_team'、'analytics_visualization_team' 等",
                    "default": None
                },
                "agent_id": {
                    "type": "string",
                    "description": "智能体ID。用于跨智能体记忆访问。如果不提供team_id和agent_id，则存储到个人记忆",
                    "default": None
                },
                "mode": {
                    "type": "string",
                    "enum": ["append", "overwrite"],
                    "description": "存储模式。'append'（默认）：追加到现有内容；'overwrite'：覆盖现有内容",
                    "default": "append"
                },
                "separator": {
                    "type": "string",
                    "description": "追加模式时使用的分隔符。默认为换行符",
                    "default": "\n"
                }
            },
            "required": ["key", "value"]
        }
    },
    {
        "name": "list_memories", 
        "server_name": "memory",
        "serverName": "memory",
        "description": "列出智能体的所有记忆键名。可以列出自己或其他智能体的记忆项。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "string",
                    "description": "目标智能体的ID。如果不提供或设为'self'，则列出自己的记忆。例如：'researcher'、'writer'、'reviewer' 等",
                    "default": "self"
                }
            },
            "required": []
        }
    },
    {
        "name": "search_memories",
        "server_name": "memory", 
        "serverName": "memory",
        "description": "在智能体的记忆中搜索包含关键词的项。可以搜索自己或其他智能体的记忆。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "pattern": {
                    "type": "string",
                    "description": "搜索关键词或模式。会在记忆的键名和值中进行模糊匹配"
                },
                "agent_id": {
                    "type": "string",
                    "description": "目标智能体的ID。如果不提供或设为'self'，则搜索自己的记忆。例如：'researcher'、'writer'、'reviewer' 等",
                    "default": "self"
                }
            },
            "required": ["pattern"]
        }
    },
    {
        "name": "clear_memory",
        "server_name": "memory",
        "serverName": "memory",
        "description": "清除智能体的记忆。只能清除自己的记忆，不能清除其他智能体的记忆。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string",
                    "description": "要清除的记忆键名。如果不提供此参数，将清除所有记忆",
                    "default": None
                }
            },
            "required": []
        }
    },
    {
        "name": "get_memory_stats",
        "server_name": "memory",
        "serverName": "memory",
        "description": "获取智能体的记忆统计信息。可以获取自己或其他智能体的记忆使用情况。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "string", 
                    "description": "目标智能体的ID。如果不提供或设为'self'，则获取自己的记忆统计。例如：'researcher'、'writer'、'reviewer' 等",
                    "default": "self"
                }
            },
            "required": []
        }
    },
    {
        "name": "list_all_agents",
        "server_name": "memory",
        "serverName": "memory",
        "description": "列出当前活跃的所有智能体ID。用于了解有哪些智能体可以访问其记忆。",
        "inputSchema": {
            "type": "object",
            "properties": {},
            "required": []
        }
    },

    {
        "name": "share_memory",
        "server_name": "memory",
        "serverName": "memory", 
        "description": "将自己的记忆分享给指定的智能体。目标智能体将在其记忆中收到一个新的记忆项。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "target_agent_id": {
                    "type": "string",
                    "description": "目标智能体的ID。例如：'researcher'、'writer'、'reviewer' 等"
                },
                "memory_key": {
                    "type": "string", 
                    "description": "要分享的记忆键名"
                },
                "new_key": {
                    "type": "string",
                    "description": "在目标智能体中的新键名。如果不提供，将使用原键名",
                    "default": None
                }
            },
            "required": ["target_agent_id", "memory_key"]
        }
    },
    {
        "name": "create_shared_memory",
        "server_name": "memory",
        "serverName": "memory",
        "description": "创建一个共享记忆并分享给指定的智能体。创建者会存储记忆，其他智能体会收到带前缀的共享版本。",
        "inputSchema": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string",
                    "description": "记忆键名。建议使用描述性名称如'team_status'、'project_progress'等"
                },
                "value": {
                    "description": "要存储和分享的记忆内容。可以是任何类型的数据"
                },
                "shared_with": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "要分享给的智能体ID列表。例如：['market_researcher', 'technical_writer']",
                    "default": []
                },
                "description": {
                    "type": "string",
                    "description": "记忆用途描述，帮助其他智能体理解这个共享记忆的作用",
                    "default": ""
                }
            },
            "required": ["key", "value"]
        }
    }
]

class MemoryToolsManager:
    """
    🧠 记忆工具管理器
    负责管理虚拟的"memory"服务器工具定义和执行
    """
    
    def __init__(self):
        """初始化记忆工具管理器"""
        self.tools = MEMORY_TOOLS_DEFINITIONS.copy()
        logger.info(f"🧠 记忆工具管理器初始化完成，加载了 {len(self.tools)} 个工具")
    
    def get_tools_definitions(self) -> List[Dict[str, Any]]:
        """
        获取所有记忆工具的定义
        
        Returns:
            List[Dict]: 工具定义列表，格式与MCP工具兼容
        """
        return self.tools.copy()
    
    def get_tool_by_name(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        根据工具名称获取工具定义
        
        Args:
            tool_name: 工具名称
            
        Returns:
            Dict或None: 工具定义字典
        """
        for tool in self.tools:
            if tool["name"] == tool_name:
                return tool.copy()
        return None
    
    def is_memory_tool(self, server_name: str, tool_name: str) -> bool:
        """
        检查是否为记忆工具调用
        
        Args:
            server_name: 服务器名称
            tool_name: 工具名称
            
        Returns:
            bool: 是否为记忆工具
        """
        if server_name != "memory":
            return False
        
        return any(tool["name"] == tool_name for tool in self.tools)
    
    def validate_arguments(self, tool_name: str, arguments: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        验证工具调用参数
        
        Args:
            tool_name: 工具名称
            arguments: 参数字典
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        tool_def = self.get_tool_by_name(tool_name)
        if not tool_def:
            return False, f"未知的记忆工具: {tool_name}"
        
        schema = tool_def.get("inputSchema", {})
        required_params = schema.get("required", [])
        properties = schema.get("properties", {})
        
        # 检查必填参数
        for param in required_params:
            if param not in arguments:
                return False, f"缺少必填参数: {param}"
        
        # 检查参数类型（简单验证）
        for param_name, param_value in arguments.items():
            if param_name in properties:
                param_schema = properties[param_name]
                expected_type = param_schema.get("type")
                
                # 简单的类型检查
                if expected_type == "string" and not isinstance(param_value, str):
                    return False, f"参数 {param_name} 应该是字符串类型"
                elif expected_type == "number" and not isinstance(param_value, (int, float)):
                    return False, f"参数 {param_name} 应该是数字类型"
                elif expected_type == "boolean" and not isinstance(param_value, bool):
                    return False, f"参数 {param_name} 应该是布尔类型"
        
        return True, None
    
    def get_tools_prompt_text(self) -> str:
        """
        生成记忆工具的提示词文本，用于添加到系统提示词中
        
        Returns:
            str: 工具提示词文本
        """
        if not self.tools:
            return ""
        
        prompt_parts = [
            "## memory 服务器",
            "虚拟的记忆管理服务器，提供智能体专用记忆的读写功能。",
            "",
            "### 可用工具:"
        ]
        
        for tool in self.tools:
            tool_name = tool["name"]
            description = tool["description"]
            
            # 生成参数描述
            params_desc = []
            schema = tool.get("inputSchema", {})
            properties = schema.get("properties", {})
            required = schema.get("required", [])
            
            for param_name, param_info in properties.items():
                param_desc = f"- {param_name}: {param_info.get('description', '无描述')}"
                if param_name in required:
                    param_desc += " (必填)"
                params_desc.append(param_desc)
            
            tool_text = f"- **{tool_name}**: {description}"
            if params_desc:
                tool_text += f"\n  参数:\n  " + "\n  ".join(params_desc)
            
            prompt_parts.append(tool_text)
        
        return "\n".join(prompt_parts)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"MemoryToolsManager(tools_count={len(self.tools)})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        tool_names = [tool["name"] for tool in self.tools]
        return f"MemoryToolsManager(tools={tool_names})"


# 🧠 全局记忆工具管理器实例
_global_memory_tools_manager = None

def get_memory_tools_manager() -> MemoryToolsManager:
    """获取全局记忆工具管理器实例"""
    global _global_memory_tools_manager
    if _global_memory_tools_manager is None:
        _global_memory_tools_manager = MemoryToolsManager()
    return _global_memory_tools_manager

# 导出主要组件
__all__ = [
    'MEMORY_TOOLS_DEFINITIONS',
    'MemoryToolsManager', 
    'get_memory_tools_manager'
]