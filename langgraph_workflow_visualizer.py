#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 LangGraph 工作流 SVG 可视化生成器

不依赖matplotlib，直接生成SVG格式的工作流图
"""

import os
from datetime import datetime

def generate_svg_workflow_diagram():
    """生成SVG格式的工作流图"""
    
    # SVG头部
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .node { 
        fill: #lightblue; 
        stroke: #333; 
        stroke-width: 2; 
        rx: 10; 
      }
      .node-text { 
        font-family: Arial, sans-serif; 
        font-size: 12px; 
        text-anchor: middle; 
        dominant-baseline: middle; 
      }
      .edge { 
        stroke: #666; 
        stroke-width: 2; 
        marker-end: url(#arrowhead); 
      }
      .title { 
        font-family: Arial, sans-serif; 
        font-size: 18px; 
        font-weight: bold; 
        text-anchor: middle; 
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="title">🔥 LangGraph 增强工作流可视化</text>
  
  <!-- 简单顺序工作流 -->
  <text x="400" y="70" class="title" style="font-size: 14px;">简单顺序工作流</text>
  
  <!-- 节点 -->
  <rect x="50" y="100" width="100" height="40" class="node"/>
  <text x="100" y="120" class="node-text">开始</text>
  
  <rect x="200" y="100" width="100" height="40" class="node"/>
  <text x="250" y="120" class="node-text">数据收集</text>
  
  <rect x="350" y="100" width="100" height="40" class="node"/>
  <text x="400" y="120" class="node-text">数据分析</text>
  
  <rect x="500" y="100" width="100" height="40" class="node"/>
  <text x="550" y="120" class="node-text">报告生成</text>
  
  <rect x="650" y="100" width="100" height="40" class="node"/>
  <text x="700" y="120" class="node-text">结束</text>
  
  <!-- 边 -->
  <line x1="150" y1="120" x2="200" y2="120" class="edge"/>
  <line x1="300" y1="120" x2="350" y2="120" class="edge"/>
  <line x1="450" y1="120" x2="500" y2="120" class="edge"/>
  <line x1="600" y1="120" x2="650" y2="120" class="edge"/>
  
  <!-- 并行工作流 -->
  <text x="400" y="220" class="title" style="font-size: 14px;">并行多智能体工作流</text>
  
  <!-- 协调器 -->
  <rect x="350" y="250" width="100" height="40" class="node"/>
  <text x="400" y="270" class="node-text">协调器</text>
  
  <!-- 并行智能体 -->
  <rect x="150" y="340" width="100" height="40" class="node" style="fill: #lightgreen;"/>
  <text x="200" y="360" class="node-text">研究智能体</text>
  
  <rect x="350" y="340" width="100" height="40" class="node" style="fill: #lightgreen;"/>
  <text x="400" y="360" class="node-text">分析智能体</text>
  
  <rect x="550" y="340" width="100" height="40" class="node" style="fill: #lightgreen;"/>
  <text x="600" y="360" class="node-text">写作智能体</text>
  
  <!-- 合并器 -->
  <rect x="350" y="430" width="100" height="40" class="node" style="fill: #lightyellow;"/>
  <text x="400" y="450" class="node-text">结果合并器</text>
  
  <!-- 输出 -->
  <rect x="350" y="520" width="100" height="40" class="node" style="fill: #lightcoral;"/>
  <text x="400" y="540" class="node-text">最终输出</text>
  
  <!-- 并行工作流的边 -->
  <line x1="400" y1="290" x2="200" y2="340" class="edge"/>
  <line x1="400" y1="290" x2="400" y2="340" class="edge"/>
  <line x1="400" y1="290" x2="600" y2="340" class="edge"/>
  
  <line x1="200" y1="380" x2="400" y2="430" class="edge"/>
  <line x1="400" y1="380" x2="400" y2="430" class="edge"/>
  <line x1="600" y1="380" x2="400" y2="430" class="edge"/>
  
  <line x1="400" y1="470" x2="400" y2="520" class="edge"/>
  
  <!-- 图例 -->
  <text x="50" y="590" style="font-size: 10px; font-family: Arial;">
    🔵 普通节点  🟢 智能体  🟡 处理器  🔴 输出节点
  </text>
  
</svg>'''
    
    return svg_content

def generate_complex_workflow_svg():
    """生成复杂工作流的SVG"""
    
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .node { 
        fill: #lightblue; 
        stroke: #333; 
        stroke-width: 2; 
        rx: 8; 
      }
      .coordinator { fill: #lightcoral; }
      .agent { fill: #lightgreen; }
      .processor { fill: #lightyellow; }
      .output { fill: #lavender; }
      
      .node-text { 
        font-family: Arial, sans-serif; 
        font-size: 11px; 
        text-anchor: middle; 
        dominant-baseline: middle; 
        font-weight: bold;
      }
      .edge { 
        stroke: #666; 
        stroke-width: 2; 
        marker-end: url(#arrowhead); 
      }
      .conditional-edge {
        stroke: #ff6600;
        stroke-dasharray: 5,5;
      }
      .title { 
        font-family: Arial, sans-serif; 
        font-size: 20px; 
        font-weight: bold; 
        text-anchor: middle; 
      }
      .subtitle {
        font-family: Arial, sans-serif; 
        font-size: 14px; 
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="500" y="30" class="title">🚀 LangGraph 复杂多智能体工作流</text>
  
  <!-- 工作流协调器 -->
  <rect x="450" y="80" width="100" height="40" class="node coordinator"/>
  <text x="500" y="100" class="node-text">工作流协调器</text>
  
  <!-- 任务分配器 -->
  <rect x="450" y="160" width="100" height="40" class="node coordinator"/>
  <text x="500" y="180" class="node-text">任务分配器</text>
  
  <!-- 智能体层 -->
  <rect x="150" y="240" width="100" height="40" class="node agent"/>
  <text x="200" y="260" class="node-text">研究智能体</text>
  
  <rect x="300" y="240" width="100" height="40" class="node agent"/>
  <text x="350" y="260" class="node-text">分析智能体</text>
  
  <rect x="450" y="240" width="100" height="40" class="node agent"/>
  <text x="500" y="260" class="node-text">写作智能体</text>
  
  <rect x="600" y="240" width="100" height="40" class="node agent"/>
  <text x="650" y="260" class="node-text">评估智能体</text>
  
  <rect x="750" y="240" width="100" height="40" class="node agent"/>
  <text x="800" y="260" class="node-text">优化智能体</text>
  
  <!-- 并行处理器 -->
  <rect x="450" y="320" width="100" height="40" class="node processor"/>
  <text x="500" y="340" class="node-text">并行处理器</text>
  
  <!-- 状态检查器（条件节点） -->
  <rect x="450" y="400" width="100" height="40" class="node processor"/>
  <text x="500" y="420" class="node-text">状态检查器</text>
  
  <!-- 分支路径 -->
  <rect x="300" y="480" width="100" height="40" class="node processor"/>
  <text x="350" y="500" class="node-text">路径A处理</text>
  
  <rect x="600" y="480" width="100" height="40" class="node processor"/>
  <text x="650" y="500" class="node-text">路径B处理</text>
  
  <!-- 结果合并器 -->
  <rect x="450" y="560" width="100" height="40" class="node processor"/>
  <text x="500" y="580" class="node-text">结果合并器</text>
  
  <!-- 最终输出 -->
  <rect x="450" y="640" width="100" height="40" class="node output"/>
  <text x="500" y="660" class="node-text">最终输出</text>
  
  <!-- 连接线 -->
  <!-- 协调器到分配器 -->
  <line x1="500" y1="120" x2="500" y2="160" class="edge"/>
  
  <!-- 分配器到智能体 -->
  <line x1="500" y1="200" x2="200" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="350" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="500" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="650" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="800" y2="240" class="edge"/>
  
  <!-- 智能体到处理器 -->
  <line x1="200" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="350" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="500" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="650" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="800" y1="280" x2="500" y2="320" class="edge"/>
  
  <!-- 处理器到检查器 -->
  <line x1="500" y1="360" x2="500" y2="400" class="edge"/>
  
  <!-- 条件分支 -->
  <line x1="500" y1="440" x2="350" y2="480" class="edge conditional-edge"/>
  <line x1="500" y1="440" x2="650" y2="480" class="edge conditional-edge"/>
  
  <!-- 分支到合并器 -->
  <line x1="350" y1="520" x2="500" y2="560" class="edge"/>
  <line x1="650" y1="520" x2="500" y2="560" class="edge"/>
  
  <!-- 合并器到输出 -->
  <line x1="500" y1="600" x2="500" y2="640" class="edge"/>
  
  <!-- 图例 -->
  <rect x="50" y="80" width="150" height="200" fill="none" stroke="#ccc" stroke-width="1"/>
  <text x="125" y="100" class="subtitle">节点类型图例</text>
  
  <rect x="60" y="110" width="20" height="15" class="coordinator"/>
  <text x="90" y="120" style="font-size: 10px;">协调器/分配器</text>
  
  <rect x="60" y="130" width="20" height="15" class="agent"/>
  <text x="90" y="140" style="font-size: 10px;">智能体</text>
  
  <rect x="60" y="150" width="20" height="15" class="processor"/>
  <text x="90" y="160" style="font-size: 10px;">处理器</text>
  
  <rect x="60" y="170" width="20" height="15" class="output"/>
  <text x="90" y="180" style="font-size: 10px;">输出节点</text>
  
  <line x1="60" y1="200" x2="90" y2="200" class="edge"/>
  <text x="100" y="205" style="font-size: 10px;">固定边</text>
  
  <line x1="60" y1="220" x2="90" y2="220" class="conditional-edge"/>
  <text x="100" y="225" style="font-size: 10px;">条件边</text>
  
  <!-- 特性说明 -->
  <text x="50" y="320" style="font-size: 12px; font-weight: bold;">🔥 特色功能：</text>
  <text x="50" y="340" style="font-size: 10px;">✅ 多智能体并行处理</text>
  <text x="50" y="355" style="font-size: 10px;">✅ 条件分支路由</text>
  <text x="50" y="370" style="font-size: 10px;">✅ 智能结果合并</text>
  <text x="50" y="385" style="font-size: 10px;">✅ 错误恢复机制</text>
  <text x="50" y="400" style="font-size: 10px;">✅ 状态检查点</text>
  <text x="50" y="415" style="font-size: 10px;">✅ 性能监控</text>
  
  <!-- 时间戳 -->
  <text x="950" y="690" style="font-size: 8px; text-anchor: end;">
    Generated: ''' + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '''
  </text>
  
</svg>'''
    
    return svg_content

def main():
    """主函数：生成SVG工作流图"""
    print("🎨 正在生成LangGraph工作流SVG可视化图...")
    
    # 确保在项目根目录
    root_dir = "/mnt/d/Develop/mcp/jimu-new"
    os.chdir(root_dir)
    
    # 生成简单工作流SVG
    simple_svg = generate_svg_workflow_diagram()
    simple_filename = "langgraph_simple_workflow.svg"
    
    with open(simple_filename, "w", encoding="utf-8") as f:
        f.write(simple_svg)
    
    print(f"✅ 简单工作流图已生成: {simple_filename}")
    print(f"📁 文件位置: {os.path.abspath(simple_filename)}")
    
    # 生成复杂工作流SVG
    complex_svg = generate_complex_workflow_svg()
    complex_filename = "langgraph_complex_workflow.svg"
    
    with open(complex_filename, "w", encoding="utf-8") as f:
        f.write(complex_svg)
    
    print(f"✅ 复杂工作流图已生成: {complex_filename}")
    print(f"📁 文件位置: {os.path.abspath(complex_filename)}")
    
    # 生成HTML查看器
    html_viewer = f'''<!DOCTYPE html>
<html>
<head>
    <title>LangGraph 工作流可视化</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .workflow-section {{ margin: 40px 0; text-align: center; }}
        h1 {{ color: #333; }}
        h2 {{ color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 LangGraph 增强工作流可视化</h1>
        
        <div class="workflow-section">
            <h2>简单顺序工作流</h2>
            <object data="{simple_filename}" type="image/svg+xml" width="800" height="600">
                您的浏览器不支持SVG
            </object>
        </div>
        
        <div class="workflow-section">
            <h2>复杂多智能体工作流</h2>
            <object data="{complex_filename}" type="image/svg+xml" width="1000" height="700">
                您的浏览器不支持SVG
            </object>
        </div>
        
        <div class="workflow-section">
            <h2>📖 说明</h2>
            <p>这些图展示了LangGraph增强框架支持的不同工作流模式：</p>
            <ul style="text-align: left; display: inline-block;">
                <li><strong>简单顺序工作流</strong>: 节点按顺序依次执行</li>
                <li><strong>复杂并行工作流</strong>: 支持多智能体并行处理</li>
                <li><strong>条件分支</strong>: 根据执行结果动态选择路径</li>
                <li><strong>智能合并</strong>: 将并行结果智能合并</li>
            </ul>
        </div>
        
        <div class="workflow-section">
            <p style="color: #666; font-size: 12px;">
                生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            </p>
        </div>
    </div>
</body>
</html>'''
    
    html_filename = "langgraph_workflow_viewer.html"
    with open(html_filename, "w", encoding="utf-8") as f:
        f.write(html_viewer)
    
    print(f"✅ HTML查看器已生成: {html_filename}")
    print(f"📁 文件位置: {os.path.abspath(html_filename)}")
    
    print("\n🎉 所有可视化文件已成功生成到项目根目录！")
    print("\n🔍 查看方法:")
    print(f"1. 直接打开SVG文件:")
    print(f"   - {simple_filename}")
    print(f"   - {complex_filename}")
    print(f"2. 在浏览器中打开HTML查看器:")
    print(f"   - {html_filename}")
    print("3. 在IDE中直接预览SVG文件")
    
    # 检查文件大小
    for filename in [simple_filename, complex_filename, html_filename]:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"📏 {filename}: {size} 字节")

if __name__ == "__main__":
    main()