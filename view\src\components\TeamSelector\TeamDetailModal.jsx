import React from 'react';
import { Modal, Descriptions, Tag, Avatar, Typography, List, Divider, Space, Badge } from 'antd';
import { TeamOutlined, UserOutlined, ApiOutlined, ClockCircleOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

/**
 * 团队详情对话框组件
 * 
 * @param {Object} props
 * @param {Boolean} props.visible - 对话框是否可见
 * @param {Function} props.onClose - 关闭对话框的回调函数
 * @param {Object} props.teamData - 团队数据
 * @returns {JSX.Element}
 */
const TeamDetailModal = ({ visible, onClose, teamData }) => {
  if (!teamData) {
    return null;
  }

  const {
    team_id,
    name,
    description,
    workflow_type,
    context_sharing_strategy,
    collaboration_rules = [],
    routing_conditions = {},
    agents = []
  } = teamData;

  // 获取工作流类型的显示名称和颜色
  const getWorkflowTypeInfo = (type) => {
    const typeMap = {
      'sequential': { name: '顺序执行', color: 'blue' },
      'parallel': { name: '并行执行', color: 'green' },
      'conditional': { name: '条件路由', color: 'orange' }
    };
    return typeMap[type] || { name: type, color: 'default' };
  };

  // 获取上下文策略的显示名称
  const getContextStrategyDisplay = (strategy) => {
    const strategyMap = {
      'selective': '选择性共享',
      'full': '完全共享',
      'minimal': '最小共享'
    };
    return strategyMap[strategy] || strategy;
  };

  const workflowInfo = getWorkflowTypeInfo(workflow_type);

  return (
    <Modal
      title="团队详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={700}
      bodyStyle={{ maxHeight: '70vh', overflow: 'auto', padding: '16px' }}
    >
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <Avatar 
          size={48} 
          icon={<TeamOutlined />} 
          style={{ backgroundColor: '#1890ff', marginRight: 12 }}
        />
        <div>
          <Title level={4} style={{ margin: 0 }}>{name}</Title>
          <Text type="secondary">ID: {team_id}</Text>
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      <Descriptions title="基本信息" layout="horizontal" size="small" column={1} labelStyle={{ fontWeight: 'bold' }}>
        <Descriptions.Item label="团队名称">{name}</Descriptions.Item>
        <Descriptions.Item label="描述">{description}</Descriptions.Item>
        <Descriptions.Item label="工作流类型">
          <Tag color={workflowInfo.color}>{workflowInfo.name}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="上下文共享策略">{getContextStrategyDisplay(context_sharing_strategy)}</Descriptions.Item>
      </Descriptions>

      {/* 协作规则 */}
      {collaboration_rules.length > 0 && (
        <>
          <Divider style={{ margin: '16px 0' }} />
          <Title level={5}>协作规则</Title>
          <List
            size="small"
            dataSource={collaboration_rules}
            renderItem={rule => (
              <List.Item>
                <Text>{rule}</Text>
              </List.Item>
            )}
          />
        </>
      )}

      {/* 条件路由配置（仅条件工作流显示） */}
      {workflow_type === 'conditional' && Object.keys(routing_conditions).length > 0 && (
        <>
          <Divider style={{ margin: '16px 0' }} />
          <Title level={5}>路由条件</Title>
          <List
            size="small"
            dataSource={Object.entries(routing_conditions)}
            renderItem={([key, condition]) => (
              <List.Item>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong>{key}</Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    条件: {condition.condition}
                  </Text>
                  <Text>{condition.description}</Text>
                  {condition.agent && (
                    <Tag icon={<UserOutlined />}>{condition.agent}</Tag>
                  )}
                  {condition.agents && (
                    <Space>
                      {condition.agents.map(agent => (
                        <Tag key={agent} icon={<UserOutlined />}>{agent}</Tag>
                      ))}
                    </Space>
                  )}
                </Space>
              </List.Item>
            )}
          />
        </>
      )}

      {/* 团队成员（智能体） */}
      <Divider style={{ margin: '16px 0' }} />
      <Title level={5}>
        团队成员 
        <Badge count={agents.length} style={{ marginLeft: 8 }} />
      </Title>
      <List
        itemLayout="horizontal"
        dataSource={agents}
        renderItem={agent => (
          <List.Item>
            <List.Item.Meta
              avatar={
                <Avatar 
                  src={agent.avatar_url} 
                  icon={<UserOutlined />} 
                  size="small"
                />
              }
              title={
                <Space>
                  <span>{agent.role_name || agent.agent_id}</span>
                  {agent.model && (
                    <Tag size="small" icon={<ApiOutlined />}>
                      {agent.model}
                    </Tag>
                  )}
                </Space>
              }
              description={
                <div>
                  <Text>{agent.role_definition}</Text>
                  {agent.associated_servers && agent.associated_servers.length > 0 && (
                    <div style={{ marginTop: 4 }}>
                      {agent.associated_servers.map(server => (
                        <Tag key={server} size="small">{server}</Tag>
                      ))}
                    </div>
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      />
    </Modal>
  );
};

export default TeamDetailModal; 