<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<style>
    /* Color Palette */
    :root {
        /* Base Blue System Colors (adapted for dark background) */
        --primary-color: #1E40AF; /* Dark Blue */
        --secondary-color: #475569; /* Slate Gray */
        --accent-color: #3B82F6; /* Bright Blue */
        
        /* Overriding background and strong highlight as per "enhanced aesthetic" */
        --background-color: #0F172A; /* Very Dark Blue/Slate, dark theme base */
        --highlight-color: #E31937; /* Tesla Red for strong emphasis (milestones) */

        /* Text and card colors adjusted for dark background */
        --text-primary: #F8FAFC; /* Light text on dark background */
        --text-secondary: #94A3B8; /* Medium light text */
        --text-light: #64748B; /* Even lighter for less important info */
        --card-background: #1E293B; /* Darker card background for contrast */
        --card-border: #3B82F6; /* Accent blue for card borders */
        --divider-color: #3B82F6; /* Accent blue for dividers */
    }

    /* Font System */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; } /* Not heavily used in this template */

    .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* For milestones */
    .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* Page title */
    .text-section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* Not directly used, but defined */
    .text-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* Event dates */
    .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; } /* Card main text */
    .text-small { font-size: 16px; font-weight: 400; line-height: 1.6; } /* Card detail text, page number */
    .text-caption { font-size: 14px; font-weight: 400; line-height: 1.6; } /* Not directly used, but defined */

    /* General Styles */
    .fill-primary { fill: var(--primary-color); }
    .fill-secondary { fill: var(--secondary-color); }
    .fill-accent { fill: var(--accent-color); }
    .fill-highlight { fill: var(--highlight-color); }
    .fill-background { fill: var(--background-color); }
    .fill-card-background { fill: var(--card-background); }

    .text-primary-color { fill: var(--text-primary); }
    .text-secondary-color { fill: var(--text-secondary); }
    .text-light-color { fill: var(--text-light); }
    .text-accent-color { fill: var(--accent-color); }
    .text-highlight-color { fill: var(--highlight-color); }

    .stroke-primary { stroke: var(--primary-color); }
    .stroke-accent { stroke: var(--accent-color); }
    .stroke-highlight { stroke: var(--highlight-color); }
    .stroke-card-border { stroke: var(--card-border); }
    .stroke-divider { stroke: var(--divider-color); }

    /* Card Style */
    .card-style {
        fill: var(--card-background);
        rx: 12px;
        ry: 12px;
        stroke: var(--card-border);
        stroke-width: 1px;
    }

    /* Shadow Filter */
    .drop-shadow { filter: url(#drop-shadow); }

</style>
<defs>
    <!-- Drop Shadow Filter for elements -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
        <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
        <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4"/>
        <feBlend in="SourceGraphic" in2="blurOut" mode="normal"/>
    </filter>

    <!-- Background Gradient for subtle depth -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
        <stop stop-color="#0F172A"/> <!-- Very Dark Blue/Slate -->
        <stop offset="1" stop-color="#1E293B"/> <!-- Slightly lighter dark blue -->
    </linearGradient>

    <!-- Decorative pattern for Bento Grid feel (subtle grid lines) -->
    <pattern id="gridPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
        <line x1="0" y1="0" x2="100" y2="0" stroke="var(--primary-color)" stroke-width="0.5" stroke-opacity="0.1"/>
        <line x1="0" y1="0" x2="0" y2="100" stroke="var(--primary-color)" stroke-width="0.5" stroke-opacity="0.1"/>
    </pattern>

    <!-- Icon for Milestone (Star) -->
    <symbol id="icon-star" viewBox="0 0 24 24">
        <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" fill="var(--highlight-color)"/>
    </symbol>

    <!-- Icon for regular event (Checkmark) -->
    <symbol id="icon-check" viewBox="0 0 24 24">
        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="var(--accent-color)"/>
    </symbol>

</defs>

<!-- Background -->
<rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

<!-- Decorative Grid Overlay (Subtle Bento Grid feel) -->
<rect x="0" y="0" width="1920" height="1080" fill="url(#gridPattern)" opacity="0.1"/>

<!-- Header Section -->
<g transform="translate(80, 60)">
    <text x="0" y="0" class="text-main-title font-primary text-primary-color">
        <tspan>发展里程碑</tspan>
    </text>
    <text x="0" y="60" class="text-content-title font-secondary text-secondary-color">
        <tspan>Business Development Timeline</tspan>
    </text>
</g>

<!-- Page Number -->
<g transform="translate(1760, 1000)">
    <text x="0" y="0" text-anchor="end" class="text-small font-primary text-light-color">
        <tspan>8/10</tspan>
    </text>
</g>

<!-- Main Timeline Content Area -->
<!-- Content is centered horizontally within the main content width (1920 - 2*80 = 1760) -->
<g transform="translate(80, 280)">
    <!-- Main Timeline Line -->
    <!-- The line spans the content width (1760px) at y=200 relative to this group's origin -->
    <line x1="0" y1="200" x2="1760" y2="200" stroke="var(--accent-color)" stroke-width="4" stroke-linecap="round" opacity="0.6"/>

    <!-- Timeline Events - 5 events, evenly spaced across 1760px width -->
    <!-- Spacing between events: 1760 / 4 = 440px -->

    <!-- Event 1: Foundation (2018) -->
    <g transform="translate(0, 0)">
        <!-- Node -->
        <circle cx="0" cy="200" r="12" fill="var(--accent-color)" class="drop-shadow"/>
        <use xlink:href="#icon-check" x="-12" y="188" width="24" height="24" fill="var(--background-color)"/>

        <!-- Date (Large Chinese, smaller English) -->
        <text x="0" y="150" text-anchor="middle" class="text-content-title font-primary text-primary-color">
            <tspan>2018</tspan>
        </text>
        <text x="0" y="178" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>Foundation</tspan>
        </text>

        <!-- Description Card -->
        <rect x="-150" y="240" width="300" height="120" class="card-style drop-shadow"/>
        <text x="0" y="270" text-anchor="middle" class="text-body font-primary text-primary-color">
            <tspan>公司成立</tspan>
        </text>
        <text x="0" y="300" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>核心团队组建和初期市场调研完成。</tspan>
            <tspan x="0" dy="20">Formed core team and completed initial market research.</tspan>
        </text>
    </g>

    <!-- Event 2: Product Launch (2019) -->
    <g transform="translate(440, 0)">
        <!-- Node -->
        <circle cx="0" cy="200" r="12" fill="var(--accent-color)" class="drop-shadow"/>
        <use xlink:href="#icon-check" x="-12" y="188" width="24" height="24" fill="var(--background-color)"/>

        <!-- Date -->
        <text x="0" y="150" text-anchor="middle" class="text-content-title font-primary text-primary-color">
            <tspan>2019</tspan>
        </text>
        <text x="0" y="178" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>Product Launch</tspan>
        </text>

        <!-- Description Card -->
        <rect x="-150" y="240" width="300" height="120" class="card-style drop-shadow"/>
        <text x="0" y="270" text-anchor="middle" class="text-body font-primary text-primary-color">
            <tspan>首款产品发布</tspan>
        </text>
        <text x="0" y="300" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>成功推出MVP，获得市场初步验证。</tspan>
            <tspan x="0" dy="20">Successfully launched MVP, gained initial market validation.</tspan>
        </text>
    </g>

    <!-- Milestone 3: Series A Funding (2021) - Highlighted -->
    <g transform="translate(880, 0)">
        <!-- Node (Milestone - larger, highlight color) -->
        <circle cx="0" cy="200" r="16" fill="var(--highlight-color)" class="drop-shadow"/>
        <use xlink:href="#icon-star" x="-16" y="184" width="32" height="32" fill="var(--background-color)"/>

        <!-- Date (Extra large, highlight color) -->
        <text x="0" y="150" text-anchor="middle" class="text-hero-title font-primary text-highlight-color">
            <tspan>2021</tspan>
        </text>
        <text x="0" y="178" text-anchor="middle" class="text-content-title font-secondary text-highlight-color">
            <tspan>Series A Funding</tspan>
        </text>

        <!-- Description Card (with highlight border) -->
        <rect x="-150" y="240" width="300" height="120" class="card-style drop-shadow" stroke="var(--highlight-color)"/>
        <text x="0" y="270" text-anchor="middle" class="text-body font-primary text-primary-color">
            <tspan>A轮融资完成</tspan>
        </text>
        <text x="0" y="300" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>获得千万级投资，加速产品迭代。</tspan>
            <tspan x="0" dy="20">Secured multi-million investment, accelerating product iteration.</tspan>
        </text>
    </g>

    <!-- Event 4: Market Expansion (2023) -->
    <g transform="translate(1320, 0)">
        <!-- Node -->
        <circle cx="0" cy="200" r="12" fill="var(--accent-color)" class="drop-shadow"/>
        <use xlink:href="#icon-check" x="-12" y="188" width="24" height="24" fill="var(--background-color)"/>

        <!-- Date -->
        <text x="0" y="150" text-anchor="middle" class="text-content-title font-primary text-primary-color">
            <tspan>2023</tspan>
        </text>
        <text x="0" y="178" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>Market Expansion</tspan>
        </text>

        <!-- Description Card -->
        <rect x="-150" y="240" width="300" height="120" class="card-style drop-shadow"/>
        <text x="0" y="270" text-anchor="middle" class="text-body font-primary text-primary-color">
            <tspan>市场份额扩大</tspan>
        </text>
        <text x="0" y="300" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>进入新的细分市场，用户增长迅速。</tspan>
            <tspan x="0" dy="20">Entered new market segments, rapid user growth.</tspan>
        </text>
    </g>

    <!-- Event 5: Future Outlook (Placeholder) -->
    <g transform="translate(1760, 0)">
        <!-- Node -->
        <circle cx="0" cy="200" r="12" fill="var(--accent-color)" class="drop-shadow"/>
        <use xlink:href="#icon-check" x="-12" y="188" width="24" height="24" fill="var(--background-color)"/>

        <!-- Date -->
        <text x="0" y="150" text-anchor="middle" class="text-content-title font-primary text-primary-color">
            <tspan>未来展望</tspan>
        </text>
        <text x="0" y="178" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>Future Outlook</tspan>
        </text>

        <!-- Description Card -->
        <rect x="-150" y="240" width="300" height="120" class="card-style drop-shadow"/>
        <text x="0" y="270" text-anchor="middle" class="text-body font-primary text-primary-color">
            <tspan>持续创新</tspan>
        </text>
        <text x="0" y="300" text-anchor="middle" class="text-small font-secondary text-secondary-color">
            <tspan>AI融合和全球化战略。</tspan>
            <tspan x="0" dy="20">AI integration and globalization strategy.</tspan>
        </text>
    </g>

</g>

<!-- Footer / Logo Placeholder -->
<g transform="translate(80, 1000)">
    <!-- Logo Placeholder (example circle) -->
    <circle cx="20" cy="20" r="20" fill="var(--primary-color)"/>
    <text x="50" y="28" class="text-small font-primary text-primary-color">
        <tspan>公司名称 {logo_url}</tspan>
    </text>
</g>

</svg>