# 预览弹窗中的重新生成功能

## 功能概述

在 SVG 预览弹窗中添加了重新生成按钮，让用户可以在查看页面详情时直接重新生成，提供更便捷的操作体验。

## 新增功能

### 1. 预览弹窗重新生成按钮
- **位置**：预览弹窗底部工具栏
- **样式**：带有刷新图标的边框按钮
- **功能**：直接重新生成当前预览的页面

### 2. 智能交互控制
- **导航控制**：重新生成期间禁用上下页切换
- **页面导航**：重新生成期间禁用页面导航点
- **按钮状态**：重新生成期间显示加载状态

### 3. 实时进度反馈
- **加载遮罩**：重新生成时显示半透明遮罩
- **进度提示**：显示"正在重新生成第 X 页..."
- **说明文字**：解释使用原始设计规范保持一致性

## 用户体验设计

### 视觉反馈
```jsx
// 重新生成按钮样式
<Button
  type="default"
  icon={<ReloadOutlined />}
  loading={regeneratingSvgIndex === currentIndex}
  style={{ 
    borderColor: '#1890ff',
    color: '#1890ff'
  }}
>
  重新生成
</Button>

// 加载遮罩
{regeneratingSvgIndex === currentIndex && (
  <div style={{
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    // 覆盖整个预览区域
  }}>
    <Spin size="large" />
    <div>正在重新生成第 {currentIndex + 1} 页...</div>
  </div>
)}
```

### 交互控制
```jsx
// 禁用导航按钮
disabled={currentIndex === 0 || regeneratingSvgIndex !== null}

// 禁用页面导航点
onClick={() => regeneratingSvgIndex === null && setCurrentIndex(index)}
cursor: regeneratingSvgIndex === null ? 'pointer' : 'not-allowed'
```

## 操作流程

### 标准操作流程
1. **打开预览**：点击任意 SVG 页面的"预览"按钮
2. **浏览页面**：使用上下页按钮或导航点查看不同页面
3. **发现问题**：在某个页面发现内容需要改进
4. **重新生成**：直接点击预览弹窗中的"重新生成"按钮
5. **等待完成**：观看实时进度反馈
6. **查看结果**：重新生成完成后自动显示新内容
7. **继续操作**：可以继续浏览其他页面或进行其他操作

### 多页面操作
1. **快速对比**：在预览弹窗中快速切换页面
2. **批量优化**：逐页进行重新生成优化
3. **实时预览**：每次重新生成后立即看到效果

## 技术实现

### 状态管理
```jsx
// 重新生成状态
const [regeneratingSvgIndex, setRegeneratingSvgIndex] = useState(null);

// 页面内容自动更新
setSvgContent(prevContent => {
  const newContent = [...prevContent];
  newContent[pageIndex] = newPageData;
  return newContent;
});
```

### 进度同步
- WebSocket 实时通信
- 状态自动同步
- 错误处理机制

## 功能优势

### 1. 操作便捷性
- **无需关闭弹窗**：在预览状态下直接重新生成
- **即时反馈**：重新生成完成后立即查看效果
- **连续操作**：可以连续对多个页面进行优化

### 2. 用户体验
- **视觉连贯性**：保持在预览环境中
- **操作直观性**：看到问题立即解决
- **反馈及时性**：实时进度和完成提示

### 3. 工作流优化
- **减少操作步骤**：从6步减少到3步
- **提高效率**：预览和重新生成无缝结合
- **降低出错率**：不需要记住页面编号

## 使用场景

### 场景1：内容审查和优化
```
用户生成了一个10页的演示文稿
→ 点击预览查看整体效果
→ 发现第3页的图表不够清晰
→ 直接在预览中点击"重新生成"
→ 查看改进效果，满意后继续查看其他页面
```

### 场景2：样式微调
```
用户对某个页面的颜色搭配不满意
→ 在预览弹窗中查看该页面
→ 点击重新生成进行优化
→ 立即看到新的颜色效果
→ 如果还不满意可以再次重新生成
```

### 场景3：内容完善
```
用户发现某页面内容不够丰富
→ 在预览中仔细查看页面细节
→ 使用重新生成功能让AI优化内容
→ 在同一界面中对比前后效果
→ 满意后继续处理其他页面
```

## 交互细节

### 按钮状态
- **正常状态**：蓝色边框，可点击
- **加载状态**：显示旋转图标，不可点击
- **禁用状态**：灰色显示，不可交互

### 导航控制
- **重新生成期间**：所有导航功能被禁用
- **视觉提示**：被禁用的控件透明度降低
- **鼠标样式**：显示"不允许"光标

### 错误处理
- **网络错误**：显示错误消息，恢复操作能力
- **生成失败**：提示具体错误原因
- **超时处理**：自动恢复，允许重试

## 未来扩展

### 增强功能
1. **进度百分比**：显示详细的重新生成进度
2. **预览对比**：并排显示重新生成前后的效果
3. **批量重新生成**：选择多个页面批量重新生成
4. **自定义提示**：允许用户输入具体的改进要求

### 性能优化
1. **预加载**：提前准备重新生成所需的资源
2. **缓存优化**：缓存常用的设计规范
3. **并发控制**：允许多个页面同时重新生成

这个功能显著提升了用户在 SVG 演示文稿编辑过程中的操作体验，让重新生成变得更加直观和高效。