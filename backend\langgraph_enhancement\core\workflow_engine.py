# -*- coding: utf-8 -*-
"""
🔥 LangGraph 增强系统 - 动态工作流引擎

核心功能：
- 动态构建LangGraph工作流
- 支持多种协作模式（sequential、parallel、conditional）
- 完全集成现有AIManager和MCP系统
- 智能上下文传递和状态管理
"""

# 🔥 修复：设置matplotlib后端为Agg，避免macOS下NSWindow线程冲突
import matplotlib
matplotlib.use('Agg')  # 非GUI后端，避免在后台线程创建窗口

import logging
import asyncio
import uuid
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Callable
from functools import partial

# 设置日志记录器
logger = logging.getLogger(__name__)

def create_workflow_independent_callback(original_callback: Optional[Callable], context_info: Dict[str, Any] = None) -> Optional[Callable]:
    """
    创建不依赖工作流状态的持久化回调
    
    此函数解决工作流结束时流式传输被中断的问题。
    通过创建一个独立于工作流生命周期的回调包装器，
    确保AI流式输出能够完整传输到前端。
    
    Args:
        original_callback: 原始的流式回调函数
        context_info: 上下文信息（用于日志和调试）
        
    Returns:
        持久化的回调函数，即使工作流结束也能继续工作
    """
    if not original_callback or not callable(original_callback):
        return None
    
    # 提取上下文信息用于日志
    workflow_id = context_info.get("workflow_id", "unknown") if context_info else "unknown"
    team_id = context_info.get("team_id", "unknown") if context_info else "unknown"
    
    async def persistent_callback(content: str) -> None:
        """
        持久化回调实现
        
        特点：
        1. 不依赖工作流状态或上下文
        2. 优雅处理异常，确保AI推理不受影响
        3. 保持对必要资源的最小引用
        4. 在工作流结束后仍能正常工作
        """
        try:
            # 执行原始回调，传输流式内容到前端
            await original_callback(content)
            
            # 调试日志（仅在必要时记录）
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"🔄 持久化流式回调执行成功 [workflow: {workflow_id}, team: {team_id}]")
                
        except Exception as e:
            # 优雅处理异常：
            # 1. 记录警告但不抛出异常
            # 2. 确保AI推理过程不会因回调失败而中断
            # 3. 为调试提供足够信息
            logger.warning(f"⚠️ 持久化流式回调执行异常 [workflow: {workflow_id}, team: {team_id}]: {e}")
            
            # 不重新抛出异常，避免影响AI推理
            pass
    
    # 添加标识，便于调试和监控
    persistent_callback._is_persistent = True
    persistent_callback._context_info = context_info or {}
    
    logger.debug(f"✅ 创建持久化流式回调 [workflow: {workflow_id}, team: {team_id}]")
    return persistent_callback

# 导入LangGraph核心组件
from ..core.state_definitions import EnhancedTeamState
from ..config.config_manager import get_config_manager
from ..core.context_manager import get_context_manager
from ..routers.intelligent_router import get_intelligent_router
from ..core.agent_transformer import get_agent_factory

# 注释：所有核心组件通过context_manager统一访问，无需直接导入

# 🔥 LangGraph增强实现 - 支持原生LangGraph和优化的Fallback
try:
    from langgraph import StateGraph, END
    from langgraph.checkpoint.memory import MemorySaver
    from langgraph.graph import Graph
    LANGGRAPH_NATIVE_AVAILABLE = True
    logger.info("✅ 使用原生LangGraph实现")
except ImportError:
    # 🔥 优化的LangGraph兼容实现
    LANGGRAPH_NATIVE_AVAILABLE = False
    logger.info("⚡ 使用优化的LangGraph兼容实现")
    
    class StateGraph:
        """🔥 优化的StateGraph实现 - 完全兼容LangGraph接口"""
        def __init__(self, state_schema):
            self.state_schema = state_schema
            self.nodes = {}
            self.edges = []
            self._conditional_edges = {}  # 存储条件边
            self.entry_point = None
            self._checkpointer = None
        
        def add_node(self, name, func):
            """添加节点到图中"""
            self.nodes[name] = func
            return self
        
        def add_edge(self, from_node, to_node):
            """添加边到图中"""
            self.edges.append((from_node, to_node))
            return self
        
        def add_conditional_edges(self, node, condition, mapping):
            """添加条件边
            
            Args:
                node: 源节点
                condition: 条件函数，接收state返回目标节点名称
                mapping: 条件结果到目标节点的映射
            """
            self._conditional_edges[node] = (condition, mapping)
            return self
        
        def set_entry_point(self, node):
            """设置图的入口点"""
            self.entry_point = node
            return self
        
        def compile(self, checkpointer=None):
            """编译图并返回可执行的工作流"""
            self._checkpointer = checkpointer
            return EnhancedWorkflowExecutor(self, checkpointer)
        
        def visualize(self, output_path=None):
            """🔥 优化：图可视化功能（线程安全）"""
            try:
                import matplotlib.pyplot as plt
                import networkx as nx
                
                # 创建网络图
                G = nx.DiGraph()
                
                # 添加节点
                for node in self.nodes.keys():
                    G.add_node(node)
                
                # 添加边
                for from_node, to_node in self.edges:
                    G.add_edge(from_node, to_node)
                
                # 添加条件边
                for node, (condition, mapping) in self._conditional_edges.items():
                    for target in mapping.values():
                        if target != END:
                            G.add_edge(node, target, style='dashed')
                
                # 绘制图
                plt.figure(figsize=(12, 8))
                pos = nx.spring_layout(G)
                nx.draw(G, pos, with_labels=True, node_color='lightblue', 
                       node_size=3000, font_size=10, font_weight='bold')
                
                if output_path:
                    plt.savefig(output_path, dpi=150, bbox_inches='tight')
                    plt.close()  # 释放图形资源
                    logger.info(f"📊 工作流图已保存到: {output_path}")
                else:
                    # 🔥 修复：Agg后端下不支持plt.show()，改为保存到临时文件
                    import tempfile
                    import os
                    temp_path = os.path.join(tempfile.gettempdir(), f"workflow_graph_{uuid.uuid4().hex[:8]}.png")
                    plt.savefig(temp_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    logger.info(f"📊 工作流图已保存到临时文件: {temp_path}")
                    
            except ImportError:
                logger.warning("⚠️ 图可视化需要安装matplotlib和networkx: pip install matplotlib networkx")
            except Exception as e:
                logger.warning(f"⚠️ 图可视化失败（已跳过）: {e}")
    
    class MemorySaver:
        """🔥 优化的内存保存器实现"""
        def __init__(self):
            self._storage = {}
            self._metadata = {}
        
        def put(self, key, value, metadata=None, new_versions=None):
            """保存状态到内存 - 兼容LangGraph接口"""
            self._storage[key] = value
            if metadata:
                self._metadata[key] = metadata
            # new_versions参数用于兼容LangGraph原生接口，但在简化实现中不使用
        
        def get(self, key, default=None):
            """从内存获取状态"""
            return self._storage.get(key, default)
        
        def delete(self, key):
            """删除内存中的状态"""
            self._storage.pop(key, None)
            self._metadata.pop(key, None)
        
        def list_keys(self):
            """列出所有保存的键"""
            return list(self._storage.keys())
    
    END = "__END__"


logger = logging.getLogger(__name__)


class SimpleParallelExecutor:
    """
    🔥 简化的并行执行器，专注于输出完整性
    
    核心理念：放弃复杂的实时流协调，改为"完整输出"模式
    - 每个智能体完成后，完整展示其全部输出
    - 保持智能体输出的完整性和连续性
    - 简化并行执行逻辑，提高可靠性
    """
    
    def __init__(self, config_manager, context_manager):
        self.config_manager = config_manager
        self.context_manager = context_manager
        self.agent_display_order = []
        self.display_mode = "sequential"  # sequential/immediate/grouped
        logger.info("🔧 简化并行执行器初始化完成")
    
    async def execute_with_complete_output(self, state, config, agents, stream_callback=None):
        """
        🔥 并行执行 + 完整输出模式
        每个智能体的输出都是完整的，不会被碎片化
        """
        try:
            # 获取团队配置
            team_config = state.get("_team_config", {})
            self.display_mode = team_config.get("display_mode", "sequential")
            self.agent_display_order = team_config.get("agent_display_order", list(agents.keys()))
            
            logger.info(f"🎯 简化并行执行：{len(agents)} 个智能体，展示模式: {self.display_mode}")
            
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__启动 {len(agents)} 个智能体并行处理..."), 
                    asyncio.get_running_loop()
                )
            
            # 1. 启动所有智能体（收集完整输出）
            agent_tasks = {}
            for agent_id, agent in agents.items():
                task = asyncio.create_task(
                    self.execute_agent_with_buffer(state, config, agent_id, agent)
                )
                agent_tasks[agent_id] = task
            
            logger.info("🚀 所有智能体后台任务已启动，等待完成...")
            
            # 2. 根据配置选择展示模式
            if self.display_mode == "sequential":
                merged_state = await self.display_sequential_complete(state, agent_tasks, agents, stream_callback)
            elif self.display_mode == "immediate":
                merged_state = await self.display_immediate_complete(state, agent_tasks, agents, stream_callback)
            else:
                merged_state = await self.display_grouped_complete(state, agent_tasks, agents, stream_callback)
            
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__所有智能体处理完成"), 
                    asyncio.get_running_loop()
                )
            
            return merged_state
            
        except Exception as e:
            logger.error(f"❌ 简化并行执行失败: {e}")
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__error__并行执行失败: {str(e)}"), 
                    asyncio.get_running_loop()
                )
            return state
    
    async def execute_agent_with_buffer(self, state, config, agent_id, agent):
        """
        🔥 执行智能体并缓冲完整输出
        收集智能体的所有输出，保证完整性
        """
        try:
            logger.info(f"⚡ 开始执行智能体: {agent_id}")
            
            # 创建输出缓冲区
            output_buffer = []
            start_time = datetime.now()
            
            async def buffer_callback(message):
                """🔥 异步缓冲回调函数，收集所有输出"""
                if message and isinstance(message, str):
                    output_buffer.append({
                        "content": message,
                        "timestamp": datetime.now(),
                        "type": "status" if message.startswith("__") else "content"
                    })
                    # 🔥 重点监控重试相关的消息
                    if "重试" in message or "retry" in message.lower() or "错误" in message or "error" in message.lower():
                        logger.info(f"🔄 智能体 {agent_id} 重试/错误消息: {message}")
                    # 🔥 记录工具调用信息和缓冲状态
                    if "工具调用" in message or "tool" in message.lower():
                        logger.debug(f"智能体 {agent_id} 工具调用缓冲: {message[:100]}...")
                    logger.debug(f"智能体 {agent_id} 缓冲消息: 类型={output_buffer[-1]['type']}, 长度={len(message)}, 内容前50字符={message[:50]}...")
            
            # 创建独立状态
            agent_state = await self._create_independent_agent_state(state, agent_id, agent)
            
            # 执行智能体，收集所有输出到缓冲区
            logger.info(f"🚀 开始执行智能体 {agent_id}，工具服务器: {getattr(agent, 'associated_servers', [])}")
            
            # 🧠 优先使用记忆增强处理，如果不可用则降级到优化版本
            if hasattr(agent, 'process_with_memory_enhancement') and getattr(agent, 'memory_enabled', False):
                logger.info(f"🧠 {agent_id} 使用记忆增强处理")
                result_state = await agent.process_with_memory_enhancement(
                    agent_state, 
                    stream_callback=buffer_callback
                )
            else:
                logger.info(f"⚡ {agent_id} 使用标准优化处理")
                result_state = await agent.process_with_optimized_context(
                    agent_state, 
                    stream_callback=buffer_callback
                )
            logger.info(f"🏁 智能体 {agent_id} 执行完成，缓冲区收集了 {len(output_buffer)} 条消息")
            
            # 🔥 检查智能体执行结果状态
            if result_state.get('last_error'):
                logger.warning(f"⚠️ 智能体 {agent_id} 执行过程中遇到错误: {result_state.get('last_error')}")
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 🔥 统计工具调用情况和重试情况
            tool_call_count = sum(1 for item in output_buffer 
                                if "工具调用" in item["content"] or "tool" in item["content"].lower())
            retry_count = sum(1 for item in output_buffer 
                            if "重试" in item["content"] or "retry" in item["content"].lower())
            error_count = sum(1 for item in output_buffer 
                            if "错误" in item["content"] or "error" in item["content"].lower())
            content_count = sum(1 for item in output_buffer if item["type"] == "content")
            
            logger.info(f"✅ 智能体 {agent_id} 执行完成，耗时: {execution_time:.2f}秒，收集了 {len(output_buffer)} 条输出（内容:{content_count}, 工具调用:{tool_call_count}, 重试:{retry_count}, 错误:{error_count}）")
            
            # 🔥 等待一小段时间，确保没有延迟的重试消息
            if retry_count > 0 or error_count > 0:
                logger.info(f"🔄 智能体 {agent_id} 检测到重试或错误，额外等待0.5秒收集完整输出...")
                await asyncio.sleep(0.5)
                if len(output_buffer) > content_count + tool_call_count + retry_count + error_count:
                    logger.info(f"📥 智能体 {agent_id} 额外收集到 {len(output_buffer) - content_count - tool_call_count - retry_count - error_count} 条延迟消息")
            
            return {
                "agent_id": agent_id,
                "agent_name": getattr(agent, 'role_name', agent_id),
                "result_state": result_state,
                "complete_output": output_buffer,
                "status": "completed",
                "execution_time": execution_time,
                "start_time": start_time,
                "end_time": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ 智能体 {agent_id} 执行失败: {e}")
            # 🔥 即使失败，也尝试收集部分输出
            execution_time = (datetime.now() - start_time).total_seconds()
            return {
                "agent_id": agent_id,
                "agent_name": getattr(agent, 'role_name', agent_id),
                "result_state": state,
                "complete_output": output_buffer,  # 🔥 保留已收集的输出
                "status": "failed",
                "error": str(e),
                "execution_time": execution_time,
                "start_time": start_time,
                "end_time": datetime.now()
            }
    
    async def display_sequential_complete(self, original_state, agent_tasks, agents, stream_callback):
        """
        🔥 顺序完整展示模式 - 按配置顺序展示每个智能体的完整输出
        """
        logger.info("📋 顺序完整展示模式")
        
        completed_results = {}
        
        # 按配置顺序等待并展示每个智能体的完整输出
        for agent_id in self.agent_display_order:
            if agent_id in agent_tasks:
                agent = agents[agent_id]
                
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__等待 {agent.role_name} 完成分析..."), 
                        asyncio.get_running_loop()
                    )
                
                # 等待该智能体完成
                agent_result = await agent_tasks[agent_id]
                completed_results[agent_id] = agent_result
                
                # 展示该智能体的完整输出
                if agent_result["status"] == "completed":
                    await self.display_agent_complete_output(agent_result, stream_callback)
                else:
                    if stream_callback:
                        asyncio.run_coroutine_threadsafe(
                            stream_callback(f"__error__{agent.role_name} 处理失败: {agent_result.get('error', '未知错误')}"), 
                            asyncio.get_running_loop()
                        )
        
        # 合并所有结果
        return await self._merge_complete_results(original_state, completed_results)
    
    async def display_immediate_complete(self, original_state, agent_tasks, agents, stream_callback):
        """
        🔥 立即完整展示模式 - 智能体完成后立即展示完整输出
        """
        logger.info("⚡ 立即完整展示模式")
        
        completed_results = {}
        remaining_tasks = dict(agent_tasks)
        
        while remaining_tasks:
            # 等待任意一个智能体完成
            done, pending = await asyncio.wait(
                remaining_tasks.values(), 
                return_when=asyncio.FIRST_COMPLETED
            )
            
            for task in done:
                # 找到完成的智能体
                completed_agent_id = None
                for agent_id, agent_task in remaining_tasks.items():
                    if agent_task == task:
                        completed_agent_id = agent_id
                        break
                
                if completed_agent_id:
                    agent_result = await task
                    completed_results[completed_agent_id] = agent_result
                    
                    # 立即展示该智能体的完整输出
                    if agent_result["status"] == "completed":
                        await self.display_agent_complete_output(agent_result, stream_callback)
                    else:
                        if stream_callback:
                            agent = agents[completed_agent_id]
                            asyncio.run_coroutine_threadsafe(
                                stream_callback(f"__error__{agent.role_name} 处理失败: {agent_result.get('error', '未知错误')}"), 
                                asyncio.get_running_loop()
                            )
                    
                    # 从剩余任务中移除
                    del remaining_tasks[completed_agent_id]
        
        return await self._merge_complete_results(original_state, completed_results)
    
    async def display_grouped_complete(self, original_state, agent_tasks, agents, stream_callback):
        """
        🔥 分组完整展示模式 - 按组展示智能体的完整输出
        """
        logger.info("🏗️ 分组完整展示模式")
        
        # 获取分组配置
        team_config = original_state.get("_team_config", {})
        agent_groups = team_config.get("agent_groups", {})
        
        if not agent_groups:
            # 如果没有配置分组，回退到顺序模式
            logger.warning("⚠️ 未配置智能体分组，回退到顺序展示模式")
            return await self.display_sequential_complete(original_state, agent_tasks, agents, stream_callback)
        
        completed_results = {}
        
        # 按组处理
        for group_name, group_agent_ids in agent_groups.items():
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__处理 {group_name} 组..."), 
                    asyncio.get_running_loop()
                )
            
            # 等待该组所有智能体完成
            group_tasks = [agent_tasks[agent_id] for agent_id in group_agent_ids if agent_id in agent_tasks]
            if group_tasks:
                group_results = await asyncio.gather(*group_tasks, return_exceptions=True)
                
                # 展示该组的所有输出
                for i, result in enumerate(group_results):
                    agent_id = group_agent_ids[i] if i < len(group_agent_ids) else f"unknown_{i}"
                    if agent_id in agent_tasks:
                        completed_results[agent_id] = result
                        
                        if isinstance(result, dict) and result.get("status") == "completed":
                            await self.display_agent_complete_output(result, stream_callback)
                
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__{group_name} 组处理完成"), 
                        asyncio.get_running_loop()
                    )
        
        return await self._merge_complete_results(original_state, completed_results)
    
    async def display_agent_complete_output(self, agent_result, stream_callback):
        """
        🔥 展示智能体的完整输出
        一次性展示该智能体的所有内容，保证完整性
        """
        if not stream_callback:
            return
        
        agent_id = agent_result["agent_id"]
        agent_name = agent_result["agent_name"]
        complete_output = agent_result["complete_output"]
        
        try:
            # 发送开始标识
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__{agent_name} 分析完成，正在展示结果..."), 
                asyncio.get_running_loop()
            )
            
            # 🔥 修复：过滤并展示有效内容，包含工具调用的短消息
            content_messages = [
                item for item in complete_output 
                if item["type"] == "content" and 
                   item["content"] and 
                   not item["content"].startswith("__") and
                   len(item["content"].strip()) > 0  # 🔥 修复：不过滤短消息，保留工具调用结果
            ]
            
            logger.debug(f"🔍 智能体 {agent_id} 内容过滤结果: 总输出{len(complete_output)}条 -> 有效内容{len(content_messages)}条")
            
            # 如果有内容，完整展示
            if content_messages:
                for i, item in enumerate(content_messages):
                    content = item["content"].strip()
                    
                    # 🔥 优化：只在第一条消息前添加智能体标识，后续消息不重复标记
                    if i == 0:
                        formatted_content = f"\n【{agent_name}】{content}"
                    else:
                        formatted_content = content
                    
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(formatted_content), 
                        asyncio.get_running_loop()
                    )
                    
                    # 短暂延迟，保持可读性
                    if i < len(content_messages) - 1:
                        await asyncio.sleep(0.05)
            else:
                # 如果没有有效内容，显示简短的完成消息
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"\n【{agent_name}】分析完成"), 
                    asyncio.get_running_loop()
                )
            
            # 发送完成标识
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__{agent_name} 输出展示完成"), 
                asyncio.get_running_loop()
            )
            
            logger.debug(f"✅ 完整展示了智能体 {agent_id} 的 {len(content_messages)} 条内容")
            
        except Exception as e:
            logger.error(f"❌ 展示智能体 {agent_id} 输出失败: {e}")
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__error__展示 {agent_name} 输出时出错"), 
                asyncio.get_running_loop()
            )
    
    async def _create_independent_agent_state(self, state, agent_id, agent):
        """为智能体创建独立状态"""
        try:
            import copy
            independent_state = copy.deepcopy(state)
            independent_state["parallel_agent_id"] = agent_id
            independent_state["parallel_execution_timestamp"] = datetime.now().isoformat()
            return independent_state
        except Exception as e:
            logger.error(f"❌ 创建独立状态失败 {agent_id}: {e}")
            import copy
            return copy.deepcopy(state)
    
    async def _merge_complete_results(self, original_state, completed_results):
        """🔥 合并所有智能体的完整结果 - 深拷贝确保上下文完整性"""
        try:
            import copy
            # 🔥 修复：使用深拷贝确保完整状态传递
            merged_state = copy.deepcopy(original_state)
            merged_messages = merged_state.get("messages", []).copy()  # 额外确保消息列表独立
            
            # 收集所有成功的结果
            successful_results = []
            failed_results = []
            
            for agent_id, result in completed_results.items():
                if result["status"] == "completed":
                    successful_results.append(result)
                    
                    # 🔥 修复：使用complete_output构建完整内容，而不是依赖可能不完整的result_state
                    complete_output = result.get("complete_output", [])
                    
                    if complete_output:
                        # 合并所有有效内容为一条完整的assistant消息
                        content_items = [
                            item for item in complete_output 
                            if item["type"] == "content" and 
                               item["content"] and 
                               not item["content"].startswith("__") and
                               len(item["content"].strip()) > 0
                        ]
                        
                        if content_items:
                            # 将所有内容片段合并为完整的响应
                            full_content = "".join([item["content"] for item in content_items])
                            
                            agent_message = {
                                "role": "assistant",
                                "content": full_content,
                                "timestamp": datetime.now().isoformat(),
                                "agent_id": agent_id,
                                "agent_name": result.get("agent_name", agent_id),
                                "status": "completed"
                            }
                            
                            # 如果result_state中有工具调用历史，也添加进来
                            result_state = result.get("result_state", {})
                            if "messages" in result_state:
                                last_message = result_state["messages"][-1] if result_state["messages"] else {}
                                if last_message.get("tool_calls_history"):
                                    agent_message["tool_calls_history"] = last_message["tool_calls_history"]
                            
                            merged_messages.append(agent_message)
                            logger.info(f"✅ 智能体 {agent_id} 完整内容已合并到状态 (长度: {len(full_content)} 字符, 来源: {len(content_items)} 个流式片段)")
                        else:
                            logger.warning(f"⚠️ 智能体 {agent_id} 没有有效的内容输出")
                    else:
                        # 如果没有complete_output，回退到原来的逻辑
                        logger.warning(f"⚠️ 智能体 {agent_id} 没有complete_output，使用result_state回退")
                        result_state = result["result_state"]
                        if "messages" in result_state:
                            agent_messages = result_state["messages"]
                            original_count = len(original_state.get("messages", []))
                            
                            if len(agent_messages) > original_count:
                                new_messages = agent_messages[original_count:]
                                merged_messages.extend(new_messages)
                else:
                    failed_results.append(result)
                    # 🔥 修复：失败的智能体也尝试合并部分结果
                    if result.get("complete_output"):
                        logger.info(f"智能体 {agent_id} 虽然失败，但保留了 {len(result['complete_output'])} 条部分输出")
                        # 将失败智能体的部分输出作为消息添加
                        content_items = [item for item in result["complete_output"] 
                                       if item["type"] == "content" and item["content"].strip()]
                        if content_items:
                            partial_content = "\n".join([item["content"] for item in content_items[-3:]])  # 取最后3条
                            merged_messages.append({
                                "role": "assistant",
                                "content": f"[{result['agent_name']} 部分结果]: {partial_content}",
                                "timestamp": datetime.now().isoformat(),
                                "agent_id": agent_id,
                                "status": "partial_from_failed"
                            })
            
            merged_state["messages"] = merged_messages
            
            # 添加执行摘要
            merged_state["simple_parallel_execution_summary"] = {
                "total_agents": len(completed_results),
                "successful_agents": len(successful_results),
                "failed_agents": len(failed_results),
                "success_rate": len(successful_results) / len(completed_results) if completed_results else 0,
                "total_execution_time": sum([r["execution_time"] for r in successful_results]),
                "completed_at": datetime.now().isoformat()
            }
            
            logger.info(f"✅ 简化并行结果合并完成: {len(successful_results)} 成功, {len(failed_results)} 失败")
            return merged_state
            
        except Exception as e:
            logger.error(f"❌ 合并完整结果失败: {e}")
            return original_state


# ParallelStreamManager已被移除，使用SimpleParallelExecutor替代


class EnhancedWorkflowExecutor:
    """🔥 增强的工作流执行器 - 完全兼容LangGraph执行模式"""
    
    def __init__(self, graph, checkpointer=None):
        self.graph = graph
        self.checkpointer = checkpointer or MemorySaver()
        # 构建边的映射关系
        self.edges = {}  # {from_node: to_node}
        self.conditional_edges = {}  # {from_node: (condition_func, mapping)}
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0
        }
        self._build_edge_mappings()
    
    def _build_edge_mappings(self):
        """构建边的映射关系，用于图遍历"""
        # 从graph的edges列表构建边映射
        if hasattr(self.graph, 'edges'):
            for from_node, to_node in self.graph.edges:
                self.edges[from_node] = to_node
        
        # 处理条件边（如果StateGraph实现了相关存储）
        if hasattr(self.graph, '_conditional_edges'):
            self.conditional_edges = self.graph._conditional_edges
    
    async def ainvoke(self, state, config=None):
        """🔥 异步执行工作流 - 增强的图遍历执行"""
        start_time = datetime.now()
        self.execution_stats["total_executions"] += 1
        
        try:
            current_state = state.copy() if isinstance(state, dict) else dict(state)
            
            # 获取入口点
            entry_point = getattr(self.graph, 'entry_point', None)
            if not entry_point:
                # 如果没有设置入口点，尝试找到没有入边的节点
                logger.warning("未设置入口点，尝试查找起始节点")
                all_to_nodes = set(self.edges.values())
                potential_entries = [node for node in self.graph.nodes.keys() 
                                   if node not in all_to_nodes and node != END]
                entry_point = potential_entries[0] if potential_entries else list(self.graph.nodes.keys())[0]
            
            # 从入口点开始遍历
            current_node = entry_point
            visited_nodes = set()
            execution_path = []
            max_loops = 150  # 设置最大循环次数，防止无限循环
            loop_count = 0
            
            logger.info(f"🚀 工作流执行开始，入口点: {current_node}")
            
            while current_node and current_node != END:
                loop_count += 1
                if loop_count > max_loops:
                    logger.error(f"❌ 超出最大循环次数 ({max_loops})，工作流强制中断。")
                    break
                
                # 允许多次访问同一节点以支持循环，但记录访问历史
                visited_nodes.add(current_node)
                execution_path.append(current_node)
                
                # 获取节点函数
                node_func = self.graph.nodes.get(current_node)
                if not node_func:
                    logger.error(f"节点 {current_node} 未找到")
                    break
                
                # 执行节点
                try:
                    logger.info(f"➡️ 执行节点: {current_node}")
                    result = await node_func(current_state, config=config or {})
                    if isinstance(result, dict):
                        current_state.update(result)
                except Exception as e:
                    logger.error(f"节点 {current_node} 执行失败: {e}")
                    current_state["errors"] = current_state.get("errors", [])
                    current_state["errors"].append({
                        "node": current_node,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
                    # 🔥 增强的错误处理：根据配置决定是否继续
                    error_handling = config.get("error_handling", "break") if config else "break"
                    if error_handling == "continue":
                        logger.warning(f"继续执行下一个节点，跳过失败的节点: {current_node}")
                        next_node = await self._get_next_node(current_node, current_state)
                        current_node = next_node
                        continue
                    else:
                        break
                
                # 决定下一个节点
                next_node = await self._get_next_node(current_node, current_state)
                logger.info(f"🔄 下一个节点: {next_node}")
                current_node = next_node
            
            # 记录执行路径和统计信息
            execution_time = (datetime.now() - start_time).total_seconds()
            current_state["_execution_path"] = execution_path
            current_state["_execution_time"] = execution_time
            current_state["_execution_stats"] = self.execution_stats.copy()
            
            # 更新统计信息
            self.execution_stats["successful_executions"] += 1
            old_avg = self.execution_stats["average_execution_time"]
            total_successful = self.execution_stats["successful_executions"]
            self.execution_stats["average_execution_time"] = (
                (old_avg * (total_successful - 1) + execution_time) / total_successful
            )
            
            logger.info(f"✅ 工作流执行完成，路径: {' -> '.join(execution_path)}, 耗时: {execution_time:.2f}s")
            
            return current_state
            
        except Exception as e:
            self.execution_stats["failed_executions"] += 1
            logger.error(f"❌ 工作流执行失败: {e}")
            raise
    
    async def _get_next_node(self, current_node, state):
        """根据边的定义获取下一个节点"""
        # 检查是否有条件边
        if current_node in self.conditional_edges:
            condition_func, mapping = self.conditional_edges[current_node]
            # 调用条件函数
            try:
                result = condition_func(state)
                next_node = mapping.get(result, END)
                logger.info(f"🔀 条件路由: {current_node} -> {next_node} (条件结果: {result})")
                return next_node
            except Exception as e:
                logger.error(f"条件函数执行失败: {e}")
                return END
        
        # 检查普通边
        if current_node in self.edges:
            return self.edges[current_node]
        
        # 没有出边，结束
        return END


class WorkflowBuilder:
    """
    🔥 工作流构建器
    负责根据团队配置动态构建 LangGraph 工作流图。
    """
    def __init__(self, engine: 'DynamicWorkflowEngine'):
        self.engine = engine

    async def build(self, team_config: Dict, stream_callback: Optional[Callable] = None) -> StateGraph:
        """
        🔥 根据团队配置动态构建 LangGraph 工作流
        
        支持的工作流类型：
        - sequential: 顺序执行
        - parallel: 并行执行
        - conditional: 条件分支执行
        """
        
        try:
            logger.info(f"🏗️(Builder) 开始构建工作流: {team_config.get('name', 'unknown')}")
            
            # 1. 动态加载智能体
            agents = await self.engine._load_agents_from_config(team_config)
            
            # 2. 创建LangGraph状态图
            graph = StateGraph(EnhancedTeamState)
            
            # 3. 添加智能体节点
            for agent_id, agent in agents.items():
                node_func = partial(self.engine._agent_node_wrapper, agent_id=agent_id, agent=agent)
                graph.add_node(agent_id, node_func)

            # 4. 添加特殊节点
            graph.add_node("workflow_coordinator", partial(self.engine._workflow_coordinator_node, stream_callback=stream_callback))
            graph.add_node("context_optimizer", partial(self.engine._context_optimizer_node, stream_callback=stream_callback))
            graph.add_node("result_merger", partial(self.engine._result_merger_node, stream_callback=stream_callback))
            graph.add_node("state_distributor", self.engine._state_distributor_node)
            graph.add_node("state_merger", self.engine._state_merger_node)
            graph.add_node("intelligent_router", self.engine._intelligent_router_node)
            graph.add_node("parallel_executor", partial(self.engine._parallel_executor_node, agents=agents, stream_callback=stream_callback))
            graph.add_node("task_list_generator", partial(self.engine._task_list_generator_node, stream_callback=stream_callback))
            graph.add_node("task_progress_tracker", partial(self.engine._task_progress_tracker_node, team_config=team_config, stream_callback=stream_callback))
            
            # 🔥 评估阶段已移除：智能体执行完成后直接结束工作流
            # graph.add_node("evaluator_agent", self.engine._evaluator_node_wrapper)
            # logger.info("✅(Builder) 全局评估节点 'evaluator_agent' 已添加。")
            
            # 5. 根据工作流类型构建图结构
            workflow_type = team_config.get('workflow_type', 'sequential')
            await self._build_workflow_structure(graph, agents, workflow_type, team_config)
            
            logger.info(f"✅(Builder) 工作流构建完成: {len(agents)} 个智能体, 类型: {workflow_type}")
            
            # 🔥 优化：生成工作流可视化图（线程安全）
            try:
                if hasattr(graph, 'visualize'):
                    # 确保matplotlib后端已正确设置（防御性编程）
                    import matplotlib
                    if matplotlib.get_backend() != 'Agg':
                        matplotlib.use('Agg')
                        logger.debug("🔧 强制设置matplotlib后端为Agg")
                    
                    import os
                    # 保存到langgraph_enhancement/visualizations目录
                    base_dir = os.path.dirname(os.path.dirname(__file__))  # langgraph_enhancement目录
                    viz_dir = os.path.join(base_dir, "visualizations")
                    os.makedirs(viz_dir, exist_ok=True)
                    visualization_path = os.path.join(viz_dir, f"workflow_{team_config.get('name', 'default')}_{workflow_type}.png")
                    graph.visualize(visualization_path)
                    logger.info(f"📊 工作流可视化图已生成: {visualization_path}")
            except ImportError as e:
                logger.warning(f"⚠️ 可视化依赖缺失（已跳过）: {e}")
            except Exception as e:
                logger.warning(f"⚠️ 可视化生成失败（已跳过）: {e}")
            
            return graph
            
        except Exception as e:
            logger.error(f"❌(Builder) 工作流构建失败: {str(e)}")
            raise

    async def _build_workflow_structure(self, graph: StateGraph, agents: Dict, 
                                      workflow_type: str, team_config: Dict):
        """构建工作流结构"""
        agent_ids = list(agents.keys())
        
        if workflow_type == "sequential":
            await self._build_sequential_workflow(graph, agent_ids)
        elif workflow_type == "parallel":
            await self._build_parallel_workflow(graph, agent_ids)
        elif workflow_type == "conditional":
            await self._build_conditional_workflow(graph, agent_ids, team_config)
        elif workflow_type == "planning_router":
            await self._build_planning_router_workflow(graph, agent_ids, team_config)
        else:
            logger.warning(f"未知的工作流类型: {workflow_type}，使用顺序模式")
            await self._build_sequential_workflow(graph, agent_ids)

    async def _build_sequential_workflow(self, graph: StateGraph, agent_ids: List[str]):
        """构建顺序工作流"""
        logger.info(f"🔄(Builder) 构建顺序工作流: {' -> '.join(agent_ids)}")
        graph.set_entry_point("workflow_coordinator")
        
        if agent_ids:
            graph.add_edge("workflow_coordinator", agent_ids[0])
            for i in range(len(agent_ids) - 1):
                graph.add_edge(agent_ids[i], agent_ids[i + 1])
            graph.add_edge(agent_ids[-1], "result_merger")
        else:
            graph.add_edge("workflow_coordinator", "result_merger")
        
        graph.add_edge("result_merger", END)

    async def _build_parallel_workflow(self, graph: StateGraph, agent_ids: List[str]):
        """构建并行工作流 - 重新设计的架构"""
        logger.info(f"⚡(Builder) 构建并行工作流: {len(agent_ids)} 个智能体并行执行")
        graph.set_entry_point("workflow_coordinator")
        
        # 🔥 新架构：使用专用的并行执行节点
        graph.add_edge("workflow_coordinator", "parallel_executor")
        graph.add_edge("parallel_executor", "result_merger")
        graph.add_edge("result_merger", END)

    async def _build_conditional_workflow(self, graph: StateGraph, agent_ids: List[str], 
                                        team_config: Dict):
        """构建由 AI 驱动的条件分支工作流"""
        logger.info(f"🔀(Builder) 构建 AI 驱动的条件工作流: {len(agent_ids)} 个智能体")
        
        def route_condition(state: EnhancedTeamState) -> str:
            decision = state.get("next_node", "result_merger")
            logger.debug(f"🔄 根据状态路由到: {decision}")
            return decision

        graph.set_entry_point("workflow_coordinator")
        graph.add_edge("workflow_coordinator", "intelligent_router")
        
        # 🔥 修复：确保评估智能体不会被加入常规路由映射
        # 🔥 简化：移除评估阶段，智能体完成后直接结束
        condition_mapping = {agent_id: agent_id for agent_id in agent_ids}
        condition_mapping["result_merger"] = "result_merger"
        # condition_mapping["__EVALUATE__"] = "evaluator_agent" # 已移除评估阶段
        
        graph.add_conditional_edges(
            "intelligent_router",
            route_condition,
            condition_mapping
        )

        for agent_id in agent_ids:
            graph.add_edge(agent_id, "intelligent_router")
            
        # 6. 评估节点已移除，无需连接评估节点边
        # graph.add_edge("evaluator_agent", "intelligent_router")

        # 7. 结果合并器 -> 结束
        graph.add_edge("result_merger", END)
        logger.info("✅(Builder) AI 驱动的条件工作流构建完成")

    async def _build_planning_router_workflow(self, graph: StateGraph, agent_ids: List[str], 
                                            team_config: Dict):
        """构建计划路由工作流 - 任务分解与循环执行"""
        logger.info(f"📋(Builder) 构建计划路由工作流: {len(agent_ids)} 个智能体支持任务分解执行")
        
        def route_condition(state: EnhancedTeamState) -> str:
            """路由条件函数：根据状态决定下一步"""
            decision = state.get("next_node", "result_merger")
            logger.debug(f"📋 计划路由决策: {decision}")
            return decision
            
        def progress_condition(state: EnhancedTeamState) -> str:
            """进度条件函数：检查是否还有未完成的任务"""
            has_pending_tasks = state.get("has_pending_tasks", False)
            if has_pending_tasks:
                logger.debug("📋 还有未完成任务，继续路由执行")
                return "continue_routing"
            else:
                logger.debug("📋 所有任务已完成，准备合并结果")
                return "finish_planning"

        # 1. 设置入口点
        graph.set_entry_point("workflow_coordinator")
        
        # 2. 工作流协调器 -> 任务列表生成器
        graph.add_edge("workflow_coordinator", "task_list_generator")
        
        # 3. 任务列表生成器 -> 智能路由器（开始任务处理循环）
        graph.add_edge("task_list_generator", "intelligent_router")
        
        # 4. 智能路由器的条件边：路由到各种智能体或结果合并器
        condition_mapping = {agent_id: agent_id for agent_id in agent_ids}
        condition_mapping["result_merger"] = "result_merger"
        
        graph.add_conditional_edges(
            "intelligent_router",
            route_condition,
            condition_mapping
        )
        
        # 5. 所有智能体执行完成后 -> 任务进度跟踪器
        for agent_id in agent_ids:
            graph.add_edge(agent_id, "task_progress_tracker")
        
        # 6. 任务进度跟踪器的条件边：继续路由或完成
        graph.add_conditional_edges(
            "task_progress_tracker",
            progress_condition,
            {
                "continue_routing": "intelligent_router",  # 继续处理下一个任务
                "finish_planning": "result_merger"        # 所有任务完成，合并结果
            }
        )
        
        # 7. 结果合并器 -> 结束
        graph.add_edge("result_merger", END)
        
        logger.info("✅(Builder) 计划路由工作流构建完成")


class DynamicWorkflowEngine:
    """🔥 动态工作流引擎 - LangGraph 多智能体协作核心"""
    
    def __init__(self):
        """🔥 优化：初始化工作流引擎，完整集成所有组件"""
        self.context_manager = get_context_manager()
        self.config_manager = get_config_manager()
        self.agent_factory = get_agent_factory()
        self.intelligent_router = get_intelligent_router(self.context_manager)  # 初始化智能路由器
        self.builder = WorkflowBuilder(self) # 初始化构建器
        
        # 🔥 新增：简化并行执行器
        self.simple_parallel_executor = SimpleParallelExecutor(self.config_manager, self.context_manager)
        
        # 注释：通过context_manager访问所有核心组件，保持架构清晰
        
        # 🔥 修复：使用 context_manager 提供的 MemorySaver，避免重复创建
        self.memory_saver = self.context_manager.get_langgraph_memory_saver()
        
        # 🔥 新增：验证checkpointer兼容性
        if hasattr(self.memory_saver, 'put') and hasattr(self.memory_saver, 'get'):
            logger.info("✅ MemorySaver兼容性验证通过")
        else:
            logger.warning("⚠️ MemorySaver可能不完全兼容LangGraph接口")
        
        # 工作流缓存
        self.compiled_workflows = {}
        
        # 执行统计
        self.execution_stats = {
            "workflows_executed": 0,
            "total_agents_invoked": 0,
            "average_execution_time": 0,
            "success_rate": 0,
            "context_compressions_skipped": 0,  # 🔥 新增：跳过的上下文压缩次数
            "performance_improvement": 0,       # 🔥 新增：性能提升估算
            "simple_parallel_executions": 0,   # 🔥 新增：简化并行执行次数
            "output_quality_score": 0          # 🔥 新增：输出质量评分
        }
        
        logger.info("🔥 动态工作流引擎初始化完成")
    
    def get_checkpoint_info(self, session_id: str) -> Dict[str, Any]:
        """🔥 新增：获取session的checkpoint信息"""
        try:
            pre_checkpoint = self.memory_saver.get(f"pre_execution_{session_id}")
            post_checkpoint = self.memory_saver.get(f"post_execution_{session_id}")
            
            return {
                "session_id": session_id,
                "has_pre_checkpoint": pre_checkpoint is not None,
                "has_post_checkpoint": post_checkpoint is not None,
                "pre_execution_time": pre_checkpoint.get("timestamp") if pre_checkpoint else None,
                "post_execution_time": post_checkpoint.get("timestamp") if post_checkpoint else None,
                "execution_time": post_checkpoint.get("execution_time") if post_checkpoint else None,
                "checkpoints_available": self.memory_saver.list_keys() if hasattr(self.memory_saver, 'list_keys') else []
            }
        except Exception as e:
            logger.error(f"获取checkpoint信息失败: {e}")
            return {"error": str(e)}
    
    def restore_from_checkpoint(self, session_id: str, checkpoint_type: str = "pre") -> Optional[Dict[str, Any]]:
        """🔥 新增：从checkpoint恢复状态"""
        try:
            checkpoint_key = f"{checkpoint_type}_execution_{session_id}"
            checkpoint_data = self.memory_saver.get(checkpoint_key)
            
            if checkpoint_data:
                logger.info(f"✅ 从checkpoint恢复状态: {checkpoint_key}")
                return checkpoint_data
            else:
                logger.warning(f"❌ Checkpoint不存在: {checkpoint_key}")
                return None
        except Exception as e:
            logger.error(f"从checkpoint恢复失败: {e}")
            return None
    
    async def build_workflow_from_team_config(self, team_config: Dict, stream_callback: Optional[Callable] = None) -> StateGraph:
        """
        🔥 根据团队配置动态构建 LangGraph 工作流
        委托给 WorkflowBuilder 执行
        """
        return await self.builder.build(team_config, stream_callback)
    
    async def _load_agents_from_config(self, team_config: Dict) -> Dict[str, Any]:
        """动态加载智能体"""
        
        agents = {}
        agent_ids = team_config.get('agents', [])
        
        # 🔥 新增：从团队配置中提取团队信息和工作流类型
        team_name = team_config.get('name', '')
        collaboration_rules = team_config.get('collaboration_rules', [])
        workflow_type = team_config.get('workflow_type', 'sequential')  # 🔥 提取工作流类型
        
        for agent_id in agent_ids:
            try:
                # 获取智能体配置
                agent_config = self.config_manager.get_agent_config(agent_id)
                if not agent_config:
                    logger.warning(f"智能体配置未找到: {agent_id}")
                    continue
                
                # 🔥 修改：创建智能体实例时传递团队信息（移除workflow_type参数）
                agent = await self.agent_factory.create_agent_from_config(
                    agent_id, 
                    agent_config,
                    team_name=team_name,
                    team_rules=collaboration_rules
                )
                agents[agent_id] = agent
                
                logger.info(f"✅ 智能体加载成功: {agent_id} (团队: {team_name}, 工作流: {workflow_type})")
                
            except Exception as e:
                logger.error(f"❌ 智能体加载失败 {agent_id}: {str(e)}")
                continue
        
        return agents
    
    async def _build_complete_context_for_agent(self, state: Dict, agent_id: str, agent) -> Dict:
        """
        🔥 新方法：为agent构建包含系统消息的完整上下文
        
        这个方法确保在压缩之前就有完整的上下文信息
        """
        try:
            # 🔥 修复：使用深拷贝确保状态完全隔离
            import copy
            complete_state = copy.deepcopy(state)
            messages = complete_state.get("messages", [])
            
            # 🔥 修复：对于LangGraph智能体，强制使用正确的系统提示词
            # 不再检查是否已有系统消息，直接重建确保正确性
            system_prompt = None
            
            # 从agent获取系统消息（复用agent的逻辑）
            if hasattr(agent, 'system_prompt') and agent.system_prompt:
                system_prompt = agent.system_prompt
                # logger.info(f"✅ 从agent获取系统提示词，长度: {len(system_prompt)}")
            else:
                # 后备方案：从配置构建系统消息
                try:
                    from ..config.config_manager import get_config_manager
                    config_manager = get_config_manager()
                    agent_config = config_manager.get_agent_config(agent_id)
                    
                    if agent_config:
                        from ..core.agent_transformer import get_agent_factory
                        agent_factory = get_agent_factory()
                        system_prompt = await agent_factory._build_system_prompt(agent_config)
                        # logger.info(f"✅ 从配置构建系统提示词，长度: {len(system_prompt)}")
                except Exception as e:
                    logger.warning(f"⚠️ 无法从配置构建系统消息: {e}")
            
            # 🔥 新增：注入团队协作规则
            team_config = complete_state.get("_team_config", {})
            collaboration_rules = team_config.get("collaboration_rules", [])
            
            if collaboration_rules and system_prompt and agent_id != "evaluator_agent": # 排除全局评估代理
                # 将协作规则添加到系统提示的末尾
                rules_section = "\n\n## Team Collaboration Rules\n"
                rules_section += "As a team member, you need to follow these collaboration rules:\n"
                for rule in collaboration_rules:
                    rules_section += f"- {rule}\n"
                
                system_prompt += rules_section
                # logger.info(f"✅ 为agent {agent_id} 注入了 {len(collaboration_rules)} 条协作规则")
            
            if system_prompt:
                # 🔥 修复：添加详细调试日志
                # logger.info(f"✅ 为agent {agent_id} 重建系统消息，长度: {len(system_prompt)}")
                logger.debug(f"🔍 系统提示词前100字符: {system_prompt[:100]}...")
                
                system_message = {
                    'role': 'system',
                    'content': system_prompt
                }
                
                # 🔥 修复：移除所有已有的系统消息，然后添加正确的系统消息
                non_system_messages = [msg for msg in messages if msg.get('role') != 'system']
                complete_state["messages"] = [system_message] + non_system_messages
                # logger.info(f"✅ 为agent {agent_id} 强制重建完整上下文（移除 {len(messages) - len(non_system_messages)} 条旧系统消息）")
                
                # 🔥 验证：再次检查构建后的系统消息
                new_messages = complete_state["messages"]
                if new_messages and new_messages[0].get('role') == 'system':
                    verify_content = new_messages[0].get('content', '')
                    logger.debug(f"🔍 验证构建后的系统消息长度: {len(verify_content)}")
                    if len(verify_content) < 100:
                        # logger.error(f"❌ 系统消息异常短！内容: {verify_content}")
                        logger.error(f"❌ 系统消息异常短！长度: {len(verify_content)}")
                    else:
                        logger.debug(f"✅ 系统消息构建正常，前100字符: {verify_content[:100]}...")
                        pass  # 系统消息构建正常，无需额外处理
            else:
                logger.warning(f"⚠️ 无法为agent {agent_id} 获取系统消息")
                
            return complete_state
            
        except Exception as e:
            logger.error(f"❌ 构建完整上下文失败 {agent_id}: {e}")
            return state

    async def _agent_node_wrapper(self, state: EnhancedTeamState, config: Dict, agent_id: str, agent) -> EnhancedTeamState:
        """
        🔥 智能体节点包装器 - 标准化的智能体执行逻辑
        增强版：支持上下文优化、错误处理、执行统计、状态完整性保护
        """
        import copy
        start_time = datetime.now()
        
        try:
            logger.info(f"🎯 开始执行智能体: {agent_id}")
            
            # 🔥 获取流式回调
            stream_callback = config.get("configurable", {}).get("stream_callback")
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__{agent.role_name} 开始分析处理..."), 
                    asyncio.get_running_loop()
                )
            
            # 🔥 智能上下文构建 - 在调用context_manager前构建
            enhanced_state = await self._build_complete_context_for_agent(state, agent_id, agent)
            
            # 🔥 新增：确保关键状态信息被完整传递
            critical_keys = ["_execution_path", "core_context", "working_memory", "_team_config", "execution_history"]
            for key in critical_keys:
                if key in state:
                    enhanced_state[key] = copy.deepcopy(state[key])  # 深拷贝确保独立性
            
            # 🔥 执行前压缩：所有智能体（包括最后一个）执行前都要进行上下文压缩
            next_agent_info = await self._get_next_agent_info(enhanced_state, agent_id)
            user_topic = self._extract_user_topic_from_state(enhanced_state)
            
            # 🔥 调试：记录主题提取结果
            if user_topic:
                logger.info(f"✅ 为智能体 {agent_id} 提取到用户主题: {user_topic}")
            else:
                logger.warning(f"⚠️ 智能体 {agent_id} 未能提取到用户主题")
            
            # 设置压缩引擎的上下文信息
            if self.context_manager.compression_engine:
                self.context_manager.compression_engine._current_user_topic = user_topic
                self.context_manager.compression_engine._current_next_agent_info = next_agent_info
                
            optimized_state = await self.context_manager.process_agent_context(agent_id, enhanced_state)
            logger.info(f"🎯 为 {agent_id} 执行前进行智能压缩，主题: {user_topic[:20] if user_topic else '无'}")
            
            # 🔥 关键修复：确保压缩后关键信息不丢失
            for key in critical_keys:
                if key in enhanced_state and (key not in optimized_state or not optimized_state[key]):
                    optimized_state[key] = copy.deepcopy(enhanced_state[key])
                    logger.debug(f"压缩后恢复关键信息: {key}")
            
            # 🔥 调用智能体处理
            result_state = await agent.process_with_optimized_context(
                optimized_state, 
                stream_callback=stream_callback
            )
            
            # 🔥 修复：检测是否为最后智能体，只在最后一个智能体执行完成后才跳过压缩
            is_last_agent = await self._is_last_agent_in_workflow(result_state, agent_id)
            
            if is_last_agent:
                # 最后智能体：执行完成后跳过压缩，因为没有下一个智能体需要处理
                logger.info(f"⚡ {agent_id} 是最后一个智能体，执行完成后跳过压缩")
                self.execution_stats["context_compressions_skipped"] = self.execution_stats.get("context_compressions_skipped", 0) + 1
            
            # 记录压缩统计（执行前的压缩）
            self.execution_stats["context_compressions_applied"] = self.execution_stats.get("context_compressions_applied", 0) + 1
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 🔥 改进：记录执行历史，支持并行和条件工作流
            execution_record = {
                "agent_id": agent_id,
                "agent_name": agent.role_name,
                "status": "success",
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "context_optimized": optimized_state.get("context_optimized", False)
            }
            
            # 🔥 新增：为并行执行添加特殊标识
            parallel_execution_id = state.get("parallel_execution_id")
            if parallel_execution_id:
                execution_record["parallel_execution_id"] = parallel_execution_id
                # 记录该智能体新增的消息
                original_messages = state.get("messages", [])
                new_messages = result_state.get("messages", [])[len(original_messages):]
                execution_record["new_messages"] = new_messages
            
            # 添加到执行历史
            if "execution_history" not in result_state:
                result_state["execution_history"] = []
            result_state["execution_history"].append(execution_record)
            
            # 🔥 改进：为条件工作流更新状态变化记录
            if state.get("_team_config", {}).get("workflow_type") == "conditional":
                result_state["last_executed_agent"] = agent_id
                result_state["state_updated_at"] = datetime.now().isoformat()
            
            # 🔥 性能统计
            self.execution_stats["total_agents_invoked"] += 1
            
            logger.info(f"✅ 智能体执行完成: {agent_id}, 耗时: {execution_time:.2f}秒")
            
            # 🔥 重要：确保关键状态信息在返回时不丢失
            for key in critical_keys:
                if key in state and key not in result_state:
                    result_state[key] = copy.deepcopy(state[key])
                    logger.debug(f"恢复关键状态信息: {key}")
            
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__{agent.role_name} 处理完成"), 
                    asyncio.get_running_loop()
                )
            
            return result_state
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"❌ 智能体执行失败: {agent_id}, 错误: {str(e)}")
            
            # 🔥 错误处理：记录失败信息
            error_record = {
                "agent_id": agent_id,
                "agent_name": getattr(agent, 'role_name', agent_id),
                "status": "failed",
                "error": str(e),
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
            # 为并行执行添加标识
            parallel_execution_id = state.get("parallel_execution_id")
            if parallel_execution_id:
                error_record["parallel_execution_id"] = parallel_execution_id
            
            # 添加错误到状态
            if "execution_history" not in state:
                state["execution_history"] = []
            state["execution_history"].append(error_record)
            
            if "errors" not in state:
                state["errors"] = []
            state["errors"].append({
                "agent_id": agent_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__error__{agent.role_name} 处理失败: {str(e)}"), 
                    asyncio.get_running_loop()
                )
            
            return state
    
    async def _is_last_agent_in_workflow(self, state: EnhancedTeamState, agent_id: str) -> bool:
        """
        🔥 新方法：判断当前智能体是否为工作流中的最后一个
        
        考虑不同工作流类型的执行逻辑：
        - sequential: 按顺序执行，最后一个为列表末尾
        - parallel: 并行执行，所有智能体都算作"最后"
        - conditional: 条件执行，需要动态判断
        """
        try:
            workflow_agents = state.get("_workflow_agents", [])
            team_config = state.get("_team_config", {})
            workflow_type = team_config.get('workflow_type', 'sequential')
            
            if not workflow_agents:
                logger.warning("⚠️ 工作流智能体列表为空，无法判断最后智能体")
                return False
            
            if workflow_type == "sequential":
                # 顺序执行：最后一个智能体为列表末尾
                is_last = agent_id == workflow_agents[-1]
                logger.debug(f"🔄 顺序工作流: {agent_id} 是否为最后智能体: {is_last}")
                return is_last
                
            elif workflow_type == "parallel":
                # 🔥 修正：并行工作流中，所有智能体都不是"最后"一个（因为同时执行）
                # 但在SimpleParallelExecutor内部，我们需要特殊处理压缩逻辑
                # 这里返回False，让每个智能体都进行压缩，确保输出质量
                logger.debug(f"⚡ 并行工作流: {agent_id} 并行执行，需要进行上下文压缩")
                return False
                
            elif workflow_type == "conditional":
                # 🔥 修正：条件工作流需要智能判断是否还有后续智能体
                execution_history = state.get("execution_history", [])
                executed_agents = {record.get("agent_id") for record in execution_history if record.get("status") == "success"}
                
                # 利用执行路径判断
                execution_path = state.get("_execution_path", [])
                if execution_path:
                    # 如果有执行路径，检查当前智能体后是否还有其他智能体节点
                    try:
                        current_idx = execution_path.index(agent_id)
                        # 查看后续节点是否包含其他智能体
                        remaining_nodes = execution_path[current_idx + 1:]
                        has_agent_after = any(node in workflow_agents for node in remaining_nodes)
                        if not has_agent_after:
                            logger.debug(f"🔀 条件工作流: {agent_id} 后续没有智能体节点，视为最后智能体")
                            return True
                    except (ValueError, IndexError):
                        pass
                
                # 通过路由条件判断是否可能有后续智能体
                routing_conditions = team_config.get('routing_conditions', {})
                if routing_conditions:
                    # 🔥 智能路由改造：调用新的智能路由节点来预测
                    # 这是一个简化的预测，实际决策由路由节点完成
                    next_step = self.intelligent_router._fallback_route_decision(
                        [agent_id for agent_id in workflow_agents if agent_id not in executed_agents]
                    )
                    
                    # 如果下一步是结果合并器或END，说明是最后一个
                    if next_step in ["result_merger", END, None]:
                        logger.debug(f"🔀 条件工作流(预测): {agent_id} 下一步是 {next_step}，视为最后智能体")
                        return True
                    else:
                        logger.debug(f"🔀 条件工作流(预测): {agent_id} 后续还有智能体 {next_step}，不是最后智能体")
                        return False
                
                # 默认情况：如果无法确定，保守地认为不是最后一个
                logger.debug(f"🔀 条件工作流: {agent_id} 无法确定是否为最后智能体，默认不跳过压缩")
                return False
            
            elif workflow_type == "planning_router":
                # 🔥 新增：计划路由工作流的最后智能体判断逻辑
                # 基于任务队列完成情况判断：如果当前任务是最后一个任务，则当前智能体是最后智能体
                task_queue = state.get("task_queue", [])
                current_task_index = state.get("current_task_index", 0)
                total_tasks = state.get("total_tasks", 0)
                
                if not task_queue or total_tasks == 0:
                    # 没有任务队列，保守判断
                    logger.debug(f"📋 计划路由工作流: {agent_id} 没有任务队列，默认不跳过压缩")
                    return False
                
                # 检查是否是最后一个任务
                is_last_task = (current_task_index >= len(task_queue) - 1) or (current_task_index >= total_tasks - 1)
                
                if is_last_task:
                    logger.debug(f"📋 计划路由工作流: {agent_id} 正在处理最后一个任务 ({current_task_index + 1}/{total_tasks})，视为最后智能体")
                    return True
                else:
                    logger.debug(f"📋 计划路由工作流: {agent_id} 还有后续任务 ({current_task_index + 1}/{total_tasks})，不是最后智能体")
                    return False
            
            else:
                logger.warning(f"⚠️ 未知工作流类型: {workflow_type}，默认不跳过压缩")
                return False
                
        except Exception as e:
            logger.error(f"❌ 判断最后智能体失败: {e}")
            return False
    
    async def _workflow_coordinator_node(self, state: EnhancedTeamState, config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """工作流协调器节点"""
        
        logger.info("🎯 工作流协调器执行")
        if stream_callback:
            # 使用 run_coroutine_threadsafe 确保线程安全
            asyncio.run_coroutine_threadsafe(stream_callback(f"__status__工作流已启动，正在分析任务..."), asyncio.get_running_loop())
        
        # 初始化工作流状态
        if "workflow_status" not in state:
            state["workflow_status"] = "running"
            state["workflow_id"] = str(uuid.uuid4())
            state["started_at"] = datetime.now().isoformat()
        
        # 分析当前上下文
        context_analysis = await self.context_manager.analyze_context_health(state)
        state["context_health"] = context_analysis
        
        return state
    
    async def _context_optimizer_node(self, state: EnhancedTeamState, config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """上下文优化器节点"""
        
        logger.info("🔥 上下文优化器执行")
        if stream_callback:
            asyncio.run_coroutine_threadsafe(stream_callback(f"__status__正在检查并优化上下文..."), asyncio.get_running_loop())
        
        # 🔥 优化：检查是否所有智能体已完成，如果是则跳过优化
        workflow_agents = state.get("_workflow_agents", [])
        execution_history = state.get("execution_history", [])
        completed_agents = {record.get("agent_id") for record in execution_history if record.get("status") == "success"}
        
        if workflow_agents and completed_agents and set(workflow_agents).issubset(completed_agents):
            logger.info("⚡ 所有智能体已完成，跳过上下文优化以提升性能")
            # 🔥 统计性能优化
            self.execution_stats["context_compressions_skipped"] += 1
            return state
        
        # 从配置获取阈值
        context_config = self.config_manager.get_context_config()
        threshold = context_config.get('optimizer_node_threshold', 20000)
        
        # 检查是否需要优化
        context_size = len(str(state))
        if context_size > threshold:
            logger.info(f"上下文过大 ({context_size} 字符，超过阈值 {threshold})，执行优化")
            
            # 执行上下文优化
            optimized_state = await self.context_manager.optimize_context_for_agent(
                "system", state
            )
            state.update(optimized_state)
            
            state["context_optimized"] = True
            state["optimization_timestamp"] = datetime.now().isoformat()
        
        return state
    
    async def _result_merger_node(self, state: EnhancedTeamState, config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """结果合并器节点"""
        
        logger.info("🔗 结果合并器执行")
        if stream_callback:
            asyncio.run_coroutine_threadsafe(stream_callback(f"__status__所有智能体执行完毕，正在合并最终结果..."), asyncio.get_running_loop())
        
        # 🔥 新增：检查是否为planning_router工作流，需要特殊处理
        team_config = state.get("_team_config", {})
        workflow_type = team_config.get("workflow_type", "conditional")
        
        if workflow_type == "planning_router":
            # 计划路由工作流：整合所有任务的执行结果
            await self._merge_planning_workflow_results(state, stream_callback)
        
        # 收集所有智能体的输出
        agent_outputs = []
        execution_history = state.get("execution_history", [])
        
        for record in execution_history:
            if record.get("status") == "success":
                agent_outputs.append({
                    "agent_id": record["agent_id"],
                    "timestamp": record["timestamp"]
                })
        
        # 生成工作流摘要
        state["workflow_summary"] = {
            "total_agents": len(agent_outputs),
            "execution_time": self._calculate_execution_time(state),
            "success_rate": len(agent_outputs) / max(len(execution_history), 1),
            "errors_count": len(state.get("errors", [])),
            "completed_at": datetime.now().isoformat()
        }
        
        state["workflow_status"] = "completed"
        return state
    
    async def _merge_planning_workflow_results(self, state: EnhancedTeamState, stream_callback: Optional[Callable] = None):
        """
        🔥 新增：合并计划路由工作流的任务执行结果
        """
        try:
            # 获取当前团队记忆作为上下文，而不是原始用户请求
            from ..context.memory_manager import get_memory_manager
            memory_manager = get_memory_manager()
            
            # 尝试从状态中获取team_id
            team_id = state.get("team_id") or state.get("current_team_id")
            team_context = ""
            
            logger.info(f"🔍 开始获取团队记忆，team_id: {team_id}")
            
            if team_id:
                # 🔥 修复：使用固定的team_work_details key获取团队记忆
                logger.debug(f"📋 尝试获取团队记忆: team_work_details")
                team_context = await memory_manager.get_team_memory(team_id, "team_work_details", "")
                if team_context:
                    logger.info(f"✅ 获取到团队工作详情: {len(team_context)} 字符")
                else:
                    logger.warning(f"⚠️ 未找到团队记忆内容 (key: team_work_details)")
            else:
                logger.warning(f"⚠️ 未找到team_id，无法获取团队记忆")
            
            # 如果团队记忆为空，回退到原始用户请求
            if not team_context:
                original_request = state.get("_original_user_request", "")
                team_context = original_request
                logger.info(f"🔄 团队记忆为空，回退到原始用户请求: {len(original_request)} 字符")
            
            # 判断最终使用的上下文来源
            context_source = "团队记忆(team_work_details)" if team_id and team_context and team_context != state.get('_original_user_request', '') else "原始请求"
            logger.info(f"📝 最终使用的上下文来源: {context_source}")
            logger.info(f"📝 最终上下文内容预览: {team_context[:100]}..." if team_context else "📝 上下文为空")
            
            completed_tasks = state.get("completed_tasks", [])
            execution_history = state.get("execution_history", [])
            
            if not team_context:
                logger.warning("未找到团队上下文或原始用户请求，无法进行结果整合")
                return
            
            if not completed_tasks:
                logger.warning("没有已完成的任务，无法进行结果整合")
                return
            
            # 收集所有任务的执行结果
            task_results = []
            for record in execution_history:
                if record.get("status") == "success":
                    # 从消息历史中提取该智能体的回答
                    agent_response = ""
                    messages = record.get("agent_messages", [])
                    for msg in reversed(messages):
                        if msg.get("role") == "assistant":
                            agent_response = msg.get("content", "")
                            break
                    
                    # 从completed_tasks中查找对应的任务描述
                    task_description = ""
                    agent_id = record["agent_id"]
                    for task in completed_tasks:
                        if task.get("completed_by") == agent_id or task.get("assigned_agent") == agent_id:
                            task_description = task.get("description", "")
                            break
                    
                    task_results.append({
                        "agent_id": agent_id,
                        "task_description": task_description or f"任务由 {agent_id} 完成",
                        "result": agent_response,
                        "timestamp": record["timestamp"]
                    })
            
            # 使用AI整合所有任务结果，生成针对团队上下文的完整回答
            if task_results:
                integration_prompt = f"""
【当前任务】
基于以下团队工作详情和各个智能体的执行结果，生成一个全面、详细、专业的综合报告。

**团队工作详情 (team_work_details):**
{team_context}

**智能体任务执行结果:**
"""
                for i, result in enumerate(task_results, 1):
                    integration_prompt += f"\n任务{i}: {result['task_description']}\n"
                    integration_prompt += f"执行者: {result['agent_id']}\n"
                    integration_prompt += f"结果: {result['result']}\n"
                    integration_prompt += "---\n"
                
                integration_prompt += f"""

请按照系统指令的专业标准，生成最终的综合报告：
"""
                
                ai_messages = [
                    {
                        'role': 'system', 
                        'content': '''【角色定义】
你是工作流的最后一个核心节点 - 结果整合专家。你的职责是将多个智能体的分工执行结果整合成一个高质量的综合报告。这是整个工作流的收官之作，你的输出质量直接决定了用户最终获得的价值和体验。你必须精细思考，全面整合，展现专业水准。

【当前任务】
基于以下团队记忆上下文和各个智能体的执行结果，生成一个全面、详细、专业的综合报告。

【核心整合指令】
作为工作流的最终输出节点，你必须遵循以下专业标准：

**1. 全面性原则 - 信息不遗漏**
- 综合所有智能体的执行结果，确保没有遗漏任何有价值的信息
- 如果出现重复信息，优先采用更详细、更完整的版本
- 特别关注并完整保留：图表URL链接、Mermaid流程图代码、文件保存路径、数据分析结果、技术规范等关键信息

**2. 专业性标准 - 学术报告质量**
- 以专业学术报告标准组织内容：摘要、详细分析、结论、建议
- 保持逻辑清晰、结构完整、表述准确
- 使用专业术语，体现专业水准和权威性

**3. 详细性要求 - 越详细越好**
- 不必局限于字数，宁可详尽也不要遗漏
- 保留所有技术细节、数据图表、代码片段、文件路径
- 对复杂概念提供充分的解释和背景信息

**4. 实用性保证 - 直接可用**
- 确保输出结果可以直接使用，包含完整的操作指导
- 保留所有可执行的代码、链接、路径信息
- 提供具体的后续行动建议

**5. 上下文整合 - 团队导向**
- 深度结合团队工作详情(team_work_details)，体现团队的长期目标和项目背景
- 确保回答符合团队的工作方向和专业领域
- 将分散的任务结果统一到团队的整体战略下

【特别关注的信息类型】
请特别注意保留和整合以下关键信息：
- 🔗 所有图表、图像的URL链接
- 📊 Mermaid、PlantUML等流程图的完整代码
- 📁 文件保存路径和目录结构
- 📋 数据分析结果和统计信息
- ⚙️ 技术配置和参数设置
- 🔧 代码片段和脚本
- 📚 参考资料和文档链接

关键能力：
1. 信息整合：能够识别、提取、组织来自不同源头的信息
2. 质量控制：确保最终输出达到专业学术报告标准
3. 细节保留：特别善于保留技术细节、代码、链接、路径等关键信息
4. 逻辑重构：将分散的信息重新组织成逻辑清晰的完整报告
5. 价值提升：通过专业整合让信息价值最大化

工作原则：宁可详尽也不遗漏，以用户获得最大价值为目标。'''
                    },
                    {'role': 'user', 'content': integration_prompt}
                ]
                
                # 调用AI进行结果整合
                from ..utils.aimanager_bridge import get_ai_manager_bridge
                ai_manager_bridge = get_ai_manager_bridge()
                
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"\n【专业结果整合中】\n🔄 正在深度整合 {len(task_results)} 个智能体的执行结果\n📊 分析团队记忆上下文，提取关键信息\n📋 生成专业级综合报告...\n"), 
                        asyncio.get_running_loop()
                    )
                
                # 🔥 创建持久化流式回调，解决工作流结束时流式传输中断问题
                persistent_stream_callback = create_workflow_independent_callback(
                    stream_callback, 
                    {
                        "workflow_id": state.get("workflow_id", "unknown"),
                        "team_id": team_id,
                        "node": "result_merger",
                        "operation": "ai_integration"
                    }
                )
                
                logger.info(f"🔄 开始AI结果整合，使用{'持久化' if persistent_stream_callback else '标准'}流式回调")
                
                response = await ai_manager_bridge.ai_manager.chat_completion(
                    messages=ai_messages,
                    model='gemini-2.5-flash-preview-05-20',
                    handle_tools=False,
                    role_id="langgraph_agent",
                    stream_callback=persistent_stream_callback  # 🔥 使用持久化回调
                )
                
                integrated_result = response.get('content', '').strip()
                
                # 🔥 日志记录：监控持久化回调效果
                logger.info(f"📊 AI结果整合完成，生成内容长度: {len(integrated_result)} 字符")
                if persistent_stream_callback and hasattr(persistent_stream_callback, '_is_persistent'):
                    logger.info("✅ 使用了持久化流式回调，应该避免了流式传输中断问题")
                
                # 创建最终的回答消息
                final_message = {
                    "role": "assistant",
                    "content": integrated_result,
                    "agent_id": "result_merger",
                    "timestamp": datetime.now().isoformat(),
                    "metadata": {
                        "team_context": team_context,
                        "team_id": team_id,
                        "tasks_completed": len(completed_tasks),
                        "integration_source": "planning_workflow"
                    }
                }
                
                # 🔥 关键：替换消息历史，只保留团队上下文和最终整合结果
                state["messages"] = [
                    {
                        "role": "user",
                        "content": team_context,
                        "timestamp": state.get("started_at", datetime.now().isoformat()),
                        "metadata": {"source": "team_memory", "team_id": team_id}
                    },
                    final_message
                ]
                
                logger.info(f"✅ 专业结果整合完成，生成 {len(integrated_result)} 字符的综合报告，整合了 {len(task_results)} 个智能体结果")
                
                # 🔥 使用持久化回调发送最终状态通知
                if persistent_stream_callback:
                    try:
                        await persistent_stream_callback(f"__status__✅ 专业综合报告生成完成，工作流圆满结束")
                        logger.debug("✅ 持久化回调成功发送工作流完成通知")
                    except Exception as e:
                        logger.warning(f"⚠️ 持久化回调发送完成通知失败: {e}")
                
                # 备用通知（如果持久化回调不可用）
                elif stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__✅ 专业综合报告生成完成，工作流圆满结束"), 
                        asyncio.get_running_loop()
                    )
            
        except Exception as e:
            logger.error(f"❌ 专业结果整合失败: {e}")
            # 如果整合失败，至少恢复团队上下文到消息历史
            if state.get("_original_user_request"):
                state["messages"] = [
                    {
                        "role": "user",
                        "content": state["_original_user_request"],
                        "timestamp": state.get("started_at", datetime.now().isoformat())
                    }
                ]
    
    async def _state_distributor_node(self, state: EnhancedTeamState, config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 新增：状态分发器节点 - 为并行执行准备状态
        """
        logger.info("📤 状态分发器：为并行执行准备状态")
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__准备并行执行，分发任务状态..."), 
                asyncio.get_running_loop()
            )
        
        # 为每个智能体创建独立的执行标识
        state["parallel_execution_id"] = str(uuid.uuid4())
        state["parallel_start_time"] = datetime.now().isoformat()
        
        # 初始化并行执行跟踪
        if "parallel_results" not in state:
            state["parallel_results"] = {}
        
        return state
    
    async def _state_merger_node(self, state: EnhancedTeamState, config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 新增：状态合并器节点 - 合并并行执行的结果
        """
        logger.info("📥 状态合并器：合并并行执行结果")
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__合并并行执行结果..."), 
                asyncio.get_running_loop()
            )
        
        # 🔥 智能状态合并逻辑
        merged_state = await self._merge_parallel_execution_results(state)
        
        return merged_state
    
    async def _parallel_executor_node(self, state: EnhancedTeamState, config: Dict, 
                                    agents: Dict[str, Any], stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 新版：简化可靠的并行执行器节点
        使用SimpleParallelExecutor确保输出完整性和系统可靠性
        """
        logger.info(f"⚡ 简化并行执行器：开始并行执行 {len(agents)} 个智能体")
        
        # 🔥 获取团队配置
        team_config = state.get("_team_config", {})
        output_mode = team_config.get("output_mode", "complete")  # complete/stream
        
        # 🔥 使用简化并行执行器（唯一执行方式）
        logger.info("🎯 使用简化并行执行器（完整输出模式）")
        
        # 更新执行统计
        self.execution_stats["simple_parallel_executions"] += 1
        
        # 使用简化并行执行器
        result_state = await self.simple_parallel_executor.execute_with_complete_output(
            state, config, agents, stream_callback
        )
        
        # 评估输出质量
        self._evaluate_output_quality(result_state)
        
        return result_state
    
    def _evaluate_output_quality(self, result_state: EnhancedTeamState):
        """
        🔥 新增：评估输出质量
        """
        try:
            summary = result_state.get("simple_parallel_execution_summary", {})
            success_rate = summary.get("success_rate", 0)
            
            # 计算质量评分 (0-100)
            quality_score = success_rate * 100
            
            # 更新统计
            current_score = self.execution_stats.get("output_quality_score", 0)
            execution_count = self.execution_stats.get("simple_parallel_executions", 1)
            
            # 计算平均质量评分
            new_average = (current_score * (execution_count - 1) + quality_score) / execution_count
            self.execution_stats["output_quality_score"] = new_average
            
            logger.debug(f"📊 输出质量评分: {quality_score:.1f}%, 平均: {new_average:.1f}%")
            
        except Exception as e:
            logger.error(f"❌ 输出质量评估失败: {e}")
    
    async def _parallel_executor_with_sequential_stream(self, state: EnhancedTeamState, config: Dict,
                                                      agents: Dict[str, Any], stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 优化：并行执行 + 顺序流式展示，带状态跟踪和重复检测
        后台并行处理，前台按顺序展示每个智能体的完整思考过程
        """
        logger.info(f"🎯 顺序流模式：后台并行执行 {len(agents)} 个智能体，前台顺序展示")
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__启动 {len(agents)} 个智能体并行处理..."), 
                asyncio.get_running_loop()
            )
        
        try:
            # 🔥 1. 初始化流输出状态跟踪
            stream_output_tracker = {
                "displayed_contents": set(),  # 跟踪已展示的内容，避免重复
                "agent_progress": {},  # 跟踪每个智能体的进度
                "original_message_count": len(state.get("messages", []))  # 记录原始消息数量
            }
            
            # 🔥 2. 启动所有智能体的后台任务（无流回调）
            agent_tasks = {}
            agent_states = {}
            
            for agent_id, agent in agents.items():
                # 创建独立状态，并记录原始消息数量
                agent_state = await self._create_independent_agent_state(state, agent_id, agent)
                agent_state["_original_message_count"] = stream_output_tracker["original_message_count"]
                agent_states[agent_id] = agent_state
                
                # 初始化进度跟踪
                stream_output_tracker["agent_progress"][agent_id] = {
                    "status": "started",
                    "start_time": datetime.now(),
                    "has_output": False
                }
                
                # 启动后台任务（静默执行）
                task = asyncio.create_task(
                    self._execute_single_agent_parallel(
                        agent_state, config, agent_id, agent, 
                        stream_callback=None  # 关键：无流回调
                    )
                )
                agent_tasks[agent_id] = task
            
            logger.info("🚀 所有智能体后台任务已启动")
            
            # 🔥 3. 获取智能体展示顺序
            agent_order = self._get_agent_display_order(agents, state)
            
            # 🔥 4. 按顺序等待并展示每个智能体的结果
            completed_results = {}
            
            for agent_id in agent_order:
                agent = agents[agent_id]
                
                # 🔥 更新进度状态
                stream_output_tracker["agent_progress"][agent_id]["status"] = "displaying"
                
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__{agent.role_name} 正在思考..."), 
                        asyncio.get_running_loop()
                    )
                
                # 等待该智能体完成
                logger.info(f"⏳ 等待智能体 {agent_id} 完成...")
                result = await agent_tasks[agent_id]
                completed_results[agent_id] = result
                
                # 🔥 检查是否为异常结果
                if isinstance(result, Exception):
                    logger.warning(f"⚠️ 智能体 {agent_id} 执行异常: {result}")
                    if stream_callback:
                        asyncio.run_coroutine_threadsafe(
                            stream_callback(f"__error__{agent.role_name} 处理失败"), 
                            asyncio.get_running_loop()
                        )
                    continue
                
                # 🔥 传递流输出跟踪器，优化思考过程模拟
                if stream_callback:
                    # 标记该智能体的流输出状态
                    result["_stream_output_tracker"] = stream_output_tracker
                    
                    # 模拟该智能体的流式思考过程（带重复检测）
                    await self._simulate_agent_thinking_process(result, agent, stream_callback)
                    
                    # 更新进度状态
                    stream_output_tracker["agent_progress"][agent_id]["has_output"] = True
                
                # 🔥 更新完成状态
                stream_output_tracker["agent_progress"][agent_id]["status"] = "completed"
                stream_output_tracker["agent_progress"][agent_id]["end_time"] = datetime.now()
                
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__{agent.role_name} 分析完成"), 
                        asyncio.get_running_loop()
                    )
            
            # 🔥 5. 合并所有结果
            parallel_results = [completed_results[agent_id] for agent_id in agents.keys()]
            merged_state = await self._merge_parallel_results(state, parallel_results, list(agents.keys()))
            
            # 🔥 添加流输出统计信息
            merged_state["_stream_output_stats"] = {
                "total_agents": len(agents),
                "successful_agents": len([r for r in parallel_results if not isinstance(r, Exception)]),
                "total_display_time": sum([
                    (tracker.get("end_time", datetime.now()) - tracker["start_time"]).total_seconds()
                    for tracker in stream_output_tracker["agent_progress"].values()
                    if "end_time" in tracker
                ]),
                "unique_contents_displayed": len(stream_output_tracker["displayed_contents"])
            }
            
            logger.info(f"✅ 顺序流并行执行完成，统计: {merged_state['_stream_output_stats']}")
            
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__所有智能体分析完毕，正在整合结果..."), 
                    asyncio.get_running_loop()
                )
            
            return merged_state
            
        except Exception as e:
            logger.error(f"❌ 顺序流并行执行失败: {e}")
            return await self._handle_parallel_execution_error(state, e, stream_callback)
    
    async def _parallel_executor_with_parallel_stream(self, state: EnhancedTeamState, config: Dict,
                                                    agents: Dict[str, Any], stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 保留：传统并行流式输出（带流管理器）
        """
        logger.info(f"🌊 并行流模式：并行执行和流式输出 {len(agents)} 个智能体")
        
        # 创建流式输出管理器
        stream_manager = SimpleParallelExecutor(stream_callback, list(agents.keys()))
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__正在并行执行 {len(agents)} 个智能体..."), 
                asyncio.get_running_loop()
            )
        
        try:
            # 为每个智能体创建独立的状态副本
            agent_states = {}
            for agent_id, agent in agents.items():
                agent_state = await self._create_independent_agent_state(state, agent_id, agent)
                agent_states[agent_id] = agent_state
            
            # 并行执行
            parallel_tasks = []
            for agent_id, agent in agents.items():
                agent_stream_callback = stream_manager.create_agent_callback(agent_id, agent.role_name)
                task = self._execute_single_agent_parallel(
                    agent_states[agent_id], config, agent_id, agent, agent_stream_callback
                )
                parallel_tasks.append(task)
            
            # 启动流协调器
            stream_coordinator_task = asyncio.create_task(stream_manager.coordinate_streams())
            
            # 执行并等待结果
            parallel_results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
            
            # 停止流协调器
            await stream_manager.finalize()
            stream_coordinator_task.cancel()
            
            # 合并结果
            merged_state = await self._merge_parallel_results(state, parallel_results, list(agents.keys()))
            
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__并行执行完成"), 
                    asyncio.get_running_loop()
                )
            
            return merged_state
            
        except Exception as e:
            logger.error(f"❌ 并行流执行失败: {e}")
            return await self._handle_parallel_execution_error(state, e, stream_callback)
    
    async def _parallel_executor_silent_mode(self, state: EnhancedTeamState, config: Dict,
                                           agents: Dict[str, Any], stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 新增：静默并行执行模式
        纯后台并行处理，只显示开始和结束状态
        """
        logger.info(f"🤫 静默模式：纯后台并行执行 {len(agents)} 个智能体")
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__后台并行处理 {len(agents)} 个智能体，请稍候..."), 
                asyncio.get_running_loop()
            )
        
        try:
            # 创建独立状态
            agent_states = {}
            for agent_id, agent in agents.items():
                agent_state = await self._create_independent_agent_state(state, agent_id, agent)
                agent_states[agent_id] = agent_state
            
            # 纯后台并行执行（无任何流输出）
            parallel_tasks = []
            for agent_id, agent in agents.items():
                task = self._execute_single_agent_parallel(
                    agent_states[agent_id], config, agent_id, agent, 
                    stream_callback=None  # 完全静默
                )
                parallel_tasks.append(task)
            
            # 等待所有任务完成
            parallel_results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
            
            # 合并结果
            merged_state = await self._merge_parallel_results(state, parallel_results, list(agents.keys()))
            
            if stream_callback:
                successful_count = len([r for r in parallel_results if not isinstance(r, Exception)])
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"__status__后台处理完成，{successful_count}/{len(agents)} 个智能体成功执行"), 
                    asyncio.get_running_loop()
                )
            
            return merged_state
            
        except Exception as e:
            logger.error(f"❌ 静默并行执行失败: {e}")
            return await self._handle_parallel_execution_error(state, e, stream_callback)
    
    def _get_agent_display_order(self, agents: Dict[str, Any], state: EnhancedTeamState) -> List[str]:
        """
        🔥 新增：获取智能体的展示顺序
        支持多种排序策略
        """
        team_config = state.get("_team_config", {})
        
        # 1. 优先使用配置中指定的顺序
        if "agent_display_order" in team_config:
            specified_order = team_config["agent_display_order"]
            # 确保所有智能体都包含在内
            remaining_agents = [aid for aid in agents.keys() if aid not in specified_order]
            return specified_order + remaining_agents
        
        # 2. 使用原始配置中的智能体顺序
        original_order = state.get("_workflow_agents", [])
        if original_order:
            return [aid for aid in original_order if aid in agents]
        
        # 3. 默认按字典序
        return sorted(agents.keys())
    
    async def _simulate_agent_thinking_process(self, result_state: Dict, agent, stream_callback: Callable):
        """
        🔥 优化：模拟智能体的思考过程，带重复检测机制和流输出跟踪
        从已完成的结果中提取关键信息，模拟流式输出
        """
        try:
            # 🔥 获取流输出跟踪器
            stream_tracker = result_state.get("_stream_output_tracker", {})
            displayed_contents = stream_tracker.get("displayed_contents", set())
            
            # 🔥 检查该智能体是否已经产生了实际的流式输出
            agent_id = getattr(agent, 'agent_id', str(agent))
            parallel_execution_info = result_state.get("parallel_execution_info", {})
            
            # 如果智能体已经产生了流式输出，跳过模拟过程
            if parallel_execution_info.get("has_stream_output", False):
                logger.debug(f"⚡ 智能体 {agent_id} 已有流式输出，跳过模拟过程")
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"分析完成"), 
                    asyncio.get_running_loop()
                )
                return
            
            # 获取该智能体产生的新消息
            messages = result_state.get("messages", [])
            original_message_count = result_state.get("_original_message_count", 0)
            
            # 🔥 只提取该智能体新增的消息，避免重复
            new_messages = []
            if len(messages) > original_message_count:
                new_messages = messages[original_message_count:]
            
            # 筛选出该智能体的助手消息
            agent_messages = []
            for msg in new_messages:
                if msg.get("role") == "assistant":
                    content = msg.get("content", "")
                    # 🔥 多重去重检测：
                    # 1. 避免与已展示内容重复
                    # 2. 避免与当前智能体内容重复
                    content_hash = hash(content.strip().lower())
                    if (content and 
                        content_hash not in displayed_contents and 
                        not self._is_duplicate_content(content, agent_messages)):
                        
                        agent_messages.append(content)
                        displayed_contents.add(content_hash)  # 标记为已展示
                        
                    if len(agent_messages) >= 1:  # 进一步减少输出量
                        break
            
            # 🔥 如果没有新的有效消息，创建简短的状态更新
            if not agent_messages:
                logger.debug(f"🔍 智能体 {agent_id} 没有新的有效消息，跳过输出")
                return
            
            # 🔥 优化：最小化模拟输出，只展示最核心的结果
            for content in agent_messages:
                # 🔥 智能内容处理：只输出核心内容，跳过重复段落
                processed_content = self._process_content_for_display(content, agent.role_name)
                if processed_content:
                    # 🔥 再次检查是否重复（双重保险）
                    processed_hash = hash(processed_content.strip().lower())
                    if processed_hash not in displayed_contents:
                        asyncio.run_coroutine_threadsafe(
                            stream_callback(processed_content), 
                            asyncio.get_running_loop()
                        )
                        displayed_contents.add(processed_hash)
                        # 极短延迟，更快的响应
                        await asyncio.sleep(0.02)
                    
        except Exception as e:
            logger.error(f"❌ 模拟智能体思考过程失败: {e}")
            # 降级为简单输出
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"{getattr(agent, 'role_name', '智能体')}处理完成"), 
                asyncio.get_running_loop()
            )
    
    def _is_duplicate_content(self, content: str, existing_contents: List[str]) -> bool:
        """
        🔥 新增：检测内容是否重复
        """
        # 简单的重复检测：检查内容相似度
        content_lower = content.lower().strip()
        for existing in existing_contents:
            existing_lower = existing.lower().strip()
            # 如果内容高度相似（90%以上），认为是重复
            if len(content_lower) > 0 and len(existing_lower) > 0:
                similarity = len(set(content_lower.split()) & set(existing_lower.split())) / max(len(set(content_lower.split())), len(set(existing_lower.split())))
                if similarity > 0.9:
                    return True
        return False
    
    def _process_content_for_display(self, content: str, agent_name: str) -> str:
        """
        🔥 新增：处理内容用于展示，移除重复和无关信息
        """
        # 移除常见的重复模式
        import re
        
        # 移除过长的重复内容
        if len(content) > 500:
            # 截取前200字符作为摘要
            content = content[:200] + "..."
        
        # 移除重复的角色描述模式
        patterns_to_remove = [
            rf"我是{agent_name}[，。]",
            rf"作为{agent_name}[，。]",
            rf"从{agent_name}的角度[，。]",
            r"经过深入分析[，。]",
            r"根据以上分析[，。]"
        ]
        
        for pattern in patterns_to_remove:
            content = re.sub(pattern, "", content)
        
        # 清理多余的空格和标点
        content = re.sub(r'\s+', ' ', content).strip()
        
        # 如果内容太短或为空，返回None以跳过输出
        if len(content.strip()) < 10:
            return None
            
        return content
    
    def _is_low_quality_message(self, content: str) -> bool:
        """
        🔥 新增：判断消息是否为低质量内容，应该被过滤
        """
        if not content or len(content.strip()) < 10:
            return True
        
        # 低质量内容模式
        low_quality_patterns = [
            r"^正在分析",
            r"^正在思考",
            r"^分析完成",
            r"^处理完成",
            r"^任务完成",
            r"^分析中",
            r"^请稍候",
            r"^正在处理",
            r"^开始分析",
            r"分析结果如下",
            r"我的分析如下",
            r"经过分析.*，",
            r"根据.*分析",
            r"^【.*】$",  # 只有标识符的内容
        ]
        
        content_lower = content.lower().strip()
        for pattern in low_quality_patterns:
            if re.match(pattern, content_lower, re.IGNORECASE):
                return True
        
        # 检查是否只包含标点符号和空格
        if re.match(r'^[。，！？；：、\s]+$', content):
            return True
        
        # 检查重复字符过多的情况
        if len(set(content)) < len(content) * 0.3:  # 重复字符超过70%
            return True
        
        return False
    
    def _split_content_into_segments(self, content: str, max_segment_length: int = 100) -> List[str]:
        """
        🔥 优化：将长内容分割成合适的段落，模拟流式输出
        """
        if len(content) <= max_segment_length:
            return [content]
        
        segments = []
        sentences = content.split('。')
        
        current_segment = ""
        for sentence in sentences:
            if len(current_segment + sentence + '。') <= max_segment_length:
                current_segment += sentence + '。'
            else:
                if current_segment:
                    segments.append(current_segment.strip())
                current_segment = sentence + '。'
        
        if current_segment:
            segments.append(current_segment.strip())
        
        return segments if segments else [content]
    
    async def _handle_parallel_execution_error(self, state: EnhancedTeamState, error: Exception, 
                                             stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 新增：统一的并行执行错误处理
        """
        logger.error(f"❌ 并行执行错误: {error}")
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__error__并行执行失败: {str(error)}"), 
                asyncio.get_running_loop()
            )
        
        # 记录错误到状态
        state["errors"] = state.get("errors", [])
        state["errors"].append({
            "node": "parallel_executor",
            "error": str(error),
            "timestamp": datetime.now().isoformat()
        })
        
        return state
    
    async def _merge_parallel_execution_results(self, state: EnhancedTeamState) -> EnhancedTeamState:
        """
        🔥 新增：智能合并并行执行结果
        """
        try:
            # 获取执行历史中的并行结果
            execution_history = state.get("execution_history", [])
            parallel_execution_id = state.get("parallel_execution_id")
            
            # 按时间戳排序并行执行的结果
            parallel_results = []
            for record in execution_history:
                if (record.get("status") == "success" and 
                    record.get("parallel_execution_id") == parallel_execution_id):
                    parallel_results.append(record)
            
            # 按时间戳排序
            parallel_results.sort(key=lambda x: x.get("timestamp", ""))
            
            # 合并消息：按智能体顺序整理
            merged_messages = state.get("messages", [])
            
            # 收集所有并行智能体的新消息
            agent_messages = {}
            for result in parallel_results:
                agent_id = result.get("agent_id")
                if agent_id and "new_messages" in result:
                    agent_messages[agent_id] = result["new_messages"]
            
            # 按智能体配置顺序添加消息，确保逻辑顺序
            workflow_agents = state.get("_workflow_agents", [])
            for agent_id in workflow_agents:
                if agent_id in agent_messages:
                    merged_messages.extend(agent_messages[agent_id])
            
            state["messages"] = merged_messages
            
            # 合并其他状态信息
            state["parallel_execution_completed"] = True
            state["parallel_completion_time"] = datetime.now().isoformat()
            
            # 统计并行执行结果
            state["parallel_summary"] = {
                "agents_count": len(parallel_results),
                "execution_time": self._calculate_parallel_execution_time(state),
                "success_rate": len(parallel_results) / max(len(workflow_agents), 1)
            }
            
            logger.info(f"✅ 并行状态合并完成：{len(parallel_results)} 个智能体结果已合并")
            return state
            
        except Exception as e:
            logger.error(f"❌ 并行状态合并失败: {e}")
            return state
    
    def _calculate_parallel_execution_time(self, state: EnhancedTeamState) -> float:
        """
        🔥 新增：计算并行执行时间
        """
        start_time_str = state.get("parallel_start_time")
        if start_time_str:
            try:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                return (datetime.now() - start_time).total_seconds()
            except:
                pass
        return 0
    
    async def _create_independent_agent_state(self, state: EnhancedTeamState, agent_id: str, agent) -> EnhancedTeamState:
        """
        🔥 新增：为智能体创建独立的状态副本
        确保并行执行时每个智能体有自己的上下文
        """
        try:
            import copy
            
            # 深度复制状态，确保完全独立
            independent_state = copy.deepcopy(state)
            
            # 🔥 为并行执行添加特殊标识
            independent_state["parallel_agent_id"] = agent_id
            independent_state["parallel_execution_timestamp"] = datetime.now().isoformat()
            
            # 🔥 构建该智能体的完整上下文
            enhanced_state = await self._build_complete_context_for_agent(independent_state, agent_id, agent)
            
            logger.debug(f"✅ 为智能体 {agent_id} 创建独立状态副本")
            return enhanced_state
            
        except Exception as e:
            logger.error(f"❌ 创建独立状态副本失败 {agent_id}: {e}")
            # 如果失败，返回原始状态的副本
            import copy
            return copy.deepcopy(state)
    
    async def _execute_single_agent_parallel(self, agent_state: EnhancedTeamState, config: Dict, 
                                           agent_id: str, agent, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """
        🔥 新增：在并行环境中执行单个智能体
        """
        try:
            logger.info(f"⚡ 并行执行智能体: {agent_id}")
            
            # 记录开始时间
            start_time = datetime.now()
            
            # 🔥 调用智能体的处理方法，这里不进行上下文压缩因为已经在创建独立状态时处理了
            result_state = await agent.process_with_optimized_context(
                agent_state, 
                stream_callback=stream_callback
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 🔥 添加并行执行的元数据
            result_state["parallel_execution_info"] = {
                "agent_id": agent_id,
                "agent_name": getattr(agent, 'role_name', agent_id),
                "execution_time": execution_time,
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "status": "completed"
            }
            
            logger.info(f"✅ 智能体 {agent_id} 并行执行完成，耗时: {execution_time:.2f}秒")
            return result_state
            
        except Exception as e:
            logger.error(f"❌ 智能体 {agent_id} 并行执行失败: {e}")
            
            # 返回带有错误信息的状态
            agent_state["parallel_execution_info"] = {
                "agent_id": agent_id,
                "agent_name": getattr(agent, 'role_name', agent_id),
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            if "errors" not in agent_state:
                agent_state["errors"] = []
            agent_state["errors"].append({
                "agent_id": agent_id,
                "error": str(e),
                "context": "parallel_execution"
            })
            
            return agent_state
    
    async def _merge_parallel_results(self, original_state: EnhancedTeamState, 
                                    parallel_results: List, agent_ids: List[str]) -> EnhancedTeamState:
        """
        🔥 新增：合并并行执行结果的新实现
        """
        try:
            logger.info(f"🔗 开始合并 {len(parallel_results)} 个并行执行结果")
            
            # 从原始状态开始
            merged_state = original_state.copy()
            
            # 初始化合并后的消息列表
            merged_messages = merged_state.get("messages", [])
            
            # 🔥 合并成功执行的结果
            successful_results = []
            failed_results = []
            
            for i, result in enumerate(parallel_results):
                agent_id = agent_ids[i] if i < len(agent_ids) else f"agent_{i}"
                
                if isinstance(result, Exception):
                    # 处理异常结果
                    logger.error(f"❌ 智能体 {agent_id} 执行异常: {result}")
                    failed_results.append({
                        "agent_id": agent_id,
                        "error": str(result),
                        "timestamp": datetime.now().isoformat()
                    })
                elif isinstance(result, dict):
                    # 处理成功结果
                    execution_info = result.get("parallel_execution_info", {})
                    if execution_info.get("status") == "completed":
                        successful_results.append(result)
                        
                        # 🔥 优化：智能消息合并，避免重复内容
                        agent_messages = result.get("messages", [])
                        
                        # 找出该智能体新增的消息（排除系统消息和原有消息）
                        original_message_count = len(original_state.get("messages", []))
                        if len(agent_messages) > original_message_count:
                            new_messages = agent_messages[original_message_count:]
                            
                            # 🔥 过滤和去重新消息
                            filtered_messages = []
                            existing_contents = {msg.get("content", "").strip().lower() for msg in merged_messages}
                            
                            for msg in new_messages:
                                if msg.get("role") == "assistant":
                                    content = msg.get("content", "").strip()
                                    content_lower = content.lower()
                                    
                                    # 跳过重复内容和过短内容
                                    if (len(content) >= 10 and 
                                        content_lower not in existing_contents and
                                        not self._is_low_quality_message(content)):
                                        
                                        filtered_messages.append(msg)
                                        existing_contents.add(content_lower)
                                else:
                                    # 保留非助手消息（如用户消息、系统消息）
                                    filtered_messages.append(msg)
                            
                            if filtered_messages:
                                merged_messages.extend(filtered_messages)
                                logger.debug(f"✅ 从智能体 {agent_id} 合并了 {len(filtered_messages)}/{len(new_messages)} 条新消息")
                    else:
                        failed_results.append({
                            "agent_id": agent_id,
                            "error": execution_info.get("error", "Unknown error"),
                            "timestamp": datetime.now().isoformat()
                        })
            
            # 更新合并后的状态
            merged_state["messages"] = merged_messages
            
            # 🔥 添加并行执行摘要
            merged_state["parallel_execution_summary"] = {
                "total_agents": len(parallel_results),
                "successful_agents": len(successful_results),
                "failed_agents": len(failed_results),
                "success_rate": len(successful_results) / len(parallel_results) if parallel_results else 0,
                "completed_at": datetime.now().isoformat()
            }
            
            # 🔥 合并执行历史
            execution_history = merged_state.get("execution_history", [])
            for result in successful_results:
                execution_info = result.get("parallel_execution_info", {})
                if execution_info:
                    execution_history.append({
                        "agent_id": execution_info.get("agent_id"),
                        "agent_name": execution_info.get("agent_name"),
                        "status": "success",
                        "execution_time": execution_info.get("execution_time", 0),
                        "timestamp": execution_info.get("end_time"),
                        "execution_type": "parallel"
                    })
            
            # 添加失败记录
            for failed in failed_results:
                execution_history.append({
                    "agent_id": failed["agent_id"],
                    "status": "failed",
                    "error": failed["error"],
                    "timestamp": failed["timestamp"],
                    "execution_type": "parallel"
                })
            
            merged_state["execution_history"] = execution_history
            
            # 合并错误信息
            all_errors = merged_state.get("errors", [])
            for failed in failed_results:
                all_errors.append(failed)
            merged_state["errors"] = all_errors
            
            logger.info(f"✅ 并行结果合并完成: {len(successful_results)} 成功, {len(failed_results)} 失败")
            return merged_state
            
        except Exception as e:
            logger.error(f"❌ 合并并行结果失败: {e}")
            # 如果合并失败，返回原始状态并记录错误
            original_state["errors"] = original_state.get("errors", [])
            original_state["errors"].append({
                "node": "parallel_merge",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return original_state
    
    async def _get_next_agent_info(self, state: EnhancedTeamState, current_agent_id: str) -> Dict:
        """
        🔥 新增：获取下一个智能体的信息
        
        Args:
            state: 当前状态
            current_agent_id: 当前智能体ID
            
        Returns:
            下一个智能体的信息字典
        """
        try:
            workflow_agents = state.get("_workflow_agents", [])
            team_config = state.get("_team_config", {})
            workflow_type = team_config.get('workflow_type', 'sequential')
            
            next_agent_info = {}
            
            if workflow_type == "sequential" and workflow_agents:
                # 顺序执行：找到当前智能体的下一个
                try:
                    current_index = workflow_agents.index(current_agent_id)
                    if current_index < len(workflow_agents) - 1:
                        next_agent_id = workflow_agents[current_index + 1]
                        # 获取下一个智能体的配置信息
                        next_agent_config = self.config_manager.get_agent_config(next_agent_id)
                        if next_agent_config:
                            next_agent_info = {
                                'agent_id': next_agent_id,
                                'role_name': next_agent_config.get('role_name', ''),
                                'expertise': next_agent_config.get('expertise', []),
                                'description': next_agent_config.get('description', '')
                            }
                except ValueError:
                    pass  # 当前智能体不在列表中
                    
            elif workflow_type == "conditional":
                # 条件执行：基于路由条件预测下一个可能的智能体
                routing_conditions = team_config.get('routing_conditions', {})
                if routing_conditions:
                    # 🔥 智能路由改造：使用智能路由器的兜底策略进行简单预测
                    available_agents = [
                        agent_id for agent_id in workflow_agents if agent_id != current_agent_id
                    ]
                    next_agent_id = self.intelligent_router._fallback_route_decision(available_agents)

                    if next_agent_id and next_agent_id != "result_merger":
                        next_agent_config = self.config_manager.get_agent_config(next_agent_id)
                        if next_agent_config:
                            next_agent_info = {
                                'agent_id': next_agent_id,
                                'role_name': next_agent_config.get('role_name', ''),
                                        'expertise': next_agent_config.get('expertise', []),
                                        'description': next_agent_config.get('description', ''),
                                        'conditional': True
                                    }
            
            # 并行执行时没有明确的"下一个"，可以选择一个代表性的智能体
            elif workflow_type == "parallel":
                # 选择第一个未执行的智能体作为代表
                execution_history = state.get("execution_history", [])
                completed_agents = {record.get("agent_id") for record in execution_history}
                
                for agent_id in workflow_agents:
                    if agent_id != current_agent_id and agent_id not in completed_agents:
                        agent_config = self.config_manager.get_agent_config(agent_id)
                        if agent_config:
                            next_agent_info = {
                                'agent_id': agent_id,
                                'role_name': agent_config.get('role_name', ''),
                                'expertise': agent_config.get('expertise', []),
                                'description': agent_config.get('description', ''),
                                'parallel': True
                            }
                        break
            
            elif workflow_type == "planning_router":
                # 🔥 新增：计划路由工作流的下一个智能体预测
                # 基于任务队列中的下一个任务来确定下一个智能体
                task_queue = state.get("task_queue", [])
                current_task_index = state.get("current_task_index", 0)
                
                if task_queue and current_task_index < len(task_queue) - 1:
                    # 还有后续任务，获取下一个任务的分配智能体
                    next_task = task_queue[current_task_index + 1]
                    next_agent_id = next_task.get("assigned_agent")
                    
                    if next_agent_id and next_agent_id in workflow_agents:
                        next_agent_config = self.config_manager.get_agent_config(next_agent_id)
                        if next_agent_config:
                            next_agent_info = {
                                'agent_id': next_agent_id,
                                'role_name': next_agent_config.get('role_name', ''),
                                'expertise': next_agent_config.get('expertise', []),
                                'description': next_agent_config.get('description', ''),
                                'planning_router': True,
                                'task_description': next_task.get("description", ""),
                                'task_priority': next_task.get("priority", "medium")
                            }
                elif task_queue and current_task_index >= len(task_queue) - 1:
                    # 这是最后一个任务，下一步是结果合并
                    logger.debug(f"📋 计划路由工作流: {current_agent_id} 处理最后任务，下一步为结果合并")
                else:
                    # 没有任务队列信息，无法确定下一个智能体
                    logger.debug(f"📋 计划路由工作流: {current_agent_id} 无任务队列信息，无法预测下一个智能体")
            
            logger.debug(f"🔍 {current_agent_id} 的下一个智能体: {next_agent_info.get('role_name', '未确定')}")
            return next_agent_info
            
        except Exception as e:
            logger.error(f"❌ 获取下一个智能体信息失败: {e}")
            return {}
    
    def _extract_user_topic_from_state(self, state: EnhancedTeamState) -> str:
        """
        🔥 优化版：从状态中提取真正的用户主题，多数据源策略
        
        Args:
            state: 当前状态
            
        Returns:
            用户主题字符串
        """
        try:
            # 🔥 新增策略0：planning_router工作流优先使用当前任务
            team_config = state.get("_team_config", {})
            workflow_type = team_config.get("workflow_type", "conditional")
            
            if workflow_type == "planning_router":
                # 对于计划路由工作流，优先使用当前任务作为主题
                current_task = state.get("next_task")
                if current_task and isinstance(current_task, dict):
                    task_description = current_task.get("description", "")
                    if task_description.strip():
                        logger.debug(f"🎯 planning_router工作流，使用当前任务: {task_description}")
                        return task_description
                
                # 如果没有下一个任务，尝试从消息中获取当前任务
                messages = state.get("messages", [])
                for msg in reversed(messages):
                    if (msg.get("role") == "user" and 
                        msg.get("agent_id") == "task_list_generator" and
                        msg.get("task_info", {}).get("is_single_task")):
                        # 这是一个单个任务消息
                        task_content = msg.get("content", "")
                        if task_content.startswith("请完成以下任务："):
                            task_description = task_content.replace("请完成以下任务：", "").strip()
                            logger.debug(f"🎯 planning_router工作流，从消息提取任务: {task_description}")
                            return task_description
            
            # 🔥 策略1：从工作流初始输入提取（最可靠）
            topic = self._extract_topic_from_workflow_input(state)
            if topic:
                logger.debug(f"🎯 从工作流输入提取主题: {topic}")
                return topic
            
            # 🔥 策略2：从core_context中提取
            topic = self._extract_topic_from_core_context(state)
            if topic:
                logger.debug(f"🎯 从核心上下文提取主题: {topic}")
                return topic
            
            # 🔥 策略3：从用户消息提取（原有逻辑，放宽过滤条件）
            messages = state.get("messages", [])
            original_user_messages = []
            for msg in messages:
                if msg.get('role') == 'user':
                    content = msg.get('content', '')
                    # 🔥 优化：放宽过滤条件，避免误过滤
                    if content.strip() and len(content.strip()) > 10:  # 简单的内容有效性检查
                        original_user_messages.append(msg)
            
            if original_user_messages:
                # 使用最早的用户消息作为主题来源
                first_user_content = original_user_messages[0].get('content', '')
                
                # 🔥 优化：更精准的主题提取逻辑
                import re
                
                # 1. 优先查找具体的主题描述模式
                topic_patterns = [
                    r'关于(.+?)的(?:分析|研究|开发|设计|报告|方案|项目)',  # "关于XX的分析"
                    r'(?:分析|研究|开发|设计|制作|构建)(.+?)(?:项目|系统|方案|产品|报告)',  # "开发XX系统"
                    r'我想(?:了解|学习|开发|设计|分析|制作)(.+)',  # "我想了解XX"
                    r'帮我(?:分析|研究|开发|设计|制作|构建)(.+)',  # "帮我分析XX"
                    r'需要(?:分析|研究|开发|设计|制作|构建)(.+)',  # "需要开发XX"
                    r'制作(?:一个|一份|一套)(.+?)(?:系统|报告|方案|产品|应用)',  # "制作一个XX系统"
                    r'(.+?)(?:怎么|如何)(?:做|开发|实现|分析)',  # "XX怎么做"
                    r'(.+?)(?:的|相关)(?:分析|研究|开发|方案)',  # "XX的分析"
                ]
                
                for pattern in topic_patterns:
                    match = re.search(pattern, first_user_content)
                    if match:
                        topic = match.group(1).strip()
                        # 清理主题内容
                        topic = self._clean_extracted_topic(topic)
                        if 3 <= len(topic) <= 50:  # 放宽长度限制
                            logger.debug(f"🎯 通过模式提取主题: {topic}")
                            return topic
                
                # 2. 使用关键词提取方法
                topic = self._extract_topic_by_keywords(first_user_content)
                if topic:
                    logger.debug(f"🎯 通过关键词提取主题: {topic}")
                    return topic
                
                # 3. 兜底：使用内容概要
                topic = self._extract_topic_summary(first_user_content)
                if topic:
                    logger.debug(f"🎯 使用概要作为主题: {topic}")
                    return topic
            
            # 🔥 策略4：从初始输入数据提取（兜底）
            topic = self._extract_topic_from_initial_input(state)
            if topic:
                logger.debug(f"🎯 从初始输入提取主题: {topic}")
                return topic
            
            logger.debug("⚠️ 所有主题提取策略都失败，返回空主题")
            return ""
            
        except Exception as e:
            logger.error(f"❌ 提取用户主题失败: {e}")
            return ""
    
    def _extract_topic_from_workflow_input(self, state: EnhancedTeamState) -> str:
        """
        🔥 新增策略1：从工作流初始输入提取主题
        """
        try:
            # 从工作流初始状态中获取用户输入
            messages = state.get("messages", [])
            
            # 查找第一个用户消息（通常是最原始的需求）
            for msg in messages:
                if msg.get('role') == 'user':
                    content = msg.get('content', '').strip()
                    if content and len(content) > 5:
                        # 提取主题
                        topic = self._extract_core_topic_from_content(content)
                        if topic:
                            return topic
            
            return ""
        except Exception:
            return ""
    
    def _extract_topic_from_core_context(self, state: EnhancedTeamState) -> str:
        """
        🔥 新增策略2：从核心上下文提取主题
        """
        try:
            core_context = state.get("core_context", {})
            
            # 检查是否有明确的主题字段
            if isinstance(core_context, dict):
                topic = core_context.get("user_topic", "") or core_context.get("topic", "")
                if topic and isinstance(topic, str) and len(topic.strip()) > 3:
                    return topic.strip()
                
                # 从会话ID或其他字段推断主题
                conversation_id = core_context.get("conversation_id", "")
                if conversation_id and isinstance(conversation_id, str):
                    # 有些会话ID可能包含主题信息
                    topic_hint = self._extract_topic_from_id(conversation_id)
                    if topic_hint:
                        return topic_hint
            
            return ""
        except Exception:
            return ""
    
    def _extract_core_topic_from_content(self, content: str) -> str:
        """
        🔥 新增：从内容中提取核心主题（高精度）
        """
        import re
        
        # 移除常见的开场白
        content = re.sub(r'^(请|帮我|我想|我需要|能否|可以|希望|想要)', '', content).strip()
        
        # 高优先级主题模式（更精确）
        high_priority_patterns = [
            r'(.+?)(?:分析|研究|开发|设计|制作)(?:报告|方案|系统|项目)',  # "XX分析报告"
            r'关于(.+?)的(?:问题|分析|研究|开发)',  # "关于XX的问题"
            r'(.+?)(?:怎么|如何)(?:做|实现|开发)',  # "XX怎么做"
            r'制作(.+?)(?:系统|应用|网站|平台)',  # "制作XX系统"
            r'设计(.+?)(?:方案|架构|系统)',  # "设计XX方案"
            r'开发(.+?)(?:应用|系统|软件|网站)',  # "开发XX应用"
        ]
        
        for pattern in high_priority_patterns:
            match = re.search(pattern, content)
            if match:
                topic = match.group(1).strip()
                topic = self._clean_extracted_topic(topic)
                if 3 <= len(topic) <= 30:
                    return topic
        
        # 关键词提取
        return self._extract_topic_by_keywords(content)
    
    def _extract_topic_from_id(self, identifier: str) -> str:
        """
        🔥 新增：从ID或标识符中提取主题提示
        """
        # 这个方法用于从会话ID或其他标识符中提取可能的主题信息
        # 通常会话ID可能包含主题相关的信息
        import re
        
        # 查找可能的主题关键词
        topic_keywords = [
            'travel', 'health', 'education', 'business', 'tech', 'finance',
            '旅游', '健康', '教育', '商业', '技术', '金融', '购物', '娱乐'
        ]
        
        identifier_lower = identifier.lower()
        for keyword in topic_keywords:
            if keyword in identifier_lower:
                return keyword
        
        return ""
    
    def _is_ai_enhanced_instruction(self, content: str) -> bool:
        """
        🔥 新增：判断是否为AI增强的指令性内容
        """
        ai_instruction_indicators = [
            '遵循系统指令',
            '完成以上需求',
            '根据上述要求',
            '按照指定格式',
            '严格按照',
            '请按照以下步骤',
            '执行以下任务',
            '根据以下指令',
            '完成下列任务',
            '请遵循',
            'follow the instructions',
            'complete the above',
            'according to the requirements'
        ]
        
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in ai_instruction_indicators)
    
    def _extract_topic_from_initial_input(self, state: EnhancedTeamState) -> str:
        """
        🔥 新增：从初始输入中提取主题
        """
        try:
            # 查找原始输入数据
            initial_input = state.get("initial_input", {})
            if isinstance(initial_input, dict):
                query = initial_input.get("query", "")
                if query and not self._is_ai_enhanced_instruction(query):
                    return self._extract_topic_summary(query)
            
            return ""
        except Exception:
            return ""
    
    def _clean_extracted_topic(self, topic: str) -> str:
        """
        🔥 新增：清理提取的主题内容
        """
        import re
        
        # 移除多余的标点和空格
        topic = re.sub(r'[，。！？；：、\s]+', ' ', topic).strip()
        
        # 移除常见的无关词汇
        stop_words = ['一个', '一份', '一些', '这个', '那个', '什么', '如何', '怎么', '的话', '等等']
        for word in stop_words:
            topic = topic.replace(word, '').strip()
        
        return topic
    
    def _extract_topic_by_keywords(self, content: str) -> str:
        """
        🔥 新增：通过关键词提取主题
        """
        import re
        
        # 定义领域关键词
        domain_keywords = {
            'AI/机器学习': ['人工智能', 'AI', '机器学习', '深度学习', '神经网络', '算法'],
            '医疗健康': ['医疗', '健康', '诊断', '治疗', '病症', '药物'],
            '电商/购物': ['电商', '购物', '商城', '订单', '支付', '商品'],
            '教育培训': ['教育', '培训', '学习', '课程', '考试', '知识'],
            '金融投资': ['金融', '投资', '银行', '股票', '基金', '理财'],
            '游戏娱乐': ['游戏', '娱乐', '音乐', '视频', '电影', '小说'],
            '企业管理': ['管理', '企业', '公司', '团队', '项目', '业务'],
            '技术开发': ['开发', '编程', '系统', '软件', '网站', '应用'],
        }
        
        # 找到匹配的领域
        content_lower = content.lower()
        for domain, keywords in domain_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                # 尝试提取更具体的主题
                for keyword in keywords:
                    if keyword in content_lower:
                        # 查找关键词周围的上下文
                        pattern = f'(.{{0,10}}{keyword}.{{0,20}})'
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            topic_candidate = match.group(1).strip()
                            topic_candidate = self._clean_extracted_topic(topic_candidate)
                            if 5 <= len(topic_candidate) <= 30:
                                return f"{domain} - {topic_candidate}"
                return domain
        
        return ""
    
    def _extract_topic_summary(self, content: str) -> str:
        """
        🔥 新增：提取内容概要作为主题
        """
        # 过滤掉常见的开头词
        prefixes_to_remove = [
            '请', '帮我', '我想', '我需要', '能否', '可以', '希望', '想要',
            '请问', '请帮助', '请协助', '麻烦', '劳烦'
        ]
        
        summary = content.strip()
        for prefix in prefixes_to_remove:
            if summary.startswith(prefix):
                summary = summary[len(prefix):].strip()
        
        # 取前25个字符作为概要，确保在标点符号处截断
        if len(summary) > 25:
            truncated = summary[:25]
            # 在最后一个句号、问号或感叹号处截断
            last_punct = max(
                truncated.rfind('。'),
                truncated.rfind('？'),
                truncated.rfind('！'),
                truncated.rfind('，')
            )
            if last_punct > 5:  # 确保有足够的内容
                summary = truncated[:last_punct]
            else:
                summary = truncated
        
        # 最终清理
        summary = self._clean_extracted_topic(summary)
        
        # 确保主题质量
        if len(summary) < 3 or '系统' in summary or '指令' in summary:
            return ""
        
        return summary
    
    async def _evaluator_node_wrapper(self, state: EnhancedTeamState, config: Dict) -> EnhancedTeamState:
        """
        🔥 直接评估节点包装器
        使用智能路由器的直接评估方法，不创建智能体实例
        """
        logger.info("🎯 直接评估节点启动...")
        
        # 🔥 调试：打印评估节点接收到的状态
        messages = state.get("messages", [])
        logger.info(f"🔍 评估节点接收到的状态包含 {len(messages)} 条消息")
        for i, msg in enumerate(messages, 1):
            role = msg.get("role", "unknown")
            agent_id = msg.get("agent_id", "无")
            content_len = len(msg.get("content", ""))
            content_preview = msg.get("content", "")[:100] + "..." if content_len > 100 else msg.get("content", "")
            logger.info(f"🔍 状态消息 {i}: [{role}] agent={agent_id} 长度={content_len} 内容={content_preview}")
        
        try:
            # 🔥 直接调用评估方法
            evaluation_result = await self.intelligent_router._direct_evaluate_content(state)
            
            # 构建评估消息
            eval_message = {
                "role": "assistant",
                "content": evaluation_result,
                "timestamp": datetime.now().isoformat(),
                "agent_id": "evaluator_agent",
                "agent_name": "Quality Evaluator",
                "status": "completed"
            }
            
            # 更新状态
            updated_state = state.copy()
            updated_state["messages"] = state.get("messages", []) + [eval_message]
            updated_state["last_executed_agent"] = "evaluator_agent"
            
            # 更新执行历史
            if "execution_history" not in updated_state:
                updated_state["execution_history"] = []
            
            updated_state["execution_history"].append({
                "agent_id": "evaluator_agent",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "evaluation_method": "direct_evaluation"
            })
            
            logger.info(f"✅ 直接评估完成: {evaluation_result[:50]}...")
            return updated_state
            
        except Exception as e:
            logger.error(f"❌ 直接评估失败: {e}")
            state["errors"] = state.get("errors", [])
            state["errors"].append({
                "agent_id": "evaluator_agent",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
        return state
    
    async def _task_list_generator_node(self, state: EnhancedTeamState, config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """任务列表生成器节点 - 使用结构化工具生成任务清单"""
        logger.info("📋 任务列表生成器启动")
        
        if stream_callback:
            asyncio.run_coroutine_threadsafe(
                stream_callback(f"__status__正在分析任务并生成执行计划..."), 
                asyncio.get_running_loop()
            )
        
        try:
            # 提取用户原始请求
            messages = state.get("messages", [])
            user_request = ""
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_request = msg.get("content", "")
                    break
            
            if not user_request:
                logger.warning("未找到用户请求，无法生成任务列表")
                state["generated_task_list"] = []
                state["task_queue"] = []
                state["current_task_index"] = 0
                return state
            
            # 🔥 新增：保存原始用户请求，供后续使用
            state["_original_user_request"] = user_request
            
            # 获取团队配置和智能体信息
            team_config = state.get("_team_config", {})
            available_agents = state.get("_workflow_agents", [])
            
            # 构建智能体描述信息
            agent_descriptions = []
            for agent_id in available_agents:
                agent_config = self.config_manager.get_agent_config(agent_id)
                if agent_config:
                    desc = f"- {agent_id}: {agent_config.get('description', '专业智能体')}"
                    agent_descriptions.append(desc)
            
            agents_info = "\n".join(agent_descriptions)
            
            # 使用AI生成结构化任务列表
            task_generation_prompt = f"""
请将以下复杂请求分解为具体的、可执行的任务清单。每个任务应该：
1. 明确具体，可以独立完成
2. 分配给最合适的专业智能体
3. 有清晰的优先级和执行顺序

用户请求：
{user_request}

可用的专业智能体：
{agents_info}

请严格按照以下JSON格式输出任务列表：
{{
    "tasks": [
        {{
            "id": 1,
            "description": "具体的任务描述",
            "assigned_agent": "最合适的智能体ID",
            "priority": "high|medium|low",
            "dependencies": [],
            "estimated_time": "预估完成时间"
        }}
    ]
}}

要求：
- 语言使用中文
- 确保id保持顺序排列
- 任务描述要具体明确，避免模糊表述
- 智能体分配要基于其专业能力
- 优先级要合理，重要基础任务优先级高
- 只返回JSON格式，不要其他说明文字
"""
            
            ai_messages = [
                {
                    'role': 'system', 
                    'content': 'You are an expert project manager. Analyze user requests and break them down into structured, actionable task lists. Return only valid JSON format.'
                },
                {'role': 'user', 'content': task_generation_prompt}
            ]
            
            # 🔥 修复：直接使用 AI 管理器，按照其他智能体的标准，支持流式处理
            from ..utils.aimanager_bridge import get_ai_manager_bridge
            ai_manager_bridge = get_ai_manager_bridge()
            
            # 显示任务分析开始
            if stream_callback:
                asyncio.run_coroutine_threadsafe(
                    stream_callback(f"\n【任务分析器】开始分析复杂请求，生成任务清单...\n"), 
                    asyncio.get_running_loop()
                )
            
            # 🔥 创建持久化流式回调，确保任务分析过程的流式输出不被中断
            persistent_stream_callback = create_workflow_independent_callback(
                stream_callback, 
                {
                    "workflow_id": state.get("workflow_id", "unknown"),
                    "team_id": state.get("team_id", "unknown"),
                    "node": "workflow_coordinator",
                    "operation": "task_generation"
                }
            )
            
            # 调用AI生成任务列表 - 使用持久化流式回调
            response = await ai_manager_bridge.ai_manager.chat_completion(
                messages=ai_messages,
                model='gemini-2.5-flash-preview-05-20',
                handle_tools=False,
                role_id="langgraph_agent",
                stream_callback=persistent_stream_callback  # 🔥 使用持久化回调
            )
            
            ai_response = response.get('content', '').strip()
            logger.info(f"📋 AI任务生成响应: {ai_response[:200]}...")
            
            # 解析JSON响应
            try:
                import json
                # 清理响应，移除可能的markdown代码块
                if '```json' in ai_response:
                    ai_response = ai_response.split('```json')[1].split('```')[0].strip()
                elif '```' in ai_response:
                    ai_response = ai_response.split('```')[1].strip()
                
                task_data = json.loads(ai_response)
                task_list = task_data.get('tasks', [])
                
                # 验证和过滤任务列表
                valid_tasks = []
                for task in task_list:
                    if (isinstance(task, dict) and 
                        'description' in task and 
                        'assigned_agent' in task and
                        task.get('assigned_agent') in available_agents):
                        valid_tasks.append(task)
                
                logger.info(f"📋 成功生成 {len(valid_tasks)} 个有效任务")
                
                # 存储任务列表到状态
                state["generated_task_list"] = valid_tasks
                state["task_queue"] = valid_tasks.copy()  # 任务队列
                state["current_task_index"] = 0
                state["completed_tasks"] = []
                state["total_tasks"] = len(valid_tasks)
                
                # 🔥 修复：设置待处理任务状态和下一个任务
                if valid_tasks:
                    state["has_pending_tasks"] = True
                    state["next_task"] = valid_tasks[0]  # 设置第一个任务为下一个任务
                else:
                    state["has_pending_tasks"] = False
                    state["next_task"] = None
                
                # 🔥 新增：显示生成的任务清单详情
                if stream_callback and valid_tasks:
                    task_summary = "\n📋 **任务清单已生成**\n\n"
                    for i, task in enumerate(valid_tasks, 1):
                        task_summary += f"**任务{i}**: {task.get('description', '未知任务')}\n"
                        task_summary += f"   - 分配给: {task.get('assigned_agent', '未分配')}\n"
                        task_summary += f"   - 优先级: {task.get('priority', 'medium')}\n\n"
                    
                    task_summary += f"总计 {len(valid_tasks)} 个任务，开始执行...\n"
                    
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(task_summary), 
                        asyncio.get_running_loop()
                    )
                    
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__任务分解完成，开始处理第1个任务..."), 
                        asyncio.get_running_loop()
                    )
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ 解析AI任务列表JSON失败: {e}")
                # 兜底：生成简单的任务列表
                fallback_tasks = [{
                    "id": 1,
                    "description": user_request,
                    "assigned_agent": available_agents[0] if available_agents else "web_researcher",
                    "priority": "high",
                    "dependencies": [],
                    "estimated_time": "5-10分钟"
                }]
                
                state["generated_task_list"] = fallback_tasks
                state["task_queue"] = fallback_tasks.copy()
                state["current_task_index"] = 0
                state["completed_tasks"] = []
                state["total_tasks"] = 1
                
                # 🔥 修复：兜底策略也需要设置待处理任务状态
                state["has_pending_tasks"] = True
                state["next_task"] = fallback_tasks[0]
                
                # 🔥 新增：显示兜底任务信息
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"\n📋 **使用简化任务清单**\n\n**任务1**: {user_request}\n   - 分配给: {fallback_tasks[0]['assigned_agent']}\n   - 优先级: high\n\n开始执行...\n"), 
                        asyncio.get_running_loop()
                    )
                
        except Exception as e:
            logger.error(f"❌ 任务列表生成失败: {e}")
            state["generated_task_list"] = []
            state["task_queue"] = []
            state["current_task_index"] = 0
            state["completed_tasks"] = []
            state["total_tasks"] = 0
            
            # 🔥 修复：异常情况下也要设置状态
            state["has_pending_tasks"] = False
            state["next_task"] = None
        
        return state
    
    async def _task_progress_tracker_node(self, state: EnhancedTeamState, config: Dict, team_config: Dict, stream_callback: Optional[Callable] = None) -> EnhancedTeamState:
        """任务进度跟踪器节点 - 跟踪任务完成情况并决定下一步"""
        logger.info("📊 任务进度跟踪器启动")
        
        try:
            # 🔥 修复：首先恢复内部任务信息（如果存在）
            if "_internal_task_list" in state:
                state["generated_task_list"] = state.pop("_internal_task_list", [])
            if "_internal_task_queue" in state:
                state["task_queue"] = state.pop("_internal_task_queue", [])
            
            # 获取任务相关信息
            task_queue = state.get("task_queue", [])
            current_task_index = state.get("current_task_index", 0)
            completed_tasks = state.get("completed_tasks", [])
            total_tasks = state.get("total_tasks", 0)
            last_executed_agent = state.get("last_executed_agent")
            
            # 记录当前任务完成
            if current_task_index < len(task_queue):
                current_task = task_queue[current_task_index]
                
                # 构建任务完成记录
                completion_record = {
                    "task_id": current_task.get("id", current_task_index + 1),
                    "description": current_task.get("description", ""),
                    "assigned_agent": current_task.get("assigned_agent", ""),
                    "completed_by": last_executed_agent,
                    "completed_at": datetime.now().isoformat(),
                    "status": "completed"
                }
                
                completed_tasks.append(completion_record)
                current_task_index += 1
                
                logger.info(f"📊 任务 {current_task_index}/{total_tasks} 已完成: {current_task.get('description', '')[:50]}...")
            
            # 更新状态
            state["completed_tasks"] = completed_tasks
            state["current_task_index"] = current_task_index
            
            # 检查是否还有未完成的任务
            if current_task_index < len(task_queue):
                # 还有未完成的任务
                next_task = task_queue[current_task_index]
                state["has_pending_tasks"] = True
                state["next_task"] = next_task
                
                # 🔥 修复：清理上下文，只保留当前任务信息，避免智能体看到完整任务列表
                
                # 1. 清理任务列表相关的状态，避免智能体看到完整列表
                # 暂时移除这些信息，让智能体专注于当前任务
                original_task_list = state.pop("generated_task_list", [])
                original_task_queue = state.pop("task_queue", [])
                
                # 2. 构造干净的任务消息，只包含当前任务描述
                task_message = {
                    "role": "user",
                    "content": f"请完成以下任务：{next_task.get('description', '')}",
                    "agent_id": "task_list_generator",
                    "timestamp": datetime.now().isoformat(),
                    "task_info": {
                        "task_id": next_task.get("id"),
                        "assigned_agent": next_task.get("assigned_agent"),
                        "priority": next_task.get("priority", "medium"),
                        "is_single_task": True  # 标记这是单个任务
                    }
                }
                
                # 3. 清理消息历史，只保留当前任务消息，让智能体专注于具体任务
                messages = state.get("messages", [])
                
                # 🔥 保存原始用户请求到内部状态，供最终结果整合时使用
                if not state.get("_original_user_request"):
                    for msg in messages:
                        if msg.get("role") == "user" and msg.get("agent_id") != "task_list_generator":
                            state["_original_user_request"] = msg.get("content", "")
                            break
                
                # 🔥 修复：不保留原始复杂请求，只给智能体看当前分解后的具体任务
                clean_messages = [task_message]
                state["messages"] = clean_messages
                
                # 4. 恢复任务列表信息（但在内部状态中）
                state["_internal_task_list"] = original_task_list
                state["_internal_task_queue"] = original_task_queue
                
                # 清除上一个智能体的执行记录，让路由器重新决策
                state["last_executed_agent"] = "task_progress_tracker"
                
                logger.info(f"📊 准备处理下一个任务: {next_task.get('description', '')[:50]}...")
                
                if stream_callback:
                    progress_percent = int((current_task_index / total_tasks) * 100)
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__进度 {progress_percent}% ({current_task_index}/{total_tasks}) - 准备处理: {next_task.get('description', '')[:30]}..."), 
                        asyncio.get_running_loop()
                    )
                
            else:
                # 所有任务已完成
                state["has_pending_tasks"] = False
                state["next_task"] = None
                
                logger.info(f"✅ 所有 {total_tasks} 个任务已完成！")
                
                if stream_callback:
                    asyncio.run_coroutine_threadsafe(
                        stream_callback(f"__status__所有任务已完成 ({total_tasks}/{total_tasks})，正在整合最终结果..."), 
                        asyncio.get_running_loop()
                    )
        
        except Exception as e:
            logger.error(f"❌ 任务进度跟踪失败: {e}")
            # 出错时标记为完成，避免死循环
            state["has_pending_tasks"] = False
            state["next_task"] = None
        
        return state
    
    async def _intelligent_router_node(self, state: EnhancedTeamState, config: Dict) -> EnhancedTeamState:
        """
        🔥 新增：AI 智能路由节点
        该节点调用 IntelligentRouter 来决定下一个步骤
        """
        logger.info("🤖 AI 智能路由器开始决策...")
        logger.info(f"🔍 Router received state keys: {state.keys()}")
        
        team_config = state.get("_team_config", {})
        routing_conditions = team_config.get('routing_conditions', {})
        
        # 🔥 修复：根据工作流类型计算可用智能体
        team_config = state.get("_team_config", {})
        workflow_type = team_config.get("workflow_type", "conditional")
        all_agents = state.get("_workflow_agents", [])
        
        if workflow_type == "planning_router":
            # 计划路由工作流：所有智能体都可用（可以重复使用）
            available_agents = [agent_id for agent_id in all_agents if agent_id != "evaluator_agent"]
            logger.debug(f"📋 计划路由工作流: 所有智能体都可用 ({len(available_agents)} 个)")
        else:
            # 其他工作流：排除已执行过的智能体
            execution_history = state.get("execution_history", [])
            executed_agents = {record.get("agent_id") for record in execution_history if record.get("status") == "success"}
            available_agents = [agent_id for agent_id in all_agents 
                               if agent_id not in executed_agents and agent_id != "evaluator_agent"]
            logger.debug(f"🔀 条件工作流: 未执行智能体可用 ({len(available_agents)} 个)")
        
        # 调用智能路由器进行决策
        decision = await self.intelligent_router.smart_route_decision(
            state, routing_conditions, available_agents
        )
        
        state["next_node"] = decision
        logger.info(f"🤖 AI 智能路由器决策结果: {decision}")
        
        return state
    
    async def execute_workflow(self, team_name: str, input_data: Dict, 
                             config: Optional[Dict] = None,
                             stream_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """执行工作流"""
        
        try:
            logger.info(f"🚀 开始执行工作流: {team_name}")
            start_time = datetime.now()
            
            # 1. 获取团队配置
            team_config = self.config_manager.get_team_config(team_name)
            if not team_config:
                raise ValueError(f"团队配置未找到: {team_name}")
            
            # 2. 构建或获取已编译的工作流
            workflow_id = f"{team_name}_{hash(str(team_config))}"
            if workflow_id not in self.compiled_workflows:
                graph = await self.build_workflow_from_team_config(team_config, stream_callback)
                compiled_workflow = graph.compile(checkpointer=self.memory_saver)
                self.compiled_workflows[workflow_id] = compiled_workflow
            else:
                compiled_workflow = self.compiled_workflows[workflow_id]
            
            # 3. 准备初始状态
            # 🔥 改进：使用内存管理器构建完整状态
            session_id = config.get("configurable", {}).get("thread_id", str(uuid.uuid4()))
            
            initial_state = EnhancedTeamState(
                messages=input_data.get("messages", []),
                core_context=input_data.get("core_context", {}),
                working_memory=input_data.get("working_memory", {}),
                agent_memory=input_data.get("agent_memory", {}),
                context_metrics={"total_tokens": 0, "compression_level": 0},
                compression_level=0,
                checkpoint_id=None
            )
            
            # 🔥 新增：从内存管理器加载已有上下文
            try:
                full_context = await self.context_manager.get_agent_context("workflow_session")
                if full_context:
                    initial_state["core_context"].update(full_context.get("core_context", {}))
                    initial_state["working_memory"].update(full_context.get("working_memory", {}))
                    logger.info(f"✅ 从内存管理器加载上下文: {len(full_context)} 项")
            except Exception as e:
                logger.warning(f"⚠️ 加载内存上下文失败: {e}")
            
            # 🔥 新增：创建工作流会话检查点
            try:
                checkpoint_id = await self.context_manager.create_context_checkpoint(
                    session_id, initial_state, "workflow_start"
                )
                initial_state["session_checkpoint_id"] = checkpoint_id
                logger.info(f"✅ 创建工作流检查点: {checkpoint_id}")
            except Exception as e:
                logger.warning(f"⚠️ 创建检查点失败: {e}")
            
            # 🔥 新增：将团队配置添加到状态中，供协作规则使用
            initial_state["_team_config"] = team_config
            
            # 🔥 新增：设置team_id和原始用户请求，确保结果整合器能获取团队记忆
            initial_state["team_id"] = team_name
            initial_state["workflow_id"] = workflow_id
            
            # 🔥 新增：保存原始用户请求作为备用上下文
            if initial_state.get("messages"):
                last_user_message = None
                for msg in reversed(initial_state["messages"]):
                    if msg.get("role") == "user":
                        last_user_message = msg.get("content", "")
                        break
                if last_user_message:
                    initial_state["_original_user_request"] = last_user_message
                    logger.info(f"💬 保存原始用户请求: {len(last_user_message)} 字符")
            
            logger.info(f"🏷️ 设置工作流状态: team_id={team_name}, workflow_id={workflow_id}")
            
            # 🔥 优化：添加智能体列表信息，用于判断最后一个智能体
            initial_state["_workflow_agents"] = team_config.get('agents', [])
            
            # 🔥 修复：正确地合并和传递config
            final_config = config or {}
            if "configurable" not in final_config:
                final_config["configurable"] = {}
            
            # 确保thread_id存在
            if "thread_id" not in final_config["configurable"]:
                final_config["configurable"]["thread_id"] = str(uuid.uuid4())

            # 将回调函数添加到最终的配置中
            if stream_callback:
                final_config["configurable"]["stream_callback"] = stream_callback
            
            # 4. 执行工作流
            # 🔥 新增：保存执行前的checkpoint
            try:
                pre_execution_checkpoint = {
                    "initial_state": initial_state,
                    "config": final_config,
                    "timestamp": datetime.now().isoformat()
                }
                self.memory_saver.put(f"pre_execution_{session_id}", pre_execution_checkpoint, metadata={"type": "pre_execution"})
                logger.info(f"💾 执行前checkpoint已保存")
            except Exception as e:
                logger.warning(f"保存执行前checkpoint失败: {e}")
            
            final_state = await compiled_workflow.ainvoke(
                initial_state,
                config=final_config
            )
            
            # 5. 计算执行统计
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_execution_stats(execution_time, final_state)
            
            # 🔥 新增：保存执行后的checkpoint
            try:
                post_execution_checkpoint = {
                    "final_state": final_state,
                    "execution_time": execution_time,
                    "execution_stats": self.execution_stats.copy(),
                    "timestamp": datetime.now().isoformat()
                }
                self.memory_saver.put(f"post_execution_{session_id}", post_execution_checkpoint, metadata={"type": "post_execution"})
                logger.info(f"💾 执行后checkpoint已保存，耗时: {execution_time:.2f}s")
            except Exception as e:
                logger.warning(f"保存执行后checkpoint失败: {e}")
            
            # 🔥 新增：工作流完成后的状态保存和内存管理
            try:
                # 保存核心上下文到内存管理器
                await self.context_manager.store_core_context(
                    f"workflow_{team_name}_result", 
                    {
                        "execution_time": execution_time,
                        "final_state_summary": final_state.get("workflow_summary", {}),
                        "completed_at": datetime.now().isoformat()
                    }
                )
                
                # 创建完成检查点
                if "session_checkpoint_id" in initial_state:
                    completion_checkpoint_id = await self.context_manager.create_context_checkpoint(
                        session_id, final_state, "workflow_completed"
                    )
                    logger.info(f"✅ 创建完成检查点: {completion_checkpoint_id}")
                
                # 存储智能体执行历史到工作记忆（60分钟过期）
                execution_history = final_state.get("execution_history", [])
                if execution_history:
                    await self.context_manager.store_working_context(
                        f"workflow_{team_name}_history",
                        execution_history,
                        ttl_minutes=60
                    )
                
                logger.info("✅ 工作流状态和记忆保存完成")
                
            except Exception as e:
                logger.warning(f"⚠️ 工作流状态保存失败: {e}")
            
            logger.info(f"✅ 工作流执行完成: {team_name}, 耗时: {execution_time:.2f}秒")
            
            return {
                "status": "success",
                "execution_time": execution_time,
                "final_state": final_state,
                "workflow_summary": final_state.get("workflow_summary", {}),
                "errors": final_state.get("errors", []),
                "session_id": session_id,
                "checkpoint_id": final_state.get("session_checkpoint_id")
            }
            
        except Exception as e:
            logger.error(f"❌ 工作流执行失败 {team_name}: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "execution_time": 0,
                "final_state": None
            }
    
    def _calculate_execution_time(self, state: EnhancedTeamState) -> float:
        """计算执行时间"""
        
        start_time_str = state.get("started_at")
        if start_time_str:
            try:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                return (datetime.now() - start_time).total_seconds()
            except:
                pass
        return 0
    
    def _update_execution_stats(self, execution_time: float, final_state: Dict):
        """更新执行统计"""
        
        self.execution_stats["workflows_executed"] += 1
        
        # 计算平均执行时间
        total_time = (self.execution_stats["average_execution_time"] * 
                     (self.execution_stats["workflows_executed"] - 1) + execution_time)
        self.execution_stats["average_execution_time"] = total_time / self.execution_stats["workflows_executed"]
        
        # 计算成功率
        errors = final_state.get("errors", [])
        success = 1 if len(errors) == 0 else 0
        total_success = (self.execution_stats["success_rate"] * 
                        (self.execution_stats["workflows_executed"] - 1) + success)
        self.execution_stats["success_rate"] = total_success / self.execution_stats["workflows_executed"]
        
        # 统计智能体调用次数
        execution_history = final_state.get("execution_history", [])
        self.execution_stats["total_agents_invoked"] += len(execution_history)
    
    async def get_workflow_stats(self) -> Dict[str, Any]:
        """获取工作流统计信息"""
        
        return {
            "execution_stats": self.execution_stats,
            "compiled_workflows": len(self.compiled_workflows),
            "context_manager_stats": await self.context_manager.get_unified_stats(),
            "timestamp": datetime.now().isoformat()
        }
    
    async def clear_workflow_cache(self):
        """清理工作流缓存"""
        
        self.compiled_workflows.clear()
        logger.info("🧹 工作流缓存已清理")


# 全局工作流引擎实例
_workflow_engine_instance = None

def get_workflow_engine() -> DynamicWorkflowEngine:
    """获取全局工作流引擎实例"""
    global _workflow_engine_instance
    if _workflow_engine_instance is None:
        _workflow_engine_instance = DynamicWorkflowEngine()
    return _workflow_engine_instance

async def execute_team_workflow(team_name: str, input_data: Dict, 
                               config: Optional[Dict] = None) -> Dict[str, Any]:
    """执行团队工作流的便捷函数"""
    engine = get_workflow_engine()
    return await engine.execute_workflow(team_name, input_data, config)

async def get_workflow_statistics() -> Dict[str, Any]:
    """获取工作流统计信息的便捷函数"""
    engine = get_workflow_engine()
    return await engine.get_workflow_stats()

# 导出主要类和函数
__all__ = [
    'DynamicWorkflowEngine',
    'get_workflow_engine',
    'execute_team_workflow',
    'get_workflow_statistics'
] 