# -*- coding: utf-8 -*-
"""LangGraph 增强系统 - 提示词构建器"""

import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class LangGraphPromptBuilder:
    """LangGraph 提示词构建器 - 完全复用现有系统"""
    
    def __init__(self):
        """初始化提示词构建器"""
        try:
            from backend.mcp.custom_prompt_generator import generate_custom_prompt
            from backend.mcp.prompts import (
                SYSTEM_PROTECTION_GUIDELINES,
                MCP_TOOL_GUIDELINES,
                get_mcp_servers_prompt,
                MCP_TOOL_EXECUTION_FLOW,
                MCP_CAPABILITIES,
                MCP_PROMPT,
                MCP_EXAMPLES
            )
            
            self.generate_custom_prompt = generate_custom_prompt
            self.system_protection = SYSTEM_PROTECTION_GUIDELINES
            self.mcp_guidelines = MCP_TOOL_GUIDELINES
            self.get_mcp_servers_prompt = get_mcp_servers_prompt
            self.mcp_execution_flow = MCP_TOOL_EXECUTION_FLOW
            self.mcp_capabilities = MCP_CAPABILITIES
            self.mcp_prompt = MCP_PROMPT
            self.mcp_examples = MCP_EXAMPLES
            
            logger.info("提示词构建器初始化成功")
        except ImportError as e:
            logger.error(f"导入现有系统组件失败: {str(e)}")
            self.generate_custom_prompt = None
            self.system_protection = ""
            self.mcp_guidelines = ""
            self.get_mcp_servers_prompt = None
            self.mcp_execution_flow = ""
            self.mcp_capabilities = ""
            self.mcp_prompt = ""
            self.mcp_examples = ""
    
    async def build_agent_prompt(self, agent_config: Dict) -> str:
        """为智能体构建系统提示词"""
        if self.generate_custom_prompt is None:
            logger.warning("generate_custom_prompt 不可用，使用简化版本")
            return self._build_fallback_prompt(agent_config)
        
        try:
            custom_sections_before = {}
            
            if agent_config.get('context_strategy_description'):
                custom_sections_before['Context Management Strategy'] = agent_config['context_strategy_description']
            
            if agent_config.get('team_collaboration_info'):
                custom_sections_before['Team Collaboration'] = agent_config['team_collaboration_info']
            
            system_prompt = self.generate_custom_prompt(
                role_name=agent_config.get('role_name', ''),
                role_definition=agent_config.get('role_definition', ''),
                background=agent_config.get('background', ''),
                description=agent_config.get('description', ''),
                core_rules=agent_config.get('core_rules', []),
                mcp_tools=agent_config.get('mcp_tools', []),
                include_system_protection=True,
                include_mcp_capabilities=True,
                custom_sections_before_tools=custom_sections_before,
                is_langgraph_agent=True  # 🧠 标识为LangGraph框架智能体
            )
            
            logger.info(f"为智能体 '{agent_config.get('role_name', 'Unknown')}' 生成系统提示词")
            return system_prompt
            
        except Exception as e:
            logger.error(f"生成提示词失败: {str(e)}")
            return self._build_fallback_prompt(agent_config)
    
    def _build_fallback_prompt(self, agent_config: Dict) -> str:
        """降级方案：手动构建提示词"""
        role_name = agent_config.get('role_name', '未知角色')
        role_definition = agent_config.get('role_definition', '')
        background = agent_config.get('background', '')
        description = agent_config.get('description', '')
        core_rules = agent_config.get('core_rules', [])
        mcp_tools = agent_config.get('mcp_tools', [])
        
        prompt_parts = [
            f"Your Name is {role_name}\n",
            f"# Role Definition\n\n{role_definition}\n" if role_definition else "",
            f"## Background\n{background}\n" if background else "",
            f"## Description\n{description}\n" if description else "",
        ]
        
        # 添加系统保护准则
        if self.system_protection:
            prompt_parts.append(f"\n{self.system_protection}\n")
        
        # 添加 MCP 能力说明
        if self.mcp_capabilities:
            prompt_parts.append(f"\n{self.mcp_capabilities}\n")
        
        # 添加自定义字段
        if agent_config.get('context_strategy_description'):
            prompt_parts.append(f"## Context Management Strategy\n{agent_config['context_strategy_description']}\n")
        
        if agent_config.get('team_collaboration_info'):
            prompt_parts.append(f"## Team Collaboration\n{agent_config['team_collaboration_info']}\n")
        
        # 添加 MCP 工具信息
        if mcp_tools and self.get_mcp_servers_prompt:
            servers_prompt = self.get_mcp_servers_prompt(mcp_tools, True)  # 🧠 标识为LangGraph框架智能体
            if servers_prompt and servers_prompt != '(No MCP servers currently connected)':
                mcp_header = (
                    "\n====\n\nMCP SERVERS\n\n"
                    "The Model Context Protocol (MCP) enables communication between the system and "
                    "locally running MCP servers that provide additional tools and resources to extend your capabilities.\n\n"
                    "# Connected MCP Servers\n"
                    "When a server is connected, you can use the server's tools via the `use_mcp_tool` tool, "
                    "and access the server's resources via the `access_mcp_resource` tool.\n"
                )
                prompt_parts.append(f"{mcp_header}\n{servers_prompt}\n")
                
                # 添加 MCP 工具使用指南
                if self.mcp_guidelines:
                    prompt_parts.append(f"\n## MCP Tool Use Guidelines\n{self.mcp_guidelines}\n")
                
                # 添加 MCP 执行流程
                if self.mcp_execution_flow:
                    prompt_parts.append(f"\n{self.mcp_execution_flow}\n")
        
        # 添加核心规则
        if core_rules:
            prompt_parts.append("\n## Core Rules\n" + "\n".join(f"- {rule}" for rule in core_rules) + "\n")
        
        # 添加 MCP 工具定义
        if self.mcp_prompt:
            prompt_parts.append(f"\n# MCP Tool Definitions\n{self.mcp_prompt}\n")
        
        # 添加 MCP 使用示例
        if self.mcp_examples:
            prompt_parts.append(f"\n# Examples of Using MCP Tools\n{self.mcp_examples}\n")
        
        return "\n".join(filter(None, prompt_parts))


def get_prompt_builder() -> LangGraphPromptBuilder:
    """获取全局提示词构建器实例"""
    if not hasattr(get_prompt_builder, '_instance'):
        get_prompt_builder._instance = LangGraphPromptBuilder()
    return get_prompt_builder._instance


async def build_langgraph_agent_prompt(agent_config: Dict) -> str:
    """便捷函数：构建 LangGraph 智能体提示词"""
    builder = get_prompt_builder()
    return await builder.build_agent_prompt(agent_config) 