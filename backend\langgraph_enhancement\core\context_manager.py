"""
🔥 LangGraph 增强系统 - 核心上下文管理器

统一管理所有上下文组件：
- 内存管理器集成
- 检查点处理器集成
- 压缩引擎集成
- 上下文路由器集成
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import json

# 导入上下文组件
from ..context.memory_manager import get_memory_manager, EnhancedMemoryManager
from ..context.checkpoint_handler import get_checkpoint_handler, EnhancedCheckpointHandler
from ..context.compression_engine import get_compression_engine, ContextCompressionEngine
from ..context.context_router import get_context_router, SmartContextRouter
from ..config.config_manager import get_config_manager

logger = logging.getLogger(__name__)


class UnifiedContextManager:
    """
    🔥 统一上下文管理器 - LangGraph 增强系统核心
    
    功能：
    - 统一管理所有上下文组件
    - 提供简化的上下文API
    - 协调各组件之间的交互
    - 提供性能监控和统计
    """
    
    def __init__(self, 
                 enable_compression: bool = True,
                 enable_checkpoints: bool = True,
                 enable_routing: bool = True):
        """初始化统一上下文管理器"""
        
        # 获取核心组件实例
        self.memory_manager = get_memory_manager()
        self.config_manager = get_config_manager()
        
        if enable_checkpoints:
            self.checkpoint_handler = get_checkpoint_handler()
        else:
            self.checkpoint_handler = None
        
        if enable_compression:
            self.compression_engine = get_compression_engine()
        else:
            self.compression_engine = None
        
        if enable_routing:
            self.context_router = get_context_router()
        else:
            self.context_router = None
        
        # 配置选项
        self.enable_compression = enable_compression
        self.enable_checkpoints = enable_checkpoints
        self.enable_routing = enable_routing
        
        # 统计信息
        self.stats = {
            "total_operations": 0,
            "context_optimizations": 0,
            "checkpoints_created": 0,
            "routing_decisions": 0,
            "last_operation": None
        }
        
        logger.info("🔥 统一上下文管理器初始化完成")
    
    async def process_agent_context(self, 
                                   agent_id: str, 
                                   state: Dict[str, Any],
                                   operation: str = "standard") -> Dict[str, Any]:
        """
        处理智能体上下文 - 核心方法
        
        Args:
            agent_id: 智能体ID
            state: 当前状态
            operation: 操作类型 ("standard", "optimize", "reset")
            
        Returns:
            处理后的状态
        """
        
        self.stats["total_operations"] += 1
        self.stats["last_operation"] = {
            "agent_id": agent_id,
            "operation": operation,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 🔥 重构：现在系统消息已在WorkflowEngine中提前构建
            processed_state = state.copy()
            messages = processed_state.get("messages", [])
            
            # 🔥 新增：应用上下文共享策略
            team_config = processed_state.get("_team_config", {})
            sharing_strategy = team_config.get("context_sharing_strategy", "selective")
            
            if sharing_strategy != "full":
                messages = await self._apply_sharing_strategy(messages, sharing_strategy, agent_id)
                processed_state["messages"] = messages
                logger.info(f"📋 应用上下文共享策略: {sharing_strategy}, 消息数量: {len(messages)}")
            
            # 记录上下文信息用于调试
            has_system = any(msg.get('role') == 'system' for msg in messages)
            logger.info(f"🔍 处理agent上下文: agent_id={agent_id}, 已有系统消息={has_system}, 消息数量={len(messages)}")
            
            # 1. 上下文路由决策
            if self.enable_routing and self.context_router:
                routing_result = await self.context_router.route_context_strategy(processed_state)
                if isinstance(routing_result, tuple):
                    strategy, decision_info = routing_result
                    # 🔥 修复：直接使用路由器返回的actions，避免枚举对象比较错误
                    routing_decision = {
                        "strategy": strategy.value if hasattr(strategy, 'value') else strategy,
                        "decision_info": decision_info,
                        "actions": decision_info.get("actions", [])  # 直接使用路由器的actions
                    }
                else:
                    routing_decision = routing_result
                self.stats["routing_decisions"] += 1
                logger.debug(f"上下文路由决策: {routing_decision}")
            else:
                routing_decision = {"strategy": "standard", "actions": []}
            
            # 2. 应用路由决策 - 注意此时processed_state已包含系统消息
            
            # 压缩处理
            if (self.enable_compression and 
                self.compression_engine and 
                "compress" in routing_decision.get("actions", [])):
                
                # 🔥 使用已包含系统消息的messages
                messages = processed_state.get("messages", [])
                # 🔥 修复：使用完整的消息大小计算，而不是只计算非系统消息
                original_size = sum(
                    len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
                    for msg in messages
                )
                logger.info(f"🧠 上下文长度 ({original_size} 字符) 触发压缩，开始优化...")

                # 调用 adaptive_compression 方法
                compressed_messages, compression_metadata = await self.compression_engine.adaptive_compression(messages)
                processed_state["messages"] = compressed_messages
                processed_state["compression_metadata"] = compression_metadata
                
                # 🔥 修复：使用相同的计算方式
                compressed_size = sum(
                    len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
                    for msg in compressed_messages
                )
                logger.info(f"✅ 上下文压缩完成: 长度从 {original_size} 减少到 {compressed_size} 字符")
                self.stats["context_optimizations"] += 1
            
            # 3. 更新智能体记忆
            await self._update_agent_memory(agent_id, processed_state)
            
            # 4. 创建检查点（如果需要）
            if (self.enable_checkpoints and 
                self.checkpoint_handler and 
                operation in ["milestone", "auto"] and 
                len(processed_state.get("messages", [])) > 0):
                
                checkpoint_id = await self.checkpoint_handler.create_checkpoint(
                    session_id=f"agent_{agent_id}",
                    state=processed_state,
                    checkpoint_type=operation
                )
                processed_state["last_checkpoint_id"] = checkpoint_id
                self.stats["checkpoints_created"] += 1
                logger.info(f"创建检查点: {checkpoint_id}")
            
            return processed_state
            
        except Exception as e:
            logger.error(f"上下文处理失败 {agent_id}: {e}")
            return state  # 返回原始状态
    
    async def get_agent_context(self, agent_id: str) -> Dict[str, Any]:
        """获取智能体的完整上下文"""
        
        try:
            context = await self.memory_manager.get_full_context_for_agent(agent_id)
            logger.debug(f"获取智能体上下文: {agent_id}")
            return context
            
        except Exception as e:
            logger.error(f"获取智能体上下文失败 {agent_id}: {e}")
            return {}
    
    async def store_core_context(self, key: str, value: Any) -> None:
        """存储核心上下文"""
        await self.memory_manager.store_core_context(key, value)
    
    async def store_agent_context(self, agent_id: str, key: str, value: Any) -> None:
        """存储智能体专用上下文"""
        await self.memory_manager.store_agent_memory(agent_id, key, value)
    
    async def store_working_context(self, key: str, value: Any, ttl_minutes: int = 60) -> None:
        """存储工作上下文"""
        await self.memory_manager.store_working_memory(key, value, ttl_minutes)
    
    async def optimize_context_for_agent(self, agent_id: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """为智能体优化上下文"""
        
        if not self.enable_compression or not self.compression_engine:
            return state
        
        # 调用 adaptive_compression 方法
        messages = state.get("messages", [])
        compressed_messages, compression_metadata = await self.compression_engine.adaptive_compression(messages)
        optimized_state = state.copy()
        optimized_state["messages"] = compressed_messages
        optimized_state["compression_metadata"] = compression_metadata
        self.stats["context_optimizations"] += 1
        logger.info(f"优化智能体上下文: {agent_id}")
        
        return optimized_state
    
    async def create_context_checkpoint(self, 
                                      session_id: str, 
                                      state: Dict[str, Any],
                                      checkpoint_type: str = "manual") -> Optional[str]:
        """创建上下文检查点"""
        
        if not self.enable_checkpoints or not self.checkpoint_handler:
            return None
        
        checkpoint_id = await self.checkpoint_handler.create_checkpoint(
            session_id=session_id,
            state=state,
            checkpoint_type=checkpoint_type
        )
        
        self.stats["checkpoints_created"] += 1
        return checkpoint_id
    
    async def restore_context_checkpoint(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """恢复上下文检查点"""
        
        if not self.enable_checkpoints or not self.checkpoint_handler:
            return None
        
        return await self.checkpoint_handler.restore_checkpoint(checkpoint_id)
    
    async def analyze_context_health(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """分析上下文健康状况"""
        
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "context_size": len(str(state)),
            "message_count": len(state.get("messages", [])),
            "memory_usage": "unknown",
            "recommendations": []
        }
        
        # 从配置获取阈值
        context_config = self.config_manager.get_context_config()
        threshold = context_config.get('health_check_threshold', 50000)

        # 检查上下文大小
        context_size = len(str(state))
        if context_size > threshold:
            health_report["recommendations"].append(f"建议执行上下文压缩 (当前: {context_size}, 阈值: {threshold})")
        
        # 检查消息数量
        message_count = len(state.get("messages", []))
        if message_count > 50:
            health_report["recommendations"].append("消息历史过长，建议摘要化")
        
        # 获取内存使用情况
        if self.memory_manager:
            memory_stats = await self.memory_manager.get_memory_usage_stats()
            health_report["memory_usage"] = memory_stats
        
        return health_report
    
    async def _apply_sharing_strategy(self, 
                                     messages: List[Dict[str, Any]], 
                                     strategy: str,
                                     current_agent_id: str) -> List[Dict[str, Any]]:
        """
        根据共享策略过滤消息
        
        Args:
            messages: 原始消息列表
            strategy: 共享策略 (selective, minimal)
            current_agent_id: 当前智能体ID
            
        Returns:
            过滤后的消息列表
        """
        
        if not messages:
            return messages
        
        filtered_messages = []
        
        # 始终保留系统消息
        system_messages = [msg for msg in messages if msg.get('role') == 'system']
        filtered_messages.extend(system_messages)
        
        # 🔥 调试：记录系统消息的状态
        logger.debug(f"🔍 共享策略 '{strategy}' 处理前：找到 {len(system_messages)} 条系统消息")
        for i, sys_msg in enumerate(system_messages):
            content = sys_msg.get('content', '')
            # logger.debug(f"🔍 系统消息 {i}: 长度={len(content)}, 前100字符: {content[:100]}...")
        
        if strategy == "full":
            # 🔥 修复：full策略保留所有消息，不进行任何过滤
            return messages
            
        elif strategy == "minimal":
            # minimal策略：只保留最新的用户消息和最后一个智能体的响应
            user_messages = [msg for msg in messages if msg.get('role') == 'user']
            if user_messages:
                # 保留最新的用户消息
                filtered_messages.append(user_messages[-1])
            
            # 保留最后一个智能体的响应
            assistant_messages = [msg for msg in messages if msg.get('role') == 'assistant']
            if assistant_messages:
                filtered_messages.append(assistant_messages[-1])
                
        elif strategy == "selective":
            # selective策略：保留用户消息和关键的智能体响应
            
            # 保留所有用户消息
            user_messages = [msg for msg in messages if msg.get('role') == 'user']
            filtered_messages.extend(user_messages)
            
            # 保留包含关键信息的助手消息
            for msg in messages:
                if msg.get('role') == 'assistant':
                    content = msg.get('content', '').lower()
                    # 判断是否包含关键信息（结果、结论、建议等）
                    key_indicators = [
                        '结果', '结论', '总结', '建议', '分析',
                        'result', 'conclusion', 'summary', 'recommendation',
                        '完成', 'completed', '##', '###'  # 标题通常表示重要内容
                    ]
                    
                    # 如果包含关键词或是最后一条消息，则保留
                    if any(indicator in content for indicator in key_indicators):
                        filtered_messages.append(msg)
                    elif msg == messages[-1] and msg.get('agent_id') != current_agent_id:
                        # 保留最后一个智能体的输出
                        filtered_messages.append(msg)
        
        # 确保消息按时间顺序排列
        if len(filtered_messages) > 1:
            # 保持原有顺序
            original_indices = {id(msg): i for i, msg in enumerate(messages)}
            filtered_messages.sort(key=lambda msg: original_indices.get(id(msg), float('inf')))
        
        logger.debug(f"🔄 上下文共享策略 '{strategy}': {len(messages)} -> {len(filtered_messages)} 条消息")
        return filtered_messages
    
    async def _update_agent_memory(self, agent_id: str, state: Dict[str, Any]) -> None:
        """更新智能体记忆"""
        
        try:
            # 存储最新状态
            await self.memory_manager.store_agent_memory(
                agent_id, 
                "last_state", 
                {
                    "state": state,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # 存储消息计数
            message_count = len(state.get("messages", []))
            await self.memory_manager.store_agent_memory(
                agent_id, 
                "message_count", 
                message_count
            )
            
        except Exception as e:
            logger.error(f"更新智能体记忆失败 {agent_id}: {e}")
    
    async def get_unified_stats(self) -> Dict[str, Any]:
        """获取统一的统计信息"""
        
        unified_stats = {
            "context_manager": self.stats,
            "timestamp": datetime.now().isoformat()
        }
        
        # 内存管理器统计
        if self.memory_manager:
            unified_stats["memory_manager"] = await self.memory_manager.get_memory_usage_stats()
        
        # 检查点处理器统计
        if self.checkpoint_handler:
            unified_stats["checkpoint_handler"] = await self.checkpoint_handler.get_checkpoint_stats()
        
        # 压缩引擎统计
        if self.compression_engine:
            from ..context.compression_engine import get_compression_engine_stats
        unified_stats["compression_engine"] = get_compression_engine_stats()
        
        # 上下文路由器统计
        if self.context_router:
            from ..context.context_router import get_routing_stats_sync
        unified_stats["context_router"] = get_routing_stats_sync()
        
        return unified_stats
    
    async def cleanup_expired_contexts(self) -> Dict[str, int]:
        """清理过期的上下文"""
        
        cleanup_results = {}
        
        # 内存清理
        if self.memory_manager:
            await self.memory_manager.auto_cleanup()
            cleanup_results["memory_cleanup"] = 1
        
        # 检查点清理
        if self.checkpoint_handler:
            cleaned_checkpoints = await self.checkpoint_handler.cleanup_expired_checkpoints()
            cleanup_results["checkpoints_cleaned"] = cleaned_checkpoints
        
        return cleanup_results
    
    def get_langgraph_memory_saver(self):
        """获取 LangGraph 兼容的内存保存器"""
        if self.checkpoint_handler:
            return self.checkpoint_handler.get_langgraph_memory_saver()
        elif self.memory_manager:
            return self.memory_manager.get_langgraph_memory_saver()
        else:
            return None


class ContextManagerSingleton:
    """上下文管理器单例"""
    _instance: Optional[UnifiedContextManager] = None
    
    @classmethod
    def get_instance(cls, **kwargs) -> UnifiedContextManager:
        if cls._instance is None:
            cls._instance = UnifiedContextManager(**kwargs)
        return cls._instance
    
    @classmethod
    def reset_instance(cls) -> None:
        """重置实例（主要用于测试）"""
        cls._instance = None


# 全局上下文管理器实例
def get_context_manager(**kwargs) -> UnifiedContextManager:
    """获取全局上下文管理器实例"""
    return ContextManagerSingleton.get_instance(**kwargs)


# 便捷函数
async def process_agent_context(agent_id: str, state: Dict[str, Any]) -> Dict[str, Any]:
    """便捷函数：处理智能体上下文"""
    manager = get_context_manager()
    return await manager.process_agent_context(agent_id, state)


async def get_agent_context(agent_id: str) -> Dict[str, Any]:
    """便捷函数：获取智能体上下文"""
    manager = get_context_manager()
    return await manager.get_agent_context(agent_id)


async def create_checkpoint(session_id: str, state: Dict[str, Any]) -> Optional[str]:
    """便捷函数：创建检查点"""
    manager = get_context_manager()
    return await manager.create_context_checkpoint(session_id, state)