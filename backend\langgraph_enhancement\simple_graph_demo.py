#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 简单的工作流图可视化演示

不依赖项目内部模块，直接演示如何创建和可视化工作流图
"""

import matplotlib.pyplot as plt
import networkx as nx
from datetime import datetime
import os

def create_simple_workflow_graph():
    """创建简单的工作流图可视化"""
    print("🎨 正在创建简单工作流图...")
    
    # 创建有向图
    G = nx.DiGraph()
    
    # 添加节点（工作流步骤）
    nodes = [
        "开始",
        "数据收集", 
        "数据分析",
        "报告生成",
        "质量检查",
        "结果输出"
    ]
    
    G.add_nodes_from(nodes)
    
    # 添加边（工作流连接）
    edges = [
        ("开始", "数据收集"),
        ("数据收集", "数据分析"),
        ("数据分析", "报告生成"),
        ("报告生成", "质量检查"),
        ("质量检查", "结果输出")
    ]
    
    G.add_edges_from(edges)
    
    # 创建可视化
    plt.figure(figsize=(12, 8))
    
    # 使用层次布局
    pos = nx.spring_layout(G, k=2, iterations=50)
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos, 
                          node_color='lightblue',
                          node_size=3000,
                          alpha=0.9)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos,
                          edge_color='gray',
                          arrows=True,
                          arrowsize=20,
                          arrowstyle='->')
    
    # 绘制标签
    nx.draw_networkx_labels(G, pos,
                           font_size=10,
                           font_weight='bold',
                           font_family='sans-serif')
    
    # 设置标题和样式
    plt.title("🔥 LangGraph 工作流可视化示例", fontsize=16, fontweight='bold', pad=20)
    plt.axis('off')  # 隐藏坐标轴
    
    # 添加图例
    plt.text(0.02, 0.98, "节点: 工作流步骤\n箭头: 执行顺序", 
             transform=plt.gca().transAxes,
             fontsize=10,
             verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"simple_workflow_demo_{timestamp}.png"
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 简单工作流图已生成: {filename}")
    print(f"📁 文件位置: {os.path.abspath(filename)}")
    
    plt.show()  # 如果在支持GUI的环境中会显示图片
    return filename

def create_complex_workflow_graph():
    """创建复杂的工作流图可视化"""
    print("🚀 正在创建复杂工作流图...")
    
    G = nx.DiGraph()
    
    # 添加更复杂的节点
    nodes = [
        "工作流协调器",
        "任务分配器",
        "研究智能体",
        "分析智能体", 
        "写作智能体",
        "并行处理器",
        "结果合并器",
        "质量评估器",
        "最终输出"
    ]
    
    G.add_nodes_from(nodes)
    
    # 复杂的边关系（包括并行分支）
    edges = [
        ("工作流协调器", "任务分配器"),
        ("任务分配器", "研究智能体"),
        ("任务分配器", "分析智能体"),
        ("任务分配器", "写作智能体"),
        ("研究智能体", "并行处理器"),
        ("分析智能体", "并行处理器"),
        ("写作智能体", "并行处理器"),
        ("并行处理器", "结果合并器"),
        ("结果合并器", "质量评估器"),
        ("质量评估器", "最终输出")
    ]
    
    G.add_edges_from(edges)
    
    # 创建可视化
    plt.figure(figsize=(14, 10))
    
    # 使用层次布局
    pos = nx.nx_agraph.graphviz_layout(G, prog='dot') if hasattr(nx, 'nx_agraph') else nx.spring_layout(G, k=3)
    
    # 定义不同类型节点的颜色
    node_colors = []
    for node in G.nodes():
        if "协调器" in node or "分配器" in node:
            node_colors.append('lightcoral')
        elif "智能体" in node:
            node_colors.append('lightgreen')
        elif "处理器" in node or "合并器" in node:
            node_colors.append('lightyellow')
        else:
            node_colors.append('lightblue')
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos,
                          node_color=node_colors,
                          node_size=2000,
                          alpha=0.9)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos,
                          edge_color='darkgray',
                          arrows=True,
                          arrowsize=15,
                          arrowstyle='->',
                          width=2)
    
    # 绘制标签
    nx.draw_networkx_labels(G, pos,
                           font_size=8,
                           font_weight='bold',
                           font_family='sans-serif')
    
    # 设置标题
    plt.title("🔥 复杂 LangGraph 多智能体工作流", fontsize=16, fontweight='bold', pad=20)
    plt.axis('off')
    
    # 添加图例
    legend_text = """
    节点类型:
    🔴 协调器/分配器
    🟢 智能体
    🟡 处理器/合并器  
    🔵 其他节点
    """
    
    plt.text(0.02, 0.98, legend_text.strip(),
             transform=plt.gca().transAxes,
             fontsize=9,
             verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"complex_workflow_demo_{timestamp}.png"
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    
    print(f"✅ 复杂工作流图已生成: {filename}")
    print(f"📁 文件位置: {os.path.abspath(filename)}")
    
    plt.show()
    return filename

def main():
    """主函数"""
    print("🎨 LangGraph 工作流可视化演示")
    print("="*40)
    
    try:
        # 生成简单工作流图
        simple_file = create_simple_workflow_graph()
        
        # 生成复杂工作流图  
        complex_file = create_complex_workflow_graph()
        
        print("\n🎉 可视化演示完成!")
        print(f"📋 生成的文件:")
        print(f"  - 简单工作流: {simple_file}")
        print(f"  - 复杂工作流: {complex_file}")
        print("\n🔍 查看方法:")
        print("  1. 在文件管理器中双击PNG文件")
        print("  2. 在IDE中打开PNG文件查看")
        print("  3. 使用图片查看器打开")
        
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("💡 请安装: pip install matplotlib networkx")
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()