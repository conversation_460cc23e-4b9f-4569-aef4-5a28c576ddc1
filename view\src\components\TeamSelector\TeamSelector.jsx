import React, { useState, useEffect } from 'react';
import { Button, Tooltip, message, Spin, Modal, Avatar, List, Space, Badge } from 'antd';
import { TeamOutlined, InfoCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import './TeamSelector.css';
import TeamDetailModal from './TeamDetailModal';

// 持久化存储键名
const TEAM_STORAGE_KEY = 'jimu_selected_team_id';

/**
 * 团队选择器组件
 * 
 * @param {Object} props
 * @param {String} props.value - 当前选中的团队ID
 * @param {Function} props.onChange - 团队改变时的回调函数
 * @param {Boolean} props.disabled - 是否禁用
 * @param {String} props.className - 自定义类名
 * @returns {JSX.Element}
 */
const TeamSelector = ({ value, onChange, disabled = false, className = '' }) => {
  // 团队列表状态
  const [teams, setTeams] = useState([]);
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 当前选中的团队
  const [selectedTeam, setSelectedTeam] = useState(value);
  // 团队详情数据
  const [teamDetail, setTeamDetail] = useState(null);
  // 详情对话框是否可见
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  // 当前查看详情的团队ID
  const [viewingTeamId, setViewingTeamId] = useState(null);
  // 团队选择模态框是否可见
  const [teamModalVisible, setTeamModalVisible] = useState(false);

  // 默认选项配置（无团队，单智能体模式）
  const defaultOption = {
    team_id: '',
    name: '单智能体模式',
    description: '不使用团队协作，仅使用单个智能体',
    agents: []
  };

  // 从localStorage获取保存的团队ID
  const getSavedTeamId = () => {
    try {
      return localStorage.getItem(TEAM_STORAGE_KEY);
    } catch (error) {
      console.error('从localStorage读取团队ID失败:', error);
      return null;
    }
  };

  // 保存团队ID到localStorage
  const saveTeamId = (teamId) => {
    try {
      if (teamId) {
        localStorage.setItem(TEAM_STORAGE_KEY, teamId);
      } else {
        localStorage.removeItem(TEAM_STORAGE_KEY);
      }
    } catch (error) {
      console.error('保存团队ID到localStorage失败:', error);
    }
  };

  // 获取所有团队信息
  const fetchTeams = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/teams');
      if (response.data && response.data.status === 'success') {
        setTeams(response.data.data || []);
      } else {
        message.error('获取团队列表失败');
      }
    } catch (error) {
      console.error('获取团队列表出错:', error);
      message.error('获取团队列表失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 查看团队详情
  const viewTeamDetails = async (teamId) => {
    if (!teamId) {
      message.info('单智能体模式无详细信息');
      return;
    }

    setViewingTeamId(teamId);
    
    try {
      const response = await axios.get(`/api/teams/${teamId}`);
      if (response.data && response.data.status === 'success') {
        setTeamDetail(response.data.data);
        setDetailModalVisible(true);
      } else {
        message.error('获取团队详情失败');
      }
    } catch (error) {
      console.error('获取团队详情出错:', error);
      message.error('获取团队详情失败: ' + (error.response?.data?.detail || error.message));
    }
  };

  // 关闭详情对话框
  const closeDetailModal = () => {
    setDetailModalVisible(false);
    setViewingTeamId(null);
  };

  // 处理选择变更
  const handleTeamSelect = (teamId) => {
    setSelectedTeam(teamId);
    setTeamModalVisible(false);
    // 保存到localStorage
    saveTeamId(teamId);
    if (onChange) {
      onChange(teamId);
    }
  };

  // 打开团队选择模态框
  const openTeamModal = () => {
    fetchTeams(); // 获取最新团队数据
    setTeamModalVisible(true);
  };

  // 关闭团队选择模态框
  const closeTeamModal = () => {
    setTeamModalVisible(false);
  };

  // 组件加载时获取团队列表
  useEffect(() => {
    fetchTeams();
  }, []);

  // 组件加载时从localStorage读取保存的团队ID
  useEffect(() => {
    const savedTeamId = getSavedTeamId();
    // 如果外部未提供有效value且localStorage有保存的值，则使用保存的值
    if ((!value || value === '') && savedTeamId) {
      console.log('[TeamSelector] 页面刷新恢复teamId:', savedTeamId);
      setSelectedTeam(savedTeamId);
      if (onChange) {
        onChange(savedTeamId);
      }
    } else {
      setSelectedTeam(value);
    }
  }, []);

  // 当外部value变化时，更新内部状态
  useEffect(() => {
    if (value !== selectedTeam) {
      setSelectedTeam(value);
      // 同步更新localStorage
      if (value) {
        saveTeamId(value);
      }
    }
  }, [value]);

  // 获取当前选中的团队信息
  const getCurrentTeam = () => {
    if (!selectedTeam) {
      return defaultOption;
    }
    return teams.find(team => team.team_id === selectedTeam) || defaultOption;
  };

  // 获取工作流类型的显示名称
  const getWorkflowTypeDisplay = (type) => {
    const typeMap = {
      'sequential': '顺序执行',
      'parallel': '并行执行',
      'conditional': '条件路由'
    };
    return typeMap[type] || type;
  };

  const currentTeam = getCurrentTeam();

  return (
    <div className={className}>
      <Button
        className="team-selector-button"
        onClick={openTeamModal}
        disabled={disabled}
        style={{ 
          display: 'flex', 
          alignItems: 'center',
          height: '32px',
          borderRadius: '4px',
          padding: className === 'home-team-selector' ? '0' : '4px 11px'
        }}
      >
        <TeamOutlined style={{ marginRight: 8, fontSize: 16 }} />
        <span className="team-button-text">
          {currentTeam.name || '选择团队'}
        </span>
        {currentTeam.agents && currentTeam.agents.length > 0 && (
          <Badge 
            count={currentTeam.agents.length} 
            style={{ 
              marginLeft: 8,
              backgroundColor: '#52c41a'
            }} 
          />
        )}
      </Button>

      {/* 团队选择模态框 */}
      <Modal
        title="选择团队"
        open={teamModalVisible}
        onCancel={closeTeamModal}
        footer={[
          <Button key="cancel" onClick={closeTeamModal}>
            取消
          </Button>
        ]}
        width={600}
        className="team-selector-modal"
        centered
      >
        <Spin spinning={loading}>
          <List
            className="team-list"
            itemLayout="horizontal"
            dataSource={[defaultOption, ...teams]}
            renderItem={team => (
              <List.Item
                className={`team-list-item ${(selectedTeam === team.team_id || (!selectedTeam && !team.team_id)) ? 'team-selected' : ''}`}
                onClick={() => handleTeamSelect(team.team_id)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      icon={<TeamOutlined />} 
                      size="large"
                      style={{ 
                        backgroundColor: team.team_id ? '#1890ff' : '#bfbfbf',
                        marginRight: 4 
                      }}
                    />
                  }
                  title={
                    <div className="team-title">
                      <span className="team-name">{team.name}</span>
                      {team.workflow_type && (
                        <span className="team-workflow-type">
                          {getWorkflowTypeDisplay(team.workflow_type)}
                        </span>
                      )}
                    </div>
                  }
                  description={
                    <div className="team-description">
                      <div className="team-desc-text">{team.description}</div>
                      {team.agents && team.agents.length > 0 && (
                        <div className="team-agents-info">
                          <TeamOutlined style={{ marginRight: 4 }} />
                          {team.agents.length} 个智能体
                        </div>
                      )}
                    </div>
                  }
                />
                <Space>
                  {team.team_id && (
                    <Button
                      type="link"
                      size="small"
                      className="team-detail-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        viewTeamDetails(team.team_id);
                      }}
                      icon={<InfoCircleOutlined />}
                    >
                      详情
                    </Button>
                  )}
                </Space>
              </List.Item>
            )}
          />
        </Spin>
      </Modal>
      
      <TeamDetailModal 
        visible={detailModalVisible}
        onClose={closeDetailModal}
        teamData={teamDetail}
      />
    </div>
  );
};

export default TeamSelector; 