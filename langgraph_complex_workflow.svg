<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .node { 
        fill: #lightblue; 
        stroke: #333; 
        stroke-width: 2; 
        rx: 8; 
      }
      .coordinator { fill: #lightcoral; }
      .agent { fill: #lightgreen; }
      .processor { fill: #lightyellow; }
      .output { fill: #lavender; }
      
      .node-text { 
        font-family: Arial, sans-serif; 
        font-size: 11px; 
        text-anchor: middle; 
        dominant-baseline: middle; 
        font-weight: bold;
      }
      .edge { 
        stroke: #666; 
        stroke-width: 2; 
        marker-end: url(#arrowhead); 
      }
      .conditional-edge {
        stroke: #ff6600;
        stroke-dasharray: 5,5;
      }
      .title { 
        font-family: Arial, sans-serif; 
        font-size: 20px; 
        font-weight: bold; 
        text-anchor: middle; 
      }
      .subtitle {
        font-family: Arial, sans-serif; 
        font-size: 14px; 
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="500" y="30" class="title">🚀 LangGraph 复杂多智能体工作流</text>
  
  <!-- 工作流协调器 -->
  <rect x="450" y="80" width="100" height="40" class="node coordinator"/>
  <text x="500" y="100" class="node-text">工作流协调器</text>
  
  <!-- 任务分配器 -->
  <rect x="450" y="160" width="100" height="40" class="node coordinator"/>
  <text x="500" y="180" class="node-text">任务分配器</text>
  
  <!-- 智能体层 -->
  <rect x="150" y="240" width="100" height="40" class="node agent"/>
  <text x="200" y="260" class="node-text">研究智能体</text>
  
  <rect x="300" y="240" width="100" height="40" class="node agent"/>
  <text x="350" y="260" class="node-text">分析智能体</text>
  
  <rect x="450" y="240" width="100" height="40" class="node agent"/>
  <text x="500" y="260" class="node-text">写作智能体</text>
  
  <rect x="600" y="240" width="100" height="40" class="node agent"/>
  <text x="650" y="260" class="node-text">评估智能体</text>
  
  <rect x="750" y="240" width="100" height="40" class="node agent"/>
  <text x="800" y="260" class="node-text">优化智能体</text>
  
  <!-- 并行处理器 -->
  <rect x="450" y="320" width="100" height="40" class="node processor"/>
  <text x="500" y="340" class="node-text">并行处理器</text>
  
  <!-- 状态检查器（条件节点） -->
  <rect x="450" y="400" width="100" height="40" class="node processor"/>
  <text x="500" y="420" class="node-text">状态检查器</text>
  
  <!-- 分支路径 -->
  <rect x="300" y="480" width="100" height="40" class="node processor"/>
  <text x="350" y="500" class="node-text">路径A处理</text>
  
  <rect x="600" y="480" width="100" height="40" class="node processor"/>
  <text x="650" y="500" class="node-text">路径B处理</text>
  
  <!-- 结果合并器 -->
  <rect x="450" y="560" width="100" height="40" class="node processor"/>
  <text x="500" y="580" class="node-text">结果合并器</text>
  
  <!-- 最终输出 -->
  <rect x="450" y="640" width="100" height="40" class="node output"/>
  <text x="500" y="660" class="node-text">最终输出</text>
  
  <!-- 连接线 -->
  <!-- 协调器到分配器 -->
  <line x1="500" y1="120" x2="500" y2="160" class="edge"/>
  
  <!-- 分配器到智能体 -->
  <line x1="500" y1="200" x2="200" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="350" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="500" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="650" y2="240" class="edge"/>
  <line x1="500" y1="200" x2="800" y2="240" class="edge"/>
  
  <!-- 智能体到处理器 -->
  <line x1="200" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="350" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="500" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="650" y1="280" x2="500" y2="320" class="edge"/>
  <line x1="800" y1="280" x2="500" y2="320" class="edge"/>
  
  <!-- 处理器到检查器 -->
  <line x1="500" y1="360" x2="500" y2="400" class="edge"/>
  
  <!-- 条件分支 -->
  <line x1="500" y1="440" x2="350" y2="480" class="edge conditional-edge"/>
  <line x1="500" y1="440" x2="650" y2="480" class="edge conditional-edge"/>
  
  <!-- 分支到合并器 -->
  <line x1="350" y1="520" x2="500" y2="560" class="edge"/>
  <line x1="650" y1="520" x2="500" y2="560" class="edge"/>
  
  <!-- 合并器到输出 -->
  <line x1="500" y1="600" x2="500" y2="640" class="edge"/>
  
  <!-- 图例 -->
  <rect x="50" y="80" width="150" height="200" fill="none" stroke="#ccc" stroke-width="1"/>
  <text x="125" y="100" class="subtitle">节点类型图例</text>
  
  <rect x="60" y="110" width="20" height="15" class="coordinator"/>
  <text x="90" y="120" style="font-size: 10px;">协调器/分配器</text>
  
  <rect x="60" y="130" width="20" height="15" class="agent"/>
  <text x="90" y="140" style="font-size: 10px;">智能体</text>
  
  <rect x="60" y="150" width="20" height="15" class="processor"/>
  <text x="90" y="160" style="font-size: 10px;">处理器</text>
  
  <rect x="60" y="170" width="20" height="15" class="output"/>
  <text x="90" y="180" style="font-size: 10px;">输出节点</text>
  
  <line x1="60" y1="200" x2="90" y2="200" class="edge"/>
  <text x="100" y="205" style="font-size: 10px;">固定边</text>
  
  <line x1="60" y1="220" x2="90" y2="220" class="conditional-edge"/>
  <text x="100" y="225" style="font-size: 10px;">条件边</text>
  
  <!-- 特性说明 -->
  <text x="50" y="320" style="font-size: 12px; font-weight: bold;">🔥 特色功能：</text>
  <text x="50" y="340" style="font-size: 10px;">✅ 多智能体并行处理</text>
  <text x="50" y="355" style="font-size: 10px;">✅ 条件分支路由</text>
  <text x="50" y="370" style="font-size: 10px;">✅ 智能结果合并</text>
  <text x="50" y="385" style="font-size: 10px;">✅ 错误恢复机制</text>
  <text x="50" y="400" style="font-size: 10px;">✅ 状态检查点</text>
  <text x="50" y="415" style="font-size: 10px;">✅ 性能监控</text>
  
  <!-- 时间戳 -->
  <text x="950" y="690" style="font-size: 8px; text-anchor: end;">
    Generated: 2025-06-29 21:53:30
  </text>
  
</svg>