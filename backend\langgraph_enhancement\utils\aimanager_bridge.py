# -*- coding: utf-8 -*-
"""
LangGraph 增强系统 - AIManager 桥接器

零修改复用现有 AIManager 系统
"""

import logging
from typing import Dict, List, Any, Optional, Union

logger = logging.getLogger(__name__)


class AIManagerBridge:
    """AIManager 桥接器 - 完全复用现有系统"""
    
    def __init__(self):
        """初始化 AIManager 桥接器"""
        try:
            from backend.ai.manager import AIManager
            self.ai_manager = AIManager()
            self.is_initialized = True
            logger.info("✅ AIManager 桥接器初始化成功")
        except ImportError as e:
            logger.error(f"❌ 导入 AIManager 失败: {str(e)}")
            self.ai_manager = None
            self.is_initialized = False
    
    async def chat_completion(self,
                            messages: List[Dict[str, str]],
                            model: Optional[str] = None,
                            selected_servers: Optional[List[str]] = None,
                            handle_tools: bool = True,
                            stream: bool = False,
                            **kwargs) -> Union[str, Dict[str, Any]]:
        """
        调用 AIManager 的聊天补全功能
        
        Args:
            messages: 消息列表
            model: 模型名称
            selected_servers: 选中的 MCP 服务器
            handle_tools: 是否处理工具调用
            stream: 是否流式输出
            **kwargs: 其他参数
        
        Returns:
            Union[str, Dict[str, Any]]: AI 响应内容或完整响应对象
        """
        if not self.is_initialized:
            logger.error("❌ AIManager 未初始化")
            return "AIManager 不可用"
        
        try:
            response = await self.ai_manager.chat_completion(
                messages=messages,
                model=model,
                selected_servers=selected_servers,
                handle_tools=handle_tools,
                stream=stream,
                **kwargs
            )
            
            logger.debug(f"✅ AI 响应成功，模型: {model or 'default'}")
            return response
            
        except Exception as e:
            logger.error(f"❌ AI 调用失败: {str(e)}")
            return f"AI 调用失败: {str(e)}"
    
    async def process_with_context(self,
                                 messages: List[Dict[str, str]],
                                 system_prompt: str,
                                 model: Optional[str] = None,
                                 selected_servers: Optional[List[str]] = None,
                                 **kwargs) -> str:
        """
        带系统提示词的处理
        
        Args:
            messages: 用户消息列表
            system_prompt: 系统提示词
            model: 模型名称
            selected_servers: MCP 服务器列表
            **kwargs: 其他参数
        
        Returns:
            str: AI 响应内容
        """
        # 构建完整消息列表
        full_messages = [{"role": "system", "content": system_prompt}]
        full_messages.extend(messages)
        
        # 调用 AI
        response = await self.chat_completion(
            messages=full_messages,
            model=model,
            selected_servers=selected_servers,
            handle_tools=True,
            **kwargs
        )
        
        # 提取响应内容
        if isinstance(response, dict):
            return response.get('content', str(response))
        return str(response)
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        if not self.is_initialized:
            return []
        
        try:
            # 从 AIManager 获取模型列表
            if hasattr(self.ai_manager, 'available_models'):
                return self.ai_manager.available_models
            else:
                # 返回默认模型列表
                return [
                    "gemini-2.5-flash-preview-05-20",
                    "gpt-4o",
                    "claude-3-5-sonnet-20241022"
                ]
        except Exception as e:
            logger.error(f"获取模型列表失败: {str(e)}")
            return []
    
    async def handle_tool_calls(self, tool_calls: List[Dict], selected_servers: List[str]) -> List[Dict]:
        """
        处理工具调用
        
        Args:
            tool_calls: 工具调用列表
            selected_servers: 选中的服务器
        
        Returns:
            List[Dict]: 工具执行结果
        """
        if not self.is_initialized:
            return []
        
        try:
            # 复用 AIManager 的工具处理逻辑
            if hasattr(self.ai_manager, 'handle_tool_calls'):
                return await self.ai_manager.handle_tool_calls(tool_calls, selected_servers)
            else:
                logger.warning("AIManager 没有 handle_tool_calls 方法")
                return []
        except Exception as e:
            logger.error(f"处理工具调用失败: {str(e)}")
            return []
    
    def validate_connection(self) -> bool:
        """验证 AIManager 连接是否正常"""
        return self.is_initialized and self.ai_manager is not None


# 全局 AIManager 桥接器实例
_ai_manager_bridge: Optional[AIManagerBridge] = None


def get_ai_manager_bridge() -> AIManagerBridge:
    """获取全局 AIManager 桥接器实例"""
    global _ai_manager_bridge
    if _ai_manager_bridge is None:
        _ai_manager_bridge = AIManagerBridge()
    return _ai_manager_bridge


# 便捷函数
async def langgraph_ai_completion(messages: List[Dict[str, str]],
                                model: Optional[str] = None,
                                selected_servers: Optional[List[str]] = None,
                                **kwargs) -> Union[str, Dict[str, Any]]:
    """便捷函数：LangGraph AI 补全"""
    bridge = get_ai_manager_bridge()
    return await bridge.chat_completion(
        messages=messages,
        model=model,
        selected_servers=selected_servers,
        **kwargs
    ) 