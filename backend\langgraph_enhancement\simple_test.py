#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 简化工作流基础测试
验证配置文件加载和基本功能
"""

import asyncio
import yaml
import os
from datetime import datetime

def test_config_loading():
    """测试配置文件加载"""
    print("🔍 测试配置文件加载...")
    
    try:
        # 测试agents.yaml
        with open('config/agents.yaml', 'r', encoding='utf-8') as f:
            agents_config = yaml.safe_load(f)
        
        agents = agents_config.get('agents', {})
        print(f"✅ agents.yaml 加载成功: {len(agents)} 个智能体")
        for agent_id in agents.keys():
            print(f"   - {agent_id}")
        
        # 测试teams.yaml 
        with open('config/teams.yaml', 'r', encoding='utf-8') as f:
            teams_config = yaml.safe_load(f)
        
        teams = teams_config.get('teams', {})
        print(f"✅ teams.yaml 加载成功: {len(teams)} 个团队")
        
        # 按工作流类型分组
        workflow_types = {}
        for team_id, team_config in teams.items():
            workflow_type = team_config.get('workflow_type', 'unknown')
            if workflow_type not in workflow_types:
                workflow_types[workflow_type] = []
            workflow_types[workflow_type].append(team_id)
        
        print(f"📊 工作流类型分布:")
        for workflow_type, team_list in workflow_types.items():
            print(f"   - {workflow_type}: {len(team_list)} 个团队")
            for team in team_list:
                print(f"     * {team}")
        
        return True, agents_config, teams_config
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False, None, None

def test_workflow_types(teams_config):
    """测试三种工作流类型的配置"""
    print("\n🧪 测试三种工作流类型配置...")
    
    test_teams = {
        "sequential": "market_intelligence_team",
        "parallel": "parallel_development_team", 
        "conditional": "intelligent_routing_team"
    }
    
    results = {}
    
    for workflow_type, team_name in test_teams.items():
        try:
            teams = teams_config.get('teams', {})
            team_config = teams.get(team_name)
            
            if not team_config:
                print(f"❌ {workflow_type} 团队 '{team_name}' 不存在")
                results[workflow_type] = False
                continue
            
            # 验证基本配置
            required_fields = ['name', 'description', 'agents', 'workflow_type']
            missing_fields = [field for field in required_fields if field not in team_config]
            
            if missing_fields:
                print(f"❌ {workflow_type} 团队缺少字段: {missing_fields}")
                results[workflow_type] = False
                continue
            
            # 验证工作流类型匹配
            actual_type = team_config.get('workflow_type')
            if actual_type != workflow_type:
                print(f"❌ {workflow_type} 团队类型不匹配: 期望{workflow_type}, 实际{actual_type}")
                results[workflow_type] = False
                continue
            
            # 验证智能体配置
            agents = team_config.get('agents', [])
            if not agents:
                print(f"❌ {workflow_type} 团队没有配置智能体")
                results[workflow_type] = False
                continue
            
            print(f"✅ {workflow_type.upper()} 模式 - {team_name}")
            print(f"   团队名称: {team_config.get('name')}")
            print(f"   智能体数量: {len(agents)}")
            print(f"   智能体列表: {agents}")
            
            # 特殊验证：conditional类型需要routing_conditions
            if workflow_type == "conditional":
                routing_conditions = team_config.get('routing_conditions')
                if routing_conditions:
                    print(f"   路由条件数量: {len(routing_conditions)}")
                else:
                    print(f"   ⚠️ 缺少路由条件配置")
            
            results[workflow_type] = True
            
        except Exception as e:
            print(f"❌ {workflow_type} 测试失败: {e}")
            results[workflow_type] = False
    
    return results

def test_agent_references(agents_config, teams_config):
    """测试智能体引用完整性"""
    print("\n🔗 测试智能体引用完整性...")
    
    available_agents = set(agents_config.get('agents', {}).keys())
    teams = teams_config.get('teams', {})
    
    all_valid = True
    
    for team_name, team_config in teams.items():
        team_agents = team_config.get('agents', [])
        
        for agent_id in team_agents:
            if agent_id not in available_agents:
                print(f"❌ 团队 '{team_name}' 引用了不存在的智能体: {agent_id}")
                all_valid = False
    
    if all_valid:
        print("✅ 所有智能体引用完整性检查通过")
    
    return all_valid

async def simulate_workflow_execution():
    """模拟工作流执行流程"""
    print("\n🎯 模拟工作流执行流程...")
    
    test_scenarios = [
        {
            "name": "Sequential 测试场景",
            "team": "market_intelligence_team",
            "input": "分析AI市场趋势并生成报告",
            "expected_workflow": "sequential"
        },
        {
            "name": "Parallel 测试场景", 
            "team": "parallel_development_team",
            "input": "开发用户注册系统",
            "expected_workflow": "parallel"
        },
        {
            "name": "Conditional 测试场景",
            "team": "intelligent_routing_team", 
            "input": "我需要开发Python代码",
            "expected_workflow": "conditional"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🧪 {scenario['name']}")
        print(f"   输入: {scenario['input']}")
        print(f"   目标团队: {scenario['team']}")
        print(f"   期望工作流: {scenario['expected_workflow']}")
        
        # 模拟工作流执行逻辑
        start_time = datetime.now()
        
        # 这里只是模拟，实际不调用真实的工作流引擎
        await asyncio.sleep(0.1)  # 模拟处理时间
        
        execution_time = (datetime.now() - start_time).total_seconds()
        print(f"   ✅ 模拟执行成功 - 耗时: {execution_time:.3f}s")

def generate_test_report(config_ok, workflow_results, reference_ok):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📋 基础功能测试报告")
    print("="*60)
    
    total_tests = 5  # 配置加载 + 3种工作流 + 引用完整性
    passed_tests = 0
    
    print(f"📁 配置文件加载: {'✅ 通过' if config_ok else '❌ 失败'}")
    if config_ok:
        passed_tests += 1
    
    for workflow_type, result in workflow_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"🔄 {workflow_type.upper()} 工作流: {status}")
        if result:
            passed_tests += 1
    
    print(f"🔗 智能体引用完整性: {'✅ 通过' if reference_ok else '❌ 失败'}")
    if reference_ok:
        passed_tests += 1
    
    success_rate = passed_tests / total_tests * 100
    print(f"\n🎯 测试结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 所有基础功能测试通过！工作流配置正确！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

async def main():
    """主测试函数"""
    print("🔥 JIMU 工作流基础功能测试")
    print("="*60)
    
    # 1. 测试配置加载
    config_ok, agents_config, teams_config = test_config_loading()
    if not config_ok:
        print("❌ 基础配置测试失败，终止测试")
        return False
    
    # 2. 测试工作流类型配置
    workflow_results = test_workflow_types(teams_config)
    
    # 3. 测试智能体引用完整性
    reference_ok = test_agent_references(agents_config, teams_config)
    
    # 4. 模拟工作流执行
    await simulate_workflow_execution()
    
    # 5. 生成测试报告
    success = generate_test_report(config_ok, workflow_results, reference_ok)
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n🎉 基础测试完成！")
            exit(0)
        else:
            print("\n❌ 基础测试失败！")
            exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n💥 测试程序异常: {e}")
        exit(1) 