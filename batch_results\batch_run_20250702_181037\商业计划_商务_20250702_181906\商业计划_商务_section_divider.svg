<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 颜色方案 (Color Palette) -->
    <style type="text/css"><![CDATA[
      /* 基础颜色 (Base Colors) */
      .bg-color { fill: #F8FAFC; } /* 背景色 */
      .primary-color { fill: #1E40AF; } /* 主色 */
      .secondary-color { fill: #475569; } /* 辅助色 */
      .accent-color { fill: #3B82F6; } /* 强调色 */
      .text-primary { fill: #1E293B; } /* 主要文字色 */
      .text-secondary { fill: #64748B; } /* 次要文字色 */
      .text-light { fill: #94A3B8; } /* 浅色文字 */
      .card-background { fill: #FFFFFF; } /* 卡片背景色 */
      .card-border { stroke: #BAE6FD; } /* 卡片边框色 */
      .container-background { fill: #E0F2FE; } /* 容器背景色 */

      /* 字体系统 (Font System) */
      /* 注意：为获得更好的兼容性，建议优先列出通用字体族或嵌入网络字体 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* 字体大小和样式 (Font Sizes and Styles) */
      .hero-title {
        font-size: 72px;
        font-weight: 700; /* bold */
        line-height: 1.1; /* tight */
        letter-spacing: 0em; /* normal */
        fill: #1E293B; /* text_primary */
      }

      .main-title {
        font-size: 56px;
        font-weight: 700;
        line-height: 1.1;
        fill: #1E293B;
      }

      .section-title {
        font-size: 36px;
        font-weight: 600; /* semibold */
        line-height: 1.4; /* normal */
        fill: #1E293B;
      }

      .content-title {
        font-size: 28px;
        font-weight: 500; /* medium */
        line-height: 1.4;
        fill: #1E293B;
      }

      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        line-height: 1.6; /* relaxed */
        fill: #64748B; /* text_secondary */
      }

      .small-text {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.4;
        fill: #64748B;
      }

      .caption-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.4;
        fill: #94A3B8; /* text_light */
      }

      /* 模板特定样式 (Template Specific Styles) */
      .chapter-number {
        font-size: 120px; /* 超大字体，强调核心要点 */
        font-weight: 900; /* black */
        fill: url(#gradientAccent); /* 使用强调色渐变 */
        opacity: 0.8; /* 略带透明度，制造科技感 */
      }

      .chapter-title-zh {
        font-size: 80px; /* 大型中文标题，粗体 */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }

      .chapter-subtitle-en {
        font-size: 32px; /* 英文副标题，小字点缀 */
        font-weight: 400; /* normal */
        fill: #475569; /* secondary_color */
        letter-spacing: 0.05em; /* wider */
        text-transform: uppercase;
      }

      .page-info-text {
        font-size: 20px;
        font-weight: 400;
        fill: #64748B;
      }

      /* 装饰元素样式 (Decorative Element Styles) */
      .deco-shape-primary { fill: #1E40AF; opacity: 0.15; }
      .deco-shape-secondary { fill: #475569; opacity: 0.1; }
      .deco-shape-accent { fill: #3B82F6; opacity: 0.2; }
      .deco-line { stroke: #BAE6FD; stroke-width: 2; opacity: 0.7; }
    ]]></style>

    <!-- 渐变定义 (Gradient Definitions) -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF" />
      <stop offset="100%" style="stop-color:#475569" />
    </linearGradient>

    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC" />
      <stop offset="100%" style="stop-color:#E0F2FE" />
    </linearGradient>

    <!-- 商务图标 (Business Icons) - 简洁勾线图形化 -->
    <!-- 注意：这些是填充路径，通过简单的形状模拟勾线风格。可根据需要替换为真实的描边图标。 -->
    <symbol id="icon-strategy" viewBox="0 0 24 24">
      <path fill="#3B82F6" d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 2.15l6.09 2.72L12 11.5l-6.09-5.63L12 3.15zM5 7.04l5.96 5.53C10.96 12.57 11 12.63 11 12.7v7.03C7.03 18.81 5 15.34 5 11.4V7.04zm7 14.77V12.7c0-.07.04-.13.04-.16L19 7.04v4.36c0 3.94-2.03 7.41-6 8.77z"/>
    </symbol>
    <symbol id="icon-chart" viewBox="0 0 24 24">
      <path fill="#3B82F6" d="M16 11V3c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v8c0 .55.45 1 1 1h6c.55 0 1-.45 1-1zM7 21h2v-7H7v7zm4 0h2v-4h-2v4zm4 0h2v-7h-2v7z"/>
    </symbol>
    <symbol id="icon-risk" viewBox="0 0 24 24">
      <path fill="#3B82F6" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
    </symbol>
  </defs>

  <!-- 背景 (Background) -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 装饰元素 - 几何形状，模仿Bento Grid风格和专业感 -->
  <!-- 左上角大型强调形状 -->
  <rect x="80" y="60" width="400" height="200" rx="20" ry="20" class="deco-shape-accent" />
  <rect x="120" y="90" width="300" height="150" rx="15" ry="15" class="deco-shape-primary" />

  <!-- 右下角微妙形状 -->
  <rect x="1440" y="800" width="400" height="200" rx="20" ry="20" class="deco-shape-secondary" />
  <rect x="1500" y="850" width="300" height="150" rx="15" ry="15" class="deco-shape-primary" />

  <!-- 中央抽象线条/路径，表示“流程”或“连接” -->
  <path d="M 0 540 H 1920" class="deco-line" stroke-dasharray="10 10" />
  <path d="M 960 0 V 1080" class="deco-line" stroke-dasharray="10 10" />

  <path d="M 120 400 Q 400 300 700 450 T 1200 600 Q 1500 750 1800 600" class="deco-line" fill="none" stroke="#3B82F6" stroke-width="3" opacity="0.3" />
  <circle cx="960" cy="540" r="150" fill="none" stroke="#1E40AF" stroke-width="2" opacity="0.15" />
  <circle cx="960" cy="540" r="200" fill="none" stroke="#475569" stroke-width="2" opacity="0.1" />

  <!-- 主要内容 - 居中显示，突出视觉冲击 -->
  <g class="font-primary" text-anchor="middle">
    <!-- 章节序号 (Chapter Number) - 超大视觉元素，强调重点 -->
    <text x="960" y="360" class="chapter-number">03</text>

    <!-- 中文章节标题 (Chinese Chapter Title) -->
    <text x="960" y="490" class="chapter-title-zh">{title}</text>
    <!-- 示例内容： 市场分析和定位 -->

    <!-- 英文副标题 (English Subtitle) -->
    <text x="960" y="550" class="chapter-subtitle-en">{subtitle}</text>
    <!-- 示例内容： MARKET ANALYSIS AND POSITIONING -->

    <!-- 图标占位符 (Icon Placeholders) - 简洁勾线图形化 -->
    <use xlink:href="#icon-chart" x="960" y="600" width="48" height="48" transform="translate(-80, -24)" />
    <use xlink:href="#icon-strategy" x="960" y="600" width="48" height="48" transform="translate(-24, -24)" />
    <use xlink:href="#icon-risk" x="960" y="600" width="48" height="48" transform="translate(32, -24)" />
  </g>

  <!-- 左下角页面信息 (Bottom-left Page Info) -->
  <text x="80" y="1020" class="font-secondary page-info-text text-light">
    <tspan x="80" y="1020">{date}</tspan>
    <!-- 示例内容： 2023年10月27日 -->
    <tspan x="80" y="1045">{author}</tspan>
    <!-- 示例内容： 项目团队 -->
  </text>

  <!-- 右下角页码 (Bottom-right Page Number) -->
  <text x="1840" y="1020" text-anchor="end" class="font-secondary page-info-text text-primary">
    {page_number}
    <!-- 示例内容： 3/10 -->
  </text>

  <!-- 左上角Logo占位符 (Top-left Logo Placeholder) -->
  <image x="80" y="60" width="160" height="80" preserveAspectRatio="xMidYMid meet" href="{logo_url}" />
  <!-- 示例内容： https://example.com/your_logo.png -->
</svg>