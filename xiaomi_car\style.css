/* General Styles */
body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-color: #f4f4f4;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4, h5, h6 {
    color: #333;
    margin-top: 0;
}

a {
    color: #ff6700; /* Xiaomi Orange */
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.btn {
    display: inline-block;
    background-color: #ff6700;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #e05c00;
    text-decoration: none;
}

/* Header & Navigation */
header {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 30px;
    margin-right: 10px;
}

.logo span {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: #ff6700;
}

/* Hero Section */
.hero-section {
    background: url('https://image.cache.autohome.com.cn/autohomepro/cms/2024/3/27/2024032717013328221_1200_0.jpg') no-repeat center center/cover;
    color: #fff;
    text-align: center;
    padding: 150px 20px;
    min-height: 600px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5); /* Overlay for readability */
}

.hero-section * {
    position: relative;
    z-index: 1;
}

.hero-section h1 {
    font-size: 3.5em;
    margin-bottom: 20px;
    color: #fff;
}

.hero-section p {
    font-size: 1.3em;
    margin-bottom: 30px;
    max-width: 700px;
}

/* Section General Styles */
section {
    padding: 80px 20px;
    text-align: center;
    background-color: #fff;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

section:nth-of-type(even) {
    background-color: #f9f9f9;
}

section h2 {
    font-size: 2.5em;
    margin-bottom: 50px;
    color: #333;
    position: relative;
    display: inline-block;
}

section h2::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #ff6700;
    border-radius: 2px;
}

/* Models Section */
.models-section .car-card {
    display: inline-block;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin: 20px;
    width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    vertical-align: top;
}

.models-section .car-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.models-section .car-card img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin-bottom: 20px;
}

.models-section .car-card h3 {
    font-size: 1.8em;
    color: #333;
    margin-bottom: 10px;
}

.models-section .car-card ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.models-section .car-card ul li {
    margin-bottom: 8px;
    font-size: 0.95em;
    color: #555;
}

/* Technology Section */
.tech-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 40px;
}

.tech-section .tech-item {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 30%;
    min-width: 280px;
    text-align: center;
    transition: transform 0.3s ease;
}

.tech-section .tech-item:hover {
    transform: translateY(-5px);
}

.tech-section .tech-item h3 {
    font-size: 1.6em;
    color: #ff6700;
    margin-bottom: 15px;
}

/* Charging Section */
.charging-section .charging-map {
    max-width: 80%;
    height: auto;
    margin: 40px auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* News Section */
.news-section .news-item {
    text-align: left;
    border-bottom: 1px solid #eee;
    padding: 20px 0;
}

.news-section .news-item:last-child {
    border-bottom: none;
}

.news-section .news-item h4 a {
    font-size: 1.3em;
    color: #333;
    transition: color 0.3s ease;
}

.news-section .news-item h4 a:hover {
    color: #ff6700;
}

.news-section .news-item p {
    color: #777;
    font-size: 0.9em;
}

/* Contact Section */
.contact-section p {
    margin-bottom: 10px;
}

.contact-section form {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.contact-section form input,
.contact-section form textarea {
    width: 100%;
    padding: 12px 15px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
    box-sizing: border-box;
}

.contact-section form textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-section form .btn {
    width: 100%;
    padding: 12px;
    font-size: 1.1em;
    cursor: pointer;
}

/* Footer */
footer {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 30px 20px;
    margin-top: 20px;
    border-radius: 8px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        align-items: flex-start;
    }

    nav ul {
        flex-direction: column;
        width: 100%;
        margin-top: 15px;
    }

    nav ul li {
        margin: 10px 0;
        text-align: center;
        width: 100%;
    }

    .hero-section h1 {
        font-size: 2.5em;
    }

    .hero-section p {
        font-size: 1em;
    }

    .models-section .car-card {
        width: 100%;
        margin: 20px 0;
    }

    .tech-section .tech-item {
        width: 100%;
    }

    section {
        padding: 60px 15px;
    }

    section h2 {
        font-size: 2em;
    }
}
