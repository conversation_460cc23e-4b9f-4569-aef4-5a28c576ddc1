# -*- coding: utf-8 -*-
"""
🎯 增强版相关性评估器 - 使用外部词典和专业分词库

核心改进：
- 集成jieba分词库
- 外部词典文件管理
- 动态词典加载
- 领域专业词汇支持
"""

import re
import json
import logging
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

# 尝试导入jieba，如果没有则回退到基础版本
try:
    import jieba
    import jieba.posseg as pseg
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("⚠️ jieba未安装，将使用基础分词。建议执行: pip install jieba")

logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """评估结果数据类"""
    result: str  # "quality_approved" 或 "quality_failed"
    score: float  # 相关性得分 0-1
    request_type: str  # 请求类型
    details: Dict  # 详细评分信息

class EnhancedRelevanceEvaluator:
    """
    🎯 增强版相关性评估器
    
    使用外部词典和专业分词库进行评估
    """
    
    def __init__(self, dict_path: str = None):
        """初始化评估器"""
        self.dict_path = dict_path or self._get_default_dict_path()
        
        # 加载外部词典
        self.dictionaries = self._load_dictionaries()
        
        # 初始化jieba（如果可用）
        if JIEBA_AVAILABLE:
            self._init_jieba()
        
        # 评估阈值配置
        self.thresholds = {
            "GREETING": 0.1,
            "SIMPLE_QUERY": 0.3,
            "TASK": 0.5,  # 降低阈值，因为jieba分词更准确
            "GENERAL": 0.3
        }
        
        logger.info(f"🎯 增强版相关性评估器初始化完成 (jieba: {'✅' if JIEBA_AVAILABLE else '❌'})")
    
    def _get_default_dict_path(self) -> str:
        """获取默认词典路径"""
        current_dir = Path(__file__).parent
        return str(current_dir / "dictionaries")
    
    def _load_dictionaries(self) -> Dict:
        """加载外部词典文件"""
        dictionaries = {}
        dict_path = Path(self.dict_path)
        
        try:
            # 加载各类词典文件
            dict_files = {
                "greeting_patterns": "greeting_patterns.json",
                "task_patterns": "task_patterns.json", 
                "simple_query_patterns": "simple_query_patterns.json",
                "action_verbs": "action_verbs.json",
                "stop_words": "stop_words.json",
                "domain_keywords": "domain_keywords.json"
            }
            
            for dict_name, filename in dict_files.items():
                file_path = dict_path / filename
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        dictionaries[dict_name] = json.load(f)
                    logger.info(f"📚 加载词典: {filename}")
                else:
                    # 使用默认词典
                    dictionaries[dict_name] = self._get_default_dict(dict_name)
                    logger.warning(f"⚠️ 词典文件不存在，使用默认: {filename}")
            
        except Exception as e:
            logger.error(f"❌ 加载词典失败: {e}")
            # 全部使用默认词典
            for dict_name in ["greeting_patterns", "task_patterns", "simple_query_patterns", 
                            "action_verbs", "stop_words", "domain_keywords"]:
                dictionaries[dict_name] = self._get_default_dict(dict_name)
        
        return dictionaries
    
    def _get_default_dict(self, dict_name: str) -> List:
        """获取默认词典（兜底方案）"""
        defaults = {
            "greeting_patterns": [
                "你好", "hello", "hi", "早上好", "下午好", "晚上好", 
                "怎么样", "好吗", "在吗", "在不在"
            ],
            "task_patterns": [
                "分析", "生成", "创建", "计算", "规划", "设计", "研究", 
                "写一个", "帮我", "制作", "开发", "实现", "搜索", "查找",
                "编写", "构建", "处理", "优化", "评估", "预测"
            ],
            "simple_query_patterns": [
                "是什么", "怎么", "为什么", "在哪里", "什么时候", "如何",
                "什么意思", "怎么办", "可以吗", "能够", "什么是"
            ],
            "action_verbs": [
                "分析", "研究", "计算", "生成", "创建", "设计", "规划", "制作",
                "开发", "实现", "搜索", "查找", "比较", "评估", "预测", "建议",
                "写", "做", "帮", "处理", "解决", "完成", "执行", "运行", "构建",
                "analyze", "research", "calculate", "generate", "create", "design",
                "develop", "implement", "search", "find", "compare", "evaluate",
                "write", "make", "help", "process", "solve", "complete", "execute"
            ],
            "stop_words": [
                "的", "了", "在", "是", "有", "和", "就", "都", "而", "及", 
                "与", "或", "但", "如果", "因为", "所以", "然后", "这", "那",
                "一个", "我", "你", "他", "她", "它", "我们", "你们", "他们",
                "a", "an", "the", "is", "are", "was", "were", "be", "been",
                "have", "has", "had", "do", "does", "did", "will", "would",
                "could", "should", "may", "might", "can", "to", "of", "in",
                "on", "at", "by", "for", "with", "from"
            ],
            "domain_keywords": {
                "technology": ["AI", "人工智能", "机器学习", "深度学习", "算法", "数据", "云计算"],
                "business": ["市场", "营销", "销售", "商业", "投资", "收益", "利润", "成本"],
                "finance": ["股票", "基金", "期货", "债券", "银行", "金融", "理财", "投资"],
                "travel": ["旅游", "路线", "景点", "酒店", "机票", "攻略", "行程", "目的地"]
            }
        }
        return defaults.get(dict_name, [])
    
    def _init_jieba(self):
        """初始化jieba分词器"""
        if not JIEBA_AVAILABLE:
            return
        
        try:
            # 添加自定义词典
            for category, words in self.dictionaries.get("domain_keywords", {}).items():
                for word in words:
                    jieba.add_word(word)
            
            # 添加任务词汇
            for word in self.dictionaries.get("task_patterns", []):
                jieba.add_word(word)
            
            logger.info("🔧 jieba分词器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ jieba初始化失败: {e}")
    
    def evaluate(self, user_request: str, agent_response: str) -> EvaluationResult:
        """
        主评估方法
        """
        try:
            # 第一步：处理边界情况
            edge_case_result = self._handle_edge_cases(user_request, agent_response)
            if edge_case_result:
                return edge_case_result
            
            # 第二步：识别请求类型
            request_type = self._classify_request(user_request)
            
            # 第三步：根据类型进行评估
            if request_type == "GREETING":
                return self._evaluate_greeting(user_request, agent_response)
            elif request_type == "SIMPLE_QUERY":
                return self._evaluate_simple_query(user_request, agent_response)
            elif request_type == "TASK":
                return self._evaluate_task(user_request, agent_response)
            else:  # GENERAL
                return self._evaluate_general(user_request, agent_response)
                
        except Exception as e:
            logger.error(f"❌ 评估过程异常: {e}")
            return EvaluationResult(
                result="quality_failed",
                score=0.0,
                request_type="ERROR",
                details={"error": str(e)}
            )
    
    def _handle_edge_cases(self, user_request: str, agent_response: str) -> EvaluationResult:
        """处理边界情况"""
        # 用户请求过短
        if len(user_request.strip()) < 2:
            if len(agent_response.strip()) > 0:
                return EvaluationResult("quality_approved", 1.0, "EMPTY_REQUEST", {"reason": "空请求有回应"})
            else:
                return EvaluationResult("quality_failed", 0.0, "EMPTY_REQUEST", {"reason": "空请求无回应"})
        
        # 智能体无回应
        if len(agent_response.strip()) < 5:
            return EvaluationResult("quality_failed", 0.0, "NO_RESPONSE", {"reason": "无有效回应"})
        
        # 明显的错误回应
        error_indicators = ["抱歉", "无法", "不知道", "错误", "失败", "sorry", "error", "failed"]
        if any(indicator in agent_response.lower() for indicator in error_indicators):
            return EvaluationResult("quality_failed", 0.0, "ERROR_RESPONSE", {"reason": "包含错误指示词"})
        
        return None
    
    def _classify_request(self, user_request: str) -> str:
        """分类用户请求类型"""
        request_lower = user_request.lower().strip()
        request_length = len(user_request.strip())
        
        # 使用外部词典进行分类
        greeting_patterns = self.dictionaries.get("greeting_patterns", [])
        if any(pattern in request_lower for pattern in greeting_patterns):
            return "GREETING"
        
        simple_query_patterns = self.dictionaries.get("simple_query_patterns", [])
        if (request_length < 30 and 
            any(pattern in request_lower for pattern in simple_query_patterns)):
            return "SIMPLE_QUERY"
        
        task_patterns = self.dictionaries.get("task_patterns", [])
        if any(pattern in request_lower for pattern in task_patterns):
            return "TASK"
        
        return "GENERAL"
    
    def _extract_keywords_with_jieba(self, text: str) -> Set[str]:
        """使用jieba提取关键词"""
        if not JIEBA_AVAILABLE:
            return self._extract_keywords_basic(text)
        
        try:
            # 使用jieba分词和词性标注
            words = pseg.cut(text)
            
            # 提取有意义的词性
            meaningful_pos = ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz', 'vn', 'an']
            keywords = set()
            
            for word, pos in words:
                # 过滤停用词和单字符
                if (len(word) > 1 and 
                    word not in self.dictionaries.get("stop_words", []) and
                    any(pos.startswith(p) for p in meaningful_pos)):
                    keywords.add(word)
            
            return keywords
            
        except Exception as e:
            logger.error(f"❌ jieba分词失败: {e}")
            return self._extract_keywords_basic(text)
    
    def _extract_keywords_basic(self, text: str) -> Set[str]:
        """基础关键词提取（兜底方案）"""
        # 中英文分词
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,}', text)
        english_words = re.findall(r'\b[a-zA-Z]{2,}\b', text.lower())
        numbers = re.findall(r'\d+', text)
        
        all_words = chinese_words + english_words + numbers
        
        # 过滤停用词
        stop_words = set(self.dictionaries.get("stop_words", []))
        keywords = {
            word for word in all_words 
            if len(word) > 1 and word not in stop_words
        }
        
        return keywords
    
    def _extract_action_verbs(self, text: str) -> Set[str]:
        """提取动作动词"""
        action_verbs = set(self.dictionaries.get("action_verbs", []))
        
        if JIEBA_AVAILABLE:
            # 使用jieba提取动词
            words = pseg.cut(text)
            found_verbs = set()
            
            for word, pos in words:
                if pos.startswith('v') and word in action_verbs:
                    found_verbs.add(word)
            
            return found_verbs
        else:
            # 基础方法
            words = re.findall(r'[\u4e00-\u9fff]+|\b\w+\b', text.lower())
            text_words = set(words)
            return text_words & action_verbs
    
    def _evaluate_greeting(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估问候类请求"""
        response_lower = agent_response.lower()
        
        # 检查礼貌回应
        polite_patterns = [
            "你好", "hello", "hi", "很高兴", "为您服务", "帮助", 
            "有什么", "需要", "可以", "欢迎"
        ]
        
        has_polite_response = any(pattern in response_lower for pattern in polite_patterns)
        is_over_response = len(agent_response) > 300
        
        if has_polite_response and not is_over_response:
            score = 0.9
            result = "quality_approved"
        elif has_polite_response:
            score = 0.6
            result = "quality_approved"
        else:
            score = 0.3
            result = "quality_approved"
        
        return EvaluationResult(
            result=result,
            score=score,
            request_type="GREETING",
            details={
                "has_polite_response": has_polite_response,
                "is_over_response": is_over_response,
                "response_length": len(agent_response)
            }
        )
    
    def _evaluate_simple_query(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估简单询问类请求"""
        # 使用增强的关键词提取
        user_keywords = self._extract_keywords_with_jieba(user_request)
        response_keywords = self._extract_keywords_with_jieba(agent_response)
        
        # 计算关键词重叠度
        overlap_score = self._calculate_keyword_overlap(user_keywords, response_keywords)
        
        # 检查回答长度适中性
        length_score = self._evaluate_response_length(user_request, agent_response, "SIMPLE_QUERY")
        
        # 综合评分
        final_score = overlap_score * 0.7 + length_score * 0.3
        
        result = "quality_approved" if final_score >= self.thresholds["SIMPLE_QUERY"] else "quality_failed"
        
        return EvaluationResult(
            result=result,
            score=final_score,
            request_type="SIMPLE_QUERY",
            details={
                "keyword_overlap": overlap_score,
                "length_score": length_score,
                "user_keywords": list(user_keywords),
                "common_keywords": list(user_keywords & response_keywords)
            }
        )
    
    def _evaluate_task(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估任务类请求"""
        return self._calculate_comprehensive_relevance(user_request, agent_response, "TASK")
    
    def _evaluate_general(self, user_request: str, agent_response: str) -> EvaluationResult:
        """评估一般类型请求"""
        return self._calculate_comprehensive_relevance(user_request, agent_response, "GENERAL")
    
    def _calculate_comprehensive_relevance(self, user_request: str, agent_response: str, request_type: str) -> EvaluationResult:
        """计算综合相关性（使用增强的分词）"""
        # 使用增强的关键词提取
        user_keywords = self._extract_keywords_with_jieba(user_request)
        response_keywords = self._extract_keywords_with_jieba(agent_response)
        topic_score = self._calculate_keyword_overlap(user_keywords, response_keywords)
        
        # 动词匹配
        user_verbs = self._extract_action_verbs(user_request)
        response_verbs = self._extract_action_verbs(agent_response)
        verb_score = self._calculate_keyword_overlap(user_verbs, response_verbs)
        
        # 实体和数字匹配
        user_entities = self._extract_entities(user_request)
        response_entities = self._extract_entities(agent_response)
        entity_score = self._calculate_keyword_overlap(user_entities, response_entities)
        
        user_numbers = self._extract_numbers(user_request)
        response_numbers = self._extract_numbers(agent_response)
        number_score = 1.0 if any(num in response_numbers for num in user_numbers) else 0.0
        
        # 计算加权总分
        final_score = (
            topic_score * 0.5 +      # 提高主题词权重
            verb_score * 0.2 + 
            entity_score * 0.2 + 
            number_score * 0.1
        )
        
        # 动态阈值调整
        threshold = self.thresholds[request_type]
        if topic_score >= 0.4:  # jieba分词更准确，降低阈值
            threshold = max(0.2, threshold - 0.1)
        
        result = "quality_approved" if final_score >= threshold else "quality_failed"
        
        return EvaluationResult(
            result=result,
            score=final_score,
            request_type=request_type,
            details={
                "topic_score": topic_score,
                "verb_score": verb_score,
                "entity_score": entity_score,
                "number_score": number_score,
                "threshold": threshold,
                "user_keywords": list(user_keywords),
                "common_keywords": list(user_keywords & response_keywords),
                "jieba_enabled": JIEBA_AVAILABLE
            }
        )
    
    def _extract_entities(self, text: str) -> Set[str]:
        """提取实体"""
        entities = set()
        
        # 大写开头的词
        capitalized_words = re.findall(r'\b[A-Z][a-z]+\b', text)
        entities.update(capitalized_words)
        
        # 年份和百分比
        years = re.findall(r'\b(19|20)\d{2}\b', text)
        percentages = re.findall(r'\d+%', text)
        entities.update(years + percentages)
        
        # 中文实体（使用jieba命名实体识别）
        if JIEBA_AVAILABLE:
            words = pseg.cut(text)
            for word, pos in words:
                if pos in ['nr', 'ns', 'nt', 'nz'] and len(word) > 1:  # 人名、地名、机构名等
                    entities.add(word)
        else:
            # 基础中文实体识别
            chinese_entities = re.findall(r'[\u4e00-\u9fff]{2,}', text)
            non_entities = {"什么", "怎么", "为什么", "在哪里", "什么时候", "如何", "可以", "需要", "帮助", "服务"}
            chinese_entities = [e for e in chinese_entities if e not in non_entities and len(e) >= 2]
            entities.update(chinese_entities)
        
        return entities
    
    def _extract_numbers(self, text: str) -> Set[str]:
        """提取数字信息"""
        numbers = set()
        
        # 整数、小数、百分比
        integers = re.findall(r'\b\d+\b', text)
        decimals = re.findall(r'\b\d+\.\d+\b', text)
        percentages = re.findall(r'\d+(?:\.\d+)?%', text)
        
        numbers.update(integers + decimals + percentages)
        return numbers
    
    def _calculate_keyword_overlap(self, set1: Set[str], set2: Set[str]) -> float:
        """计算关键词重叠度"""
        if not set1:
            return 1.0
        if not set2:
            return 0.0
        
        overlap = len(set1 & set2)
        return overlap / len(set1)
    
    def _evaluate_response_length(self, user_request: str, agent_response: str, request_type: str) -> float:
        """评估回答长度适中性"""
        request_length = len(user_request)
        response_length = len(agent_response)
        
        if request_type == "SIMPLE_QUERY":
            min_length = 20
            optimal_length = request_length * 2
            max_length = request_length * 5
        else:
            min_length = 50
            optimal_length = max(100, request_length * 3)
            max_length = request_length * 10
        
        if response_length < min_length:
            return 0.3
        elif response_length <= optimal_length:
            return 1.0
        elif response_length <= max_length:
            return 0.8
        else:
            return 0.5
    
    def create_default_dictionaries(self):
        """创建默认词典文件"""
        dict_path = Path(self.dict_path)
        dict_path.mkdir(exist_ok=True)
        
        # 创建所有词典文件
        for dict_name in ["greeting_patterns", "task_patterns", "simple_query_patterns", 
                         "action_verbs", "stop_words", "domain_keywords"]:
            file_path = dict_path / f"{dict_name}.json"
            if not file_path.exists():
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self._get_default_dict(dict_name), f, ensure_ascii=False, indent=2)
                logger.info(f"📝 创建默认词典: {file_path}")

# 全局实例
_enhanced_evaluator_instance = None

def get_enhanced_relevance_evaluator(dict_path: str = None) -> EnhancedRelevanceEvaluator:
    """获取增强版相关性评估器实例"""
    global _enhanced_evaluator_instance
    if _enhanced_evaluator_instance is None:
        _enhanced_evaluator_instance = EnhancedRelevanceEvaluator(dict_path)
    return _enhanced_evaluator_instance 