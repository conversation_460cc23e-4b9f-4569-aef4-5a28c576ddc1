<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors -->
    <style type="text/css"><![CDATA[
      /* Basic Color Palette */
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }

      /* Visual Elements Colors */
      .card-background { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; } /* Light blue for active/hover states */
      .hover-color { fill: #7DD3FC; } /* For hover state backgrounds */

      /* Gradients */
      .primary-gradient-fill { fill: url(#primaryGradient); }
      .accent-gradient-fill { fill: url(#accentGradient); }
      .text-gradient-fill { fill: url(#textGradient); }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Icon Styles */
      .icon-style { stroke: #4A86E8; stroke-width: 2; fill: none; stroke-linecap: round; stroke-linejoin: round; }

      /* Card Style - using CSS classes for properties */
      .card-base {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        rx: 12px;
        ry: 12px;
        filter: url(#cardShadow);
      }

      /* Hover Effects for Chapter Items (simulated for visual representation) */
      /* Note: SVG does not directly support :hover on <g> for fill changes without JS.
         These classes are for visual intent and would typically be applied via JS for true interactivity. */
      .chapter-item:hover .chapter-text { fill: #3B82F6; }
      .chapter-item:hover .chapter-number { fill: #3B82F6; }
      .chapter-item:hover .chapter-icon { stroke: #3B82F6; }
      .chapter-item:hover .chapter-card-bg { fill: #E0F2FE; } /* container background on hover */
    ]]></style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Card Shadow Filter -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon Definitions (simple outline icons for business context) -->
    <symbol id="icon-briefcase" viewBox="0 0 24 24">
      <rect x="2" y="7" width="20" height="14" rx="2" ry="2"/>
      <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
    </symbol>
    <symbol id="icon-info" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10"/>
      <line x1="12" y1="16" x2="12" y2="12"/>
      <line x1="12" y1="8" x2="12.01" y2="8"/>
    </symbol>
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10"/>
      <circle cx="12" cy="12" r="6"/>
      <circle cx="12" cy="12" r="2"/>
    </symbol>
    <symbol id="icon-pie-chart" viewBox="0 0 24 24">
      <path d="M21.21 15.89A10 10 0 1 1 8 2.83M22 12A10 10 0 0 0 12 2v10z"/>
    </symbol>
    <symbol id="icon-package" viewBox="0 0 24 24">
      <line x1="16.5" y1="9.4" x2="7.5" y2="4.2"/>
      <polyline points="2.5 7.6 12 13 21.5 7.6"/>
      <line x1="12" y1="22" x2="12" y2="13"/>
      <path d="M21.5 12.5v5.7c0 .8-.6 1.2-1.5 1.2h-16c-.9 0-1.5-.4-1.5-1.2v-5.7"/>
    </symbol>
    <symbol id="icon-trending-up" viewBox="0 0 24 24">
      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"/>
      <polyline points="17 6 23 6 23 12"/>
    </symbol>
    <symbol id="icon-shield" viewBox="0 0 24 24">
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
    </symbol>
    <symbol id="icon-dollar-sign" viewBox="0 0 24 24">
      <line x1="12" y1="1" x2="12" y2="23"/>
      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
    </symbol>
    <symbol id="icon-users" viewBox="0 0 24 24">
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
      <circle cx="9" cy="7" r="4"/>
      <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
      <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
    </symbol>
    <symbol id="icon-book" viewBox="0 0 24 24">
      <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20V2H6.5A2.5 2.5 0 0 0 4 4.5v15z"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color"/>

  <!-- Decorative elements - subtle geometric shapes and gradients -->
  <g opacity="0.08">
    <circle cx="1700" cy="100" r="150" class="primary-color"/>
    <rect x="1600" y="800" width="300" height="300" rx="40" ry="40" class="accent-color"/>
    <path d="M-50 900 C 100 1050, 400 1000, 500 850 L 500 1080 L -50 1080 Z" class="secondary-color"/>
    <rect x="80" y="-50" width="200" height="200" rx="30" ry="30" class="container-background"/>
  </g>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <!-- The actual logo would be an <image> tag with xlink:href="{logo_url}" -->
    <rect x="80" y="60" width="180" height="50" rx="8" ry="8" class="card-background card-border-color"/>
    <text x="170" y="95" text-anchor="middle" class="body-text text-primary font-primary">
      <tspan font-weight="700">企业</tspan> 徽标
    </text>

    <!-- Page Number and Progress Indicator -->
    <text x="1840" y="95" text-anchor="end" class="small-text text-light font-primary">
      页码: <tspan font-weight="600" class="text-secondary">2</tspan>/10
    </text>

    <!-- Progress Bar -->
    <rect x="1640" y="105" width="200" height="4" rx="2" ry="2" fill="#E0E7EB"/>
    <!-- Active progress segment -->
    <rect x="1640" y="105" width="40" height="4" rx="2" ry="2" class="accent-color"/>
  </g>

  <!-- Main Content Area -->
  <g id="main-content">
    <!-- Left Section: Title and Overview -->
    <g transform="translate(80, 180)">
      <text x="0" y="0" class="hero-title text-primary font-primary">
        <tspan>{title}</tspan>
      </text>
      <text x="0" y="80" class="section-title text-secondary font-primary">
        <tspan>{subtitle}</tspan>
      </text>
      <text x="0" y="150" class="body-text text-secondary font-secondary" style="white-space: pre-line;">
        <tspan x="0" dy="1.4em">本商业计划书旨在向潜在投资人、合作伙伴</tspan>
        <tspan x="0" dy="1.4em">以及其他利益相关者展示我们的愿景、策略</tspan>
        <tspan x="0" dy="1.4em">和增长潜力。它详细阐述了市场分析、财务</tspan>
        <tspan x="0" dy="1.4em">预测以及风险评估，为决策提供全面依据。</tspan>
      </text>

      <!-- Decorative divider line -->
      <rect x="0" y="400" width="600" height="2" rx="1" ry="1" class="text-light"/>

      <!-- Key Focus Points -->
      <text x="0" y="440" class="content-title text-primary font-primary">
        <tspan>核心关注点</tspan>
      </text>
      <g transform="translate(0, 480)" class="text-secondary body-text font-secondary">
        <use xlink:href="#icon-info" x="0" y="-18" width="32" height="32" class="icon-style"/>
        <text x="40" y="0">市场洞察和竞争优势</text>
        <use xlink:href="#icon-trending-up" x="0" y="42" width="32" height="32" class="icon-style"/>
        <text x="40" y="60">财务稳健性与增长潜力</text>
        <use xlink:href="#icon-shield" x="0" y="102" width="32" height="32" class="icon-style"/>
        <text x="40" y="120">风险管理和应对策略</text>
      </g>
    </g>

    <!-- Right Section: Chapter Navigation (Bento Grid inspired layout) -->
    <g id="chapter-navigation" transform="translate(800, 180)">
      <text x="0" y="0" class="section-title text-primary font-primary">
        <tspan>目录章节</tspan>
      </text>

      <!-- Chapter List -->
      <g class="body-text font-secondary">
        <!-- Chapter 1: 执行摘要 -->
        <g class="chapter-item" transform="translate(0, 60)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-briefcase" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">01.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">执行摘要</text>
          <text x="280" y="20" class="small-text text-light">Executive Summary</text>
        </g>
        <!-- Chapter 2: 公司简介 -->
        <g class="chapter-item" transform="translate(0, 130)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-info" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">02.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">公司简介</text>
          <text x="280" y="20" class="small-text text-light">Company Profile</text>
        </g>
        <!-- Chapter 3: 市场分析 -->
        <g class="chapter-item" transform="translate(0, 200)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-target" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">03.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">市场分析</text>
          <text x="280" y="20" class="small-text text-light">Market Analysis</text>
        </g>
        <!-- Chapter 4: 产品和服務 -->
        <g class="chapter-item" transform="translate(0, 270)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-package" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">04.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">产品和服務</text>
          <text x="280" y="20" class="small-text text-light">Products and Services</text>
        </g>
        <!-- Chapter 5: 营销策略 -->
        <g class="chapter-item" transform="translate(0, 340)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-pie-chart" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">05.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">营销策略</text>
          <text x="280" y="20" class="small-text text-light">Marketing Strategy</text>
        </g>
        <!-- Chapter 6: 管理团队 -->
        <g class="chapter-item" transform="translate(0, 410)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-users" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">06.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">管理团队</tspan>
          <text x="280" y="20" class="small-text text-light">Management Team</text>
        </g>
        <!-- Chapter 7: 财务预测 (Current Page Focus - Visually Highlighted) -->
        <g class="chapter-item" transform="translate(0, 480)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base" fill="#E0F2FE"/> <!-- Highlighted background -->
          <use xlink:href="#icon-dollar-sign" x="0" y="0" width="28" height="28" class="icon-style chapter-icon" stroke="#3B82F6"/>
          <text x="40" y="20" class="chapter-number" fill="#3B82F6">07.</text>
          <text x="90" y="20" class="chapter-text content-title" fill="#3B82F6">财务预测</text>
          <text x="280" y="20" class="small-text text-secondary">Financial Projections</text>
        </g>
        <!-- Chapter 8: 风险评估 -->
        <g class="chapter-item" transform="translate(0, 550)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-shield" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">08.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">风险评估</text>
          <text x="280" y="20" class="small-text text-light">Risk Assessment</text>
        </g>
        <!-- Chapter 9: 融资需求 -->
        <g class="chapter-item" transform="translate(0, 620)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-dollar-sign" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">09.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">融资需求</text>
          <text x="280" y="20" class="small-text text-light">Funding Request</text>
        </g>
        <!-- Chapter 10: 附录 -->
        <g class="chapter-item" transform="translate(0, 690)">
          <rect x="-20" y="-20" width="550" height="60" class="card-base chapter-card-bg"/>
          <use xlink:href="#icon-book" x="0" y="0" width="28" height="28" class="icon-style chapter-icon"/>
          <text x="40" y="20" class="chapter-number text-secondary">10.</text>
          <text x="90" y="20" class="chapter-text content-title text-primary">附录</text>
          <text x="280" y="20" class="small-text text-light">Appendix</text>
        </g>
      </g>
    </g>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1020" class="small-text text-secondary font-primary">
      {date} | {author}
    </text>
    <text x="1840" y="1020" text-anchor="end" class="small-text text-light font-primary">
      版权所有 © {date}
    </text>
  </g>

</svg>