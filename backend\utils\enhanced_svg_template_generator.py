#!/usr/bin/env python3
"""
增强版SVG模板生成器 - 支持多场景多风格组合
Enhanced SVG Template Generator - Multi-Scenario Multi-Style Support

功能特点：
- 支持13种应用场景（年中总结、教育培训、医学医疗等）
- 支持10种设计风格（简约、商务、科技、插画等）
- 统一蓝色系配色体系，层次丰富
- 大幅提升美观度和专业性
- 智能场景适配和风格融合
- 批量生成和质量控制

Created: 2025-07-02T14:28:12
Enhanced by: <PERSON> AI Assistant
"""

import asyncio
import json
import logging
import os
import re
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

# 导入必要的模块
from backend.ai.openai_service import OpenAIService
from backend.utils.prompts import PPT_SVG

# 配置日志
logger = logging.getLogger(__name__)

class ScenarioType(Enum):
    """应用场景枚举"""
    ANNUAL_SUMMARY = "年中总结"
    SUMMARY_REPORT = "总结汇报"
    EDUCATION_TRAINING = "教育培训"
    MEDICAL_HEALTHCARE = "医学医疗"
    MARKETING_PROMOTION = "营销推广"
    BUSINESS_PLAN = "商业计划"
    UNIVERSITY_ZONE = "高校专区"
    COMPANY_INTRO = "企业介绍"
    POLITICAL_PROMOTION = "党政宣传"
    SELF_INTRODUCTION = "自我介绍"
    ANALYSIS_REPORT = "分析报告"
    PRESS_CONFERENCE = "发布会"
    PUBLIC_WELFARE = "公益宣传"

class StyleType(Enum):
    """设计风格枚举"""
    MINIMALIST = "简约"
    BUSINESS = "商务"
    TECHNOLOGY = "科技"
    ILLUSTRATION = "插画"
    GOVERNMENT = "政务"
    DIFFUSION = "弥散"
    TRENDY = "潮流"
    CHINESE_STYLE = "国风"
    THREE_DIMENSIONAL = "立体"
    RETRO = "复古"

@dataclass
class BlueColorPalette:
    """蓝色系调色板"""
    # 主要蓝色
    primary_blue: str = "#4A86E8"
    
    # 深蓝色系
    deep_blue: str = "#1E3A8A"
    navy_blue: str = "#1E40AF"
    midnight_blue: str = "#1E293B"
    
    # 中蓝色系
    royal_blue: str = "#3B82F6"
    ocean_blue: str = "#0EA5E9"
    sky_blue: str = "#06B6D4"
    
    # 浅蓝色系
    light_blue: str = "#7DD3FC"
    powder_blue: str = "#BAE6FD"
    ice_blue: str = "#E0F2FE"
    
    # 渐变蓝色
    gradient_start: str = "#3B82F6"
    gradient_end: str = "#1E40AF"
    
    # 辅助色（基于蓝色的变化）
    blue_gray: str = "#64748B"
    steel_blue: str = "#475569"
    
    # 背景色
    background_light: str = "#F8FAFC"
    background_dark: str = "#0F172A"
    
    # 文字色
    text_primary: str = "#1E293B"
    text_secondary: str = "#64748B"
    text_light: str = "#94A3B8"
    
    def get_palette_for_style(self, style: StyleType) -> Dict[str, str]:
        """根据风格获取适配的调色板"""
        base_palette = {
            "primary": self.primary_blue,
            "secondary": self.royal_blue,
            "accent": self.ocean_blue,
            "background": self.background_light,
            "text_primary": self.text_primary,
            "text_secondary": self.text_secondary
        }
        
        # 根据不同风格调整色彩倾向
        style_adjustments = {
            StyleType.MINIMALIST: {
                "primary": self.royal_blue,
                "secondary": self.light_blue,
                "accent": self.powder_blue
            },
            StyleType.BUSINESS: {
                "primary": self.navy_blue,
                "secondary": self.steel_blue,
                "accent": self.royal_blue
            },
            StyleType.TECHNOLOGY: {
                "primary": self.ocean_blue,
                "secondary": self.sky_blue,
                "accent": self.light_blue
            },
            StyleType.GOVERNMENT: {
                "primary": self.deep_blue,
                "secondary": self.navy_blue,
                "accent": self.royal_blue
            },
            StyleType.DIFFUSION: {
                "primary": self.gradient_start,
                "secondary": self.gradient_end,
                "accent": self.sky_blue
            }
        }
        
        if style in style_adjustments:
            base_palette.update(style_adjustments[style])
        
        return base_palette

@dataclass 
class ScenarioConfig:
    """场景配置"""
    scenario_type: ScenarioType
    display_name: str
    description: str
    visual_characteristics: Dict[str, Any]
    content_focus: List[str]
    target_audience: str
    tone: str  # formal, casual, professional, creative, etc.
    
    @classmethod
    def get_all_scenarios(cls) -> Dict[ScenarioType, 'ScenarioConfig']:
        """获取所有场景配置"""
        return {
            ScenarioType.ANNUAL_SUMMARY: cls(
                scenario_type=ScenarioType.ANNUAL_SUMMARY,
                display_name="年中总结",
                description="年度或半年度工作总结汇报",
                visual_characteristics={
                    "emphasis_on": "数据展示、成果突出、时间线",
                    "layout_style": "正式规范",
                    "decorative_elements": "数据图表、进度条、里程碑"
                },
                content_focus=["成果展示", "数据分析", "未来规划"],
                target_audience="领导层、同事",
                tone="professional"
            ),
            ScenarioType.SUMMARY_REPORT: cls(
                scenario_type=ScenarioType.SUMMARY_REPORT,
                display_name="总结汇报",
                description="项目或工作阶段性总结报告",
                visual_characteristics={
                    "emphasis_on": "逻辑清晰、重点突出",
                    "layout_style": "结构化布局",
                    "decorative_elements": "图表、要点标记、流程图"
                },
                content_focus=["执行情况", "问题分析", "改进建议"],
                target_audience="管理层、团队",
                tone="formal"
            ),
            ScenarioType.EDUCATION_TRAINING: cls(
                scenario_type=ScenarioType.EDUCATION_TRAINING,
                display_name="教育培训",
                description="教学、培训、知识分享类演示",
                visual_characteristics={
                    "emphasis_on": "易读易懂、互动友好",
                    "layout_style": "清晰明了",
                    "decorative_elements": "教学图标、知识点标记、进度指示"
                },
                content_focus=["知识传递", "技能培养", "互动参与"],
                target_audience="学生、学员",
                tone="educational"
            ),
            ScenarioType.MEDICAL_HEALTHCARE: cls(
                scenario_type=ScenarioType.MEDICAL_HEALTHCARE,
                display_name="医学医疗",
                description="医疗健康、学术研究、临床应用",
                visual_characteristics={
                    "emphasis_on": "严谨准确、专业权威",
                    "layout_style": "学术规范",
                    "decorative_elements": "医疗图标、数据图表、流程图"
                },
                content_focus=["临床数据", "研究成果", "治疗方案"],
                target_audience="医护人员、研究者",
                tone="scientific"
            ),
            ScenarioType.MARKETING_PROMOTION: cls(
                scenario_type=ScenarioType.MARKETING_PROMOTION,
                display_name="营销推广",
                description="产品推广、市场营销、品牌宣传",
                visual_characteristics={
                    "emphasis_on": "吸引眼球、情感共鸣",
                    "layout_style": "创意灵活",
                    "decorative_elements": "品牌元素、吸引图形、强调标记"
                },
                content_focus=["产品特色", "市场优势", "用户价值"],
                target_audience="潜在客户、市场",
                tone="persuasive"
            ),
            ScenarioType.BUSINESS_PLAN: cls(
                scenario_type=ScenarioType.BUSINESS_PLAN,
                display_name="商业计划",
                description="商业计划书、投资提案、战略规划",
                visual_characteristics={
                    "emphasis_on": "逻辑严密、数据支撑",
                    "layout_style": "商务正式",
                    "decorative_elements": "商业图表、财务数据、流程图"
                },
                content_focus=["市场分析", "财务预测", "风险评估"],
                target_audience="投资人、合作伙伴",
                tone="professional"
            ),
            ScenarioType.UNIVERSITY_ZONE: cls(
                scenario_type=ScenarioType.UNIVERSITY_ZONE,
                display_name="高校专区",
                description="学术会议、论文发表、学术交流",
                visual_characteristics={
                    "emphasis_on": "学术严谨、知识权威",
                    "layout_style": "学术规范",
                    "decorative_elements": "学术图标、引用标记、公式图表"
                },
                content_focus=["研究成果", "理论分析", "学术贡献"],
                target_audience="学者、研究人员",
                tone="academic"
            ),
            ScenarioType.COMPANY_INTRO: cls(
                scenario_type=ScenarioType.COMPANY_INTRO,
                display_name="企业介绍",
                description="公司简介、企业文化、团队展示",
                visual_characteristics={
                    "emphasis_on": "品牌形象、企业文化",
                    "layout_style": "品牌一致性",
                    "decorative_elements": "企业标识、团队照片、文化元素"
                },
                content_focus=["企业优势", "团队实力", "发展历程"],
                target_audience="客户、合作伙伴",
                tone="corporate"
            ),
            ScenarioType.POLITICAL_PROMOTION: cls(
                scenario_type=ScenarioType.POLITICAL_PROMOTION,
                display_name="党政宣传",
                description="政策宣传、党建工作、政府报告",
                visual_characteristics={
                    "emphasis_on": "庄重正式、权威可信",
                    "layout_style": "正式规范",
                    "decorative_elements": "党政元素、旗帜图案、正式标识"
                },
                content_focus=["政策解读", "工作成果", "发展规划"],
                target_audience="党员干部、公众",
                tone="official"
            ),
            ScenarioType.SELF_INTRODUCTION: cls(
                scenario_type=ScenarioType.SELF_INTRODUCTION,
                display_name="自我介绍",
                description="个人简历、求职面试、个人展示",
                visual_characteristics={
                    "emphasis_on": "个人特色、能力突出",
                    "layout_style": "个性化设计",
                    "decorative_elements": "个人照片、技能图标、成就标记"
                },
                content_focus=["个人经历", "专业技能", "个人优势"],
                target_audience="面试官、同事",
                tone="personal"
            ),
            ScenarioType.ANALYSIS_REPORT: cls(
                scenario_type=ScenarioType.ANALYSIS_REPORT,
                display_name="分析报告",
                description="数据分析、市场研究、调研报告",
                visual_characteristics={
                    "emphasis_on": "数据可视化、逻辑分析",
                    "layout_style": "分析导向",
                    "decorative_elements": "数据图表、分析工具、统计元素"
                },
                content_focus=["数据洞察", "趋势分析", "结论建议"],
                target_audience="决策者、分析师",
                tone="analytical"
            ),
            ScenarioType.PRESS_CONFERENCE: cls(
                scenario_type=ScenarioType.PRESS_CONFERENCE,
                display_name="发布会",
                description="产品发布、新闻发布、重要公告",
                visual_characteristics={
                    "emphasis_on": "重点突出、媒体友好",
                    "layout_style": "发布会格式",
                    "decorative_elements": "产品图片、Logo标识、发布元素"
                },
                content_focus=["产品亮点", "重要信息", "媒体关注点"],
                target_audience="媒体、公众",
                tone="announcement"
            ),
            ScenarioType.PUBLIC_WELFARE: cls(
                scenario_type=ScenarioType.PUBLIC_WELFARE,
                display_name="公益宣传",
                description="公益活动、社会责任、慈善宣传",
                visual_characteristics={
                    "emphasis_on": "温暖人心、正能量",
                    "layout_style": "情感导向",
                    "decorative_elements": "公益图标、爱心元素、温暖色调"
                },
                content_focus=["社会价值", "公益意义", "参与方式"],
                target_audience="公众、志愿者",
                tone="humanitarian"
            )
        }

@dataclass
class StyleConfig:
    """风格配置"""
    style_type: StyleType
    display_name: str
    description: str
    design_principles: Dict[str, Any]
    visual_elements: Dict[str, Any]
    typography: Dict[str, Any]
    
    @classmethod
    def get_all_styles(cls) -> Dict[StyleType, 'StyleConfig']:
        """获取所有风格配置"""
        return {
            StyleType.MINIMALIST: cls(
                style_type=StyleType.MINIMALIST,
                display_name="简约",
                description="简洁干净，注重留白和清晰度",
                design_principles={
                    "layout": "大量留白、网格化布局",
                    "elements": "几何形状、简单线条",
                    "emphasis": "功能性、可读性"
                },
                visual_elements={
                    "shapes": "圆形、方形、简单几何",
                    "lines": "细线条、清晰边界",
                    "decorations": "最少装饰、点缀性元素"
                },
                typography={
                    "font_style": "无衬线字体",
                    "weight": "中等粗细",
                    "spacing": "宽松间距"
                }
            ),
            StyleType.BUSINESS: cls(
                style_type=StyleType.BUSINESS,
                display_name="商务",
                description="专业正式，体现权威和可信度",
                design_principles={
                    "layout": "规范布局、对称平衡",
                    "elements": "正式图形、商务图标",
                    "emphasis": "专业性、权威性"
                },
                visual_elements={
                    "shapes": "矩形、稳重几何形",
                    "lines": "直线、规整边框",
                    "decorations": "商务图标、数据图表"
                },
                typography={
                    "font_style": "正式字体",
                    "weight": "中粗",
                    "spacing": "标准间距"
                }
            ),
            StyleType.TECHNOLOGY: cls(
                style_type=StyleType.TECHNOLOGY,
                display_name="科技",
                description="现代科技感，体现创新和未来",
                design_principles={
                    "layout": "动态布局、科技感排版",
                    "elements": "科技图形、未来元素",
                    "emphasis": "创新性、未来感"
                },
                visual_elements={
                    "shapes": "六边形、菱形、科技图形",
                    "lines": "渐变线条、科技线框",
                    "decorations": "电路图案、科技图标、光效"
                },
                typography={
                    "font_style": "现代字体",
                    "weight": "中等",
                    "spacing": "紧凑间距"
                }
            ),
            StyleType.ILLUSTRATION: cls(
                style_type=StyleType.ILLUSTRATION,
                display_name="插画",
                description="插画风格，生动有趣，富有创意",
                design_principles={
                    "layout": "自由布局、创意排版",
                    "elements": "插画元素、手绘感",
                    "emphasis": "创意性、趣味性"
                },
                visual_elements={
                    "shapes": "有机形状、不规则图形",
                    "lines": "手绘线条、流畅曲线",
                    "decorations": "插画元素、装饰图案、图标"
                },
                typography={
                    "font_style": "创意字体",
                    "weight": "多样化",
                    "spacing": "灵活间距"
                }
            ),
            StyleType.GOVERNMENT: cls(
                style_type=StyleType.GOVERNMENT,
                display_name="政务",
                description="庄重正式，体现权威和规范",
                design_principles={
                    "layout": "严谨布局、正式格式",
                    "elements": "正式图形、官方元素",
                    "emphasis": "权威性、正式性"
                },
                visual_elements={
                    "shapes": "规整图形、正式边框",
                    "lines": "稳重线条、规范边界",
                    "decorations": "官方图标、正式标识"
                },
                typography={
                    "font_style": "正式字体",
                    "weight": "粗体",
                    "spacing": "规范间距"
                }
            ),
            StyleType.DIFFUSION: cls(
                style_type=StyleType.DIFFUSION,
                display_name="弥散",
                description="柔和渐变，温和的视觉效果",
                design_principles={
                    "layout": "流动布局、柔和过渡",
                    "elements": "渐变元素、柔和图形",
                    "emphasis": "柔和性、流动性"
                },
                visual_elements={
                    "shapes": "柔和圆形、渐变图形",
                    "lines": "柔和边缘、模糊边界",
                    "decorations": "渐变背景、柔光效果"
                },
                typography={
                    "font_style": "柔和字体",
                    "weight": "轻薄",
                    "spacing": "舒适间距"
                }
            ),
            StyleType.TRENDY: cls(
                style_type=StyleType.TRENDY,
                display_name="潮流",
                description="时尚前卫，跟随最新设计趋势",
                design_principles={
                    "layout": "动态布局、创新排版",
                    "elements": "时尚元素、流行图形",
                    "emphasis": "时尚性、创新性"
                },
                visual_elements={
                    "shapes": "不规则形状、动态图形",
                    "lines": "动感线条、流行元素",
                    "decorations": "时尚图标、流行装饰"
                },
                typography={
                    "font_style": "时尚字体",
                    "weight": "变化丰富",
                    "spacing": "动态间距"
                }
            ),
            StyleType.CHINESE_STYLE: cls(
                style_type=StyleType.CHINESE_STYLE,
                display_name="国风",
                description="中国传统文化元素，古典雅致",
                design_principles={
                    "layout": "对称布局、传统格式",
                    "elements": "传统图案、文化元素",
                    "emphasis": "文化性、雅致性"
                },
                visual_elements={
                    "shapes": "传统图形、文化符号",
                    "lines": "书法线条、传统边框",
                    "decorations": "中式图案、文化装饰"
                },
                typography={
                    "font_style": "书法字体、传统字体",
                    "weight": "典雅",
                    "spacing": "传统间距"
                }
            ),
            StyleType.THREE_DIMENSIONAL: cls(
                style_type=StyleType.THREE_DIMENSIONAL,
                display_name="立体",
                description="3D效果，立体感强",
                design_principles={
                    "layout": "层次布局、空间感",
                    "elements": "立体图形、3D效果",
                    "emphasis": "空间感、层次感"
                },
                visual_elements={
                    "shapes": "立体图形、3D造型",
                    "lines": "立体边缘、阴影线",
                    "decorations": "阴影效果、立体装饰"
                },
                typography={
                    "font_style": "立体字体",
                    "weight": "有立体感",
                    "spacing": "层次间距"
                }
            ),
            StyleType.RETRO: cls(
                style_type=StyleType.RETRO,
                display_name="复古",
                description="怀旧风格，经典元素",
                design_principles={
                    "layout": "经典布局、怀旧格式",
                    "elements": "复古图形、怀旧元素",
                    "emphasis": "怀旧感、经典性"
                },
                visual_elements={
                    "shapes": "经典图形、复古造型",
                    "lines": "复古线条、经典边框",
                    "decorations": "怀旧图案、复古装饰"
                },
                typography={
                    "font_style": "复古字体",
                    "weight": "经典粗细",
                    "spacing": "复古间距"
                }
            )
        }

@dataclass
class EnhancedTemplateConfig:
    """增强版模板配置"""
    scenario: ScenarioType
    style: StyleType
    canvas_width: int = 1920
    canvas_height: int = 1080
    
    # AI生成配置
    model: str = "gemini-2.5-flash"
    temperature: float = 0.7
    max_retries: int = 3
    
    # 美观度增强配置
    enable_advanced_aesthetics: bool = True
    enable_color_harmony: bool = True
    enable_typography_optimization: bool = True
    enable_layout_enhancement: bool = True

class ScenarioStyleMatrix:
    """场景风格矩阵管理器"""
    
    def __init__(self):
        self.scenarios = ScenarioConfig.get_all_scenarios()
        self.styles = StyleConfig.get_all_styles()
        self.color_palette = BlueColorPalette()
    
    def get_optimized_combination(self, scenario: ScenarioType, style: StyleType) -> Dict[str, Any]:
        """获取优化的场景风格组合配置"""
        scenario_config = self.scenarios[scenario]
        style_config = self.styles[style]
        
        # 获取适配的调色板
        colors = self.color_palette.get_palette_for_style(style)
        
        # 生成组合配置
        combination = {
            "scenario": asdict(scenario_config),
            "style": asdict(style_config),
            "colors": colors,
            "fusion_rules": self._get_fusion_rules(scenario, style),
            "aesthetic_enhancements": self._get_aesthetic_enhancements(scenario, style)
        }
        
        return combination
    
    def _get_fusion_rules(self, scenario: ScenarioType, style: StyleType) -> Dict[str, Any]:
        """获取场景风格融合规则"""
        # 基于场景和风格的特点，定义融合规则
        fusion_rules = {
            "layout_priority": "scenario",  # scenario 或 style 主导
            "color_adaptation": "balanced",  # style_dominant, scenario_dominant, balanced
            "typography_blend": "harmonious",
            "visual_emphasis": []
        }
        
        # 特殊组合的融合规则
        special_combinations = {
            (ScenarioType.MEDICAL_HEALTHCARE, StyleType.MINIMALIST): {
                "layout_priority": "scenario",
                "visual_emphasis": ["数据准确性", "简洁专业", "易读性"]
            },
            (ScenarioType.MARKETING_PROMOTION, StyleType.TRENDY): {
                "layout_priority": "style",
                "visual_emphasis": ["视觉冲击", "品牌突出", "创意表达"]
            },
            (ScenarioType.POLITICAL_PROMOTION, StyleType.GOVERNMENT): {
                "layout_priority": "scenario",
                "visual_emphasis": ["权威感", "正式性", "可信度"]
            },
            (ScenarioType.EDUCATION_TRAINING, StyleType.ILLUSTRATION): {
                "layout_priority": "balanced",
                "visual_emphasis": ["趣味性", "易理解", "互动性"]
            }
        }
        
        combination_key = (scenario, style)
        if combination_key in special_combinations:
            fusion_rules.update(special_combinations[combination_key])
        
        return fusion_rules
    
    def _get_aesthetic_enhancements(self, scenario: ScenarioType, style: StyleType) -> Dict[str, Any]:
        """获取美学增强配置"""
        return {
            "typography_refinements": {
                "font_pairing": "complementary",
                "hierarchy_enhancement": True,
                "readability_optimization": True
            },
            "layout_improvements": {
                "golden_ratio_application": True,
                "visual_balance_optimization": True,
                "spacing_harmonization": True
            },
            "color_harmony": {
                "contrast_optimization": True,
                "accessibility_compliance": True,
                "emotional_resonance": True
            },
            "visual_elements": {
                "icon_style_consistency": True,
                "decorative_element_coordination": True,
                "brand_element_integration": True
            }
        }

class TemplateAestheticsEnhancer:
    """模板美观度增强器"""
    
    def __init__(self, color_palette: BlueColorPalette):
        self.color_palette = color_palette
    
    def enhance_design_specification(self, base_spec: Dict[str, Any], 
                                   scenario: ScenarioType, 
                                   style: StyleType) -> Dict[str, Any]:
        """增强设计规范的美观度"""
        enhanced_spec = base_spec.copy()
        
        # 增强颜色规范
        enhanced_spec["color_palette"] = self._enhance_color_system(
            base_spec.get("color_palette", {}), style
        )
        
        # 增强字体系统
        enhanced_spec["typography_system"] = self._enhance_typography_system(
            base_spec.get("typography_system", {}), scenario, style
        )
        
        # 增强布局原则
        enhanced_spec["layout_principles"] = self._enhance_layout_principles(
            base_spec.get("layout_principles", {}), scenario, style
        )
        
        # 增强视觉元素
        enhanced_spec["visual_elements"] = self._enhance_visual_elements(
            base_spec.get("visual_elements", {}), scenario, style
        )
        
        return enhanced_spec
    
    def _enhance_color_system(self, base_colors: Dict[str, str], style: StyleType) -> Dict[str, str]:
        """增强色彩系统"""
        style_palette = self.color_palette.get_palette_for_style(style)
        
        enhanced_colors = {
            # 基础色彩
            "primary_color": style_palette["primary"],
            "secondary_color": style_palette["secondary"],
            "accent_color": style_palette["accent"],
            "background_color": style_palette["background"],
            
            # 文字色彩
            "text_primary": self.color_palette.text_primary,
            "text_secondary": self.color_palette.text_secondary,
            "text_light": self.color_palette.text_light,
            
            # 功能色彩
            "success_color": "#10B981",  # 绿色的蓝调变体
            "warning_color": "#F59E0B",  # 橙色的蓝调变体
            "error_color": "#EF4444",    # 红色的蓝调变体
            "info_color": style_palette["accent"],
            
            # 渐变色彩
            "gradient_primary": f"linear-gradient(135deg, {style_palette['primary']}, {style_palette['secondary']})",
            "gradient_accent": f"linear-gradient(45deg, {style_palette['accent']}, {style_palette['primary']})",
            
            # 卡片和容器色彩
            "card_background": "#FFFFFF",
            "card_border": self.color_palette.powder_blue,
            "container_background": self.color_palette.ice_blue,
            
            # 交互色彩
            "hover_color": self.color_palette.light_blue,
            "active_color": style_palette["primary"],
            "disabled_color": self.color_palette.blue_gray
        }
        
        return enhanced_colors
    
    def _enhance_typography_system(self, base_typography: Dict[str, Any], 
                                 scenario: ScenarioType, 
                                 style: StyleType) -> Dict[str, Any]:
        """增强字体系统"""
        # 根据场景和风格选择最佳字体组合
        font_selections = {
            StyleType.MINIMALIST: {
                "primary_font": "Inter, Helvetica, Arial, sans-serif",
                "secondary_font": "SF Pro Display, system-ui, sans-serif",
                "accent_font": "Poppins, sans-serif"
            },
            StyleType.BUSINESS: {
                "primary_font": "Microsoft YaHei, Segoe UI, sans-serif",
                "secondary_font": "Source Han Sans CN, Noto Sans CJK SC, sans-serif",
                "accent_font": "Times New Roman, serif"
            },
            StyleType.TECHNOLOGY: {
                "primary_font": "JetBrains Mono, Roboto, sans-serif",
                "secondary_font": "Fira Code, Monaco, monospace",
                "accent_font": "Orbitron, futura, sans-serif"
            },
            StyleType.CHINESE_STYLE: {
                "primary_font": "Source Han Serif CN, 思源宋体, serif",
                "secondary_font": "Ma Shan Zheng, 马善政楷书, cursive",
                "accent_font": "Zhi Mang Xing, 志芒星, cursive"
            }
        }
        
        selected_fonts = font_selections.get(style, font_selections[StyleType.BUSINESS])
        
        enhanced_typography = {
            "primary_font": selected_fonts["primary_font"],
            "secondary_font": selected_fonts["secondary_font"],
            "accent_font": selected_fonts["accent_font"],
            
            "font_sizes": {
                "hero_title": 72,      # 主标题
                "main_title": 56,      # 页面标题
                "section_title": 36,   # 章节标题
                "content_title": 28,   # 内容标题
                "body_text": 22,       # 正文
                "small_text": 16,      # 小字
                "caption": 14          # 说明文字
            },
            
            "font_weights": {
                "thin": 100,
                "light": 300,
                "normal": 400,
                "medium": 500,
                "semibold": 600,
                "bold": 700,
                "black": 900
            },
            
            "line_heights": {
                "tight": 1.1,
                "normal": 1.4,
                "relaxed": 1.6,
                "loose": 1.8
            },
            
            "letter_spacing": {
                "tight": "-0.025em",
                "normal": "0em",
                "wide": "0.025em",
                "wider": "0.05em"
            }
        }
        
        return enhanced_typography
    
    def _enhance_layout_principles(self, base_layout: Dict[str, Any], 
                                 scenario: ScenarioType, 
                                 style: StyleType) -> Dict[str, Any]:
        """增强布局原则"""
        enhanced_layout = {
            "canvas_size": {"width": 1920, "height": 1080},
            
            # 黄金比例应用
            "golden_ratio": 1.618,
            "grid_system": {
                "columns": 12,
                "gutter": 24,
                "margin": 80
            },
            
            # 间距系统（基于8px网格）
            "spacing": {
                "xs": 4,
                "sm": 8,
                "md": 16,
                "lg": 24,
                "xl": 32,
                "2xl": 48,
                "3xl": 64,
                "4xl": 96
            },
            
            # 页面边距
            "page_margins": {
                "horizontal": 80,
                "vertical": 60,
                "content_max_width": 1760
            },
            
            # 内容间距
            "content_spacing": {
                "module_gap": 32,
                "section_gap": 48,
                "element_gap": 16,
                "text_gap": 12
            },
            
            # 视觉层次
            "visual_hierarchy": {
                "z_index_background": 0,
                "z_index_content": 10,
                "z_index_overlay": 20,
                "z_index_modal": 30
            },
            
            # 对齐系统
            "alignment": {
                "text_align": "left",
                "content_align": "center",
                "title_align": "center"
            }
        }
        
        # 根据风格调整布局
        style_layout_adjustments = {
            StyleType.MINIMALIST: {
                "spacing_multiplier": 1.5,  # 更多留白
                "content_density": "sparse"
            },
            StyleType.BUSINESS: {
                "spacing_multiplier": 1.0,  # 标准间距
                "content_density": "balanced"
            },
            StyleType.TECHNOLOGY: {
                "spacing_multiplier": 0.8,  # 紧凑布局
                "content_density": "dense"
            }
        }
        
        if style in style_layout_adjustments:
            adjustments = style_layout_adjustments[style]
            multiplier = adjustments.get("spacing_multiplier", 1.0)
            
            # 应用间距调整
            for key in enhanced_layout["spacing"]:
                enhanced_layout["spacing"][key] = int(enhanced_layout["spacing"][key] * multiplier)
        
        return enhanced_layout
    
    def _enhance_visual_elements(self, base_elements: Dict[str, Any], 
                               scenario: ScenarioType, 
                               style: StyleType) -> Dict[str, Any]:
        """增强视觉元素"""
        enhanced_elements = {
            # 卡片样式
            "card_style": {
                "background": "#FFFFFF",
                "border_radius": 12,
                "border": f"1px solid {self.color_palette.powder_blue}",
                "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
            },
            
            # 装饰元素
            "decorative_elements": [
                "几何图形装饰",
                "渐变背景",
                "图标点缀",
                "分割线",
                "阴影效果"
            ],
            
            # 渐变定义
            "gradients": {
                "primary_gradient": f"linear-gradient(135deg, {self.color_palette.primary_blue}, {self.color_palette.royal_blue})",
                "accent_gradient": f"linear-gradient(45deg, {self.color_palette.ocean_blue}, {self.color_palette.sky_blue})",
                "background_gradient": f"linear-gradient(180deg, {self.color_palette.background_light}, {self.color_palette.ice_blue})",
                "text_gradient": f"linear-gradient(135deg, {self.color_palette.deep_blue}, {self.color_palette.navy_blue})"
            },
            
            # 图片效果
            "image_effects": {
                "border_radius": 8,
                "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)",
                "overlay_style": f"linear-gradient(135deg, {self.color_palette.primary_blue}20, {self.color_palette.royal_blue}20)",
                "hover_transform": "scale(1.02)",
                "filter_effects": "brightness(1.05) contrast(1.05)"
            },
            
            # Logo效果
            "logo_effects": {
                "positioning": "左上角或页面顶部中央",
                "size_guidelines": "最大高度80px，宽度自适应",
                "background_treatment": "透明背景或浅色背景",
                "shadow": "subtle drop shadow",
                "hover_effect": "slight glow"
            },
            
            # 图标系统
            "icon_system": {
                "style": "outline",  # outline, filled, duotone
                "stroke_width": 2,
                "size_sm": 16,
                "size_md": 24,
                "size_lg": 32,
                "color": self.color_palette.primary_blue
            },
            
            # 分割元素
            "dividers": {
                "horizontal_line": f"1px solid {self.color_palette.powder_blue}",
                "vertical_line": f"1px solid {self.color_palette.powder_blue}",
                "decorative_divider": "波浪线或装饰性分割",
                "gradient_divider": f"linear-gradient(90deg, transparent, {self.color_palette.light_blue}, transparent)"
            }
        }
        
        # 根据风格调整视觉元素
        style_visual_adjustments = {
            StyleType.MINIMALIST: {
                "card_style": {"border_radius": 0, "shadow": "none"},
                "decorative_elements": ["简单线条", "几何形状"]
            },
            StyleType.TECHNOLOGY: {
                "card_style": {"border_radius": 4, "border": f"1px solid {self.color_palette.ocean_blue}"},
                "decorative_elements": ["电路图案", "科技线框", "发光效果"]
            },
            StyleType.CHINESE_STYLE: {
                "card_style": {"border_radius": 0, "border": f"2px solid {self.color_palette.deep_blue}"},
                "decorative_elements": ["传统图案", "中式边框", "书法元素"]
            }
        }
        
        if style in style_visual_adjustments:
            adjustments = style_visual_adjustments[style]
            for key, value in adjustments.items():
                if key in enhanced_elements:
                    if isinstance(enhanced_elements[key], dict):
                        enhanced_elements[key].update(value)
                    else:
                        enhanced_elements[key] = value
        
        return enhanced_elements

class EnhancedSVGTemplateGenerator:
    """增强版SVG模板生成器
    
    支持13种应用场景和10种设计风格的组合生成
    统一蓝色系配色，大幅提升美观度和专业性
    """
    
    def __init__(self, config: Optional[EnhancedTemplateConfig] = None):
        """初始化增强版生成器
        
        Args:
            config: 增强版模板配置，为None时使用默认配置
        """
        self.config = config or EnhancedTemplateConfig(
            scenario=ScenarioType.BUSINESS_PLAN,
            style=StyleType.BUSINESS
        )
        
        # 初始化核心组件
        self.ai_service = OpenAIService()
        self.ai_service.model = self.config.model
        
        self.matrix = ScenarioStyleMatrix()
        self.aesthetics_enhancer = TemplateAestheticsEnhancer(BlueColorPalette())
        
        # 内部状态
        self.combination_config = None
        self.enhanced_design_spec = None
        self.generated_templates = []
        self.current_stage = "initial"
        
        # 设置保存路径
        self.templates_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            "resources", "svg_templates"
        )
        os.makedirs(self.templates_dir, exist_ok=True)
        
        # 10种基础模板类型（保持与原版一致）
        self.template_types = [
            {
                "id": "cover",
                "type": "封面页",
                "title": "封面模板",
                "description": "主标题 + 副标题 + logo + 装饰元素",
                "page_number": 1,
                "content_focus": "标题展示",
                "visual_priority": "品牌识别"
            },
            {
                "id": "agenda",
                "type": "目录页", 
                "title": "目录模板",
                "description": "内容概览 + 章节导航 + 进度指示",
                "page_number": 2,
                "content_focus": "结构导航",
                "visual_priority": "层级清晰"
            },
            {
                "id": "section_divider",
                "type": "章节分隔页",
                "title": "章节分隔模板", 
                "description": "章节标题 + 装饰背景 + 过渡设计",
                "page_number": 3,
                "content_focus": "章节切换",
                "visual_priority": "视觉冲击"
            },
            {
                "id": "title_content",
                "type": "标题内容页",
                "title": "标题内容模板",
                "description": "页面标题 + 主要内容 + 要点列表",
                "page_number": 4,
                "content_focus": "信息传达",
                "visual_priority": "内容层次"
            },
            {
                "id": "image_text",
                "type": "图文混排页",
                "title": "图文混排模板",
                "description": "图片展示 + 文字说明 + 图文平衡",
                "page_number": 5,
                "content_focus": "图文结合",
                "visual_priority": "视觉平衡"
            },
            {
                "id": "data_display",
                "type": "数据展示页",
                "title": "数据展示模板",
                "description": "图表区域 + 数据卡片 + 统计信息",
                "page_number": 6,
                "content_focus": "数据可视化", 
                "visual_priority": "数据突出"
            },
            {
                "id": "comparison",
                "type": "对比分析页",
                "title": "对比分析模板",
                "description": "左右对比 + 差异展示 + 结论总结",
                "page_number": 7,
                "content_focus": "对比分析",
                "visual_priority": "对比清晰"
            },
            {
                "id": "timeline",
                "type": "时间线页",
                "title": "时间线模板", 
                "description": "时间轴 + 里程碑 + 发展历程",
                "page_number": 8,
                "content_focus": "时间流程",
                "visual_priority": "流程连贯"
            },
            {
                "id": "quote",
                "type": "引用页",
                "title": "引用模板",
                "description": "重要引用 + 来源信息 + 视觉强调",
                "page_number": 9,
                "content_focus": "重点强调",
                "visual_priority": "情感共鸣"
            },
            {
                "id": "conclusion",
                "type": "结尾页",
                "title": "结尾模板",
                "description": "总结要点 + 行动呼吁 + 联系信息",
                "page_number": 10,
                "content_focus": "总结呼吁",
                "visual_priority": "行动导向"
            }
        ]
        
        logger.info(f"增强版SVG模板生成器初始化完成 - 场景: {self.config.scenario.value}, 风格: {self.config.style.value}")
    
    async def analyze_scenario_style_combination(self) -> Dict[str, Any]:
        """分析场景风格组合，生成优化配置"""
        try:
            logger.info(f"🔍 分析场景风格组合: {self.config.scenario.value} × {self.config.style.value}")
            self.current_stage = "analyzing_combination"
            
            # 获取优化的组合配置
            self.combination_config = self.matrix.get_optimized_combination(
                self.config.scenario, 
                self.config.style
            )
            
            # 记录分析结果
            analysis_result = {
                "success": True,
                "scenario_info": self.combination_config["scenario"],
                "style_info": self.combination_config["style"],
                "color_palette": self.combination_config["colors"],
                "fusion_rules": self.combination_config["fusion_rules"],
                "aesthetic_enhancements": self.combination_config["aesthetic_enhancements"],
                "compatibility_score": self._calculate_compatibility_score(),
                "recommendations": self._generate_optimization_recommendations()
            }
            
            logger.info("✅ 场景风格组合分析完成")
            return analysis_result
            
        except Exception as e:
            error_msg = f"场景风格组合分析失败: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    def _calculate_compatibility_score(self) -> float:
        """计算场景风格兼容性评分"""
        # 基于场景和风格特征计算兼容性评分（0-1）
        scenario = self.config.scenario
        style = self.config.style
        
        # 兼容性矩阵（简化版，实际可以更复杂）
        compatibility_matrix = {
            ScenarioType.BUSINESS_PLAN: {
                StyleType.BUSINESS: 0.95,
                StyleType.MINIMALIST: 0.85,
                StyleType.TECHNOLOGY: 0.75,
                StyleType.GOVERNMENT: 0.70
            },
            ScenarioType.MEDICAL_HEALTHCARE: {
                StyleType.MINIMALIST: 0.90,
                StyleType.BUSINESS: 0.85,
                StyleType.TECHNOLOGY: 0.80
            },
            ScenarioType.EDUCATION_TRAINING: {
                StyleType.ILLUSTRATION: 0.95,
                StyleType.MINIMALIST: 0.80,
                StyleType.TRENDY: 0.75
            },
            ScenarioType.MARKETING_PROMOTION: {
                StyleType.TRENDY: 0.95,
                StyleType.ILLUSTRATION: 0.85,
                StyleType.THREE_DIMENSIONAL: 0.80
            }
        }
        
        # 获取评分，默认为0.6
        score = compatibility_matrix.get(scenario, {}).get(style, 0.6)
        return score
    
    def _generate_optimization_recommendations(self) -> List[str]:
        """生成优化建议"""
        scenario = self.config.scenario
        style = self.config.style
        compatibility_score = self._calculate_compatibility_score()
        
        recommendations = []
        
        if compatibility_score < 0.7:
            recommendations.append("当前场景风格组合兼容性较低，建议考虑调整风格选择")
        
        # 基于场景的建议
        scenario_recommendations = {
            ScenarioType.MEDICAL_HEALTHCARE: [
                "建议使用简洁的数据可视化",
                "保持专业和权威的视觉风格",
                "确保医疗术语的准确性"
            ],
            ScenarioType.EDUCATION_TRAINING: [
                "增加互动元素和视觉提示",
                "使用清晰的层次结构",
                "考虑添加进度指示器"
            ],
            ScenarioType.MARKETING_PROMOTION: [
                "强化品牌元素的视觉冲击力",
                "使用吸引人的色彩和图形",
                "突出产品或服务的优势"
            ]
        }
        
        if scenario in scenario_recommendations:
            recommendations.extend(scenario_recommendations[scenario])
        
        # 基于风格的建议
        style_recommendations = {
            StyleType.MINIMALIST: [
                "充分利用留白空间",
                "保持元素的简洁性",
                "注重信息的层次感"
            ],
            StyleType.TECHNOLOGY: [
                "融入科技感的视觉元素",
                "使用现代感的图标和线条",
                "考虑添加动效暗示"
            ]
        }
        
        if style in style_recommendations:
            recommendations.extend(style_recommendations[style])
        
        return recommendations
    
    async def generate_enhanced_design_specification(self, content_context: str = "智能场景风格组合模板") -> Dict[str, Any]:
        """生成增强的设计规范"""
        try:
            logger.info("🎨 生成增强设计规范...")
            self.current_stage = "enhanced_design_spec"
            
            # 确保已有组合配置
            if not self.combination_config:
                await self.analyze_scenario_style_combination()
            
            # 构建增强设计规范提示词
            design_prompt = self._build_enhanced_design_prompt(content_context)
            
            # 调用AI生成基础设计规范
            messages = [{"role": "user", "content": design_prompt}]
            response = await self.ai_service.chat_completion(
                messages=messages,
                temperature=self.config.temperature
            )
            
            if "error" in response:
                logger.error(f"设计规范生成失败: {response['error']}")
                return {"success": False, "error": response["error"]}
            
            # 解析基础设计规范
            base_spec = self._parse_design_specification(response["content"])
            if not base_spec:
                return {"success": False, "error": "设计规范解析失败"}
            
            # 应用美学增强
            if self.config.enable_advanced_aesthetics:
                enhanced_spec = self.aesthetics_enhancer.enhance_design_specification(
                    base_spec, self.config.scenario, self.config.style
                )
                self.enhanced_design_spec = enhanced_spec
            else:
                self.enhanced_design_spec = base_spec
            
            logger.info("✅ 增强设计规范生成完成")
            
            return {
                "success": True,
                "enhanced_design_spec": self.enhanced_design_spec,
                "base_design_spec": base_spec,
                "enhancement_applied": self.config.enable_advanced_aesthetics,
                "compatibility_score": self._calculate_compatibility_score(),
                "message": "增强设计规范生成成功"
            }
            
        except Exception as e:
            error_msg = f"生成增强设计规范异常: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    def _build_enhanced_design_prompt(self, content_context: str) -> str:
        """构建增强版设计规范生成提示词"""
        
        # 基础提示词
        base_prompt = PPT_SVG
        
        # 场景风格信息
        scenario_info = self.combination_config["scenario"]
        style_info = self.combination_config["style"]
        colors = self.combination_config["colors"]
        fusion_rules = self.combination_config["fusion_rules"]
        
        # 构建详细的设计指导
        enhanced_prompt = f"""{base_prompt}

## 增强版多场景多风格SVG模板设计规范生成器

### 任务目标
为黑底红高亮风格的10张SVG模板生成一套增强的设计规范，确保：
1. 完美融合选定的应用场景和设计风格
2. 黑底特斯拉红高亮的现代对比配色
3. 大幅提升的美观度和专业性
4. 智能适配不同内容类型

### 当前场景风格组合
**应用场景**: {scenario_info['display_name']}
- 描述: {scenario_info['description']}
- 内容焦点: {', '.join(scenario_info['content_focus'])}
- 目标受众: {scenario_info['target_audience']}
- 语调风格: {scenario_info['tone']}

**设计风格**: {style_info['display_name']}
- 描述: {style_info['description']}
- 设计原则: {style_info['design_principles']['emphasis']}
- 布局特点: {style_info['design_principles']['layout']}

### 融合规则
- 布局优先级: {fusion_rules['layout_priority']}
- 色彩适配策略: {fusion_rules['color_adaptation']}
- 视觉强调重点: {', '.join(fusion_rules.get('visual_emphasis', []))}

### 现代高对比配色方案
**主要色彩**:
- 背景色: #000000 (纯黑色背景)
- 高亮色: #E31937 (特斯拉红色)
- 主色: {colors['primary']} (蓝色系主色)
- 辅助色: {colors['secondary']}
- 文字色: #FFFFFF (白色) / #F5F5F7 (浅灰白)
- 次要文字色: #86868B (浅灰色)

### 内容上下文
{content_context}

### 增强要求
1. **Bento Grid设计**: 使用类似Apple官网的Bento Grid布局，黑底红高亮
2. **超大字体对比**: 使用超大字体/数字突出重点，创造视觉层次和冲击力
3. **中英文混排**: 中文使用大号粗体，英文作为小号点缀，增强国际化视觉效果
4. **简洁勾线图形**: 使用简约的线条图形作为数据可视化和配图元素
5. **渐变科技感**: 特斯拉红色透明度渐变创造科技感，避免不同颜色互相渐变
6. **场景适配**: 确保设计完全符合"{scenario_info['display_name']}"场景的需求和特点
7. **风格融合**: 充分体现"{style_info['display_name']}"风格的设计语言和视觉特征

### 特殊设计指导
- **Bento Grid布局**: 使用类似Apple官网的网格布局，不同大小的区块组合
- **黑底红高亮**: 纯黑色背景(#000000)配合特斯拉红色(#E31937)作为高亮
- **超大字体设计**: 使用超大字号(120px+)的数字或文字作为视觉焦点
- **中英文混排**: 中文使用大号粗体，英文使用小号字体作为点缀和细节
- **简洁线条图形**: 使用简约的线条图形作为数据可视化和配图元素
- **透明度渐变**: 特斯拉红色自身透明度渐变创造科技感，避免多色渐变
- **动效设计理念**: 模仿Apple官网的动效设计，考虑滚动时的视觉变化
- **图表组件**: 可引用在线图表组件，确保样式与整体设计一致
- 支持高清图片：使用SVG的<image>标签，支持外部URL和本地路径
- Logo整合：为品牌logo预留专门区域，确保视觉和谐
- 渐变背景：巧妙使用蓝色系渐变，增强视觉层次
- 图标系统：统一的图标风格，符合场景和风格特点
- 装饰元素：根据风格特点添加合适的装饰元素

### 输出要求
请按以下JSON格式输出完整的增强设计规范：

```json
{{
    "theme_selection": {{
        "primary_style": "{style_info['display_name']}风格",
        "scenario_adaptation": "{scenario_info['display_name']}场景优化",
        "visual_theme": "基于蓝色系的{style_info['display_name']}风格{scenario_info['display_name']}模板",
        "design_philosophy": "结合{scenario_info['target_audience']}需求的{style_info['description']}设计理念",
        "fusion_strategy": "{fusion_rules['layout_priority']}优先的场景风格融合"
    }},
    "color_palette": {{
        "background_color": "#000000",
        "highlight_color": "#E31937",
        "highlight_transparent": "rgba(227, 25, 55, 0.7)",
        "highlight_gradient_start": "#E31937",
        "highlight_gradient_end": "rgba(227, 25, 55, 0.3)",
        "primary_color": "{colors['primary']}",
        "secondary_color": "{colors['secondary']}",
        "text_primary": "#FFFFFF",
        "text_secondary": "#86868B",
        "text_subtle": "#F5F5F7",
        "success_color": "#10B981",
        "warning_color": "#F59E0B",
        "error_color": "#EF4444",
        "card_background": "#0D0D0D",
        "card_border": "#1D1D1F",
        "hover_color": "#E31937"
    }},
    "layout_principles": {{
        "canvas_size": {{"width": {self.config.canvas_width}, "height": {self.config.canvas_height}}},
        "golden_ratio_application": true,
        "grid_system": {{"columns": 12, "gutter": 24, "margin": 80}},
        "page_margins": {{"horizontal": 80, "vertical": 60}},
        "content_spacing": {{"module_gap": 32, "section_gap": 48, "element_gap": 16}},
        "visual_hierarchy": "清晰的层次结构，符合{scenario_info['display_name']}场景需求",
        "alignment_system": "基于{style_info['display_name']}风格的对齐原则"
    }},
    "typography_system": {{
        "primary_font": "针对{scenario_info['display_name']}优化的主字体",
        "font_sizes": {{
            "hero_title": 120,
            "main_title": 72,
            "section_title": 48,
            "content_title": 32,
            "body_text": 24,
            "small_text": 16,
            "accent_number": 180
        }},
        "font_weights": {{
            "title": "bold",
            "content": "normal",
            "emphasis": "600",
            "chinese": "bold",
            "english": "300"
        }},
        "line_heights": {{
            "title": 1.1,
            "content": 1.5,
            "dense": 1.3
        }},
        "mixed_typography": {{
            "chinese_style": "大号粗体",
            "english_style": "小号细体",
            "number_style": "超大号突出"
        }},
        "readability_optimization": "针对{scenario_info['target_audience']}优化的可读性"
    }},
    "visual_elements": {{
        "scenario_specific_elements": "适合{scenario_info['display_name']}的视觉元素",
        "style_characteristics": "体现{style_info['display_name']}风格的设计特征",
        "bento_grid_layout": "Apple风格的Bento Grid网格布局",
        "black_red_theme": "黑底特斯拉红色高亮配色方案",
        "oversized_typography": "超大字体与数字设计",
        "decorative_elements": [
            "简洁线条图形元素",
            "特斯拉红色透明度渐变元素",
            "中英文混排排版元素",
            "符合{style_info['display_name']}风格的装饰元素",
            "适合{scenario_info['display_name']}场景的装饰元素"
        ],
        "card_style": {{
            "background": "#0D0D0D",
            "border_radius": "根据{style_info['display_name']}风格调整",
            "shadow": "现代化的阴影效果",
            "border": "特斯拉红色细边框"
        }},
        "image_integration": {{
            "border_radius": 0,
            "shadow_style": "无阴影或极简阴影",
            "overlay_style": "特斯拉红色半透明遮罩",
            "bento_grid_placement": "根据网格系统放置图片"
        }},
        "logo_treatment": {{
            "positioning": "符合{scenario_info['display_name']}场景的Logo位置",
            "size_guidelines": "适合{style_info['display_name']}风格的Logo尺寸",
            "integration_style": "与黑底红高亮主题和谐的Logo处理",
            "animation_hint": "滚动时的Logo动效提示"
        }}
    }},
    "template_consistency": {{
        "scenario_coherence": "确保所有模板符合{scenario_info['display_name']}场景特点",
        "style_unity": "保持{style_info['display_name']}风格的一致性",
        "color_harmony": "黑底红高亮的现代对比配色体系",
        "visual_rhythm": "协调的视觉节奏和层次",
        "brand_consistency": "一致的品牌形象和视觉识别"
    }}
}}
```

请确保生成的设计规范：
1. 完美融合{scenario_info['display_name']}场景和{style_info['display_name']}风格
2. 统一使用蓝色系但层次丰富
3. 针对{scenario_info['target_audience']}优化用户体验
4. 体现{style_info['description']}的设计特征
5. 确保10种模板类型的高度一致性

请生成完整的增强设计规范JSON。"""
        
        return enhanced_prompt
    
    def _parse_design_specification(self, ai_response: str) -> Optional[Dict[str, Any]]:
        """解析AI返回的设计规范"""
        try:
            # 提取JSON内容
            json_match = re.search(r'```json\s*(.*?)\s*```', ai_response, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
            else:
                # 尝试直接解析
                json_content = ai_response
            
            spec_data = json.loads(json_content)
            
            # 添加时间戳和元数据
            spec_data["created_at"] = datetime.now().isoformat()
            spec_data["scenario"] = self.config.scenario.value
            spec_data["style"] = self.config.style.value
            spec_data["canvas_size"] = {"width": self.config.canvas_width, "height": self.config.canvas_height}
            spec_data["raw_response"] = ai_response
            
            return spec_data
            
        except Exception as e:
            logger.error(f"解析设计规范失败: {str(e)}")
            return None 

    # =================== 新增：SVG模板生成方法 ===================
    
    # 定义10种模板类型（从原始生成器继承）
    TEMPLATE_TYPES = [
        {
            "id": "cover",
            "type": "封面页",
            "title": "封面模板",
            "description": "主标题 + 副标题 + logo + 装饰元素",
            "page_number": 1,
            "content_focus": "标题展示",
            "visual_priority": "品牌识别"
        },
        {
            "id": "agenda",
            "type": "目录页", 
            "title": "目录模板",
            "description": "内容概览 + 章节导航 + 进度指示",
            "page_number": 2,
            "content_focus": "结构导航",
            "visual_priority": "层级清晰"
        },
        {
            "id": "section_divider",
            "type": "章节分隔页",
            "title": "章节分隔模板", 
            "description": "章节标题 + 装饰背景 + 过渡设计",
            "page_number": 3,
            "content_focus": "章节切换",
            "visual_priority": "视觉冲击"
        },
        {
            "id": "title_content",
            "type": "标题内容页",
            "title": "标题内容模板",
            "description": "页面标题 + 主要内容 + 要点列表",
            "page_number": 4,
            "content_focus": "信息传达",
            "visual_priority": "内容层次"
        },
        {
            "id": "image_text",
            "type": "图文混排页",
            "title": "图文混排模板",
            "description": "图片展示 + 文字说明 + 图文平衡",
            "page_number": 5,
            "content_focus": "图文结合",
            "visual_priority": "视觉平衡"
        },
        {
            "id": "data_display",
            "type": "数据展示页",
            "title": "数据展示模板",
            "description": "图表区域 + 数据卡片 + 统计信息",
            "page_number": 6,
            "content_focus": "数据可视化", 
            "visual_priority": "数据突出"
        },
        {
            "id": "comparison",
            "type": "对比分析页",
            "title": "对比分析模板",
            "description": "左右对比 + 差异展示 + 结论总结",
            "page_number": 7,
            "content_focus": "对比分析",
            "visual_priority": "对比清晰"
        },
        {
            "id": "timeline",
            "type": "时间线页",
            "title": "时间线模板", 
            "description": "时间轴 + 里程碑 + 发展历程",
            "page_number": 8,
            "content_focus": "时间流程",
            "visual_priority": "流程连贯"
        },
        {
            "id": "quote",
            "type": "引用页",
            "title": "引用模板",
            "description": "重要引用 + 来源信息 + 视觉强调",
            "page_number": 9,
            "content_focus": "重点强调",
            "visual_priority": "情感共鸣"
        },
        {
            "id": "conclusion",
            "type": "总结页",
            "title": "总结模板",
            "description": "主要结论 + 行动要点 + 联系信息",
            "page_number": 10,
            "content_focus": "总结强化",
            "visual_priority": "行动导向"
        }
    ]

    async def generate_enhanced_template_set(self, content_context: str = "多场景智能适配模板") -> Dict[str, Any]:
        """生成增强版模板集合 - 主要接口"""
        try:
            logger.info(f"开始生成增强版模板集合: {self.config.scenario.value} + {self.config.style.value}")
            
            # 1. 首先生成设计规范
            spec_result = await self.generate_enhanced_design_specification(content_context)
            if not spec_result.get("success"):
                return {"success": False, "error": "设计规范生成失败"}
                
            self.design_spec = spec_result["enhanced_design_spec"]
            
            # 2. 生成所有模板
            generated_templates = []
            failed_templates = []
            
            for template_type in self.TEMPLATE_TYPES:
                logger.info(f"生成模板: {template_type['type']} ({template_type['id']})")
                
                template_result = await self._generate_enhanced_single_template(template_type)
                
                if template_result.get("success"):
                    generated_templates.append(template_result["template"])
                else:
                    failed_templates.append({
                        "template_id": template_type["id"],
                        "error": template_result.get("error", "未知错误")
                    })
                    logger.warning(f"模板生成失败: {template_type['type']} - {template_result.get('error')}")
                
                # 生成间隔，避免API限制
                await asyncio.sleep(0.5)
            
            # 3. 保存模板集合
            if generated_templates:
                save_result = self._save_enhanced_template_set(generated_templates)
                
                return {
                    "success": True,
                    "template_set": {
                        "scenario": self.config.scenario.value,
                        "style": self.config.style.value,
                        "total_templates": len(generated_templates),
                        "failed_templates": len(failed_templates),
                        "templates": generated_templates,
                        "save_path": save_result.get("save_path"),
                        "design_specification": asdict(self.design_spec) if hasattr(self.design_spec, '__dict__') else self.design_spec
                    },
                    "failed_templates": failed_templates
                }
            else:
                return {"success": False, "error": "所有模板生成失败"}
                
        except Exception as e:
            logger.error(f"模板集合生成失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _generate_enhanced_single_template(self, template_type: Dict[str, Any]) -> Dict[str, Any]:
        """生成单个增强版SVG模板"""
        try:
            # 构建增强版模板生成提示词
            prompt = self._build_enhanced_template_prompt(template_type)
            
            # 调用AI生成
            for attempt in range(self.config.max_retries):
                try:
                    logger.info(f"生成模板尝试 {attempt + 1}/{self.config.max_retries}: {template_type['type']}")
                    
                    response = await self.ai_service.chat_completion(
                        messages=[{"role": "user", "content": prompt}],
                        temperature=self.config.temperature
                    )
                    
                    # 检查AI响应
                    if "error" in response:
                        logger.warning(f"尝试 {attempt + 1} AI响应错误: {response.get('error')}")
                        continue
                    
                    # 提取SVG代码
                    svg_code = self._extract_enhanced_svg_code(response["content"])
                    if not svg_code:
                        logger.warning(f"尝试 {attempt + 1} 未能提取到有效SVG代码")
                        continue
                        
                    # 创建模板对象
                    template = {
                        "template_id": f"{self.config.scenario.value}_{self.config.style.value}_{template_type['id']}",
                        "template_type": template_type["type"],
                        "title": template_type["title"],
                        "description": template_type["description"],
                        "svg_code": svg_code,
                        "placeholders": self._extract_enhanced_placeholders(svg_code),
                        "layout_zones": self._extract_enhanced_layout_zones(template_type, svg_code),
                        "style_elements": self._extract_enhanced_style_elements(svg_code),
                        "created_at": datetime.now().isoformat(),
                        "page_number": template_type["page_number"],
                        "scenario": self.config.scenario.value,
                        "style": self.config.style.value,
                        "combination_config": self.combination_config
                    }
                    
                    return {"success": True, "template": template}
                    
                except Exception as e:
                    logger.warning(f"生成模板尝试 {attempt + 1} 失败: {str(e)}")
                    if attempt == self.config.max_retries - 1:
                        raise e
                    await asyncio.sleep(1)
            
            return {"success": False, "error": "达到最大重试次数"}
            
        except Exception as e:
            logger.error(f"单个模板生成失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _build_enhanced_template_prompt(self, template_type: Dict[str, Any]) -> str:
        """构建增强版模板生成提示词"""
        
        # 获取场景风格信息
        scenario_info = self.combination_config["scenario"]
        style_info = self.combination_config["style"]
        colors = self.combination_config["colors"]
        
        # 获取设计规范信息
        design_spec_info = ""
        if self.design_spec:
            if hasattr(self.design_spec, '__dict__'):
                spec_data = asdict(self.design_spec)
            else:
                spec_data = self.design_spec
                
            design_spec_info = f"""
### 设计规范约束
**配色方案**: {spec_data.get('color_palette', {})}
**字体系统**: {spec_data.get('typography_system', {})}
**布局原则**: {spec_data.get('layout_principles', {})}
**视觉元素**: {spec_data.get('visual_elements', {})}
"""
        
        # 构建模板特定需求
        template_requirements = self._get_enhanced_template_requirements(template_type)
        
        prompt = f"""请生成一个高质量的SVG模板，要求如下：

## 基础要求
- 画布尺寸: {self.config.canvas_width}x{self.config.canvas_height} (宽x高)
- 文件格式: 完整的SVG代码，包含所有必要元素
- 兼容性: 确保在现代浏览器中正常显示

## 场景风格组合
**应用场景**: {scenario_info['display_name']}
- 描述: {scenario_info['description']}
- 目标受众: {scenario_info['target_audience']}
- 内容焦点: {', '.join(scenario_info['content_focus'])}
- 语调风格: {scenario_info['tone']}

**设计风格**: {style_info['display_name']}
- 描述: {style_info['description']}
- 设计原则: {style_info['design_principles']['emphasis']}
- 视觉特征: {style_info['visual_elements']['decorations']}

## 统一蓝色系配色
- 主色: {colors['primary']}
- 辅助色: {colors['secondary']}
- 强调色: {colors['accent']}
- 背景色: {colors['background']}
- 文字色: {colors['text_primary']} / {colors['text_secondary']}

{design_spec_info}

## 当前模板信息
- 模板类型: {template_type['type']}
- 页面序号: {template_type['page_number']}/10
- 内容描述: {template_type['description']}
- 内容焦点: {template_type['content_focus']}
- 视觉优先级: {template_type['visual_priority']}

## 模板特定要求
{template_requirements}

## 技术要求
1. **响应式设计**: 适配不同显示尺寸
2. **高清显示**: 使用矢量图形，确保缩放不失真
3. **元素完整**: 包含所有必要的文字、图片、装饰元素
4. **易于编辑**: 使用清晰的元素命名和分组
5. **兼容性**: 遵循SVG 1.1标准

## 特殊符号处理要求 (重要!)
**注意以下特殊符号处理规则：**
- 禁止在文本内容中使用 `&` 符号，请使用 "和" 替代，或使用 `&#38;` 实体
- 在属性值中的 `&` 符号，请使用 `&#38;` 或 `&#x26;` 替代
- 在属性值中的引号，请使用 `&#34;` 或 `&#39;` 替代

**文本内容规范：**
- 所有文本内容都不能包含 & 符号
- 使用 "和" 替代 "&"
- SVG标签中的 < 和 > 符号是必要的，不要替换

**示例：**
- 错误：`公司 & 企业` → 正确：`公司和企业`
- URL中的&符号：`href="http://example.com?a=1&b=2"` → 正确：`href="http://example.com?a=1&#38;b=2"`

## 内容占位符约定
请在合适位置使用以下占位符：
- 标题: {{title}}
- 副标题: {{subtitle}}
- 正文内容: {{content}}
- 图片: {{image_url}}
- Logo: {{logo_url}}
- 日期: {{date}}
- 作者: {{author}}

## 增强美观度要求
1. 使用Bento Grid风格的视觉设计，纯黑色底配合特斯拉红色#E31937作为高亮
2. 强调超大字体或数字突出核心要点，画面中有超大视觉元素强调重点，与小元素的比例形成反差
3. 中英文混用，中文大字体粗体，英文小字作为点缀
4. 简洁的勾线图形化作为数据可视化或者配图元素
5. 运用高亮色自身透明度渐变制造科技感，但是不同高亮色不要互相渐变
6. 模仿apple官网的动效设计理念
7. 数据可以引用在线的图表组件，样式需要跟主题一致
8. 完美融合{scenario_info['display_name']}场景和{style_info['display_name']}风格

请输出完整的SVG代码，确保代码规范、美观度高、功能完整。

SVG代码应该包裹在 ```svg 和 ``` 之间。"""

        return prompt

    def _get_enhanced_template_requirements(self, template_type: Dict[str, Any]) -> str:
        """获取增强版模板特定要求"""
        
        # 基础需求映射
        requirements_map = {
            "cover": """
            - 突出主标题和副标题的层次关系
            - 为品牌Logo预留显著位置
            - 使用视觉冲击力强的背景设计
            - 体现专业感和品牌形象
            - 适当的装饰元素增强视觉效果
            """,
            "agenda": """
            - 清晰的目录结构和导航设计
            - 支持多级内容层次显示
            - 进度指示器和页码信息
            - 易于扫读的版式设计
            - 统一的图标和符号系统
            """,
            "section_divider": """
            - 强烈的视觉分隔效果
            - 章节标题的突出显示
            - 过渡性的装饰元素
            - 与整体风格保持一致
            - 合适的留白和视觉呼吸感
            """,
            "title_content": """
            - 标题与内容的清晰层次
            - 支持要点列表和段落文本
            - 合理的信息密度控制
            - 易读的排版和间距
            - 重点信息的视觉强调
            """,
            "image_text": """
            - 图片与文字的平衡布局
            - 支持不同尺寸的图片展示
            - 图文之间的关联性设计
            - 合适的图片框架和边界
            - 文字环绕或对称布局
            """,
            "data_display": """
            - 数据图表的清晰展示区域
            - 支持多种图表类型布局
            - 数据标签和说明的合理位置
            - 突出关键数据和趋势
            - 统一的数据视觉化风格
            """,
            "comparison": """
            - 明确的左右对比结构
            - 对比项目的对齐和平衡
            - 差异点的视觉强调
            - 结论区域的突出显示
            - 对比表格或卡片设计
            """,
            "timeline": """
            - 清晰的时间轴线条设计
            - 时间节点的标记和说明
            - 事件描述的合理布局
            - 时间流向的视觉引导
            - 里程碑的特殊标注
            """,
            "quote": """
            - 引用内容的突出显示
            - 引号符号的装饰性设计
            - 来源信息的适当位置
            - 情感化的视觉表达
            - 与整体风格的协调
            """,
            "conclusion": """
            - 总结要点的清晰列示
            - 行动建议的强调设计
            - 联系信息的便利获取
            - 感谢或结束语的表达
            - 号召行动的视觉引导
            """
        }
        
        # 根据场景添加特定要求
        scenario_specific = {
            ScenarioType.MEDICAL_HEALTHCARE: "- 符合医疗行业的严谨性要求\n- 突出科学性和专业性\n",
            ScenarioType.EDUCATION_TRAINING: "- 强调教学的易懂性和互动性\n- 适合学习者的认知特点\n",
            ScenarioType.MARKETING_PROMOTION: "- 增强吸引力和说服力\n- 突出产品优势和价值主张\n",
            ScenarioType.BUSINESS_PLAN: "- 体现商业专业性和逻辑性\n- 适合投资人和合作伙伴审阅\n"
        }
        
        # 根据风格添加特定要求
        style_specific = {
            StyleType.MINIMALIST: "- 保持简洁和留白的设计原则\n- 避免过度装饰\n",
            StyleType.TECHNOLOGY: "- 增加科技感和未来感的视觉元素\n- 使用现代化的设计语言\n",
            StyleType.CHINESE_STYLE: "- 融入中国传统文化元素\n- 体现古典雅致的美学特点\n",
            StyleType.THREE_DIMENSIONAL: "- 增加立体感和空间层次\n- 使用阴影和3D效果\n"
        }
        
        base_requirements = requirements_map.get(template_type["id"], "")
        scenario_req = scenario_specific.get(self.config.scenario, "")
        style_req = style_specific.get(self.config.style, "")
        
        return f"{base_requirements}\n{scenario_req}\n{style_req}"

    def _extract_enhanced_svg_code(self, response_content: str) -> Optional[str]:
        """提取增强版SVG代码"""
        try:
            # 尝试多种SVG代码提取模式
            patterns = [
                r'```svg\s*(.*?)\s*```',
                r'```\s*((?:<svg|<\?xml).*?</svg>)\s*```',
                r'(<svg[^>]*>.*?</svg>)',
                r'(<\?xml.*?</svg>)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, response_content, re.DOTALL | re.IGNORECASE)
                if match:
                    svg_code = match.group(1).strip()
                    
                    # 验证SVG代码的基本结构
                    if '<svg' in svg_code and '</svg>' in svg_code:
                        # 预处理SVG代码，自动修复特殊字符问题
                        svg_code = self._preprocess_svg_special_chars(svg_code)
                        
                        # 特殊符号验证（现在总是返回True，因为我们已经修复了问题）
                        if self._validate_svg_special_chars(svg_code):
                            logger.info("✅ SVG代码通过特殊符号验证")
                            return svg_code
                        else:
                            # 这个分支应该不会执行，因为_validate_svg_special_chars现在总是返回True
                            logger.warning("⚠️ SVG代码包含禁用的特殊符号，跳过该结果")
                            continue
            
            logger.warning("未能从响应中提取有效的SVG代码")
            return None
            
        except Exception as e:
            logger.error(f"SVG代码提取失败: {str(e)}")
            return None
            
    def _preprocess_svg_special_chars(self, svg_code: str) -> str:
        """预处理SVG代码中的特殊字符，在提取阶段就进行自动修复"""
        try:
            # 1. 替换文本节点中的特殊字符
            # 找到所有文本节点
            text_pattern = r'(<text[^>]*>)(.*?)(</text>)'
            
            def replace_text_content(match):
                open_tag = match.group(1)
                content = match.group(2)
                close_tag = match.group(3)
                
                # 只替换&符号，保留<>符号，因为它们可能是SVG标签的一部分
                content = content.replace('&', '和')
                # 不替换<>符号，因为可能导致页面错乱
                # content = content.replace('<', '小于')
                # content = content.replace('>', '大于')
                
                return open_tag + content + close_tag
            
            svg_code = re.sub(text_pattern, replace_text_content, svg_code, flags=re.DOTALL)
            
            # 2. 替换属性值中的特殊字符
            attr_pattern = r'(\w+)="([^"]*&[^"]*)"'
            
            def replace_attr_value(match):
                attr_name = match.group(1)
                attr_value = match.group(2)
                
                # 对于URL属性，我们需要特殊处理
                if attr_name in ['href', 'src', 'xlink:href'] and ('http' in attr_value or '#' in attr_value):
                    # URL中的&应该编码为&amp;
                    attr_value = attr_value.replace('&', '&amp;')
                else:
                    # 其他属性中的&替换为XML实体
                    attr_value = attr_value.replace('&', '&#38;')
                
                return f'{attr_name}="{attr_value}"'
            
            svg_code = re.sub(attr_pattern, replace_attr_value, svg_code)
            
            logger.info("SVG特殊字符预处理完成")
            return svg_code
            
        except Exception as e:
            logger.error(f"预处理SVG特殊字符失败: {str(e)}")
            return svg_code  # 返回原始代码，避免阻塞流程
    
    def _validate_svg_special_chars(self, svg_code: str) -> bool:
        """验证SVG代码中是否包含禁用的特殊符号，并自动修复"""
        try:
            # 检查文本内容中的禁用符号（排除XML标签和属性）
            # 提取所有文本节点
            text_pattern = r'>([^<]+)<'
            text_matches = re.findall(text_pattern, svg_code)
            
            modified = False
            for text_content in text_matches:
                text_content = text_content.strip()
                if not text_content:  # 跳过空白文本
                    continue
                    
                # 检查是否包含禁用的特殊符号
                if '&' in text_content and not ('&#' in text_content):
                    logger.warning(f"发现禁用的&符号在文本中，将自动替换: {text_content}")
                    # 自动替换文本中的&符号为"和"
                    new_text = text_content.replace('&', '和')
                    svg_code = svg_code.replace(text_content, new_text)
                    modified = True
                
                # 不再检查和替换<>符号，因为它们可能是SVG标签的一部分
                # 保留这些符号以避免页面错乱
            
            # 检查属性值中的特殊符号
            attr_pattern = r'="([^"]*)"'
            attr_matches = re.findall(attr_pattern, svg_code)
            
            for attr_value in attr_matches:
                if '&' in attr_value and not ('&#' in attr_value or attr_value.startswith('http')):
                    logger.warning(f"发现禁用的&符号在属性值中，将自动替换: {attr_value}")
                    # 对于属性值，使用XML实体替换
                    new_value = attr_value.replace('&', '&#38;')
                    svg_code = svg_code.replace(attr_value, new_value)
                    modified = True
            
            if modified:
                logger.info("SVG特殊符号已自动修复")
            
            return True  # 总是返回True，因为我们已经修复了问题
            
        except Exception as e:
            logger.error(f"特殊符号验证时发生异常: {str(e)}")
            return True  # 验证失败时默认通过，避免阻塞流程

    def _extract_enhanced_placeholders(self, svg_code: str) -> List[str]:
        """提取增强版占位符"""
        try:
            # 查找各种占位符格式
            patterns = [
                r'\{\{([^}]+)\}\}',  # {{placeholder}}
                r'\{([^}]+)\}',      # {placeholder}
                r'%([^%]+)%',        # %placeholder%
                r'\[([^\]]+)\]'      # [placeholder]
            ]
            
            placeholders = set()
            for pattern in patterns:
                matches = re.findall(pattern, svg_code)
                placeholders.update(matches)
            
            return list(placeholders)
            
        except Exception as e:
            logger.error(f"占位符提取失败: {str(e)}")
            return []

    def _extract_enhanced_layout_zones(self, template_type: Dict[str, Any], svg_code: str) -> Dict[str, Dict[str, Any]]:
        """提取增强版布局区域信息"""
        try:
            # 基于模板类型定义默认布局区域
            zones = {
                "header": {"x": 0, "y": 0, "width": self.config.canvas_width, "height": 120},
                "content": {"x": 80, "y": 150, "width": self.config.canvas_width-160, "height": self.config.canvas_height-300},
                "footer": {"x": 0, "y": self.config.canvas_height-80, "width": self.config.canvas_width, "height": 80}
            }
            
            # 根据模板类型调整布局区域
            type_specific_zones = {
                "cover": {
                    "title": {"x": 100, "y": 300, "width": self.config.canvas_width-200, "height": 200},
                    "logo": {"x": 100, "y": 150, "width": 200, "height": 100},
                    "background": {"x": 0, "y": 0, "width": self.config.canvas_width, "height": self.config.canvas_height}
                },
                "image_text": {
                    "image": {"x": 100, "y": 200, "width": 600, "height": 400},
                    "text": {"x": 750, "y": 200, "width": 500, "height": 400}
                },
                "data_display": {
                    "chart": {"x": 100, "y": 200, "width": 800, "height": 500},
                    "metrics": {"x": 950, "y": 200, "width": 300, "height": 500}
                }
            }
            
            if template_type["id"] in type_specific_zones:
                zones.update(type_specific_zones[template_type["id"]])
                
            return zones
            
        except Exception as e:
            logger.error(f"布局区域提取失败: {str(e)}")
            return {}

    def _extract_enhanced_style_elements(self, svg_code: str) -> List[str]:
        """提取增强版样式元素"""
        try:
            style_elements = []
            
            # 提取颜色信息
            color_pattern = r'(?:fill|stroke|color):\s*([#\w]+)'
            colors = re.findall(color_pattern, svg_code)
            style_elements.extend([f"color: {color}" for color in set(colors)])
            
            # 提取字体信息
            font_pattern = r'font-family:\s*([^;]+)'
            fonts = re.findall(font_pattern, svg_code)
            style_elements.extend([f"font: {font}" for font in set(fonts)])
            
            # 提取特殊效果
            if 'gradient' in svg_code.lower():
                style_elements.append("effect: gradient")
            if 'shadow' in svg_code.lower():
                style_elements.append("effect: shadow")
            if 'opacity' in svg_code.lower():
                style_elements.append("effect: transparency")
                
            return style_elements
            
        except Exception as e:
            logger.error(f"样式元素提取失败: {str(e)}")
            return []

    def _save_enhanced_template_set(self, templates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """保存增强版模板集合"""
        try:
            # 创建保存目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            set_name = f"{self.config.scenario.value}_{self.config.style.value}_{timestamp}"
            set_dir = os.path.join(self.templates_dir, set_name)
            os.makedirs(set_dir, exist_ok=True)

            # 保存每个模板
            saved_files = []
            for template in templates:
                filename = f"{template['template_id']}.svg"
                filepath = os.path.join(set_dir, filename)

                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(template['svg_code'])

                saved_files.append(filepath)

            # 处理 combination_config，确保枚举类型被正确序列化
            serializable_combination_config = None
            if self.combination_config:
                serializable_combination_config = self._make_json_serializable(self.combination_config)

            # 处理 design_spec，确保可以序列化
            serializable_design_spec = None
            if self.design_spec:
                if hasattr(self.design_spec, '__dict__'):
                    serializable_design_spec = self._make_json_serializable(asdict(self.design_spec))
                else:
                    serializable_design_spec = self._make_json_serializable(self.design_spec)

            # 保存模板集合信息
            set_info = {
                "set_name": set_name,
                "scenario": self.config.scenario.value,
                "style": self.config.style.value,
                "created_at": datetime.now().isoformat(),
                "template_count": len(templates),
                "templates": [
                    {
                        "template_id": t["template_id"],
                        "type": t["template_type"],
                        "filename": f"{t['template_id']}.svg",
                        "page_number": t["page_number"]
                    }
                    for t in templates
                ],
                "combination_config": serializable_combination_config,
                "design_specification": serializable_design_spec
            }

            info_file = os.path.join(set_dir, "set_info.json")
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(set_info, f, ensure_ascii=False, indent=2)

            logger.info(f"模板集合已保存: {set_dir}")

            return {
                "success": True,
                "save_path": set_dir,
                "set_name": set_name,
                "files_saved": len(saved_files),
                "info_file": info_file
            }

        except Exception as e:
            logger.error(f"模板集合保存失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _make_json_serializable(self, obj: Any) -> Any:
        """将对象转换为JSON可序列化的格式"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, (ScenarioType, StyleType)):
            return obj.value  # 将枚举转换为其值
        elif hasattr(obj, '__dict__'):
            return self._make_json_serializable(asdict(obj))
        else:
            return obj

# =================== 快速生成函数 ===================

async def create_enhanced_template_set(
    scenario: ScenarioType = ScenarioType.BUSINESS_PLAN,
    style: StyleType = StyleType.BUSINESS,
    content_context: str = "智能多场景模板集合"
) -> Dict[str, Any]:
    """快速创建增强版模板集合"""
    try:
        config = EnhancedTemplateConfig(
            scenario=scenario,
            style=style
        )
        
        generator = EnhancedSVGTemplateGenerator(config)
        result = await generator.generate_enhanced_template_set(content_context)
        
        return result
        
    except Exception as e:
        logger.error(f"快速模板集合创建失败: {str(e)}")
        return {"success": False, "error": str(e)}

# =================== 测试函数 ===================

async def test_enhanced_generator():
    """测试增强版生成器"""
    try:
        logger.info("开始测试增强版SVG模板生成器")
        
        # 测试商务风格的商业计划场景
        result = await create_enhanced_template_set(
            scenario=ScenarioType.BUSINESS_PLAN,
            style=StyleType.BUSINESS,
            content_context="商业计划书演示模板"
        )
        
        if result.get("success"):
            template_set = result["template_set"]
            logger.info(f"✅ 测试成功！")
            logger.info(f"场景风格: {template_set['scenario']} + {template_set['style']}")
            logger.info(f"生成模板: {template_set['total_templates']}个")
            logger.info(f"保存路径: {template_set['save_path']}")
        else:
            logger.error(f"❌ 测试失败: {result.get('error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"测试过程出错: {str(e)}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    asyncio.run(test_enhanced_generator()) 