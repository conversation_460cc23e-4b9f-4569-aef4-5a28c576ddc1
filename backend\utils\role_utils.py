import json
import os
from typing import Optional, Dict, List, Any
import logging
import unittest.mock # Added for testing

# Assuming this file is in backend/utils, we need to adjust path for imports
# and for loading the roles_config.json
try:
    from backend.mcp.custom_prompt_generator import generate_custom_prompt
    from backend.mcp.server_manager import mcp_server
except ImportError:
    # This might happen if running script directly or in certain test environments.
    # For robust path handling, consider how your project is structured for imports.
    # Fallback for direct execution / testing - adjust as needed for your project structure
    import sys
    # Construct path to the project root assuming backend/utils/role_utils.py
    PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    sys.path.append(PROJECT_ROOT)
    from backend.mcp.custom_prompt_generator import generate_custom_prompt
    from backend.mcp.server_manager import mcp_server


logger = logging.getLogger(__name__)

class RoleManager:
    _instance = None
    _roles_data: Optional[List[Dict[str, Any]]] = None
    _roles_config_path: str = ""

    def __new__(cls, roles_config_path: Optional[str] = None):
        if cls._instance is None:
            cls._instance = super(RoleManager, cls).__new__(cls)
            
            if roles_config_path:
                cls._roles_config_path = roles_config_path
            else:
                # Default path: assumes this file is in backend/utils/
                # and resources/ is at project_root/resources/
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
                cls._roles_config_path = os.path.join(project_root, "resources", "roles_config.json")
            
            cls._load_roles()
        elif roles_config_path and cls._roles_config_path != roles_config_path:
            # Allow re-init with a different path for testing purposes
            # This is a common pattern for making singletons testable
            # Or, provide a reset_instance() class method if preferred
            logger.info(f"Re-initializing RoleManager with new config path: {roles_config_path}")
            cls._roles_config_path = roles_config_path
            cls._load_roles() # Reload with new path
            
        return cls._instance

    @classmethod
    def _load_roles(cls):
        if not os.path.exists(cls._roles_config_path):
            logger.error(f"Roles configuration file not found at: {cls._roles_config_path}")
            cls._roles_data = []
            return

        try:
            with open(cls._roles_config_path, 'r', encoding='utf-8') as f:
                cls._roles_data = json.load(f)
            logger.info(f"Roles loaded successfully from {cls._roles_config_path}")
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {cls._roles_config_path}: {e}")
            cls._roles_data = []
        except Exception as e:
            logger.error(f"An unexpected error occurred while loading roles from {cls._roles_config_path}: {e}")
            cls._roles_data = []

    def get_role_info(self, role_id: str) -> Optional[str]:
        """
        Retrieves the full information for a given role_id as a JSON string.
        The role information includes role_id, role_name, role_definition, background, description,
        core_rules, and other fields defined in the roles_config.json.
        The 'associated_server_name' field will be normalized to a list of strings if present.

        Args:
            role_id (str): The ID of the role to retrieve.

        Returns:
            Optional[str]: The role's data as a JSON string if found, otherwise None.
                           The 'associated_server_name' in the JSON string will be a list of strings or null.
        """
        if self._roles_data is None:
            self._load_roles() # Attempt to reload if not loaded

        if not self._roles_data:
            return None

        for role_dict_from_json in self._roles_data:
            if role_dict_from_json.get("role_id") == role_id:
                role_info_to_return = role_dict_from_json.copy()
                
                asn_value = role_info_to_return.get("associated_server_name")
                
                if isinstance(asn_value, str):
                    role_info_to_return["associated_server_name"] = [asn_value]
                elif asn_value is not None and not (isinstance(asn_value, list) and all(isinstance(s, str) for s in asn_value)):
                    logger.warning(
                        f"Role ID '{role_id}' has an 'associated_server_name' with an unexpected format. "
                        f"Expected a string or a list of strings, but got {type(asn_value)}. "
                        f"Normalizing to an empty list."
                    )
                    role_info_to_return["associated_server_name"] = []
                
                try:
                    return json.dumps(role_info_to_return, ensure_ascii=False, indent=2)
                except TypeError as e:
                    logger.error(f"Error serializing role info for '{role_id}' to JSON: {e}")
                    return None
                
        logger.warning(f"Role with ID '{role_id}' not found.")
        return None

    def get_all_roles_info(self) -> str:
        """
        Retrieves all role information from the configuration file as a JSON string.
        Each role includes role_id, role_name, role_definition, background, description,
        core_rules, and other fields defined in the roles_config.json.
        The 'associated_server_name' field in each role will be normalized to a list of strings.

        Returns:
            str: A JSON string representing a list of all role objects.
                 Returns '[]' (an empty JSON array string) if no roles are found or if loading failed.
        """
        if self._roles_data is None:
            self._load_roles() # Ensure an attempt to load has been made

        if not self._roles_data: # Covers None or empty list after loading attempt
            return json.dumps([])

        processed_roles_list = []
        for role_dict_from_json in self._roles_data:
            role_info_to_process = role_dict_from_json.copy()
            asn_value = role_info_to_process.get("associated_server_name")

            if isinstance(asn_value, str):
                role_info_to_process["associated_server_name"] = [asn_value]
            elif asn_value is not None and not (isinstance(asn_value, list) and all(isinstance(s, str) for s in asn_value)):
                logger.warning(
                    f"Role ID '{role_info_to_process.get('role_id', '<unknown>')}' has an 'associated_server_name' with an unexpected format. "
                    f"Expected a string or a list of strings, but got {type(asn_value)}. "
                    f"Normalizing to an empty list."
                )
                role_info_to_process["associated_server_name"] = []
            
            processed_roles_list.append(role_info_to_process)
        
        try:
            return json.dumps(processed_roles_list, ensure_ascii=False, indent=2)
        except TypeError as e:
            logger.error(f"Error serializing all roles info to JSON: {e}")
            return json.dumps([]) # Return empty list string on serialization error

    async def construct_system_prompt_for_role(self, role_id: str) -> Optional[str]:
        """
        Constructs a complete system prompt for a given role_id.
        It fetches role details and associated MCP tools to generate the prompt.

        Args:
            role_id (str): The ID of the role.

        Returns:
            Optional[str]: The generated system prompt string, or None if the role is not found
                           or if an error occurs during tool fetching.
        """
        # Get the role info as a dictionary first for internal processing
        role_info_str = self.get_role_info(role_id) # This now returns a JSON string
        if not role_info_str:
            return None
        
        try:
            role_info = json.loads(role_info_str) # Deserialize for internal use
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding role info JSON string for '{role_id}' in construct_system_prompt_for_role: {e}")
            return None

        role_name = role_info.get("role_name", "")
        role_definition = role_info.get("role_definition", "")
        background = role_info.get("background", "")
        description = role_info.get("description", "")
        core_rules = role_info.get("core_rules", [])
        associated_server_name = role_info.get("associated_server_name")

        mcp_tools_for_role: List[Dict[str, Any]] = []
        if associated_server_name:
            try:
                # Ensure associated_server_name is treated as a list for list_tools_from_servers
                server_names_to_query = [associated_server_name] if isinstance(associated_server_name, str) else associated_server_name
                if isinstance(server_names_to_query, list) and all(isinstance(name, str) for name in server_names_to_query):
                    logger.info(f"Fetching tools for server(s): {server_names_to_query} associated with role '{role_id}'")
                    fetched_tools = await mcp_server.list_tools_from_servers(server_names_to_query)
                    
                    # Ensure each tool has serverName, infer if possible (as get_mcp_prompt expects it)
                    for tool in fetched_tools:
                        if 'name' in tool and '_' in tool['name'] and 'serverName' not in tool :
                            sn, _ = tool['name'].split('_', 1)
                            tool['serverName'] = sn
                        elif 'server_name' in tool and 'serverName' not in tool: # Handle existing server_name
                             tool['serverName'] = tool['server_name']


                    mcp_tools_for_role = fetched_tools
                else:
                    logger.warning(f"associated_server_name for role '{role_id}' is not a valid string or list of strings: {associated_server_name}")

            except Exception as e:
                logger.error(f"Error fetching MCP tools for server(s) '{associated_server_name}' (role '{role_id}'): {e}")
                # Decide if you want to proceed without tools or return None/raise error
                # For now, proceeding without tools if fetching fails for some reason.
                pass
        else:
            logger.info(f"No associated_server_name for role '{role_id}'. Prompt will not include specific tools for this server.")

        # Additional prompt components from role_info if needed by generate_custom_prompt
        # For now, we assume generate_custom_prompt takes these as direct args.
        
        # Use the generate_custom_prompt function
        # You might need to pass more arguments to generate_custom_prompt based on its definition
        # such as include_system_protection, include_mcp_capabilities etc.
        # Using defaults for those for now.
        try:
            full_prompt = generate_custom_prompt(
                role_name=role_name,
                role_definition=role_definition,
                background=background,
                description=description,
                core_rules=core_rules,
                mcp_tools=mcp_tools_for_role,
                # Add other parameters for generate_custom_prompt as needed:
                include_system_protection=True,
                include_mcp_capabilities=True,
                include_mcp_examples=True,
                include_mcp_definitions=True,
                # 注意：这里不设置is_langgraph_agent=True，因为role_utils是通用工具
                # 不是专门为LangGraph智能体服务的
                # custom_sections_before_tools=None,
                # custom_sections_after_tools=None,
            )
            return full_prompt
        except Exception as e:
            logger.error(f"Error generating custom prompt for role '{role_id}': {e}")
            return None

# Example Usage (optional, for testing or demonstration)
async def main_test():
    logging.basicConfig(level=logging.INFO)
    print("--- Running RoleManager Tests ---")
    current_script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root_for_test = os.path.abspath(os.path.join(current_script_dir, "..", ".."))
    test_roles_config_path = os.path.join(project_root_for_test, "resources", "roles_config.json")

    if not os.path.exists(test_roles_config_path):
        print(f"ERROR: Test roles_config.json not found at {test_roles_config_path}. Aborting.")
        return

    role_manager = RoleManager(roles_config_path=test_roles_config_path)

    print("\n--- Test 1: get_role_info() for 'coder_assistant_python' ---")
    coder_role_json = role_manager.get_role_info("coder_assistant_python")
    if coder_role_json:
        print("SUCCESS: Fetched 'coder_assistant_python'.")
        try:
            coder_data = json.loads(coder_role_json)
            assert coder_data.get("role_id") == "coder_assistant_python", "Role ID mismatch"
            assert isinstance(coder_data.get("associated_server_name"), list), "ASN should be list"
            assert "role_name" in coder_data, "role_name field should exist"
            print(f"SUCCESS: 'coder_assistant_python' data valid. Role name: {coder_data.get('role_name')}, Avatar: {coder_data.get('avatar_url')}")
        except (json.JSONDecodeError, AssertionError) as e: print(f"ERROR: Validation failed: {e}")
    else: print("ERROR: 'coder_assistant_python' not found.")

    print("\n--- Test 2: get_role_info() for 'non_existent_role' ---")
    non_existent_json = role_manager.get_role_info("non_existent_role")
    assert non_existent_json is None, "Expected None for non-existent role"
    print("SUCCESS: 'non_existent_role' correctly not found.")

    print("\n--- Test 3: get_all_roles_info() ---")
    all_roles_json = role_manager.get_all_roles_info()
    if all_roles_json and all_roles_json != "[]":
        print("SUCCESS: Fetched all roles.")
        try:
            all_data = json.loads(all_roles_json)
            assert isinstance(all_data, list), "All roles should be a list"
            print(f"Total roles: {len(all_data)}")
            if all_data:
                assert isinstance(all_data[0].get("associated_server_name"), list), "First role ASN should be list"
            print("SUCCESS: All roles data structure valid.")
        except (json.JSONDecodeError, AssertionError) as e: print(f"ERROR: Validation failed for all_roles: {e}")
    elif all_roles_json == "[]": print("WARN: get_all_roles_info() returned empty list '[]'. Check config.")
    else: print("ERROR: Could not fetch all roles.")

    print("\n--- Test 4: construct_system_prompt_for_role() for 'research_assistant_web' ---")
    mock_mcp_tools_research = [{"name": "web_search_1", "description": "Searches web", "serverName": "web_search_tools"}]
    mock_prompt_text_research = "Mocked prompt for research_assistant_web"

    with unittest.mock.patch('backend.mcp.server_manager.mcp_server.list_tools_from_servers', new_callable=unittest.mock.AsyncMock, return_value=mock_mcp_tools_research) as mock_list_tools_research, \
         unittest.mock.patch('__main__.generate_custom_prompt', return_value=mock_prompt_text_research) as mock_gen_prompt_research:
        
        research_prompt = await role_manager.construct_system_prompt_for_role("research_assistant_web")
        if research_prompt:
            print(f"SUCCESS: Constructed prompt: {research_prompt}")
            research_details_json = role_manager.get_role_info("research_assistant_web")
            research_details = json.loads(research_details_json)
            expected_server_list_research = research_details.get("associated_server_name")
            mock_list_tools_research.assert_called_once_with(expected_server_list_research)
            mock_gen_prompt_research.assert_called_once()
            call_args_research = mock_gen_prompt_research.call_args[1]
            assert call_args_research['role_definition'] == research_details.get("role_definition")
            assert call_args_research['mcp_tools'] == mock_mcp_tools_research, "MCP tools mismatch for research assistant"
            assert research_prompt == mock_prompt_text_research, "Returned prompt mismatch for research assistant"
            print("SUCCESS: Mocks called correctly for research_assistant_web.")
        else: print("ERROR: Prompt construction failed for research_assistant_web.")

    print("\n--- Test 5: construct_system_prompt_for_role() for 'coder_assistant_python' ---")
    mock_mcp_tools_coder = [{"name": "fs_read", "description": "Reads a file", "serverName": "filesystem_tools"}]
    mock_prompt_text_coder = "Mocked system prompt for coder_assistant_python"

    with unittest.mock.patch('backend.mcp.server_manager.mcp_server.list_tools_from_servers', new_callable=unittest.mock.AsyncMock, return_value=mock_mcp_tools_coder) as mock_list_tools_coder, \
         unittest.mock.patch('__main__.generate_custom_prompt', return_value=mock_prompt_text_coder) as mock_gen_prompt_coder:
        
        coder_prompt = await role_manager.construct_system_prompt_for_role("coder_assistant_python")
        if coder_prompt:
            print(f"SUCCESS: Constructed prompt: {coder_prompt}")
            coder_details_json = role_manager.get_role_info("coder_assistant_python")
            coder_details = json.loads(coder_details_json)
            expected_server_list_coder = coder_details.get("associated_server_name")
            mock_list_tools_coder.assert_called_once_with(expected_server_list_coder)
            mock_gen_prompt_coder.assert_called_once()
            call_args_coder = mock_gen_prompt_coder.call_args[1]
            assert call_args_coder['role_definition'] == coder_details.get("role_definition")
            assert call_args_coder['mcp_tools'] == mock_mcp_tools_coder, "MCP tools mismatch for coder assistant"
            assert coder_prompt == mock_prompt_text_coder, "Returned prompt mismatch for coder assistant"
            print("SUCCESS: Mocks called correctly for coder_assistant_python.")
        else: print("ERROR: Prompt construction failed for coder_assistant_python.")

    print("\n--- Test 6: RoleManager loading with non-existent/malformed config ---")
    temp_config_dir = os.path.join(project_root_for_test, "temp_test_resources")
    os.makedirs(temp_config_dir, exist_ok=True)
    
    non_existent_config_path = os.path.join(temp_config_dir, "non_existent_roles.json")
    malformed_config_path = os.path.join(temp_config_dir, "malformed_roles.json")

    if os.path.exists(non_existent_config_path): os.remove(non_existent_config_path)
    with open(malformed_config_path, "w") as f: f.write("{\"invalid_json\": ")

    manager_ne = RoleManager(roles_config_path=non_existent_config_path)
    assert manager_ne.get_all_roles_info() == "[]", "Should be '[]' for non-existent config."
    print("SUCCESS: Handles non-existent config path.")

    manager_mf = RoleManager(roles_config_path=malformed_config_path)
    assert manager_mf.get_all_roles_info() == "[]", "Should be '[]' for malformed config."
    print("SUCCESS: Handles malformed config file.")

    if os.path.exists(malformed_config_path): os.remove(malformed_config_path)
    if os.path.exists(temp_config_dir): os.rmdir(temp_config_dir)
    
    RoleManager(roles_config_path=test_roles_config_path)
    print("RoleManager reset to original config for subsequent runs.")

    print("\n--- All RoleManager Tests Completed ---")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main_test()) 