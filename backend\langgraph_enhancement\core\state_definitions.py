# -*- coding: utf-8 -*-
"""
🔥 LangGraph 增强系统 - 状态定义

核心状态结构：
- EnhancedTeamState：增强的团队状态
- 支持分层上下文管理
- 兼容LangGraph的状态管理机制
"""

from typing import Dict, List, Any, Optional, TypedDict
from typing_extensions import Annotated

try:
    from langgraph.graph.message import add_messages
except ImportError:
    # 如果没有LangGraph，提供模拟实现
    def add_messages(x: List[Dict], y: List[Dict]) -> List[Dict]:
        """模拟的消息添加函数"""
        return x + y


class EnhancedTeamState(TypedDict):
    """
    🔥 增强团队状态 - LangGraph 最强上下文管理
    
    分层上下文架构：
    - messages: LangGraph消息管理（自动去重、压缩）
    - core_context: 永久核心上下文（任务关键信息）
    - working_memory: 可清理工作记忆（当前任务相关）
    - agent_memory: 智能体专用记忆（按需加载）
    - context_metrics: 上下文大小监控
    - compression_level: 压缩等级 (0-3)
    - checkpoint_id: 检查点标识
    """
    
    # 🔥 智能消息管理（自动去重、压缩）
    messages: Annotated[List[Dict], add_messages]
    
    # 🔥 分层上下文存储
    core_context: Dict[str, Any]          # 永久核心上下文（任务关键信息）
    working_memory: Dict[str, Any]        # 可清理工作记忆（当前任务相关）
    agent_memory: Dict[str, Dict]         # 智能体专用记忆（按需加载）
    
    # 🔥 上下文控制元数据
    context_metrics: Dict[str, int]       # 上下文大小监控
    compression_level: int                # 压缩等级 (0-3)
    checkpoint_id: Optional[str]          # 检查点标识


class AgentState(TypedDict):
    """单个智能体状态"""
    
    agent_id: str
    status: str  # ready, processing, completed, error
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]]
    execution_time: float
    error_message: Optional[str]


class WorkflowState(TypedDict):
    """工作流状态"""
    
    workflow_id: str
    team_id: str
    workflow_type: str  # sequential, parallel, conditional
    status: str  # initializing, running, completed, error
    started_at: str
    completed_at: Optional[str]
    agent_states: Dict[str, AgentState]
    execution_history: List[Dict[str, Any]]
    errors: List[Dict[str, Any]]


# 导出状态类型
__all__ = [
    'EnhancedTeamState',
    'AgentState', 
    'WorkflowState',
    'add_messages'
] 