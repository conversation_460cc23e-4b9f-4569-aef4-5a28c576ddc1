"""
🔥 上下文优化智能体 - LangGraph 增强系统

特性：
- 完全复用现有 AIManager.chat_completion() 方法
- 提供 LangGraph 兼容的处理接口
- 内置上下文管理和压缩机制
- 零破坏性集成现有系统
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime

# 🔥 完全复用现有系统
from ..utils import get_ai_manager_bridge
from ..config import get_config_manager

logger = logging.getLogger(__name__)


class ContextOptimizedAgent:
    """
    🔥 上下文优化智能体
    
    核心功能：
    - LangGraph 状态处理接口
    - 100% 复用现有 AIManager
    - 智能上下文管理
    - 完全兼容现有架构
    """
    
    def __init__(
        self, 
        agent_id: str,
        config: Dict,
        system_prompt: str,
                         ai_manager = None,
        context_strategy: str = 'adaptive'
    ):
        """
        初始化上下文优化智能体
        
        Args:
            agent_id: 智能体唯一标识
            config: 智能体配置（来自 YAML）
            system_prompt: 完整的系统提示词
            ai_manager: 现有的 AIManager 实例
            context_strategy: 上下文管理策略
        """
        self.agent_id = agent_id
        self.config = config
        self.system_prompt = system_prompt
        # 🔥 使用桥接器或传入的实例
        if ai_manager is None:
            ai_bridge = get_ai_manager_bridge()
            self.ai_manager = ai_bridge.ai_manager
        else:
            self.ai_manager = ai_manager
        self.context_strategy = context_strategy
        
        # 🔥 新增：记忆管理能力
        try:
            from ..core.context_manager import get_context_manager
            self.context_manager = get_context_manager()
            self.memory_manager = self.context_manager.memory_manager
            self.memory_enabled = config.get('enable_memory_access', True)
            logger.info(f"🧠 智能体 {agent_id} 记忆访问已启用")
        except Exception as e:
            logger.warning(f"⚠️ 智能体 {agent_id} 记忆管理器初始化失败: {e}")
            self.context_manager = None
            self.memory_manager = None
            self.memory_enabled = False
        
        # 智能体元信息
        self.role_name = config.get('role_name', agent_id)
        self.model = config.get('model', 'gemini-2.5-flash-preview-05-20')
        self.associated_servers = config.get('associated_servers', [])
        self.max_context_window = config.get('max_context_window', 80000)
        self.avatar_url = config.get('avatar_url', 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png')
        
        # 上下文管理
        self.context_history = []
        self.last_processing_time = None
        
        # 🧠 自动注册到全局智能体池，用于记忆工具的智能体识别
        try:
            agent_pool = get_agent_pool()
            agent_pool.add_agent(self)
            logger.info(f"🧠 智能体 {self.agent_id} 已注册到全局智能体池")
        except Exception as e:
            logger.warning(f"⚠️ 智能体池注册失败: {e}")
        
        logger.info(f"🔥 上下文优化智能体初始化: {self.agent_id} ({self.role_name})")
    
    def _should_compress(self, context_size: int) -> bool:
        """根据配置决定是否应该压缩上下文"""
        
        # 优先使用 agent_config 中的设置
        trigger_ratio_str = self.config.get('context_management', {}).get('compression_trigger_ratio')
        
        if trigger_ratio_str:
            try:
                trigger_ratio = float(trigger_ratio_str)
                logger.debug(f"使用智能体 '{self.agent_id}' 特定压缩比例: {trigger_ratio}")
            except (ValueError, TypeError):
                trigger_ratio = None
        else:
            trigger_ratio = None

        if trigger_ratio is None:
            # 获取全局默认配置
            config_manager = get_config_manager()
            context_config = config_manager.get_context_config()
            trigger_ratio = context_config.get('default_compression_trigger_ratio', 0.8)
            logger.debug(f"智能体 '{self.agent_id}' 使用全局默认压缩比例: {trigger_ratio}")

        threshold = self.max_context_window * trigger_ratio
        logger.info(f"'{self.agent_id}' 压缩检查: 当前大小={context_size}, 阈值={int(threshold)} (max_window={self.max_context_window} * ratio={trigger_ratio})")
        return context_size > threshold

    def _estimate_context_size(self, messages: List[Dict]) -> int:
        """简单地估算上下文大小（字符数）"""
        return sum(len(str(msg.get('content', ''))) for msg in messages)

    async def process_with_optimized_context(self, state: Dict, stream_callback: Optional[Callable] = None) -> Dict:
        """
        🔥 LangGraph 兼容的状态处理方法 (已优化)
        这是智能体的主要工作方法，接收 LangGraph 状态并返回更新后的状态
        """
        start_time = datetime.now()
        try:
            logger.info(f"🔥 {self.agent_id} 开始处理已优化的上下文")
            
            messages = state.get("messages", [])
            
            # 1. 🔥 修复：强制替换为智能体专用系统提示词
            # 移除所有现有的系统消息，确保使用智能体的专用提示词
            messages = [msg for msg in messages if msg.get("role") != "system"]
            logger.info(f"✅ {self.agent_id} 清除原有系统消息，设置智能体专用提示词")
            logger.info(f"🔍 {self.agent_id} 系统提示词长度: {len(self.system_prompt)} 字符")
            logger.debug(f"🔍 {self.agent_id} 系统提示词前200字符: {self.system_prompt[:200]}...")
            messages.insert(0, {"role": "system", "content": self.system_prompt})

            # 2. 检查和压缩上下文 (注意：实际压缩由ContextManager处理，这里只做逻辑演示)
            context_size = self._estimate_context_size(messages)
            # if self._should_compress(context_size):
            #     logger.warning(f"⚠️ {self.agent_id}: 上下文大小 {context_size} 超出阈值，应由 ContextManager 压缩。")
                # 在真实流程中，这里的 messages 应该是已经被 ContextManager 处理过的。
                # 此处日志用于验证 _should_compress 逻辑是否被正确触发。

            # 3. 调用AI Manager
            response = await self._call_ai_manager(messages, stream_callback)
            
            # 🔥 检查AI管理器是否返回错误
            if response.get('error'):
                logger.error(f"❌ {self.agent_id} AI管理器返回错误: {response.get('error')}")
                # 即使有错误，也要返回状态，避免中断流程
                return {
                    **state,
                    'last_error': response.get('error'),
                    'error_timestamp': datetime.now().isoformat(),
                    'messages': state.get('messages', []) + [{
                        'role': 'assistant',
                        'content': f"处理过程中遇到错误：{response.get('error')}",
                        'timestamp': datetime.now().isoformat(),
                        'agent_id': self.agent_id,
                        'agent_name': self.role_name,
                        'status': 'error'
                    }]
                }
            
            # 4. 处理响应并更新状态
            updated_state = self._update_state_with_response(state, response, start_time)
            
            logger.info(f"✅ {self.agent_id} 优化上下文处理完成，耗时: {datetime.now() - start_time}")
            return updated_state
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 优化上下文处理失败: {str(e)}")
            return {
                **state,
                'last_error': str(e),
                'error_timestamp': datetime.now().isoformat()
            }
    
    async def process_with_context_optimization(self, state: Dict, stream_callback: Optional[Callable] = None) -> Dict:
        """
        🔥 LangGraph 兼容的状态处理方法
        
        这是智能体的主要工作方法，接收 LangGraph 状态并返回更新后的状态
        
        Args:
            state: LangGraph 状态字典，包含 messages 等信息
            stream_callback: 用于流式输出的回调函数
            
        Returns:
            Dict: 更新后的状态字典
        """
        try:
            start_time = datetime.now()
            logger.info(f"🔥 {self.agent_id} 开始处理状态")
            
            # 1. 从状态中提取消息
            messages = state.get('messages', [])
            if not messages:
                logger.warning(f"⚠️ {self.agent_id} 收到空消息列表")
                return state
            
            # 2. 构建完整的消息列表（系统提示词 + 历史消息）
            full_messages = self._build_full_messages(messages)
            
            # 3. 🔥 调用现有 AIManager 处理（零修改复用），并传入回调
            response = await self._call_ai_manager(full_messages, stream_callback)
            
            # 4. 处理响应并更新状态
            updated_state = self._update_state_with_response(state, response, start_time)
            
            logger.info(f"✅ {self.agent_id} 处理完成，耗时: {datetime.now() - start_time}")
            return updated_state
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 处理失败: {str(e)}")
            # 返回原状态，添加错误信息
            return {
                **state,
                'last_error': str(e),
                'error_timestamp': datetime.now().isoformat()
            }
    
    def _build_full_messages(self, messages: List[Dict]) -> List[Dict]:
        """构建包含系统提示词的完整消息列表"""
        # 🔥 调试：记录系统提示词状态
        logger.info(f"🔍 {self.agent_id} 构建消息列表 - 系统提示词长度: {len(self.system_prompt)}")
        # logger.info(f"🔍 {self.agent_id} 系统提示词前100字符: {self.system_prompt[:100]}...")
        
        # 检查是否已有系统消息
        has_system = any(msg.get('role') == 'system' for msg in messages)
        logger.info(f"🔍 {self.agent_id} 输入消息已有系统消息: {has_system}")
        
        if has_system:
            # 🔥 修复：即使已有系统消息，也要检查并替换为正确的系统提示词
            logger.warning(f"⚠️ {self.agent_id} 检测到已有系统消息，将替换为智能体专用提示词")
            result_messages = []
            for msg in messages:
                if msg.get('role') == 'system':
                    # 替换为智能体的系统提示词
                    result_messages.append({
                        'role': 'system',
                        'content': self.system_prompt
                    })
                    logger.info(f"✅ {self.agent_id} 已替换系统消息为智能体提示词")
                else:
                    result_messages.append(msg)
            return result_messages
        else:
            # 添加系统提示词作为第一条消息
            system_message = {
                'role': 'system',
                'content': self.system_prompt
            }
            logger.info(f"✅ {self.agent_id} 添加系统提示词到消息列表")
            return [system_message] + messages
    
    async def _call_ai_manager(self, messages: List[Dict], stream_callback: Optional[Callable] = None) -> Dict:
        """
        🔥 调用现有 AIManager.chat_completion() 方法
        完全零修改复用现有系统
        """
        try:
            logger.info(f"🔧 {self.agent_id} 调用 AIManager，模型: {self.model}")
            
            # 🧠 设置线程本地的智能体ID，用于记忆工具调用时的智能体识别
            import threading
            current_thread = threading.current_thread()
            old_agent_id = getattr(current_thread, 'current_agent_id', None)
            current_thread.current_agent_id = self.agent_id
            
            try:
                # 🔥 修复：传入一个特殊的role_id来保护系统提示词不被覆盖
                # 使用"langgraph_agent"作为特殊标识，告诉AI管理器不要覆盖系统提示词
                logger.info(f"🔧 {self.agent_id} 调用 AIManager - 工具服务器: {self.associated_servers}")
                response = await self.ai_manager.chat_completion(
                    messages=messages,
                    model=self.model,
                    role_id="langgraph_agent",  # 🔥 修复：添加特殊role_id
                    selected_servers=self.associated_servers,
                    handle_tools=True,  # 启用工具调用
                    max_tool_calls=50,   # 最大工具调用次数  
                    is_agent=False,      # 关闭agent模式
                    stream_callback=stream_callback  # 🔥 传入流式回调
                )
            finally:
                # 🧠 恢复线程本地的智能体ID
                if old_agent_id is not None:
                    current_thread.current_agent_id = old_agent_id
                elif hasattr(current_thread, 'current_agent_id'):
                    delattr(current_thread, 'current_agent_id')
            
            logger.info(f"✅ {self.agent_id} AIManager 调用成功")
            return response
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} AIManager 调用失败: {str(e)}")
            raise
    
    def _update_state_with_response(self, original_state: Dict, response: Dict, start_time: datetime) -> Dict:
        """根据 AI 响应更新 LangGraph 状态"""
        # 提取响应内容
        assistant_message = {
            'role': 'assistant',
            'content': response.get('content', ''),
            'timestamp': datetime.now().isoformat(),
            'agent_id': self.agent_id,
            'agent_name': self.role_name
        }
        
        # 如果有工具调用历史，也添加到消息中
        if response.get('tool_calls_history'):
            assistant_message['tool_calls_history'] = response['tool_calls_history']
        
        # 更新状态
        updated_state = {
            **original_state,
            'messages': original_state.get('messages', []) + [assistant_message],
            'last_agent': self.agent_id,
            'last_processing_time': (datetime.now() - start_time).total_seconds(),
            'processing_metadata': {
                'agent_id': self.agent_id,
                'agent_name': self.role_name,
                'model_used': self.model,
                'context_strategy': self.context_strategy,
                'timestamp': datetime.now().isoformat(),
                'max_context_window': self.max_context_window,
            }
        }
        
        return updated_state
    
    async def process_direct_input(self, user_input: str, context: Dict = None) -> str:
        """
        直接处理用户输入的便捷方法
        
        Args:
            user_input: 用户输入文本
            context: 可选的上下文信息
            
        Returns:
            str: AI 响应文本
        """
        try:
            # 构建消息
            messages = [
                {'role': 'system', 'content': self.system_prompt},
                {'role': 'user', 'content': user_input}
            ]
            
            # 如果有上下文，添加到系统消息中
            if context:
                context_str = f"\n\n## 上下文信息\n{context}"
                messages[0]['content'] += context_str
            
            # 调用 AIManager
            response = await self._call_ai_manager(messages)
            
            return response.get('content', '')
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 直接处理输入失败: {str(e)}")
            return f"处理失败: {str(e)}"
    
    def get_agent_info(self) -> Dict:
        """获取智能体信息"""
        return {
            'agent_id': self.agent_id,
            'role_name': self.role_name,
            'description': self.config.get('description', ''),
            'model': self.model,
            'associated_servers': self.associated_servers,
            'context_strategy': self.context_strategy,
            'max_context_window': self.max_context_window,
            'avatar_url': self.avatar_url,
            'last_processing_time': self.last_processing_time
        }
    
    def estimate_context_size(self, messages: List[Dict]) -> int:
        """估算上下文大小（字符数）"""
        total_chars = len(self.system_prompt)
        for msg in messages:
            total_chars += len(str(msg.get('content', '')))
        return total_chars
    
    async def compress_context_if_needed(self, messages: List[Dict]) -> List[Dict]:
        """如果需要，压缩上下文"""
        if not self._should_compress(self.estimate_context_size(messages)):
            return messages
        
        logger.info(f"🔧 {self.agent_id} 开始上下文压缩")
        
        # 简单的压缩策略：保留系统消息 + 最近的几条消息
        if len(messages) <= 3:
            return messages
        
        # 保留第一条（系统消息）和最后两条消息
        compressed = [messages[0]] + messages[-2:]
        
        logger.info(f"✅ {self.agent_id} 上下文压缩完成：{len(messages)} -> {len(compressed)} 条消息")
        return compressed
    
    # 🧠 ============ 记忆管理方法 ============
    
    async def get_my_memory(self, key: str, default: Any = None) -> Any:
        """
        🔥 获取自己的专用记忆
        
        Args:
            key: 记忆键名
            default: 默认值
            
        Returns:
            记忆值或默认值
        """
        if not self.memory_enabled or not self.memory_manager:
            logger.warning(f"⚠️ 智能体 {self.agent_id} 记忆访问未启用")
            return default
        
        try:
            value = await self.memory_manager.get_agent_memory(
                self.agent_id, key, default
            )
            logger.debug(f"🧠 {self.agent_id} 获取记忆: {key}")
            return value
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 获取记忆失败 {key}: {e}")
            return default
    
    async def store_my_memory(self, key: str, value: Any) -> bool:
        """
        🔥 存储到自己的专用记忆
        
        Args:
            key: 记忆键名
            value: 要存储的值
            
        Returns:
            bool: 存储是否成功
        """
        if not self.memory_enabled or not self.memory_manager:
            logger.warning(f"⚠️ 智能体 {self.agent_id} 记忆访问未启用")
            return False
        
        try:
            await self.memory_manager.store_agent_memory(
                self.agent_id, key, value
            )
            logger.debug(f"🧠 {self.agent_id} 存储记忆: {key}")
            return True
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 存储记忆失败 {key}: {e}")
            return False
    
    async def list_my_memories(self) -> List[str]:
        """
        🔥 列出自己的所有记忆键名
        
        Returns:
            List[str]: 记忆键名列表
        """
        if not self.memory_enabled or not self.memory_manager:
            return []
        
        try:
            agent_memories = self.memory_manager.agent_memory.get(self.agent_id, {})
            return list(agent_memories.keys())
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 列出记忆失败: {e}")
            return []
    
    async def clear_my_memory(self, key: str = None) -> bool:
        """
        🔥 清除自己的记忆
        
        Args:
            key: 要清除的记忆键名，None表示清除所有
            
        Returns:
            bool: 清除是否成功
        """
        if not self.memory_enabled or not self.memory_manager:
            return False
        
        try:
            if key is None:
                # 清除所有记忆
                if self.agent_id in self.memory_manager.agent_memory:
                    self.memory_manager.agent_memory[self.agent_id].clear()
                    logger.info(f"🧠 {self.agent_id} 清除所有记忆")
            else:
                # 清除特定记忆
                agent_memories = self.memory_manager.agent_memory.get(self.agent_id, {})
                if key in agent_memories:
                    del agent_memories[key]
                    logger.debug(f"🧠 {self.agent_id} 清除记忆: {key}")
            return True
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 清除记忆失败: {e}")
            return False
    
    async def get_my_memory_stats(self) -> Dict[str, Any]:
        """
        🔥 获取自己的记忆统计信息
        
        Returns:
            Dict: 记忆统计信息
        """
        if not self.memory_enabled or not self.memory_manager:
            return {"memory_enabled": False}
        
        try:
            agent_memories = self.memory_manager.agent_memory.get(self.agent_id, {})
            stats = {
                "memory_enabled": True,
                "total_memories": len(agent_memories),
                "memory_keys": list(agent_memories.keys()),
                "most_accessed": None,
                "total_access_count": 0
            }
            
            # 计算访问统计
            if agent_memories:
                access_counts = [(k, v.get("accessed_count", 0)) for k, v in agent_memories.items()]
                access_counts.sort(key=lambda x: x[1], reverse=True)
                stats["most_accessed"] = access_counts[0] if access_counts else None
                stats["total_access_count"] = sum(count for _, count in access_counts)
            
            return stats
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 获取记忆统计失败: {e}")
            return {"memory_enabled": False, "error": str(e)}
    
    async def search_my_memories(self, pattern: str) -> Dict[str, Any]:
        """
        🔥 搜索自己的记忆
        
        Args:
            pattern: 搜索模式（支持关键词匹配）
            
        Returns:
            Dict: 匹配的记忆项
        """
        if not self.memory_enabled or not self.memory_manager:
            return {}
        
        try:
            agent_memories = self.memory_manager.agent_memory.get(self.agent_id, {})
            matched = {}
            
            for key, memory_item in agent_memories.items():
                # 在键名和值中搜索
                if (pattern.lower() in key.lower() or 
                    pattern.lower() in str(memory_item.get("value", "")).lower()):
                    matched[key] = memory_item["value"]
            
            logger.debug(f"🧠 {self.agent_id} 记忆搜索 '{pattern}': 找到 {len(matched)} 项")
            return matched
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 记忆搜索失败: {e}")
            return {}
    
    # 🔥 ============ 记忆驱动的处理流程 ============
    
    async def process_with_memory_enhancement(self, state: Dict, stream_callback: Optional[Callable] = None) -> Dict:
        """
        🔥 带记忆增强的处理方法
        
        这个方法会：
        1. 自动加载相关记忆
        2. 在系统提示中包含记忆信息
        3. 处理完成后更新记忆
        
        Args:
            state: LangGraph 状态
            stream_callback: 流式输出回调
            
        Returns:
            Dict: 更新后的状态
        """
        try:
            start_time = datetime.now()
            logger.info(f"🧠 {self.agent_id} 开始记忆增强处理")
            
            # 1. 加载相关记忆
            memory_context = await self._load_relevant_memories(state)
            
            # 2. 增强系统提示词
            enhanced_state = await self._enhance_state_with_memory(state, memory_context)
            
            # 3. 执行标准处理流程
            result = await self.process_with_context_optimization(enhanced_state, stream_callback)
            
            # 4. 更新记忆
            await self._update_memories_after_processing(state, result, start_time)
            
            logger.info(f"✅ {self.agent_id} 记忆增强处理完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 记忆增强处理失败: {e}")
            # 降级到标准处理
            return await self.process_with_context_optimization(state, stream_callback)
    
    async def _load_relevant_memories(self, state: Dict) -> Dict[str, Any]:
        """加载相关记忆"""
        if not self.memory_enabled:
            return {}
        
        try:
            # 获取常用记忆
            user_preferences = await self.get_my_memory("user_preferences", {})
            interaction_history = await self.get_my_memory("interaction_history", [])
            last_context = await self.get_my_memory("last_context", {})
            specialized_knowledge = await self.get_my_memory("specialized_knowledge", {})
            
            # 基于当前输入搜索相关记忆
            messages = state.get("messages", [])
            if messages:
                last_user_message = ""
                for msg in reversed(messages):
                    if msg.get("role") == "user":
                        last_user_message = msg.get("content", "")
                        break
                
                # 搜索相关记忆
                relevant_memories = {}
                if last_user_message:
                    # 提取关键词进行搜索
                    keywords = last_user_message.lower().split()[:5]  # 取前5个词
                    for keyword in keywords:
                        if len(keyword) > 3:  # 只搜索长度>3的词
                            search_results = await self.search_my_memories(keyword)
                            relevant_memories.update(search_results)
            else:
                relevant_memories = {}
            
            return {
                "user_preferences": user_preferences,
                "interaction_history": interaction_history[-5:] if interaction_history else [],  # 最近5次
                "last_context": last_context,
                "specialized_knowledge": specialized_knowledge,
                "relevant_memories": relevant_memories
            }
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 加载记忆失败: {e}")
            return {}
    
    async def _enhance_state_with_memory(self, state: Dict, memory_context: Dict) -> Dict:
        """用记忆信息增强状态"""
        if not memory_context or not self.memory_enabled:
            return state
        
        try:
            enhanced_state = state.copy()
            messages = enhanced_state.get("messages", [])
            
            # 构建记忆信息文本
            memory_text = self._build_memory_context_text(memory_context)
            
            if memory_text and messages:
                # 找到系统消息并增强
                for i, msg in enumerate(messages):
                    if msg.get("role") == "system":
                        enhanced_content = msg["content"] + f"\n\n## 🧠 记忆信息\n{memory_text}"
                        messages[i] = {**msg, "content": enhanced_content}
                        break
                else:
                    # 如果没有系统消息，创建一个
                    memory_system_msg = {
                        "role": "system",
                        "content": f"## 🧠 记忆信息\n{memory_text}"
                    }
                    messages.insert(0, memory_system_msg)
                
                enhanced_state["messages"] = messages
                logger.debug(f"🧠 {self.agent_id} 已将记忆信息添加到系统提示")
            
            return enhanced_state
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 记忆增强失败: {e}")
            return state
    
    def _build_memory_context_text(self, memory_context: Dict) -> str:
        """构建记忆上下文文本"""
        parts = []
        
        # 用户偏好
        if memory_context.get("user_preferences"):
            parts.append(f"**用户偏好**: {memory_context['user_preferences']}")
        
        # 交互历史
        history = memory_context.get("interaction_history", [])
        if history:
            parts.append(f"**最近交互**: {len(history)} 次交互记录")
        
        # 专业知识
        knowledge = memory_context.get("specialized_knowledge")
        if knowledge:
            parts.append(f"**专业知识**: {len(knowledge)} 项专业知识")
        
        # 相关记忆
        relevant = memory_context.get("relevant_memories", {})
        if relevant:
            parts.append(f"**相关记忆**: {list(relevant.keys())[:3]}")  # 显示前3个相关记忆键
        
        return "\n".join(parts)
    
    async def _update_memories_after_processing(self, original_state: Dict, result_state: Dict, start_time: datetime) -> None:
        """处理完成后更新记忆"""
        if not self.memory_enabled:
            return
        
        try:
            # 更新交互历史
            interaction_record = {
                "timestamp": start_time.isoformat(),
                "input_message_count": len(original_state.get("messages", [])),
                "output_message_count": len(result_state.get("messages", [])),
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "success": not result_state.get("last_error")
            }
            
            history = await self.get_my_memory("interaction_history", [])
            history.append(interaction_record)
            
            # 只保留最近50次交互
            if len(history) > 50:
                history = history[-50:]
            
            await self.store_my_memory("interaction_history", history)
            
            # 更新最后上下文
            await self.store_my_memory("last_context", {
                "state": original_state,
                "result": result_state,
                "timestamp": datetime.now().isoformat()
            })
            
            # 更新统计信息
            stats = await self.get_my_memory("processing_stats", {
                "total_processed": 0,
                "total_time": 0,
                "success_count": 0,
                "error_count": 0
            })
            
            stats["total_processed"] += 1
            stats["total_time"] += (datetime.now() - start_time).total_seconds()
            
            if result_state.get("last_error"):
                stats["error_count"] += 1
            else:
                stats["success_count"] += 1
            
            await self.store_my_memory("processing_stats", stats)
            
            logger.debug(f"🧠 {self.agent_id} 已更新处理记忆")
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id} 更新记忆失败: {e}")
    
    def __str__(self) -> str:
        """字符串表示"""
        memory_status = "🧠" if self.memory_enabled else "❌"
        return f"ContextOptimizedAgent({self.agent_id}: {self.role_name}) {memory_status}"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"ContextOptimizedAgent(id={self.agent_id}, role={self.role_name}, model={self.model}, memory={self.memory_enabled})"


class AgentPool:
    """
    🔥 智能体池管理器
    
    功能：
    - 管理多个智能体实例
    - 提供智能体查找和调用
    - 支持动态添加和移除
    """
    
    def __init__(self):
        """初始化智能体池"""
        self._agents: Dict[str, ContextOptimizedAgent] = {}
        
    def add_agent(self, agent: ContextOptimizedAgent) -> None:
        """添加智能体到池中"""
        self._agents[agent.agent_id] = agent
        logger.info(f"➕ 智能体池添加: {agent.agent_id}")
    
    def remove_agent(self, agent_id: str) -> bool:
        """从池中移除智能体"""
        if agent_id in self._agents:
            del self._agents[agent_id]
            logger.info(f"➖ 智能体池移除: {agent_id}")
            return True
        return False
    
    def get_agent(self, agent_id: str) -> Optional[ContextOptimizedAgent]:
        """获取指定智能体"""
        return self._agents.get(agent_id)
    
    def get_all_agents(self) -> Dict[str, ContextOptimizedAgent]:
        """获取所有智能体"""
        return self._agents.copy()
    
    def get_agent_list(self) -> List[str]:
        """获取智能体ID列表"""
        return list(self._agents.keys())
    
    async def process_with_agent(self, agent_id: str, state: Dict) -> Dict:
        """使用指定智能体处理状态"""
        agent = self.get_agent(agent_id)
        if not agent:
            raise ValueError(f"智能体不存在: {agent_id}")
        
        return await agent.process_with_context_optimization(state)
    
    def __len__(self) -> int:
        """返回智能体数量"""
        return len(self._agents)


# 🔥 全局智能体池实例
_global_agent_pool = AgentPool()

def get_agent_pool() -> AgentPool:
    """获取全局智能体池"""
    return _global_agent_pool


# 导出主要类
__all__ = [
    'ContextOptimizedAgent',
    'AgentPool',
    'get_agent_pool'
] 