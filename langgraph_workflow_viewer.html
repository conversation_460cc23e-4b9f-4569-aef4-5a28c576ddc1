<!DOCTYPE html>
<html>
<head>
    <title>LangGraph 工作流可视化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .workflow-section { margin: 40px 0; text-align: center; }
        h1 { color: #333; }
        h2 { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 LangGraph 增强工作流可视化</h1>
        
        <div class="workflow-section">
            <h2>简单顺序工作流</h2>
            <object data="langgraph_simple_workflow.svg" type="image/svg+xml" width="800" height="600">
                您的浏览器不支持SVG
            </object>
        </div>
        
        <div class="workflow-section">
            <h2>复杂多智能体工作流</h2>
            <object data="langgraph_complex_workflow.svg" type="image/svg+xml" width="1000" height="700">
                您的浏览器不支持SVG
            </object>
        </div>
        
        <div class="workflow-section">
            <h2>📖 说明</h2>
            <p>这些图展示了LangGraph增强框架支持的不同工作流模式：</p>
            <ul style="text-align: left; display: inline-block;">
                <li><strong>简单顺序工作流</strong>: 节点按顺序依次执行</li>
                <li><strong>复杂并行工作流</strong>: 支持多智能体并行处理</li>
                <li><strong>条件分支</strong>: 根据执行结果动态选择路径</li>
                <li><strong>智能合并</strong>: 将并行结果智能合并</li>
            </ul>
        </div>
        
        <div class="workflow-section">
            <p style="color: #666; font-size: 12px;">
                生成时间: 2025-06-29 21:53:30
            </p>
        </div>
    </div>
</body>
</html>