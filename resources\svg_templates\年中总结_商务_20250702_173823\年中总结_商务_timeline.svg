<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
  <defs>
    <!-- Color Palette from Design Norms -->
    <style type="text/css">
      <![CDATA[
        :root {
          --primary-color: #1E40AF;
          --secondary-color: #475569;
          --accent-color: #3B82F6;
          --background-color: #F8FAFC;
          --text-primary: #1E293B;
          --text-secondary: #64748B;
          --text-light: #94A3B8;
          --card-background: #FFFFFF;
          --card-border: #BAE6FD;
          --container-background: #E0F2FE;
        }

        /* Font Definitions */
        .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
        .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
        .font-accent { font-family: "Times New Roman", serif; }

        /* Font Sizes and Weights */
        .text-hero-title { font-size: 72px; font-weight: 700; } /* bold */
        .text-main-title { font-size: 56px; font-weight: 700; } /* bold */
        .text-section-title { font-size: 36px; font-weight: 700; } /* bold */
        .text-content-title { font-size: 28px; font-weight: 600; } /* semibold */
        .text-body { font-size: 22px; font-weight: 400; line-height: 1.4; } /* normal */
        .text-small { font-size: 16px; font-weight: 400; } /* normal */
        .text-caption { font-size: 14px; font-weight: 400; } /* normal */

        /* Text Colors */
        .fill-text-primary { fill: var(--text-primary); }
        .fill-text-secondary { fill: var(--text-secondary); }
        .fill-text-light { fill: var(--text-light); }
        .fill-primary { fill: var(--primary-color); }
        .fill-secondary { fill: var(--secondary-color); }
        .fill-accent { fill: var(--accent-color); }
        .fill-background { fill: var(--background-color); }
        .fill-card-background { fill: var(--card-background); }
        .fill-container-background { fill: var(--container-background); }

        /* Stroke Colors */
        .stroke-primary { stroke: var(--primary-color); }
        .stroke-secondary { stroke: var(--secondary-color); }
        .stroke-accent { stroke: var(--accent-color); }
        .stroke-card-border { stroke: var(--card-border); }

        /* Common Styles */
        .card-style {
          fill: var(--card-background);
          stroke: var(--card-border);
          stroke-width: 1px;
          rx: 12;
          ry: 12;
          filter: url(#card-shadow);
        }
      ]]>
    </style>

    <!-- Drop Shadow Filter for cards -->
    <filter id="card-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2"/>
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2"/>
      <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
      <feMerge>
        <feMergeNode in="matrixOut"/>
        <feMergeNode in="matrixOut2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Linear Gradient for background (subtle) -->
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="var(--background-color)"/>
      <stop offset="100%" stop-color="var(--container-background)"/>
    </linearGradient>

    <!-- Accent Gradient for highlight elements -->
    <linearGradient id="accent-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Icon for milestones (simple star) -->
    <symbol id="icon-star" viewBox="0 0 24 24">
      <path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.908-7.416 3.908 1.48-8.279-6.064-5.828 8.332-1.151z" fill="var(--accent-color)"/>
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#bg-gradient)"/>

  <!-- Decorative elements (subtle geometric shapes - Bento Grid influence) -->
  <rect x="150" y="80" width="250" height="100" fill="var(--primary-color)" opacity="0.05" rx="15" ry="15"/>
  <rect x="1500" y="900" width="200" height="80" fill="var(--accent-color)" opacity="0.03" rx="10" ry="10"/>
  <circle cx="1750" cy="150" r="120" fill="var(--secondary-color)" opacity="0.04"/>

  <!-- Header Section -->
  <g id="header" transform="translate(80 60)">
    <!-- Logo Placeholder -->
    <rect x="0" y="0" width="120" height="40" fill="var(--primary-color)" rx="5" ry="5"/>
    <text x="60" y="27" class="font-primary text-small" text-anchor="middle" fill="white">LOGO</text>
    <text x="1760" y="30" class="font-secondary text-small fill-text-light" text-anchor="end">页面 8/10</text>

    <!-- Main Title -->
    <text x="960" y="80" class="font-primary text-main-title fill-text-primary" text-anchor="middle">{title}</text>
    <text x="960" y="135" class="font-secondary text-content-title fill-text-secondary" text-anchor="middle">{subtitle}</text>
  </g>

  <!-- Main Content Area - Timeline -->
  <g id="timeline-content" transform="translate(160 300)">
    <!-- Timeline Line -->
    <line x1="0" y1="250" x2="1600" y2="250" stroke="var(--secondary-color)" stroke-width="3" stroke-dasharray="8 8"/>

    <!-- Timeline Nodes and Descriptions -->

    <!-- Node 1: Project Kick-off (Milestone) -->
    <g id="timeline-node-1">
      <circle cx="0" cy="250" r="10" fill="var(--accent-color)" stroke="var(--accent-color)" stroke-width="3"/>
      <circle cx="0" cy="250" r="6" fill="var(--card-background)"/>
      <use xlink:href="#icon-star" x="-12" y="220" width="24" height="24"/> <!-- Milestone icon -->
      <rect x="-100" y="50" width="200" height="150" class="card-style"/>
      <text x="0" y="85" class="font-primary text-content-title fill-primary" text-anchor="middle">Q1 启动</text>
      <text x="0" y="115" class="font-secondary text-small fill-text-secondary" text-anchor="middle">Project Kick-off</text>
      <text x="0" y="150" class="font-primary text-body fill-text-primary" text-anchor="middle" dominant-baseline="middle">
        <tspan x="0" dy="0">{date}: 2023年1月</tspan>
        <tspan x="0" dy="25">{content}: 团队组建和需求分析完成</tspan>
      </text>
    </g>

    <!-- Node 2: Prototype Design -->
    <g id="timeline-node-2" transform="translate(400 0)">
      <circle cx="0" cy="250" r="10" fill="var(--primary-color)" stroke="var(--primary-color)" stroke-width="3"/>
      <circle cx="0" cy="250" r="6" fill="var(--card-background)"/>
      <rect x="-100" y="290" width="200" height="150" class="card-style"/>
      <text x="0" y="325" class="font-primary text-content-title fill-primary" text-anchor="middle">原型设计</text>
      <text x="0" y="355" class="font-secondary text-small fill-text-secondary" text-anchor="middle">Prototype Design</text>
      <text x="0" y="390" class="font-primary text-body fill-text-primary" text-anchor="middle" dominant-baseline="middle">
        <tspan x="0" dy="0">{date}: 2023年3月</tspan>
        <tspan x="0" dy="25">{content}: 用户反馈收集和迭代</tspan>
      </text>
    </g>

    <!-- Node 3: Core Features (Milestone) -->
    <g id="timeline-node-3" transform="translate(800 0)">
      <circle cx="0" cy="250" r="10" fill="var(--accent-color)" stroke="var(--accent-color)" stroke-width="3"/>
      <circle cx="0" cy="250" r="6" fill="var(--card-background)"/>
      <use xlink:href="#icon-star" x="-12" y="220" width="24" height="24"/> <!-- Milestone icon -->
      <rect x="-100" y="50" width="200" height="150" class="card-style"/>
      <text x="0" y="85" class="font-primary text-content-title fill-primary" text-anchor="middle">核心功能</text>
      <text x="0" y="115" class="font-secondary text-small fill-text-secondary" text-anchor="middle">Core Features</text>
      <text x="0" y="150" class="font-primary text-body fill-text-primary" text-anchor="middle" dominant-baseline="middle">
        <tspan x="0" dy="0">{date}: 2023年5月</tspan>
        <tspan x="0" dy="25">{content}: 主要模块开发和测试</tspan>
      </text>
    </g>

    <!-- Node 4: Internal Review -->
    <g id="timeline-node-4" transform="translate(1200 0)">
      <circle cx="0" cy="250" r="10" fill="var(--primary-color)" stroke="var(--primary-color)" stroke-width="3"/>
      <circle cx="0" cy="250" r="6" fill="var(--card-background)"/>
      <rect x="-100" y="290" width="200" height="150" class="card-style"/>
      <text x="0" y="325" class="font-primary text-content-title fill-primary" text-anchor="middle">内部评审</text>
      <text x="0" y="355" class="font-secondary text-small fill-text-secondary" text-anchor="middle">Internal Review</text>
      <text x="0" y="390" class="font-primary text-body fill-text-primary" text-anchor="middle" dominant-baseline="middle">
        <tspan x="0" dy="0">{date}: 2023年6月</tspan>
        <tspan x="0" dy="25">{content}: 收集部门反馈和优化</tspan>
      </text>
    </g>

    <!-- Current Node / End of Mid-Year (Highlighted) -->
    <g id="timeline-node-current" transform="translate(1600 0)">
      <circle cx="0" cy="250" r="15" fill="url(#accent-gradient)" stroke="var(--primary-color)" stroke-width="4"/>
      <circle cx="0" cy="250" r="8" fill="var(--card-background)"/>
      <text x="0" y="240" class="font-primary text-small fill-primary" text-anchor="middle" font-weight="bold">NOW</text>
      <text x="0" y="270" class="font-secondary text-small fill-text-secondary" text-anchor="middle">Mid-Year</text>

      <rect x="-120" y="50" width="240" height="180" class="card-style"/>
      <text x="0" y="85" class="font-primary text-section-title fill-primary" text-anchor="middle">年中总结</text>
      <text x="0" y="120" class="font-secondary text-content-title fill-text-secondary" text-anchor="middle">Mid-Year Review</text>
      <text x="0" y="160" class="font-primary text-body fill-text-primary" text-anchor="middle" dominant-baseline="middle">
        <tspan x="0" dy="0">{date}: 2023年7月</tspan>
        <tspan x="0" dy="25">{content}: 成果汇报和下半年规划</tspan>
        <tspan x="0" dy="25">{author}: {author}</tspan>
      </text>
    </g>
  </g>

  <!-- Footer -->
  <g id="footer" transform="translate(80 980)">
    <text x="0" y="0" class="font-secondary text-small fill-text-light">{author} | {date}</text>
  </g>

  <!-- Super large number / element for emphasis (Bento Grid influence) -->
  <g id="emphasis-element" transform="translate(1840 180)">
    <text x="0" y="0" class="font-accent text-hero-title" fill="var(--primary-color)" opacity="0.1" text-anchor="end">
      <tspan>2023</tspan>
    </text>
    <text x="0" y="60" class="font-accent text-section-title" fill="var(--accent-color)" opacity="0.2" text-anchor="end">
      <tspan>Milestones</tspan>
    </text>
  </g>

</svg>